<template>
    <base-content>
        <div class="q-pa-md">
            <q-dialog v-model="showDetailDialog" transition-show="flip-down">
                <q-card style="width:1000px; max-width: 100vw;" v-if="showDetailData">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6 ">BP查询</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>

                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template v-for="(value, key) in showDetailData">
                                <div v-if="key == '发现的问题' || key == '建议解决方案' || key == '拒绝理由'" style="width: 100%;">
                                    <q-input type="textarea" filled v-model="showDetailData[key]" dense stack-label
                                        :label="key" readonly style="height: 100px;" />
                                </div>
                                <div v-else>
                                    <q-input filled v-model="showDetailData[key]" dense stack-label :label="key"
                                        readonly style="width:300px" />
                                </div>

                                <!-- {{ key }}:{{ value }} -->
                            </template>
                        </div>
                        <!-- <div class="q-gutter-md row items-start"></div> -->
                        <div class="q-gutter-md" style="margin-top: 10px;">
                        </div>
                        <div class="q-gutter-md row items-start" style="margin-top: 10px;">

                        </div>
                        <!-- <q-input outlined v-model="approveIdea.row['Owner_Reject_Cause']" label-slot clearable
                            type="textarea" style="height: 100px;margin-top: 10px;">
                            <template v-slot:label>拒绝理由</template>
                        </q-input> -->
                    </q-card-section>
                </q-card>
            </q-dialog>


            <q-dialog v-model="firtDialog" transition-show="flip-down">
                <q-card style="width:800px; max-width: 100vw;" v-if="approveIdea">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6 ">BP审批</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>

                    <q-card-section>
                        <div class="q-gutter-md row items-start">

                            <q-input v-model="approveIdea.row['部门']" standout disable filled autogrow label="部门/生产线"
                                style="width: 100px;" dense />
                            <q-input v-model="approveIdea.row['提交人']" standout disable filled autogrow label="提交人"
                                style="width: 150px;" dense />
                            <q-input v-model="approveIdea.row['BP类型']" standout disable filled autogrow label="BP类型"
                                style="width: 150px;" dense />
                            <q-input v-model="approveIdea.row['BP类型']" standout disable filled autogrow label="BP类型"
                                style="width: 150px;" dense />
                            <q-input v-model="approveIdea.row['是否变更设备']" standout disable filled autogrow label="是否变更设备"
                                style="width: 150px;" dense />
                        </div>
                        <!-- <div class="q-gutter-md row items-start"></div> -->
                        <div class="q-gutter-md" style="margin-top: 10px;">
                            <q-input outlined v-model="approveIdea.row['发现的问题']" label-slot clearable type="textarea"
                                disable filled style="height: 100px;" dense>
                                <template v-slot:label>发现的问题</template>
                            </q-input>
                            <q-input outlined v-model="approveIdea.row['建议解决方案']" label-slot clearable type="textarea"
                                disable filled style="height: 100px;" dense>
                                <template v-slot:label>请写下您的建议</template>
                            </q-input>
                        </div>
                        <div class="q-gutter-md row items-start" style="margin-top: 10px;">
                            <q-btn class="text-bold btn-fixed-width" color="primary" style="width: 300px;" dense
                                @click="downloadFile(approveIdea.row['BP链接'])">BP文件</br>{{
                                approveIdea.row['BP链接'].split('/')[4] }}</q-btn>
                            <q-btn v-if="approveIdea.row['变更设备图纸']" class="text-bold btn-fixed-width" color="primary"
                                style="width: 300px;" dense
                                @click="downloadFile(approveIdea.row['变更设备图纸'])">变更设备图纸</br>{{
                                approveIdea.row['变更设备图纸'].split('/')[4] }}</q-btn>
                        </div>

                        <div class="q-gutter-md row items-start" style="margin-top: 10px;">
                            <q-select outlined v-model="approveIdea.row['BP价值']"
                                :options="approveIdeaDropdown['Sharing']" label-slot clearable style="width: 200px;"
                                dense>
                                <template v-slot:label>BP价值
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必选</em>
                                </template>
                            </q-select>

                        </div>
                        <q-input outlined v-model="approveIdea.row['拒绝理由']" label-slot clearable type="textarea"
                            style="height: 100px;margin-top: 10px;" dense>
                            <template v-slot:label>拒绝理由</template>
                        </q-input>
                    </q-card-section>
                    <q-card-actions align="right">
                        <q-btn style="width:150px" label="批准" color="purple" @click="updateIdea('Yes')" />
                        <q-btn style="width:150px" label="拒绝" color="red" @click="updateIdea('No')" />
                    </q-card-actions>
                </q-card>
            </q-dialog>






            <div class="q-gutter-y-md">
                <q-card>
                    <q-tabs v-model="tab" inline-label outside-arrows mobile-arrows align="justify"
                        class="bg-primary text-white shadow-2">
                        <q-tab name="inputBP" icon="mail" label="BP提交" />
                        <q-tab name="queryBP" icon="mail" label="BP查询" />
                        <q-tab name="myApproval" icon="alarm" label="BP审批" />
                    </q-tabs>
                    <q-separator />

                    <q-tab-panels v-model="tab" animated>
                        <q-tab-panel name="queryBP">
                            <div class="q-gutter-md row items-start">
                                <q-select dense outlined v-model="myIdeaSelectedData['department']"
                                    :options="myIdeaDropDownData['department']" label-slot clearable
                                    style="width:200px;">
                                    <template v-slot:label>部门/生产线 <em
                                            class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必选</em></template>
                                </q-select>

                                <q-select dense outlined v-model="myIdeaSelectedData['employee_Name']"
                                    :options="myIdeaDropDownData['employee_Name']" label-slot clearable
                                    style="width: 200px;">
                                    <template v-slot:label>提交人<em class="q-px-sm bg-primary text-white rounded-borders"
                                            style="font-size: 12px;">可选</em></template>
                                </q-select>
                            </div>
                            <div v-if="myIdeaData">
                                <q-table :data="myIdeaData" row-key="name" :columns="myIdeaColumn"
                                    :pagination.sync="myPagination" :filter="filter" dense
                                    class="my-sticky-virtscroll-table" virtual-scroll
                                    :virtual-scroll-sticky-size-start="48" :rows-per-page-options="[0]">
                                    <template v-slot:top="props">
                                        <div class="text-h6" style="font-weight: 600;">BP清单</div>
                                        <q-space></q-space>
                                        <q-input borderless dense debounce="300" v-model="filter" placeholder="Search"
                                            class="bg-indigo-1">
                                            <template v-slot:append>
                                                <q-icon name="search" />
                                            </template>
                                        </q-input>

                                        <q-btn flat round dense
                                            :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
                                            @click="props.toggleFullscreen" class="q-ml-md" label="全屏" />
                                    </template>

                                    <template v-slot:body="props">
                                        <q-tr :props="props" @click="toShow(props)">
                                            <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                                {{ col.value }}
                                            </q-td>
                                        </q-tr>
                                    </template>
                                </q-table>
                            </div>
                        </q-tab-panel>
                    </q-tab-panels>

                    <q-tab-panels v-model="tab" animated>
                        <q-tab-panel name="inputBP">
                            <div>
                                <div class="q-gutter-md row items-start">
                                    <q-select outlined v-model="inputData['department']"
                                        :options="dropDownData['department']" label-slot clearable style="width: 200px;"
                                        dense>
                                        <template v-slot:label>部门/生产线
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['employee_Name']"
                                        :options="dropDownData['employee_Name']" label-slot clearable
                                        style="width: 200px;" dense>
                                        <template v-slot:label>提交人
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['BP_Type']" :options="dropDownData['BP_Type']"
                                        label-slot clearable style="width: 200px;" dense>
                                        <template v-slot:label>BP类型
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['Improve_Line']"
                                        :options="dropDownData['Improve_Area']" label-slot clearable
                                        style="width: 200px;" dense>
                                        <template v-slot:label>改善区域
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>
                                    <q-select outlined v-model="inputData['BPEquipment_Upgrading']"
                                        :options="['是', '否']" label-slot clearable style="width: 200px;" dense>
                                        <template v-slot:label>是否设备变更
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>
                                </div>
                                <!-- <div class="q-gutter-md row items-start"></div> -->
                                <div class="q-gutter-md" style="margin-top: 10px;">
                                    <q-input outlined v-model="inputData['BP_Problem']" label-slot clearable
                                        type="textarea" style="height: 100px;" dense>
                                        <template v-slot:label>请描述发现的问题
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填
                                            </em>
                                        </template>
                                    </q-input>

                                    <q-input outlined v-model="inputData['BP_Solution']" label-slot clearable
                                        type="textarea" style="height: 100px;" dense>
                                        <template v-slot:label>请写下您的建议
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填
                                            </em>
                                        </template>
                                    </q-input>
                                </div>

                                <div class="q-gutter-md row items-start" style="margin-top: 0px;">
                                    <q-uploader batch multiple auto-upload dense :url="webURL_upload + '?system=BP'"
                                        style="max-width: 300px;height:150px" @uploaded="handleUploadSuccess"
                                        label="BP文件上传" />
                                    <template v-if="inputData['BPEquipment_Upgrading'] == '是'">
                                        <q-uploader batch multiple auto-upload dense
                                            :url="webURL_upload + '?system=BPDraw'" color="secondary"
                                            style="max-width: 300px;height:150px"
                                            @uploaded="handleUploadSuccess_BPdrawing" label="设备改造图纸上传" />
                                    </template>
                                </div>
                                <q-btn color="secondary" label="提交BP" size="lg" @click="submitIdea()"
                                    style="margin-top: 10px;" />
                            </div>
                        </q-tab-panel>

                        <q-tab-panel name="myApproval">
                            <div v-if="approveIdeaList">
                                <q-table :data="approveIdeaList" row-key="name" :pagination.sync="myPagination"
                                    :filter="filter1" dense class="my-sticky-virtscroll-table" virtual-scroll
                                    :virtual-scroll-sticky-size-start="48" :rows-per-page-options="[0]">
                                    <template v-slot:top="props">
                                        <div class="text-h6" style="font-weight: 600;">我的审批</div>
                                        <q-space></q-space>
                                        <div style="font-weight: 600;">如需查看所有项目请点击-></div>
                                        <q-toggle v-model="adpotFilter" color="green"
                                            :label="adpotFilter ? '未审批项目' : '所有项目'"
                                            style="font-weight: 600;margin-right: 20px;" />
                                        <q-input borderless dense debounce="300" v-model="filter1" placeholder="Search"
                                            class="bg-indigo-1">
                                            <template v-slot:append>
                                                <q-icon name="search" />
                                            </template>
                                        </q-input>

                                        <q-btn flat round dense
                                            :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
                                            @click="props.toggleFullscreen" class="q-ml-md" label="全屏" />
                                    </template>

                                    <template v-slot:body="props">
                                        <q-tr :props="props" @click="toApprove(props)">
                                            <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                                {{ col.value }}
                                            </q-td>
                                        </q-tr>
                                    </template>
                                </q-table>
                            </div>
                        </q-tab-panel>
                    </q-tab-panels>
                </q-card>

            </div>
        </div>
    </base-content>
</template>
<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import uploadURL from '../../utils/uploadURL'
export default {
    components: {
        BaseContent
    },
    data() {
        return {
            webURL_upload: '',
            filter: '',
            filter1: '',
            tab: 'inputBP',
            myPagination: { rowsPerPage: 0 },
            employee_List: [],
            dropDownData: {
                department: [],
                employee_Name: [],
                BP_Type: [],
                Improve_Area:[],
            },

            inputData: {
                department: '',
                employee_Name: '',
                date: '',
                BP_Type: '',
                BP_Problem: '',
                BP_Solution: '',
                BP_URL: '',
                BPEquipment_Upgrading: '',
                BPdrawing_URL: '',
                Improve_Line: ''
            },
            myIdeaData: false,
            myIdeaRawData: false,
            myIdeaDropDownData: {
                department: [],
                employee_Name: [],
            },
            myIdeaSelectedData: {
                department: [],
                employee_Name: [],
            },
            approveIdeaList: false,
            approveRawIdeaList: false,
            approveIdea: false,
            approveIdeaDropdown: {
                Sharing: ['铂金', '金', '银'],
                Idea_Sub_Type: [],
                assignedBy: []
            },
            //visibleColumns: ['Date','Employee_Name','Owner_Adopt','Owner_Reject_Cause','Idea_Problem','Idea_Solution'],
            myIdeaColumn: [
                { name: '提交日期', align: 'left', label: '提交日期', field: '提交日期', sortable: true, format: val => `${val.substring(0, 19).replace('T', ' ')}` },
                { name: '提交人', align: 'left', label: '提交人', field: '提交人', sortable: true },
                { name: '是否采纳', align: 'left', label: '是否采纳', field: '是否采纳', sortable: true },
                { name: '点子价值', align: 'left', label: '点子价值', field: '点子价值', sortable: true },
                { name: '发现的问题', align: 'left', label: '发现的问题', field: '发现的问题', sortable: true, style: 'width: 100px' },
                { name: '建议解决方案', align: 'left', label: '解决方案', field: '建议解决方案', sortable: true }
            ],
            adpotFilter: true,
            firtDialog: false,
            showDetailDialog: false,
            showDetailData: false
        }
    },
    mounted() {
        console.log('this.$query', this.$route.query)
        this.webURL_upload = uploadURL

        this.getDepartment()
        this.getImproveArea()
        this.getBPType()
        this.getEmployee()
        if (this.$route.query.function) {
            console.log('有参数')
            this.tab = this.$route.query.function
            this.myIdeaSelectedData['department'] = this.$route.query.department
        }
    },
    watch: {
        'inputData.department'(newValue, oldValue) {
            this.dropDownData['employee_Name'] = []
            this.inputData['employee_Name'] = ''
            this.filterEmployee('input')
        },
        'myIdeaSelectedData.department'(newValue, oldValue) {
            this.myIdeaDropDownData['employee_Name'] = []
            this.myIdeaSelectedData['employee_Name'] = ''
            this.filterEmployee('myIdea')
            this.getMyIdead()
            // this.filterApplicant()
        },
        'myIdeaSelectedData.employee_Name'(newValue, oldValue) {
            this.filterApplicant()
        },
        adpotFilter(newValue, oldValue) {
            this.filterMyApproval()
        },
        tab() {
            if (this.tab == 'myApproval') {
                this.getMyApprovalIdea()
            }
        }
    },

    methods: {
        async getDepartment() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getDepartment')
            console.log('getDepartment', res)
            _this.dropDownData['department'] = res
            _this.myIdeaDropDownData['department'] = res
        },
        async getImproveArea() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getDepartment_Owner_List')
            console.log('getDepartment_Owner_List', res)
            let area = []
            for (let i = 0; i < res.length; i++){
                area.push(res[i]['Area'])
            }
            _this.dropDownData['Improve_Area'] = area
            // _this.myIdeaDropDownData['department'] = res
        },
        async getEmployee() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getEmployee')
            console.log('getEmployee', res)
            _this.employee_List = res
            // _this.filterAssginedBy()
        },
        async getBPType() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getBPType')
            console.log('getBPType', res)
            _this.dropDownData['BP_Type'] = res
        },
        async getMyIdead() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/queryBP_ByDepartment?department=' + _this.myIdeaSelectedData['department'])
            console.log('queryIdea_ByDepartment', res)
            _this.myIdeaData = res
            _this.myIdeaRawData = res
            // const keys = Object.keys(res);
            // keys.forEach(key => {
            //     employeeList[key] = null;
            // });
            _this.filterApplicant()

        },
        async getMyApprovalIdea() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`ideabank/myApprovalIdea_BP?employeeID=${localStorage.getItem('account')}&processType=ideaApprove`)
            console.log('getMyApprovalIdead', res)
            _this.approveIdeaList = res
            _this.approveRawIdeaList = res
            // const keys = Object.keys(res);
            // keys.forEach(key => {
            //     employeeList[key] = null;
            // });
            _this.filterMyApproval()
        },
        filterEmployee(fun) {
            const _this = this
            console.log('department', _this.inputData['department'])
            for (let i = 0; i < _this.employee_List.length; i++) {
                if (fun == 'input') {
                    if (_this.employee_List[i]['line'] === _this.inputData['department']) {
                        _this.dropDownData['employee_Name'].push(_this.employee_List[i]['Employee_PYName'])
                    }
                } else {
                    if (_this.employee_List[i]['line'] === _this.myIdeaSelectedData['department']) {
                        _this.myIdeaDropDownData['employee_Name'].push({ label: _this.employee_List[i]['Employee_PYName'], value: _this.employee_List[i]['Employee_ID'] })
                    }
                }

            }
            console.log('selectedEmployee', _this.dropDownData['employee_Name'])
        },

        filterApplicant() {
            const _this = this
            console.log('_this.myIdeaSelectedData', _this.myIdeaSelectedData)
            const employee_name = _this.myIdeaSelectedData['employee_Name']
            console.log('employee_name', employee_name)
            _this.myIdeaData = []
            if (employee_name != null && employee_name != '') {
                for (let i = 0; i < _this.myIdeaRawData.length; i++) {
                    if (_this.myIdeaRawData[i]['提交人'] == employee_name['label']) {
                        _this.myIdeaData.push(_this.myIdeaRawData[i])
                    }
                }
            } else {
                _this.myIdeaData = _this.myIdeaRawData
            }
        },
        filterMyApproval() {
            const _this = this
            _this.approveIdeaList = []
            for (let i = 0; i < _this.approveRawIdeaList.length; i++) {
                if (_this.adpotFilter) {
                    if (_this.approveRawIdeaList[i]['是否采纳'] == null) {
                        _this.approveIdeaList.push(_this.approveRawIdeaList[i])
                    }
                } else {
                    _this.approveIdeaList = _this.approveRawIdeaList
                }
            }
        },
        submitIdea() {
            const _this = this
            console.log('_this.inputData', _this.inputData)
            if (_this.inputData['BP_Type'] == ''||_this.inputData['BP_Problem:'] ==''||_this.inputData['BP_Solution'] == ''||_this.inputData['BP_URL'] == ''||_this.inputData['BPEquipment_Upgrading'] == ''||_this.inputData['Improve_Line'] == '') {
                _this.$q.dialog({
                    title: 'BP无法保存',
                    message: '请填写完整必填项'
                })
                return
            }
            let Manager_ID = ''
            for (let i = 0; i < _this.employee_List.length; i++) {
                if (_this.employee_List[i]['Employee_PYName'] == _this.inputData['employee_Name']) {
                    Manager_ID = _this.employee_List[i]['Manager_ID']
                    break
                }
            }
            _this.$http.post('ideabank/insertBP', { 'data': _this.inputData, 'Manager_ID': Manager_ID }).then(function (response) {
                console.log('response', response)
                if (response.data === '已发送') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `BP提交成功！系统第一时间发送邮件给您的主管`,
                        position: 'top'
                    })
                    _this.inputData['BP_Type'] = ''
                    _this.inputData['BP_Problem:'] = ''
                    _this.inputData['BP_Solution'] = ''
                    _this.inputData['BP_URL'] = ''
                    _this.inputData['BPEquipment_Upgrading'] = ''
                    _this.inputData['BPdrawing_URL'] = ''
                    _this.inputData['Improve_Line'] = ''
                }
            })
        },
        toShow(props) {
            const _this = this
            _this.showDetailData = props.row
            console.log(_this.showDetailData)
            _this.showDetailDialog = true
        },


        toApprove(props) {
            console.log('toApprove', props)
            const _this = this
            _this.approveIdea = props
            let assignedName = ''
            // if (!_this.approveIdea.row['Assigned_By'].hasOwnProperty('value')) {
            //     for (let i = 0; i < _this.employee_List.length; i++) {
            //         if (_this.employee_List[i]['Employee_ID'] == props.row['Assigned_By']) {
            //             assignedName = _this.employee_List[i]['Employee_PYName'] + '-' + _this.employee_List[i]['Position']
            //             break
            //         }
            //     }
            //     _this.approveIdea.row['Assigned_By'] = { 'label': assignedName, 'value': props.row['Assigned_By'] }
            // }

            _this.firtDialog = true

        },
        updateIdea(status) {
            const _this = this
            console.log('approveIdea', _this.approveIdea)
            if (status == 'No' && _this.approveIdea.row['拒绝理由'] == null) {
                _this.$q.dialog({
                    title: '拒绝BP无法保存',
                    message: '拒绝BP需要写拒绝原因。请填写后继续点击拒绝'
                })
                return
            }
            if (status == 'Yes' && _this.approveIdea.row['BP价值'] == null) {
                _this.$q.dialog({
                    title: 'BP价值没有选择',
                    message: '由于BP价值没有选择无法保存。请选择后继续点击批准'
                })
                return
            }
            _this.approveIdea.row['是否采纳'] = status
            _this.$http.post('ideabank/updateBP', { 'data': _this.approveIdea.row, 'approver': localStorage.getItem('username')}).then(function (response) {
                console.log('response', response)
                if (response.data === "已更新") {
                    _this.$q.notify({
                        type: 'positive',
                        message: `点子审批成功！`,
                        position: 'top'
                    })
                    _this.getMyApprovalIdea()
                    _this.firtDialog = false
                }
            })

        },
        handleUploadSuccess(response) {
            console.log('url', this.webURL_upload + '?system=BP')
            console.log('response', response)
            var _this = this
            var responseFileName = response['files'][0]['name']
            var responseURL = response['files'][0]['xhr']['responseText']
            _this.inputData['BP_URL'] = responseURL
        },
        handleUploadSuccess_BPdrawing(response) {
            console.log('response', response)
            var _this = this
            var responseFileName = response['files'][0]['name']
            var responseURL = response['files'][0]['xhr']['responseText']
            _this.inputData['BPdrawing_URL'] = responseURL
        },
        downloadFile(url) {
            window.open(url, '_blank');
        },

    }
}
</script>

<style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */
  height: 500px
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0
</style>


