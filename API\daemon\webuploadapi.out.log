
Upload server running on http://localhost:3001
C:/uploadFile/Safty/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-24T07:09:08.813Z,
  filepath: 'D:\\uploadFile\\Safty\\invalid-name',
  newFilename: '14e98c9c416a02397c01a4300',
  originalFilename: '夹子.jpg',
  mimetype: 'image/jpeg',
  hashAlgorithm: false,
  size: 5335468,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: false,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\Safty\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 5335468,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name 夹子.jpg
D:\uploadFile\Safty\invalid-name
夹子.jpg
D:/uploadFile/Safty/ub36ipfbUd_夹子.jpg
newName  http://*************:3003/Safty/ub36ipfbUd_夹子.jpg
C:/uploadFile/Safty/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-24T07:12:28.019Z,
  filepath: 'D:\\uploadFile\\Safty\\invalid-name',
  newFilename: '14e98c9c416a02397c01a4301',
  originalFilename: 'a870e030434c78ef5250a6c8d2b5308.jpg',
  mimetype: 'image/jpeg',
  hashAlgorithm: false,
  size: 7932140,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: true,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\Safty\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 7932140,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name a870e030434c78ef5250a6c8d2b5308.jpg
D:\uploadFile\Safty\invalid-name
a870e030434c78ef5250a6c8d2b5308.jpg
D:/uploadFile/Safty/09FFTe8SUe_a870e030434c78ef5250a6c8d2b5308.jpg
newName  http://*************:3003/Safty/09FFTe8SUe_a870e030434c78ef5250a6c8d2b5308.jpg
C:/uploadFile/Safty/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-24T07:41:40.207Z,
  filepath: 'D:\\uploadFile\\Safty\\invalid-name',
  newFilename: '14e98c9c416a02397c01a4302',
  originalFilename: '微信图片_20240624154110.jpg',
  mimetype: 'image/jpeg',
  hashAlgorithm: false,
  size: 122609,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: true,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\Safty\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 122609,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name 微信图片_20240624154110.jpg
D:\uploadFile\Safty\invalid-name
微信图片_20240624154110.jpg
D:/uploadFile/Safty/007PSqUoue_微信图片_20240624154110.jpg
newName  http://*************:3003/Safty/007PSqUoue_微信图片_20240624154110.jpg
C:/uploadFile/Safty/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-24T07:47:57.349Z,
  filepath: 'D:\\uploadFile\\Safty\\invalid-name',
  newFilename: '14e98c9c416a02397c01a4303',
  originalFilename: '微信图片_20240624154740.jpg',
  mimetype: 'image/jpeg',
  hashAlgorithm: false,
  size: 362974,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: true,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\Safty\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 362974,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name 微信图片_20240624154740.jpg
D:\uploadFile\Safty\invalid-name
微信图片_20240624154740.jpg
D:/uploadFile/Safty/EIEDmoDAKy_微信图片_20240624154740.jpg
newName  http://*************:3003/Safty/EIEDmoDAKy_微信图片_20240624154740.jpg
C:/uploadFile/Odelay/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-24T13:23:37.100Z,
  filepath: 'D:\\uploadFile\\Odelay\\invalid-name',
  newFilename: '14e98c9c416a02397c01a4304',
  originalFilename: '2024-06-06白班.JPG',
  mimetype: 'image/jpeg',
  hashAlgorithm: false,
  size: 111167,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: true,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\Odelay\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 111167,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name 2024-06-06白班.JPG
D:\uploadFile\Odelay\invalid-name
2024-06-06白班.JPG
D:/uploadFile/Odelay/lIz4ts5i6h_2024-06-06白班.JPG
newName  http://*************:3003/Odelay/lIz4ts5i6h_2024-06-06白班.JPG
C:/uploadFile/Odelay/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-24T13:38:03.117Z,
  filepath: 'D:\\uploadFile\\Odelay\\invalid-name',
  newFilename: '14e98c9c416a02397c01a4305',
  originalFilename: '2024.06.23白.pdf',
  mimetype: 'application/pdf',
  hashAlgorithm: false,
  size: 475977,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: false,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\Odelay\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 475977,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name 2024.06.23白.pdf
D:\uploadFile\Odelay\invalid-name
2024.06.23白.pdf
D:/uploadFile/Odelay/274tPcKZCN_2024.06.23白.pdf
newName  http://*************:3003/Odelay/274tPcKZCN_2024.06.23白.pdf
C:/uploadFile/Safty/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-25T01:44:22.639Z,
  filepath: 'D:\\uploadFile\\Safty\\invalid-name',
  newFilename: '14e98c9c416a02397c01a4306',
  originalFilename: '1.jfif',
  mimetype: 'image/jpeg',
  hashAlgorithm: false,
  size: 117776,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: true,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\Safty\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 117776,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name 1.jfif
D:\uploadFile\Safty\invalid-name
1.jfif
D:/uploadFile/Safty/1YzqkzWqjx_1.jfif
newName  http://*************:3003/Safty/1YzqkzWqjx_1.jfif
C:/uploadFile/Safty/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-25T01:58:32.099Z,
  filepath: 'D:\\uploadFile\\Safty\\invalid-name',
  newFilename: '14e98c9c416a02397c01a4307',
  originalFilename: '1152.jfif',
  mimetype: 'image/jpeg',
  hashAlgorithm: false,
  size: 215176,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: true,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\Safty\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 215176,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name 1152.jfif
D:\uploadFile\Safty\invalid-name
1152.jfif
D:/uploadFile/Safty/VMkQ9HX67k_1152.jfif
newName  http://*************:3003/Safty/VMkQ9HX67k_1152.jfif
C:/uploadFile/hk/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-25T02:13:03.618Z,
  filepath: 'D:\\uploadFile\\hk\\invalid-name',
  newFilename: '14e98c9c416a02397c01a4308',
  originalFilename: 'Capture.PNG',
  mimetype: 'image/png',
  hashAlgorithm: false,
  size: 504654,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: true,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\hk\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 504654,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name Capture.PNG
D:\uploadFile\hk\invalid-name
Capture.PNG
D:/uploadFile/hk/vTj3tSfshT_Capture.PNG
newName  http://*************:3003/hk/vTj3tSfshT_Capture.PNG
C:/uploadFile/hk/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-25T02:15:00.565Z,
  filepath: 'D:\\uploadFile\\hk\\invalid-name',
  newFilename: '14e98c9c416a02397c01a4309',
  originalFilename: 'Capture.PNG',
  mimetype: 'image/png',
  hashAlgorithm: false,
  size: 504654,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: true,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\hk\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 504654,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name Capture.PNG
D:\uploadFile\hk\invalid-name
Capture.PNG
D:/uploadFile/hk/NG8wNqOoFp_Capture.PNG
newName  http://*************:3003/hk/NG8wNqOoFp_Capture.PNG
C:/uploadFile/lsw/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-25T02:39:23.899Z,
  filepath: 'D:\\uploadFile\\lsw\\invalid-name',
  newFilename: '14e98c9c416a02397c01a430a',
  originalFilename: 'Capture.PNG',
  mimetype: 'image/png',
  hashAlgorithm: false,
  size: 504654,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: false,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\lsw\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 504654,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name Capture.PNG
D:\uploadFile\lsw\invalid-name
Capture.PNG
D:/uploadFile/lsw/J4GmpeDhzL_Capture.PNG
newName  http://*************:3003/lsw/J4GmpeDhzL_Capture.PNG
C:/uploadFile/Safty/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-25T02:47:49.187Z,
  filepath: 'D:\\uploadFile\\Safty\\invalid-name',
  newFilename: '14e98c9c416a02397c01a430b',
  originalFilename: 'Image (1).jfif',
  mimetype: 'image/jpeg',
  hashAlgorithm: false,
  size: 2767375,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: true,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\Safty\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 2767375,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name Image (1).jfif
D:\uploadFile\Safty\invalid-name
Image (1).jfif
D:/uploadFile/Safty/aF64xxlq7A_Image(1).jfif
newName  http://*************:3003/Safty/aF64xxlq7A_Image(1).jfif
C:/uploadFile/lsw/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-25T03:17:17.295Z,
  filepath: 'D:\\uploadFile\\lsw\\invalid-name',
  newFilename: '14e98c9c416a02397c01a430c',
  originalFilename: 'powerBIImage.PNG',
  mimetype: 'image/png',
  hashAlgorithm: false,
  size: 122211,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: false,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\lsw\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 122211,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name powerBIImage.PNG
D:\uploadFile\lsw\invalid-name
powerBIImage.PNG
D:/uploadFile/lsw/vCWRBXkkM8_powerBIImage.PNG
newName  http://*************:3003/lsw/vCWRBXkkM8_powerBIImage.PNG
C:/uploadFile/lsw/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-25T03:28:55.328Z,
  filepath: 'D:\\uploadFile\\lsw\\invalid-name',
  newFilename: '14e98c9c416a02397c01a430d',
  originalFilename: 'Capture.PNG',
  mimetype: 'image/png',
  hashAlgorithm: false,
  size: 504654,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: true,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\lsw\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 504654,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name Capture.PNG
D:\uploadFile\lsw\invalid-name
Capture.PNG
D:/uploadFile/lsw/t7iGrz9gqb_Capture.PNG
newName  http://*************:3003/lsw/t7iGrz9gqb_Capture.PNG
C:/uploadFile/lsw/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-25T03:30:39.787Z,
  filepath: 'D:\\uploadFile\\lsw\\invalid-name',
  newFilename: '14e98c9c416a02397c01a430e',
  originalFilename: 'Capture.PNG',
  mimetype: 'image/png',
  hashAlgorithm: false,
  size: 504654,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: false,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\lsw\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 504654,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name Capture.PNG
D:\uploadFile\lsw\invalid-name
Capture.PNG
D:/uploadFile/lsw/QKFiRi0WJV_Capture.PNG
newName  http://*************:3003/lsw/QKFiRi0WJV_Capture.PNG
C:/uploadFile/lsw/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-25T03:31:40.894Z,
  filepath: 'D:\\uploadFile\\lsw\\invalid-name',
  newFilename: '14e98c9c416a02397c01a430f',
  originalFilename: 'Capture.PNG',
  mimetype: 'image/png',
  hashAlgorithm: false,
  size: 504654,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: true,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\lsw\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 504654,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name Capture.PNG
D:\uploadFile\lsw\invalid-name
Capture.PNG
D:/uploadFile/lsw/K0T5a29jap_Capture.PNG
newName  http://*************:3003/lsw/K0T5a29jap_Capture.PNG
C:/uploadFile/lsw/
files PersistentFile {
  _events: [Object: null prototype] { error: [Function] },
  _eventsCount: 1,
  _maxListeners: undefined,
  lastModifiedDate: 2024-06-25T04:18:10.839Z,
  filepath: 'D:\\uploadFile\\lsw\\invalid-name',
  newFilename: '14e98c9c416a02397c01a4310',
  originalFilename: 'Capture.PNG',
  mimetype: 'image/png',
  hashAlgorithm: false,
  size: 504654,
  _writeStream: WriteStream {
    _writableState: WritableState {
      objectMode: false,
      highWaterMark: 16384,
      finalCalled: true,
      needDrain: true,
      ending: true,
      ended: true,
      finished: true,
      destroyed: true,
      decodeStrings: true,
      defaultEncoding: 'utf8',
      length: 0,
      writing: false,
      corked: 0,
      sync: false,
      bufferProcessing: false,
      onwrite: [Function: bound onwrite],
      writecb: null,
      writelen: 0,
      afterWriteTickInfo: null,
      bufferedRequest: null,
      lastBufferedRequest: null,
      pendingcb: 0,
      prefinished: true,
      errorEmitted: false,
      emitClose: false,
      autoDestroy: false,
      bufferedRequestCount: 0,
      corkedRequestsFree: [Object]
    },
    writable: false,
    _events: [Object: null prototype] { error: [Function] },
    _eventsCount: 1,
    _maxListeners: undefined,
    path: 'D:\\uploadFile\\lsw\\invalid-name',
    fd: null,
    flags: 'w',
    mode: 438,
    start: undefined,
    autoClose: true,
    pos: undefined,
    bytesWritten: 504654,
    closed: false,
    [Symbol(kCapture)]: false,
    [Symbol(kIsPerformingIO)]: false
  },
  hash: null,
  [Symbol(kCapture)]: false
}
name Capture.PNG
D:\uploadFile\lsw\invalid-name
Capture.PNG
D:/uploadFile/lsw/DmeBBAXFpt_Capture.PNG
newName  http://*************:3003/lsw/DmeBBAXFpt_Capture.PNG
