const _Base = require('../config/dbbase')
const crypto = require('crypto');

function sqlexec() {
  _Base.call(this);
}

// 添加密码加密函数
function encryptPassword(password) {
  const secretKey = 'supply_chain_secret_key';
  const hash = crypto.createHmac('sha256', secretKey)
                    .update(password)
                    .digest('hex');
  return hash;
}

// 验证密码函数
function verifyPassword(plainPassword, hashedPassword) {
  const hash = encryptPassword(plainPassword);
  return hash === hashedPassword;
}

sqlexec.prototype = new _Base()


sqlexec.prototype.checkAccount = function (account, password, callBack) {
  // 去除传入参数的引号
  const cleanAccount = account.replace(/'/g, '');
  const cleanPassword = password.replace(/'/g, '');
  
  console.log(`查询用户: ${cleanAccount}`);
  
  // 查询用户信息，不再直接比对密码
  sqlexec.prototype._query(
    `SELECT a.*,b.Position, a.Psw as HashedPassword, c.Access_Group 
     FROM WebSite_login_List as a 
     LEFT JOIN Access_List as c ON a.Employee_ID = c.Employee_ID
	 left join Employee_List as b on a.Employee_ID=b.Employee_ID
     WHERE a.Employee_ID = '${cleanAccount}'`, 
    function (err, result) {
      if (err) {
        console.error('查询用户出错:', err);
        return callBack([]);
      }
      
      // 检查是否找到用户
      if (result.recordset.length === 0) {
        console.log('未找到用户');
        return callBack([]);
      }
      
      // 验证密码
      const user = result.recordset[0];
      const storedPassword = user.HashedPassword;
      
      if (storedPassword === cleanPassword) {
        // 旧密码是明文存储，进行密码升级
        console.log('用户使用旧密码登录，升级到加密密码');
        
        // 更新为加密密码
        const encryptedPassword = encryptPassword(cleanPassword);
        sqlexec.prototype._query(
          `UPDATE WebSite_login_List SET Psw = '${encryptedPassword}' WHERE Employee_ID = '${cleanAccount}'`,
          function(updateErr) {
            if (updateErr) {
              console.error('密码升级失败:', updateErr);
            } else {
              console.log('密码已升级为加密格式');
            }
          }
        );
        
        // 返回用户信息
        return callBack(result.recordset);
      } else if (verifyPassword(cleanPassword, storedPassword)) {
        // 使用加密比对验证通过
        console.log('密码验证成功');
        return callBack(result.recordset);
      } else {
        // 密码不匹配
        console.log('密码验证失败');
        return callBack([]);
      }
    }
  );
}

sqlexec.prototype.modifyPSW = function (account, password, callBack) {
  // 去除传入参数的引号
  const cleanAccount = account.replace(/'/g, '');
  const cleanPassword = password.replace(/'/g, '');
  
  // 加密密码
  const encryptedPassword = encryptPassword(cleanPassword);
  
  sqlexec.prototype._query(
    `UPDATE dbo.WebSite_login_List SET Psw = '${encryptedPassword}' WHERE Employee_ID = '${cleanAccount}'`, 
    function (err, result) {
      if (err) {
        console.error('修改密码出错:', err);
        return callBack('修改失败');
      }
      return callBack('修改成功');
    }
  );
}

module.exports = sqlexec;