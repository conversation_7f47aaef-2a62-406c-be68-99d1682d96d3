{"name": "NanjingMill", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@quasar/extras": "^1.16.17", "axios": "^1.6.0", "core-js": "^3.30.0", "crypto-js": "^4.2.0", "dhtmlx-gantt": "^9.0.7", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "moment": "^2.29.4", "papaparse": "^5.5.2", "pptxgenjs": "^4.0.0", "qrcode": "^1.5.3", "qs": "^6.12.0", "quagga": "^0.12.1", "quasar": "^1.0.0", "vue": "^2.6.14", "vue-gantt-chart": "^1.0.11", "vue-print-nb": "^1.7.5", "vue-router": "^3.5.4", "vue-webview": "0.0.34", "vuex": "^3.6.2", "xlsx": "^0.18.5"}, "devDependencies": {"@achrinza/node-ipc": "^10.1.11", "@babel/plugin-transform-class-properties": "^7.18.6", "@kangc/v-md-editor": "1.4.10", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "animate.css": "^3.6.1", "babel-plugin-transform-imports": "1.5.0", "compression-webpack-plugin": "^1.1.12", "cross-env": "^7.0.3", "echarts": "^4.9.0", "joi": "^17.6.0", "lottie-web": "^5.7.3", "name-styles": "^2.0.2", "sass": "^1.32.8", "sass-loader": "^10.0.2", "svgo": "^2.8.0", "uuid": "^9.0.0", "vue-cli-plugin-quasar": "~2.0.2", "vue-count-to": "^1.0.13", "vue-echarts": "^5.0.0-beta.0", "vue-json-views": "^1.3.0", "vue-template-compiler": "^2.6.14", "webpack-bundle-analyzer": "^4.2.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "IE 11", "IE 10"]}