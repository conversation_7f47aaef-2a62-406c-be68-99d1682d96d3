<template>
	<div>
		<div style="width:100%;height:500px;" :id="echarts" class="echarts" ref="echarts"></div>
	</div>
</template>

<script>
	// 引入echarts
	import echarts from 'echarts'
	export default {
		name: 'EchartsComponents',
		props: {
			// 接收父组件传递过来的信息

			chartData: {
				categories: {
					type: Array,
					default: () => []
				},
				series: {
					type: Array,
					default: () => []
				},
				name: {
					type: String
				},

			}

		},
		data() {
			return {}
		},
		methods: {
			drawChart() {
				var _this = this
				// myChart.hideLoading();
				var sizeFunction = function(x) {
					var y = Math.sqrt(x / 50) + 0.1;
					return y * 80;
				};
				var itemStyle ={
					
				}
				const data = _this.chartData
				console.log(data)
				// Schema:
				var schema = [{
						name: '营业收入',
						index: 0,
						text: '营业收入',
						unit: '元'
					},
					{
						name: '扣非净利润',
						index: 1,
						text: '扣非净利润',
						unit: '元'
					},
					{
						name: 'ROE',
						index: 2,
						text: 'ROE',
						unit: '%'
					},
					{
						name: '股票名称',
						index: 3,
						text: '股票名称',
						unit: ''
					}
				];
				const option = {
					baseOption: {
						timeline: {
							axisType: 'category',
							orient: 'horizontal',
							autoPlay: false,
							inverse: false,
							playInterval: 1000,
							left: 0,
							right: 100,
							top: 0,
							bottom: 20,
							width: '98%',
							height: 40,
							symbol: 'none',
							currentIndex:data.timeline.length-1,
							loop:false,
							symbolRotate:20,
							checkpointStyle: {
								borderWidth: 2
							},
							controlStyle: {
								showNextBtn: true,
								showPrevBtn: true
							},
							data: []
						},
						title: [{
								text: data.timeline[0],
								textAlign: 'left',
								left: '63%',
								top: '55%',
								textStyle: {
									fontSize: 50
								}
							},
							// {
							//   text: '各国人均寿命与GDP关系演变',
							//   left: 'center',
							//   top: 10,
							//   textStyle: {
							//     fontWeight: 'normal',
							//     fontSize: 20
							//   }
							// }
						],
						tooltip: {
							padding: 5,
							borderWidth: 1,
							formatter: function(obj) {
								// console.log(obj)
								var value = obj.value;
								var res = schema[3].text + '：' + value[3] + '<br>'+ schema[2].text + '：' + value[2] + schema[2].unit + '<br>' 
								var val=''
								var d=[1,2]
								for(var i=0;i<2;i++){
									// console.log(value[i])
									if (value[i] >= 100000000 ||value[i] <= -100000000) {
									  val = (value[i] / 100000000).toFixed(2) + '亿';
									} else {
									  val = (value[i] / 10000).toFixed(0) + '万';
									}
									res +=schema[i].text + '：' + val + '<br>' 
								}
								// console.log(res)
								return res;
								
							}
						},
						grid: {
							top: 100,
							containLabel: true,
							left: 30,
							right: '110'
						},
						xAxis: {
							type: 'log',
							name: '营业收入',

							nameGap: 25,
							nameLocation: 'middle',
							nameTextStyle: {
								fontSize: 18
							},
							splitLine: {
								show: false
							},
							axisLabel: {
								formatter:function(datas){

									var val=''
									if (datas >= 100000000 || datas <= -100000000) {
									  val = (datas / 100000000).toFixed(1) + '亿';
									} else {
									  val = (datas / 10000).toFixed(0) + '万';
									}
									return val;
								}
							}
						},
						yAxis: {
							type: 'value',
							name: '扣非净利润',

							nameTextStyle: {
								fontSize: 18
							},
							splitLine: {
								show: false
							},
							axisLabel: {
								// formatter: '{value} 元'
								formatter:function(datas){
									// console.log(datas)
									var val=''
									if (datas >= 100000000 || datas <= -100000000) {
									  val = (datas / 100000000).toFixed(1) + '亿';
									} else {
									  val = (datas / 10000).toFixed(0) + '万';
									}
									return val;
								}
							}
						},
						//   visualMap: [
						//     {
						//       show: false,
						//       dimension: 3,
						//       categories: data.stockname,
						//       inRange: {
						//         color: function (params) {
						// console.log('params',params)
						//           // prettier-ignore
						//           var colors = ['#51689b'];
						//           return colors.concat(colors);
						//         }()
						//       }
						//     }
						//   ],
						series: [{
							type: 'scatter',
							itemStyle: itemStyle,
							data: data.series[0],
							symbolSize: function(val) {
								// console.log(val)
								return sizeFunction(val[2]);
							}
						}],
						animationDurationUpdate: 500,
						animationEasingUpdate: 'quinticInOut'
					},
					options: []
				};
				for (var n = 0; n < data.timeline.length; n++) {
					option.baseOption.timeline.data.push(data.timeline[n]);
					option.options.push({
						title: {
							show: true,
							text: data.timeline[n] + ''
						},
						series: {
							name: data.timeline[n],
							type: 'scatter',
							// color: function(params){
							// 	console.log(params)
							// 	if (params[3].indexof(data.queryCode)!=-1){
							// 		return '#aa0000'
							// 	}else{
							// 		return '#51689b'
							// 	}
							// },
							// color:'#aa0000',
							itemStyle: {
								opacity: 0.8,
								normal: {
									color: function(params) {
										// console.log(params.value[3])
										if (params.value[3].indexOf(data.queryCode) != -1) {
											return '#aa0000'
										} else {
											return '#55aaff'
										}
									}
			  			}
							},
							data: data.series[n],
							symbolSize: function(val) {
								// console.log(val)
								return sizeFunction(val[2]);
							}
						}
					});
				}


				// 基于准备好的dom，初始化echarts实例
				var myChart = echarts.init(document.getElementById(_this.echarts))
				// 绘制图表
				myChart.setOption(option)
				myChart.on('click',function(param){
					console.log(param)
					var codeArray=param.value[3]
					console.log(codeArray)
					codeArray=codeArray.split('(')
					var stockname=codeArray[0]
					codeArray=codeArray[1].split(')')
					console.log(codeArray[0])
					_this.$router.push({
						path: '/analysis',
						query: {
							code: codeArray[0],
							stockname: stockname
						}
					})
				})
			}
		},
		computed: {
			echarts() {
				return 'echarts' + Math.random() * 100000
			}
		},
		mounted: function() {
			const vm = this
			vm.$nextTick(() => {
				vm.drawChart()
			})
		},
		created: () => {}
	}
</script>

<style scoped>
</style>
