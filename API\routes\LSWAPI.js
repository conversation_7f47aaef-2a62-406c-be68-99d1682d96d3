var express = require('express')
var router = express.Router()
var sqlexec_lsw = require('../sql/sqlLSW.js')
var moment = require('moment')
var request = require('request')
const { resolve } = require('q')
const web_url = require('../config/webURL');

router.get("/LSW_checktheChecker_base", (req, res) => {
    var account = "'" + req.query.account + "'"
    var returnQuery = new sqlexec_lsw()
    returnQuery.LSW_checktheChecker_base(account, function (result) {
        res.send(result)
    })
})
router.get("/my_LSW_checktheChecker_base_list", (req, res) => {
    var account = "'" + req.query.account + "'"
    var returnQuery = new sqlexec_lsw()
    returnQuery.my_LSW_checktheChecker_base_list(account, function (result) {
        res.send(result)
    })
})

router.get("/getSafeIncidentDept", (req, res) => {
    var mill = "'" + req.query.mill + "'"
    // console.log('mill', mill)
    var returnQuery = new sqlexec_lsw()
    returnQuery.getSafeIncidentDept(mill, function (result) {
        res.send(result)
    })
})

router.get("/getManagerList", (req, res) => {
    var mill = "'" + req.query.mill + "'"
    var returnQuery = new sqlexec_lsw()
    returnQuery.getManagerList(mill, function (result) {
        // console.log(result)
        res.send(result)
    })
})

router.get("/getLSW_Problem_Data_List", (req, res) => {
    var account = "'" + req.query.account + "'"
    var returnQuery = new sqlexec_lsw()
    returnQuery.getLSW_Problem_Data_List(account, function (result) {
        res.send(result)
    })
})
router.get("/getLSW_Assign_List", (req, res) => {
    var returnQuery = new sqlexec_lsw()
    returnQuery.getLSW_Assign_List(req.query.employeeID, function (result) {
        res.send(result)
    })
})

router.get("/LSW_checktheChecker_data", (req, res) => {
    var LSW_Base_ID = "'" + req.query.LSW_Base_ID + "'"
    var returnQuery = new sqlexec_lsw()
    returnQuery.LSW_checktheChecker_data(LSW_Base_ID, function (result) {
        res.send(result)
    })
})

router.get("/LSW_Problem_Data", (req, res) => {
    var LSW_Base_ID = "'" + req.query.LSW_Base_ID + "'"
    var returnQuery = new sqlexec_lsw()
    returnQuery.LSW_Problem_Data(LSW_Base_ID, function (result) {
        res.send(result)
    })
})


router.post("/insert_LSW_checktheChecker_data", (req, res) => {
    console.log(req.body)
    var username = "'" + req.body.username + "'"
    var LSW_Base_ID = req.body.LSW_Base_ID
    var insertSQL = new sqlexec_lsw()
    insertSQL.insert_LSW_checktheChecker_data(username, LSW_Base_ID, function (result) {
        console.log(result)
        let msg = ''
        if (result == 'insert completed') {
            msg = result
            res.send(msg)
        } else {
            msg = 'error'
            res.send(msg)
        }
    })
})


router.post("/insert_LSW_Problem_Data", async (req, res) => {
    console.log(req.body)
    const username = req.body.name
    const account = req.body.account
    const mill = req.body.mill
    const LSW_Base_ID = req.body.LSW_Base_ID
    const Problem_Area = req.body['dept[label]']
    const Owner_Name = req.body['dept[ownerID]']
    const problemContent = req.body.problemContent
    const Priority= req.body.Priority
    const problem_category= req.body.problem_category
    let fileNameArray = req.body.fileNameArray
    const fileURLArray = req.body.fileURLArray
    let NotificationMail = ''
    let relateID=''
    const reqBody = req.body
    if ('NotificationMail' in reqBody) {
        NotificationMail = req.body.NotificationMail
    } else {
        NotificationMail = ''
    }
    if ('relateID' in reqBody) {
        relateID = req.body.relateID
    } else {
        relateID = ''
    }
    let attachment = ''
    if (fileURLArray != '{}') {
        fileNameArray = fileNameArray.split(',')
        for (var i = 0; i < fileNameArray.length; i++) {
            attachment = attachment + '  ' + JSON.parse(fileURLArray)[fileNameArray[i]]
        }
    }
    let mailTitle = username + '发现了一个问题。请关注'
    let strHtml = `<table border="2" cellpadding="6" cellspacing="0"  width="100%" >
    <caption style="font-weight:600">LSW问题发送-<span style="color:red">${username} 发现的问题</span></caption>
    <tr><td >日期：</td><td >${new Date()}</td></tr>
    <tr><td >区域： </td><td>${Problem_Area}</td></tr>
    <tr><td >提交人：</td><td>${username}</td></tr>
    <tr><td >问题分类：</td><td>${problem_category}</td></tr>
    <tr><td >优先级：</td><td>${Priority}</td></tr>
    <tr><td >问题描述：</td><td >${problemContent}</td></tr>
	<tr><td>附件：</td><td >${attachment}</td></tr></table>
    <div><a href="${web_url.webSite + '/#/LSW/LSW?mail=true&tab=LSW审批'}" style="text-decoration:underline;font-weight: 600">点击此处登录网页进行反馈</a></div>`
    await sendMailtoApprover(Owner_Name + '@kcc.com;' + NotificationMail, mailTitle, strHtml, 'LSW Task')

    // console.log(line)
    const insertSQL = new sqlexec_lsw()
    insertSQL.insert_LSW_Problem_Data(mill, LSW_Base_ID, account, username, Problem_Area, problemContent, Owner_Name, fileNameArray, fileURLArray, Priority, problem_category, relateID, function (result) {
        console.log(result)
        let msg = ''
        if (result == 'insert completed') {
            msg = result
            res.send(msg)
        } else {
            msg = 'error'
            res.send(msg)
        }
    })
})


router.post("/insert_LSW_check_base", (req, res) => {
    console.log(req.body)
    var name = "'" + req.body.name + "'"
    var mill = "'" + req.body.mill + "'"
    var account = "'" + req.body.account + "'"
    var LSW_category = "'" + req.body.LSW_category + "'"
    var checkContent = "'" + req.body.checkContent + "'"
    var cycle = "'" + req.body.cycle + "'"
    var baseTime = "'" + req.body.baseTime + "'"
    var checkListFileArray = "'" + req.body.checkListFileArray + "'"
    var Role = "'" + req.body.Role + "'"

    var insertSQL = new sqlexec_lsw()

    insertSQL.insert_LSW_check_base(mill, account, name, LSW_category, checkContent, cycle, baseTime, checkListFileArray, Role, function (result) {
        console.log(result)
        let msg = ''
        if (result == 'insert completed') {
            msg = result
            res.send(msg)
        } else {
            msg = 'error'
            res.send(msg)
        }
    })
})


router.post("/Update_LSW_check_base", (req, res) => {
    console.log(req.body)
    var name = "'" + req.body.name + "'"
    var mill = "'" + req.body.mill + "'"
    var account = "'" + req.body.account + "'"
    var LSW_category = "'" + req.body.LSW_category + "'"
    var checkContent = "'" + req.body.checkContent + "'"
    var cycle = "'" + req.body.cycle + "'"
    var baseTime = "'" + req.body.baseTime + "'"
    var checkListFileArray = "'" + req.body.checkListFileArray + "'"
    var Role = "'" + req.body.Role + "'"
    var LSW_Base_ID = "'" + req.body.LSW_Base_ID + "'"

    var insertSQL = new sqlexec_lsw()

    insertSQL.Update_LSW_check_base(mill, account, name, LSW_category, checkContent, cycle, baseTime, checkListFileArray, Role, LSW_Base_ID, function (result) {
        console.log(result)
        let msg = ''
        if (result == 'update completed') {
            msg = result
            res.send(msg)
        } else {
            msg = 'error'
            res.send(msg)
        }
    })
})

router.post("/delete_LSW_check_base", (req, res) => {
    var LSW_Base_ID = "'" + req.body.LSW_Base_ID + "'"

    var insertSQL = new sqlexec_lsw()
    insertSQL.delete_LSW_check_base(LSW_Base_ID, function (result) {
        console.log(result)
        let msg = ''
        if (result == 'delete completed') {
            msg = result
            res.send(msg)
        } else {
            msg = 'error'
            res.send(msg)
        }
    })
})

router.get("/LSW_List", (req, res) => {
    var returnQuery = new sqlexec_lsw()
    returnQuery.LSW_List( function (result) {
        res.send(result)
    })
})

router.get("/getLSW_Assign_Data_List_MyApproval", (req, res) => {
    var returnQuery = new sqlexec_lsw()
    returnQuery.getLSW_Assign_Data_List_MyApproval(req.query.account, function (result) {
        res.send(result)
    })
})

router.post("/updateLSW_Problem", (req, res) => {
    console.log(req.body)
    const inputData = req.body['data']
    const Process_Status = req.body['Process_Status']
    const rowData = req.body['rowData']
    let fileNameArray = rowData['Problem_File_Name_Array']
    const fileURLArray = rowData['Problem_File_URL_Array']
    var insertSQL = new sqlexec_lsw()
    insertSQL.updateLSW_Problem(inputData, Process_Status, async function (result) {
        console.log('result', result)
        let attachment = ''
        if (result == '已更新' && (Process_Status != 'Final Check' && inputData['Adopt']=='Yes') ) {
            if (fileURLArray != '{}') {
                fileNameArray = fileNameArray.split(',')
                for (var i = 0; i < fileNameArray.length; i++) {
                    attachment = attachment + '  ' + JSON.parse(fileURLArray)[fileNameArray[i]]
                }
            }
            let mailTitle = `LSW问题整改：${rowData['Problem_Area']}区域负责人分配了一个任务给您`
            console.log('mailTitle', mailTitle)
            let strHtml = `<table border="2" cellpadding="6" cellspacing="0"  width="100%" >
    <caption style="font-weight:600">LSW问题发送-<span style="color:red">${rowData['Employee_Name']} 发现的问题需要您的整改</span></caption>
    <tr><td >日期：</td><td >${rowData['Date']}</td></tr>
    <tr><td >提交人：</td><td>${rowData['Employee_Name']}</td></tr>
    <tr><td >区域： </td><td>${rowData['Problem_Area']}</td></tr>
    <tr><td >类型： </td><td>${rowData['Problem_Category']}</td></tr>
    <tr><td >问题描述：</td><td >${rowData['Problem_Desc']}</td></tr>
    <tr><td >优先级：</td><td >${rowData['Priority']}</td></tr>
	<tr><td>附件：</td><td >${attachment}</td></tr></table>
    <a href="${web_url.webSite + '/#/LSW/LSWFeedBack?mail=true'}" style="text-decoration:underline;font-weight: 600">点击此处登录网页进行反馈</a>
    `
            console.log('strHtml', strHtml)
            let response = await sendMailtoApprover(`${inputData['assignedBy'].hasOwnProperty('value') ? inputData['assignedBy']['value'] : inputData['assignedBy']}` + '@kcc.com', mailTitle, strHtml, 'LSW Task')
            res.send(response)
        } else {
            res.send('已发送')
        }
    })
})

router.post('/test', (req, res) => {
    const inputData = 1
    
})



router.post("/updateLSW_Problem_AssignFeedBack", (req, res) => {
    console.log(req.body)
    const inputData = req.body['data']
    const Process_Status = inputData['process_status']
    const rowData = req.body['rowData']
    var insertSQL = new sqlexec_lsw()
    insertSQL.updateLSW_Problem_AssignFeedBack(inputData, Process_Status, async function (result) {
        console.log('result', result)
    //     let attachment = ''
        if (result == '已更新') {
            let mailTitle = `LSW整改反馈：${rowData['Problem_Area']}整改人已完成反馈`
            //console.log('mailTitle', mailTitle)
            let strHtml = `<table border="2" cellpadding="6" cellspacing="0"  width="100%" >
    <caption style="font-weight:600">LSW整改反馈-<span style="color:red">${rowData['Assigned_By_Name']} 已完成反馈。</span></caption>
    <tr><td >日期：</td><td >${rowData['Date']}</td></tr>
    <tr><td >提交人：</td><td>${rowData['Employee_Name']}</td></tr>
    <tr><td >区域： </td><td>${rowData['Problem_Area']}</td></tr>
    <tr><td >类型： </td><td>${rowData['Problem_Category']}</td></tr>
    <tr><td >问题描述：</td><td >${rowData['Problem_Desc']}</td></tr>
    <tr><td >优先级：</td><td >${rowData['Priority']}</td></tr>
    <tr><td >根本原因：</td><td >${inputData['rootCase']}</td></tr>
    <tr><td >解决方案：</td><td >${inputData['problemSolution']}</td></tr>
    <tr><td >整改人是否同意整改：</td><td >${inputData['Adopt']}</td></tr>
    <tr><td >预计完成时间：</td><td >${inputData['completeDate']}</td></tr>
    <tr><td >退回理由：</td><td >${inputData['rejectedReason']}</td></tr>
    </table>
    <a href="${web_url.webSite + '/#/LSW/LSWFeedBack?mail=true'}" style="text-decoration:underline;font-weight: 600">点击此处登录网页进行最终确认</a>
    `
            //console.log('strHtml', strHtml)
            let response = await sendMailtoApprover(rowData['Owner_Name'] + '@kcc.com', mailTitle, strHtml, 'LSW Task')
            res.send(response)
        }
    })
})






function sendMailtoApprover(to, subject, htmlbody, fromName) {
    return new Promise((resolve, reject) => {
        // 定义 API 接口地址
        const apiUrl = web_url.mailAPI
        const data = {
            'to': to,
            'subject': subject,
            'htmlbody': htmlbody,
            'fromName': fromName
        }
        request({
            method: "POST",
            url: apiUrl,
            form: data
        }, (err, response, data) => {
            // if(err){resolve(err)}
            // console.log('response',response.statusCode)
            if (response.statusCode == 200) {
                resolve('已发送')
            } else {
                resolve('发送失败')
            }

        })
    })
}






module.exports = router