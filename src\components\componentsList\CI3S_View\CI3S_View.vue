<template>

    <div>
        <q-dialog v-model="uploadImagePrompt">
            <q-card>
                <q-card-section>
                    <div class="text-h6">上传照片</div>
                </q-card-section>

                <q-card-section class="q-pt-none">
                    <template v-if="problem_Image_URL == '' || problem_Image_URL == null">
                        <q-uploader style="width: 100%;height: 500px;" batch auto-upload dense :factory="factoryFn"
                            @uploaded="handleUploadSuccess_Problem" label="照片上传" />
                    </template>
                    <template v-else>
                        <q-img :src="problem_Image_URL" :ratio="16 / 9" />
                    </template>

                </q-card-section>

                <q-card-actions align="right" class="text-primary">
                    <q-btn flat label="重新选取照片" @click="rePickImage()" />
                    <q-btn flat label="确定" v-close-popup />
                </q-card-actions>
            </q-card>
        </q-dialog>

        <q-btn v-if="opType != '查询' && opType != 'Approver'" color="primary" label="保存提交" @click="insertCI3S_Summary()"
            :loading="loading1" :disable="loading1" />
        <q-btn v-if="opType == 'Approver'" color="primary" label="审批提交" @click="Insert_CI_3S_Score()"
            :loading="loading2" :disable="loading2" />
        <div class="fit row wrap justify-between items-start content-start">
            <div style="font-size: 20px;font-weight: 600;">
                南京工厂AM小组改善活动总结网页版
            </div>
            <div class="black_border"
                style="line-height: 45px; background-color: aquamarine; font-weight: 500; font-size: 15px ">
                编号：{{ displayLinkID }}
            </div>
            <q-btn v-if="opType == 'Approver' || opType == '查询'" padding="xs" color="red" icon="clear" v-close-popup />
        </div>



        <div v-if="opType == '查询' || opType == 'Approver'">
            <q-table :data="score_data" :columns="score_columns" row-key="name" hide-bottom>
                <template v-slot:body="props">
                    <q-tr :props="props" @click="tableDetail(props)">
                        <!-- <q-td key="红牌统计" :props="props">
                            <div @click="showProps(props)">{{ props.row.红牌统计 }}</div>
                        </q-td> -->

                        <q-td key="清扫干净" :props="props">
                            <!-- <div>{{ props.row.OP1 }}</div> -->
                            <q-input :disable="opType != 'Approver'" v-model.number="props.row['清扫干净']" type="number"
                                filled :rules="[
                                    val => val !== null && val !== '' || '请输入分值',
                                    val => val >= 0 && val <= 20 || '最高分值不能超过20分'
                                ]" lazy-rules placeholder="Max:20" />
                        </q-td>

                        <q-td key="红牌问题描述清晰" :props="props">
                            <!-- <div>{{ props.row.OP1 }}</div> -->
                            <q-input :disable="opType != 'Approver'" v-model.number="props.row['红牌问题描述清晰']"
                                type="number" filled :rules="[
                                    val => val !== null && val !== '' || '请输入分值',
                                    val => val >= 0 && val <= 20 || '最高分值不能超过20分'
                                ]" lazy-rules placeholder="Max:20" />
                        </q-td>

                        <q-td key="原因分析符合逻辑" :props="props">
                            <!-- <div>{{ props.row.OP1 }}</div> -->
                            <q-input :disable="opType != 'Approver'" v-model.number="props.row['原因分析符合逻辑']"
                                type="number" filled :rules="[
                                    val => val !== null && val !== '' || '请输入分值',
                                    val => val >= 0 && val <= 30 || '最高分值不能超过30分'
                                ]" lazy-rules placeholder="Max:30" />
                        </q-td>

                        <q-td key="改善对策制定有效" :props="props">
                            <!-- <div>{{ props.row.OP1 }}</div> -->
                            <q-input :disable="opType != 'Approver'" v-model.number="props.row['改善对策制定有效']"
                                type="number" filled :rules="[
                                    val => val !== null && val !== '' || '请输入分值',
                                    val => val >= 0 && val <= 20 || '最高分值不能超过20分'
                                ]" lazy-rules placeholder="Max:20" />
                        </q-td>

                        <q-td key="总结内容完整清晰" :props="props">
                            <!-- <div>{{ props.row.OP1 }}</div> -->
                            <q-input :disable="opType != 'Approver'" v-model.number="props.row['总结内容完整清晰']"
                                type="number" filled :rules="[
                                    val => val !== null && val !== '' || '请输入分值',
                                    val => val >= 0 && val <= 10 || '最高分值不能超过10分'
                                ]" lazy-rules placeholder="Max:10" />
                        </q-td>
                        <q-td key="总计" :props="props">
                            <div>{{ props.row['清扫干净'] + props.row['红牌问题描述清晰'] + props.row['原因分析符合逻辑'] +
                                props.row['改善对策制定有效'] +
                                props.row['总结内容完整清晰'] }}
                            </div>

                        </q-td>
                    </q-tr>
                </template>
            </q-table>

        </div>

        <div class="row q-col-gutter-md q-mt-md">
            <div class="col-12 col-md-4">
                <div class="q-mb-sm text-subtitle1 text-weight-bold text-center bg-blue-8 text-white q-py-sm">组员合影</div>
                <q-uploader v-if="opType != '查询' && opType != 'Approver'" :factory="factoryFn" label="组员合影" batch
                    auto-upload @uploaded="handleUploadSuccess_team" style="width: 100%;" class="q-mb-md" />
                <div v-else>
                    <q-img v-if="formData['Team_Image']" :src="formData['Team_Image']" :ratio="4 / 3"
                        @click="gotoImage(formData['Team_Image'])" />
                    <div v-else class="text-grey-7 q-pa-md text-center">暂无组员合影</div>
                </div>
            </div>

            <div class="col-12 col-md-8">
                <div class="row q-col-gutter-md">
                    <div class="col-12 col-sm-6">
                        <q-select :disable="opType == '查询' || opType == 'Approver'" outlined v-model="formData['Line']"
                            :options="lineArray" label-slot clearable dense class="full-width">
                            <template v-slot:label>机台
                                <em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
                            </template>
                        </q-select>
                    </div>
                    <div class="col-12 col-sm-6">
                        <q-input :disable="opType == '查询' || opType == 'Approver'" class="full-width"
                            v-model="formData['Date']" filled type="date" label="日期" stack-label dense>
                            <template v-slot:prepend>
                                <q-icon name="event" />
                            </template>
                        </q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                        <q-select :disable="opType == '查询' || opType == 'Approver'" outlined v-model="formData['Crew']"
                            :options="['A', 'B', 'C', 'D', 'E', 'F']" label-slot clearable dense class="full-width">
                            <template v-slot:label>班组
                                <em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
                            </template>
                        </q-select>
                    </div>
                    <div class="col-12 col-sm-6">
                        <q-select :disable="opType == '查询' || opType == 'Approver'" outlined v-model="formData['Unit']"
                            :options="unitArray" label-slot clearable dense class="full-width" multiple use-chips>
                            <template v-slot:label>清扫单元
                                <em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
                            </template>
                        </q-select>
                    </div>
                    <div class="col-12 col-sm-6">
                        <q-input :disable="opType == '查询' || opType == 'Approver'" class="full-width"
                            v-model="formData['Start_Time']" filled type="time" label="开始时间" stack-label dense>
                        </q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                        <q-input :disable="opType == '查询' || opType == 'Approver'" class="full-width"
                            v-model="formData['End_Time']" filled type="time" label="结束时间" stack-label dense>
                        </q-input>
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top:10px">
            <q-table :data="red_Statistics_data" :columns="red_Statistics_columns" row-key="name">

                <template v-slot:body="props">
                    <q-tr :props="props">
                        <q-td key="红牌统计" :props="props">
                            <div @click="showProps(props)">{{ props.row.红牌统计 }}</div>

                            <!-- <q-select outlined v-model="props.row.Assigned_By" :options="assignedByDropDown"
                                    style="width:130px;" dense /> -->
                        </q-td>
                        <q-td key="OP1" :props="props">
                            <!-- <div>{{ props.row.OP1 }}</div> -->
                            <q-input :disable="opType == '查询' || opType == 'Approver'" v-model.number="props.row.OP1"
                                type="number" filled style="max-width:100px" />
                        </q-td>

                        <q-td key="OP2" :props="props">
                            <!-- <div>{{ props.row.OP2 }}</div> -->
                            <q-input :disable="opType == '查询' || opType == 'Approver'" v-model.number="props.row.OP2"
                                type="number" filled style="max-width:100px" />
                        </q-td>

                        <q-td key="OP3" :props="props">
                            <!-- <div>{{ props.row.OP3 }}</div> -->
                            <q-input :disable="opType == '查询' || opType == 'Approver'" v-model.number="props.row.OP3"
                                type="number" filled style="max-width:100px" />
                        </q-td>

                        <q-td key="OP4" :props="props">
                            <!-- <div>{{ props.row.OP1 }}</div> -->
                            <q-input :disable="opType == '查询' || opType == 'Approver'" v-model.number="props.row.OP4"
                                type="number" filled style="max-width:100px" />
                        </q-td>

                        <q-td key="OP5" :props="props">
                            <!-- <div>{{ props.row.OP1 }}</div> -->
                            <q-input :disable="opType == '查询' || opType == 'Approver'" v-model.number="props.row.OP5"
                                type="number" filled style="max-width:100px" />
                        </q-td>

                        <q-td key="总计" :props="props">
                            <div>{{ props.row.OP1 + props.row.OP2 + props.row.OP3 + props.row.OP4 +
                                props.row.OP5 }}
                            </div>

                        </q-td>
                    </q-tr>
                </template>
            </q-table>
        </div>
        <div class="row q-col-gutter-md q-mt-md">
            <div class="col-12 col-md-6">
                <div class="q-mb-sm text-subtitle1 text-weight-bold text-center bg-blue-8 text-white q-py-sm relative-position">
                    <div class="row justify-between items-center">
                        <div class="col-3">
                            <!-- 左侧空白区域 -->
                        </div>
                        <div class="col-6 text-center">
                            <a href="https://mm.edrawsoft.cn/app/editor/zgLeRcB5lQZ3HMJvq7wc9mDGGMDg5zbZ" target="_blank" class="text-white" style="text-decoration: none; cursor: pointer;">
                                Top问题5WHY分析
                                <q-icon name="open_in_new" size="xs" class="q-ml-xs" />
                            </a>
                        </div>
                        <div class="col-3 text-right">
                            <a href="https://mm.edrawsoft.cn/app/editor/zgLeRcB5lQZ3HMJvq7wc9mDGGMDg5zbZ" target="_blank" class="text-cyan-2 text-caption" style="text-decoration: none; cursor: pointer; font-weight: 400;">
                                点击生成脑图
                            </a>
                        </div>
                    </div>
                </div>
                <q-uploader v-if="opType != '查询' && opType != 'Approver'" :factory="factoryFn" label="上传5why分析图片（可上传多张）" batch multiple
                    auto-upload @uploaded="handleUploadSuccess_why" style="width: 100%;" class="q-mb-md" />

                <div v-if="opType == '查询' || opType == 'Approver'">
                    <div v-if="whyAnalysisImages && whyAnalysisImages.length > 0">
                        <div v-for="(image, index) in whyAnalysisImages" :key="index" class="q-mb-md">
                            <q-img :src="image" style="width:100%;" :ratio="16/9" @click="gotoImage(image)" />
                        </div>
                    </div>
                    <div v-else-if="formData['Problem_Analysis_Image']" class="q-mb-md">
                        <q-img :src="formData['Problem_Analysis_Image']" style="width:100%;" :ratio="16/9"
                            @click="gotoImage(formData['Problem_Analysis_Image'])" />
                    </div>
                    <div v-else class="text-grey-7 q-pa-md text-center">暂无5why分析图片</div>
                </div>

                <div v-if="opType != '查询' && opType != 'Approver' && whyAnalysisImages.length > 0" class="q-mt-md">
                    <div class="text-subtitle1 q-mb-sm">已上传图片预览：</div>
                    <div v-for="(image, index) in whyAnalysisImages" :key="index" class="q-mb-md row items-center">
                        <q-img :src="image" style="width:40%;" :ratio="16/9" @click="gotoImage(image)" />
                        <q-btn round dense color="negative" icon="delete" class="q-ml-md" @click="removeWhyAnalysisImage(index)" />
                    </div>
                </div>
            </div>

            <div class="col-12 col-md-6">
                <div class="q-mb-sm text-subtitle1 text-weight-bold text-center bg-blue-8 text-white q-py-sm">
                    非必需品区域照片
                </div>
                <q-uploader v-if="opType != '查询' && opType != 'Approver'" :factory="factoryFn" label="上传红牌区域照片" batch
                    auto-upload @uploaded="handleUploadSuccess_red" style="width: 100%;" class="q-mb-md" />
                <div v-else>
                    <q-img v-if="formData['Red_Area_Image']" :src="formData['Red_Area_Image']" style="width:100%;" :ratio="16/9"
                        @click="gotoImage(formData['Red_Area_Image'])" />
                    <div v-else class="text-grey-7 q-pa-md text-center">暂无红牌区域照片</div>
                </div>
            </div>
        </div>

        <div class="q-mt-lg">
            <div class="q-mb-sm text-subtitle1 text-weight-bold text-center bg-blue-8 text-white q-py-sm">
                主要问题
                <div class="text-caption text-right text-cyan-2" style="font-weight: 400;">如需要生成报告,请确保, "quickfix" 和"long term"各3条</div>
            </div>

            <div class="q-pa-md q-mb-md black_border" v-for="(item, index) in problemData" :key="index">
                <div class="row q-col-gutter-md">
                    <div class="col-12 col-md-2">
                        <q-select :disable="opType == '查询' || opType == 'Approver'" outlined
                            v-model="item['Problem_Category']" :options="Problem_CategoryDropdown"
                            dense label="问题类型" class="full-width" />
                    </div>

                    <div class="col-12 col-md-2">
                        <q-input :disable="opType == '查询' || opType == 'Approver'" v-model="item['Problem_Desc']" filled
                            autogrow dense label="问题现象描述" class="full-width" />
                    </div>

                    <div class="col-12 col-md-2">
                        <q-input :disable="opType == '查询' || opType == 'Approver'" v-model="item['Root_Cause']" filled
                            autogrow dense label="主要原因" class="full-width" />
                    </div>
                    <div class="col-12 col-md-3">
                        <q-input :disable="opType == '查询' || opType == 'Approver'" v-model="item['Corrective_Action']"
                            filled autogrow dense label="纠正措施" class="full-width" />
                    </div>
                    <div class="col-12 col-md-3">
                        <q-input :disable="opType == '查询' || opType == 'Approver'" v-model="item['Preventive_Measure']"
                            filled autogrow dense label="预防措施" class="full-width" />
                    </div>
                </div>

                <div class="row q-col-gutter-md q-mt-md">
                    <div class="col-12 col-md-6">
                        <div class="text-subtitle2 q-mb-sm">改善前</div>
                        <template v-if="item['Before_Image'] == ''">
                            <q-btn dark-percentage unelevated color='orange' text-color="grey-9"
                                @click="imageUpload(index, 'before', item['Before_Image'])" icon="cloud_upload"
                                label="上传改善前照片" class="q-mb-sm" />
                        </template>
                        <template v-else>
                            <div class="row items-center">
                                <q-img :src="item['Before_Image']" :ratio="16/9" style="width:100%;"
                                    @click="gotoImage(item['Before_Image'])" />
                                <q-btn v-if="opType != '查询' && opType != 'Approver'" round dense
                                    color="red" icon="clear" class="q-ml-sm" @click="clearProblemImage(index, 'before')" />
                            </div>
                        </template>
                    </div>

                    <div class="col-12 col-md-6">
                        <div class="text-subtitle2 q-mb-sm">改善后</div>
                        <template v-if="item['After_Image'] == ''">
                            <q-btn dark-percentage unelevated color='orange' text-color="grey-9"
                                @click="imageUpload(index, 'after', item['After_Image'])" icon="cloud_upload"
                                label="上传改善后照片" class="q-mb-sm" />
                        </template>
                        <template v-else>
                            <div class="row items-center">
                                <q-img :src="item['After_Image']" :ratio="16/9" style="width:100%;"
                                    @click="gotoImage(item['After_Image'])" />
                                <q-btn v-if="opType != '查询' && opType != 'Approver'" round dense
                                    color="red" icon="clear" class="q-ml-sm" @click="clearProblemImage(index, 'after')" />
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <div class="q-mt-md">
                <q-btn v-if="opType != '查询' && opType != 'Approver'" color="primary" label="添加行" @click="addRow()" />
            </div>
        </div>

    </div>


</template>

<script>
export default {
    props: {
        opType: {
            type: String,
            default: '新增'
        },
        linkid: {
            type: String,
            default: false
        }
    },
    mounted() {
        console.log('子组件opType:', this.opType)
        console.log('子组件LinkID:', this.linkid)
        if (this.opType == '查询' || this.opType == 'Approver') {
            this.query_CI_3S_byLinkID(this.linkid)
        }
        this.query_CI_3S_Area_Base()
        this.getLine()
    },
    watch: {
        'formData.Line'(newValue1, oldValue1) {
            console.log('newValue', newValue1)
            console.log('oldValue', oldValue1)
            // this.formData['Unit'] = ''
            this.filterUnitList(newValue1)
        }
    },

    data() {
        return {
            lineArray: ['ND05', 'ND06', 'ND07', 'ND08', 'ND09', 'ND34', 'ND37'],
            unitArray: [],
            Problem_CategoryDropdown: ['quick fix', 'long term'],
            formData: {
                Date: '',
                Line: '',
                Crew: '',
                Unit: [], // 改为数组，支持多选
                Start_Time: '',
                End_Time: '',
                Team_Image: '',
                Red_Area_Image: '',
                Problem_Analysis_Image: '',
                LinkID: '',
            },
            red_Statistics_data: [
                {
                    红牌统计: '共计',
                    OP1: '',
                    OP2: '',
                    OP3: '',
                    OP4: '',
                    OP5: '',
                    总计: '',
                },
                {
                    红牌统计: '已解决',
                    OP1: '',
                    OP2: '',
                    OP3: '',
                    OP4: '',
                    OP5: '',
                    总计: '',
                },
                {
                    红牌统计: '待解决',
                    OP1: '',
                    OP2: '',
                    OP3: '',
                    OP4: '',
                    OP5: '',
                    总计: '',
                },
            ],
            problemData: [
                {
                    'Problem_Category': '',
                    'Problem_Desc': '',
                    'Root_Cause': '',
                    'Corrective_Action': '',
                    'Preventive_Measure': '',
                    'Before_Image': '',
                    'After_Image': '',
                },
            ],
            red_Statistics_columns: [
                { name: '红牌统计', align: 'left', label: '红牌统计', field: '红牌统计', sortable: true },
                { name: 'OP1', align: 'left', label: 'OP1', field: 'OP1', sortable: true },
                { name: 'OP2', align: 'left', label: 'OP2', field: 'OP2', sortable: true },
                { name: 'OP3', align: 'left', label: 'OP3', field: 'OP3', sortable: true },
                { name: 'OP4', align: 'left', label: 'OP4', field: 'OP4', sortable: true },
                { name: 'OP5', align: 'left', label: 'OP5', field: 'OP5', sortable: true },
                { name: '总计', align: 'left', label: '总计', field: '总计', sortable: true }
            ],
            uploadImagePrompt: false,
            problem_Image_URL: '',
            problem_Index: '',
            problem_Image_Type: '',
            ImageShow: true,
            score_columns: [
                { name: '清扫干净', align: 'left', label: '清扫干净', field: '清扫干净', sortable: true },
                { name: '红牌问题描述清晰', align: 'left', label: '描述清晰', field: '红牌问题描述清晰', sortable: true },
                { name: '原因分析符合逻辑', align: 'left', label: '分析符合逻辑', field: '原因分析符合逻辑', sortable: true },
                { name: '改善对策制定有效', align: 'left', label: '对策制定有效', field: '改善对策制定有效', sortable: true },
                { name: '总结内容完整清晰', align: 'left', label: '内容完整清晰', field: '总结内容完整清晰', sortable: true },
                { name: '总计', align: 'left', label: '总计', field: '总计', sortable: true }
            ],
            score_data: [
                {
                    '清扫干净': '',
                    '红牌问题描述清晰': '',
                    '原因分析符合逻辑': '',
                    '改善对策制定有效': '',
                    '总结内容完整清晰': '',
                    '总计': '',
                },
            ],
            unitRawData: [
                { 'Line': 'ND05', 'Unit': 'QCO' },
                { 'Line': 'ND05', 'Unit': 'QCO1' },
                { 'Line': 'ND05', 'Unit': 'QCO2' },
                { 'Line': 'ND06', 'Unit': 'ff1' },
                { 'Line': 'ND06', 'Unit': 'ff2' },
                { 'Line': 'ND06', 'Unit': 'ff3' },
            ],
            loading1: false,
            loading2: false,
            whyAnalysisImages: [],


        }
    },

    computed: {
        // 计算显示用的编号，只使用第一个清扫单元
        displayLinkID() {
            const firstUnit = Array.isArray(this.formData.Unit) && this.formData.Unit.length > 0
                ? this.formData.Unit[0]
                : this.formData.Unit || ''
            return `${this.formData.Line || ''}-${this.formData.Date || ''}-${this.formData.Crew || ''}-${firstUnit}`
        }
    },


    methods: {
        async getLine() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`approve/getLine?mill=${localStorage.getItem('mill')}`)
            console.log('line', res)
            this.lineArray=res
        },
        filterUnitList(Line) {
            const rawData = this.unitRawData
            this.unitArray = rawData.filter(item => item.Line === Line).map(unit => unit.Area);
            console.log('unit', this.unitArray)

        },

        factoryFn() {
            var _this = this
            _this.prompt_uploadFile = true
            return {
                url: 'http://172.21.65.192:3001/upload?system=CI3S',
                method: 'POST'
            }
        },
        result() {
            // 只使用第一个清扫单元作为ID的一部分，避免ID过长
            const firstUnit = this.formData.Unit.length > 0 ? this.formData.Unit[0] : ''
            this.formData['LinkID'] = `${this.formData.Line}-${this.formData.Date}-${this.formData.Crew}-${firstUnit}`
            console.log(this.formData)
        },
        //新服务器使用
        handleUploadSuccess_team(response) {
            console.log('FileUploadResponse', response)
            var _this = this
            //response.files[0].xlr.responseText
            var responseURL = response['files'][0]['xhr']['responseText']
            _this.formData.Team_Image = responseURL
        },
        //新服务器使用
        handleUploadSuccess_why(response) {
            console.log('FileUploadResponse', response)
            var _this = this
            // 处理多个文件上传
            response.files.forEach(file => {
                // 获取响应URL
                var responseURL = file['xhr']['responseText']
                // 将图片添加到数组中
                _this.whyAnalysisImages.push(responseURL)
            })
            // 同时保持兼容性，将第一张图片设置为主图
            if (response.files.length > 0) {
                var responseURL = response['files'][0]['xhr']['responseText']
                _this.formData.Problem_Analysis_Image = responseURL
            }
        },

        // 删除5WHY分析图片
        removeWhyAnalysisImage(index) {
            this.$q.dialog({
                title: '确认删除',
                message: '确定要删除这张图片吗？',
                cancel: true,
                persistent: true
            }).onOk(() => {
                this.whyAnalysisImages.splice(index, 1)
                // 如果删除后还有图片，则将第一张设为主图
                if (this.whyAnalysisImages.length > 0) {
                    this.formData.Problem_Analysis_Image = this.whyAnalysisImages[0]
                } else {
                    this.formData.Problem_Analysis_Image = ''
                }
            })
        },
        //新服务器使用
        handleUploadSuccess_red(response) {
            console.log('FileUploadResponse', response)
            var _this = this
            //response.files[0].xlr.responseText
            var responseURL = response['files'][0]['xhr']['responseText']
            _this.formData.Red_Area_Image = responseURL
        },
        //新服务器使用
        handleUploadSuccess_Problem(response) {
            console.log('FileUploadResponse', response)
            var _this = this
            //response.files[0].xlr.responseText
            var responseURL = response['files'][0]['xhr']['responseText']
            if (_this.problem_Image_Type == 'before') {
                _this.problemData[_this.problem_Index]['Before_Image'] = responseURL
            }
            if (_this.problem_Image_Type == 'after') {
                _this.problemData[_this.problem_Index]['After_Image'] = responseURL
            }
            console.log('resultData:', _this.problemData[_this.problem_Index])
            _this.ImageShow = true
            // _this.problemData[_this.problem_Index][_this.problem_Image_Type == 'before' ? 'Before_Image' :'After_Image'] = responseURL

        },

        showProps(props) {
            console.log('props', props)
        },
        addRow() {
            const newRow = {
                'Problem_Category': '',
                'Problem_Desc': '',
                'Root_Cause': '',
                'Corrective_Action': '',
                'Preventive_Measure': '',
                'Before_Image': '',
                'After_Image': ''
            }
            this.problemData.push(newRow)
            console.log('this.problemData', this.problemData)
        },
        imageUpload(problem_Index, problem_image_type, problem_Image_URL) {
            const _this = this
            console.log('problem_Index', problem_Index)
            _this.problem_Index = problem_Index
            _this.problem_Image_Type = problem_image_type
            _this.problem_Image_URL = problem_Image_URL
            _this.ImageShow = false
            _this.uploadImagePrompt = true
        },
        clearProblemImage(problem_Index, problem_image_type) {
            const _this = this
            if (problem_image_type == 'before') {
                _this.problemData[problem_Index]['Before_Image'] = ""
            }
            if (problem_image_type == 'after') {
                _this.problemData[problem_Index]['After_Image'] = ""
            }
        },
        gotoImage(url) {
            window.open(url, '_blank');
        },
        insertCI3S_Summary() {
            const _this = this
            if (this.formData.Line == "" || _this.formData.Date == "" || _this.formData.Crew == "" || !this.formData.Unit.length) {
                _this.$q.notify({
                    type: 'negative',
                    message: `请填写完整信息`,
                    position: 'top'
                })
                return
            }
            _this.loading1 = true
            // 只使用第一个清扫单元作为ID的一部分，避免ID过长
            const firstUnit = this.formData.Unit.length > 0 ? this.formData.Unit[0] : ''
            this.formData['LinkID'] = `${this.formData.Line}-${this.formData.Date}-${this.formData.Crew}-${firstUnit}`

            // 处理多张5WHY分析图片，将数组转换为逗号分隔的字符串
            if (_this.whyAnalysisImages.length > 0) {
                _this.formData.Problem_Analysis_Image = _this.whyAnalysisImages.join(',')
            }

            _this.$http.post('ci/Insert_CI_3S_Summary', _this.formData).then(function (response) {
                console.log('response', response)
                if (response.data === '已更新') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `AM概览录入成功`,
                        position: 'top'
                    })
                }
            })
            this.Insert_CI_3S_Red_Statistics()
            this.Insert_CI_3S_Problem_List()
        },

        Insert_CI_3S_Red_Statistics() {
            const _this = this
            // 只使用第一个清扫单元作为ID的一部分，避免ID过长
            const firstUnit = this.formData.Unit.length > 0 ? this.formData.Unit[0] : ''
            this.formData['LinkID'] = `${this.formData.Line}-${this.formData.Date}-${this.formData.Crew}-${firstUnit}`
            const param = {
                'LinkID': _this.formData['LinkID'],
                'data': _this.red_Statistics_data
            }
            _this.$http.post('ci/Insert_CI_3S_Red_Statistics', param).then(function (response) {
                console.log('response', response)
                if (response.data === '已更新') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `红牌统计录入成功`,
                        position: 'top'
                    })
                }
            })
        },
        Insert_CI_3S_Problem_List() {
            const _this = this
            // 只使用第一个清扫单元作为ID的一部分，避免ID过长
            const firstUnit = this.formData.Unit.length > 0 ? this.formData.Unit[0] : ''
            this.formData['LinkID'] = `${this.formData.Line}-${this.formData.Date}-${this.formData.Crew}-${firstUnit}`
            const param = {
                'LinkID': _this.formData['LinkID'],
                'data': _this.problemData
            }
            _this.$http.post('ci/Insert_CI_3S_Problem_List', param).then(function (response) {
                console.log('response', response)
                if (response.data === '已更新') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `问题描述录入成功`,
                        position: 'top'
                    })
                    _this.loading1 = false
                    _this.formData = {
                        Date: '',
                        Line: '',
                        Crew: '',
                        Unit: [], // 重置为空数组
                        Start_Time: '',
                        End_Time: '',
                        Team_Image: '',
                        Red_Area_Image: '',
                        Problem_Analysis_Image: '',
                        LinkID: ''
                    }
                }
            })


        },
        async query_CI_3S_byLinkID(LinkID) {
            var _this = this
            // _this.rows = false
            const {
                data: res
            } = await _this.$http.get(`ci/query_CI_3S_byLinkID?LinkID=${LinkID}`)
            console.log('query_CI_3S_byLinkID', res)
            _this.formData = res['CI_3S_Summary_byLinkid'][0]

            // 处理清扫单元，如果是逗号分隔的字符串，转换为数组
            if (_this.formData && _this.formData.Unit) {
                if (typeof _this.formData.Unit === 'string' && _this.formData.Unit.includes(',')) {
                    _this.formData.Unit = _this.formData.Unit.split(',')
                } else if (typeof _this.formData.Unit === 'string') {
                    _this.formData.Unit = [_this.formData.Unit]
                }
            } else {
                _this.formData.Unit = []
            }

            _this.problemData = res['CI_3S_Problem_List_byLinkid']
            _this.format_statistics_data(res['CI_3S_Red_Statistics_byLinkid'])
            _this.format_score_data(res['CI_3S_Score_byLinkid'])

            // 处理5WHY分析图片
            if (_this.formData && _this.formData.Problem_Analysis_Image) {
                // 如果有多张图片（以逗号分隔），则拆分为数组
                if (_this.formData.Problem_Analysis_Image.includes(',')) {
                    _this.whyAnalysisImages = _this.formData.Problem_Analysis_Image.split(',')
                } else {
                    // 否则将单张图片添加到数组
                    _this.whyAnalysisImages = [_this.formData.Problem_Analysis_Image]
                }
            } else {
                _this.whyAnalysisImages = []
            }
            // _this.rows = res
        },

        format_statistics_data(api_red_statistics_data) {
            const data = api_red_statistics_data
            const red_Statistics_data = [
                {
                    红牌统计: '红牌',
                    OP1: '',
                    OP2: '',
                    OP3: '',
                    OP4: '',
                    OP5: '',
                    总计: '',
                },
                {
                    红牌统计: '已解决',
                    OP1: '',
                    OP2: '',
                    OP3: '',
                    OP4: '',
                    OP5: '',
                    总计: '',
                },
                {
                    红牌统计: '待解决',
                    OP1: '',
                    OP2: '',
                    OP3: '',
                    OP4: '',
                    OP5: '',
                    总计: '',
                },
            ];
            data.forEach(item => {
                const { OP, Red_Count, Resolved_Count, Pending_Count } = item;
                red_Statistics_data[0][OP] = Red_Count;
                red_Statistics_data[1][OP] = Resolved_Count;
                red_Statistics_data[2][OP] = Pending_Count;
            });
            red_Statistics_data.forEach(item => {
                let total = 0;
                Object.values(item).forEach(value => {
                    if (typeof value !== 'string') {
                        total += value;
                    }
                });
                item['总计'] = total;
            });
            console.log('red_Statistics_data', red_Statistics_data);
            this.red_Statistics_data = red_Statistics_data
        },
        tableDetail(props) {
            console.log('props', props)
        },
        Insert_CI_3S_Score() {
            const _this = this
            _this.loading2 = true
            // 只使用第一个清扫单元作为ID的一部分，避免ID过长
            const firstUnit = this.formData.Unit.length > 0 ? this.formData.Unit[0] : ''
            this.formData['LinkID'] = `${this.formData.Line}-${this.formData.Date}-${this.formData.Crew}-${firstUnit}`
            const param = {
                'LinkID': _this.formData['LinkID'],
                'data': _this.score_data,
                'approver': localStorage.getItem('username')
            }
            _this.$http.post('ci/Insert_CI_3S_Score', param).then(function (response) {
                console.log('response', response)
                if (response.data === '已更新') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `AM评分录入成功`,
                        position: 'top'
                    })
                    _this.loading2 = false
                }
            })
        },

        format_score_data(api_score_data) {
            const data = api_score_data
            console.log('data', api_score_data)
            let score_data = [
                {
                    '清扫干净': '',
                    '红牌问题描述清晰': '',
                    '原因分析符合逻辑': '',
                    '改善对策制定有效': '',
                    '总结内容完整清晰': '',
                    '总计': '',
                },
            ];
            data.forEach(item => {
                const { Item, Score } = item;
                console.log(Item)
                score_data[0][Item] = Score;
            });
            score_data.forEach(item => {
                let total = 0;
                Object.values(item).forEach(value => {
                    if (typeof value !== 'string') {
                        total += value;
                    }
                });
                item['总计'] = total;
            });
            console.log('score_data', score_data);
            this.score_data = score_data
        },
        async query_CI_3S_Area_Base() {
            var _this = this
            // _this.rows = false
            const { data: res } = await _this.$http.get(`ci/query_CI_3S_Area_Base`)
            console.log('query_CI_3S_Area_Base', res)
            this.unitRawData = res

        },
    }
}


</script>

<style>
.black_border {
    border: 1px solid black;
}
</style>
