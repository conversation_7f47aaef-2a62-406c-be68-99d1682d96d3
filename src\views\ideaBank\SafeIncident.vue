<template>
    <base-content>
        <div class="q-pa-md">

            <q-dialog v-model="prompt_uploadFile" persistent>
                <q-card style="width: 300px">
                    <q-card-section>
                        <div class="text-h6">上传文件中.........</div>
                    </q-card-section>

                    <q-card-section class="q-pt-none">
                        文件上传完毕后自动消失
                    </q-card-section>
                </q-card>
            </q-dialog>

            <q-dialog v-model="rejectDialog" persistent>
                <q-card style="min-width: 350px">
                    <q-card-section>
                        <div class="text-h6">拒绝理由</div>
                    </q-card-section>

                    <q-card-section class="q-pt-none">
                        <q-input dense v-model="assignedInput['rejectedComment']" autofocus />
                    </q-card-section>

                    <q-card-actions align="right" class="text-primary">
                        <q-btn flat label="确定" @click="updateSafty('No')" />
                        <q-btn flat label="取消" v-close-popup />
                    </q-card-actions>
                </q-card>
            </q-dialog>

            <q-dialog v-model="calRiskDialog" persistent>
                <q-card style="min-width: 900px">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6">计算风险等级</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>

                    <q-card-section class="q-pt-none">
                        <q-img src="http://172.21.118.163:3003/Safty/RiskStandardImage.PNG" :ratio="16/ 9"
                            style="height: 200px;width: 880px;" />
                        <!--  -->
                        <div class="q-gutter-md row wrap justify-end items-start content-start">
                            <q-select outlined v-model="assignedInput['P_Value']" :options="P_Value_DropDown" label-slot
                                clearable style="width: 160px;" dense>
                                <template v-slot:label>P值
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-select>
                            <q-select outlined v-model="assignedInput['S_Value']" :options="S_Value_DropDown" label-slot
                                clearable style="width: 160px;" dense>
                                <template v-slot:label>S值
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-select>
                        </div>

                    </q-card-section>

                    <q-card-actions align="right" class="text-primary">
                        <q-btn flat label="确定" @click="cal_riskLevel" />
                        <q-btn flat label="取消" v-close-popup />
                    </q-card-actions>
                </q-card>
            </q-dialog>


            <q-dialog v-model="showDetailDialog" transition-show="flip-down">
                <q-card style="width:1000px; max-width: 100vw;" v-if="showDetailData">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6 ">安全隐患查询</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>

                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template v-for="(value, key) in showDetailData">
                                <div v-if="key == '隐患内容' || key == '建议解决方案' || key == '整改人方案' || key == '临时方案'"
                                    style="width: 100%;">
                                    <q-input type="textarea" filled v-model="showDetailData[key]" dense stack-label
                                        :label="key" readonly style="height: 100px;" />
                                </div>
                                <div v-else>
                                    <q-input filled v-model="showDetailData[key]" dense stack-label :label="key"
                                        readonly style="width:300px" />
                                </div>
                            </template>
                        </div>
                    </q-card-section>
                </q-card>
            </q-dialog>

            <q-dialog v-model="firtDialog" transition-show="flip-down">
                <q-card style="width:1000px; max-width: 100vw;" v-if="approveIdea">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6 ">安全隐患审批</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>

                    <q-card-section v-if="approveIdea['流程状态'] != 'Completed' && approveIdea['流程状态'] != 'Rejected'">
                        <div style="font-weight: 600;font-size: 15px;">
                            {{ approveIdea['流程状态'] != 'Final Check' && approveIdea['流程状态'] != 'EHS Check' ? '确认隐患' :
                            '确认整改是否完成' }}
                        </div>
                        <div class="q-gutter-md row items-start">
                            <q-select v-if="approveIdea['流程状态'] != 'Final Check' && approveIdea['流程状态'] != 'EHS Check'"
                                outlined v-model="assignedInput['assigned_Department']"
                                :options="approveIdeaDropdown['assignedBy_Department']" label-slot clearable
                                style="width: 190px;" dense>
                                <template v-slot:label>分配人员部门
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-select>
                            <q-select v-if="approveIdea['流程状态'] != 'Final Check' && approveIdea['流程状态'] != 'EHS Check'"
                                outlined v-model="assignedInput['assigned_name']"
                                :options="approveIdeaDropdown['assignedBy']" label-slot clearable style="width: 160px;"
                                dense>
                                <template v-slot:label>分配人员
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-select>

                            <q-select v-if="approveIdea['流程状态'] != 'Final Check' && approveIdea['流程状态'] != 'EHS Check'"
                                outlined v-model="assignedInput['riskLevel']"
                                :options="approveIdeaDropdown['Risk_Level']" label-slot clearable style="width: 130px;"
                                dense disable>
                                <template v-slot:label>风险等级
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-select>


                            <q-btn style="width:120px" label="风险等级计算" color="secondary" @click="cal_risk" />


                            <q-btn v-if="approveIdea['流程状态'] != 'Final Check' && approveIdea['流程状态'] != 'EHS Check'"
                                color="primary" label="自主解决" @click="assignedToMe" />
                            <q-btn style="width:120px"
                                :label="approveIdea['流程状态'] != 'Final Check' && approveIdea['流程状态'] != 'EHS Check' ? '批准' : '确认完成整改'"
                                color="purple" @click="updateSafty('Yes')" />
                            <q-btn style="width:120px"
                                :label="approveIdea['流程状态'] != 'Final Check' && approveIdea['流程状态'] != 'EHS Check' ? '拒绝' : '退回整改'"
                                color="red" @click="updateSafty('No')" />

                        </div>
                    </q-card-section>

                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template v-for="(value, key) in approveIdea">
                                <div v-if="key == '隐患内容' || key == '建议解决方案' || key == '整改人方案' || key == '临时方案'"
                                    style="width: 100%;">
                                    <q-input type="textarea" filled v-model="approveIdea[key]" dense stack-label
                                        :label="key" readonly style="height: 100px;" />
                                </div>
                                <div v-else-if="key == '整改人'">
                                    <q-input v-if="value != null && value.hasOwnProperty('label')" filled
                                        v-model="approveIdea[key]['label']" dense stack-label :label="key" readonly
                                        style="width:300px" />
                                    <q-input v-else filled v-model="approveIdea[key]" dense stack-label :label="key"
                                        readonly style="width:300px" />
                                </div>

                                <div v-else-if="!key.includes('照片') && key !== '整改人'">
                                    <q-input filled v-model="approveIdea[key]" dense stack-label :label="key" readonly
                                        style="width:300px" />
                                </div>



                                <div v-else-if="key.includes('照片') && value != null">
                                    <q-btn class="text-bold btn-fixed-width" color="primary" style="width: 300px;" dense
                                        @click="downloadFile(value)">{{ key }}:{{
                                        value.split('/')[4] }}</q-btn>
                                </div>

                                <!-- {{ key }}:{{ value }} -->
                            </template>
                        </div>
                        <!-- <div class="q-gutter-md row items-start"></div> -->
                        <div class="q-gutter-md" style="margin-top: 10px;">
                        </div>
                        <div class="q-gutter-md row items-start" style="margin-top: 10px;">

                        </div>

                    </q-card-section>
                </q-card>
            </q-dialog>

            <div class="q-gutter-y-md">
                <q-card>
                    <q-tabs v-model="tab" inline-label outside-arrows mobile-arrows align="justify"
                        class="bg-primary text-white shadow-2">
                        <q-tab name="inputSafty" icon="mail" label="隐患提交" />
                        <q-tab name="querySafty" icon="mail" label="隐患查询" />
                        <q-tab name="myApproval" icon="alarm" label="隐患审批" />
                    </q-tabs>
                    <q-separator />

                    <q-tab-panels v-model="tab" animated>
                        <q-tab-panel name="querySafty">
                            <div class="q-gutter-md row items-start">
                                <q-input v-model="myIdeaSelectedData['startDate']" filled type="date" stack-label
                                    label-slot dense>
                                    <template v-slot:prepend>
                                        <q-icon name="event" />
                                    </template>
                                    <template v-slot:label>开始时间
                                        <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em>
                                    </template>
                                </q-input>

                                <q-input v-model="myIdeaSelectedData['endDate']" filled type="date" stack-label
                                    label-slot dense>
                                    <template v-slot:prepend>
                                        <q-icon name="event" />
                                    </template>
                                    <template v-slot:label>结束时间
                                        <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em>
                                    </template>
                                </q-input>
                                <q-select dense outlined v-model="myIdeaSelectedData['部门']"
                                    :options="myIdeaDropDownData['department']" label-slot clearable
                                    style="width:180px;">
                                    <template v-slot:label>部门/生产线 <em
                                            class="q-px-sm bg-primary text-white rounded-border"
                                            style="font-size: 12px;">可选</em></template>
                                </q-select>

                                <q-select dense outlined v-model="myIdeaSelectedData['提交人']"
                                    :options="myIdeaDropDownData['employee_Name']" label-slot clearable
                                    style="width: 180px;">
                                    <template v-slot:label>提交人<em class="q-px-sm bg-primary text-white rounded-borders"
                                            style="font-size: 12px;">可选</em></template>
                                </q-select>

                                <q-select dense outlined v-model="myIdeaSelectedData['流程状态']"
                                    :options="myIdeaDropDownData['process_status']" label-slot clearable
                                    style="width: 180px;">
                                    <template v-slot:label>流程状态<em class="q-px-sm bg-primary text-white rounded-borders"
                                            style="font-size: 12px;">可选</em></template>
                                </q-select>

                                <q-select dense outlined v-model="myIdeaSelectedData['隐患区域']"
                                    :options="myIdeaDropDownData['area']" label-slot clearable style="width: 180px;">
                                    <template v-slot:label>区域<em class="q-px-sm bg-primary text-white rounded-borders"
                                            style="font-size: 12px;">可选</em></template>
                                </q-select>







                            </div>
                            <div v-if="myIdeaData">
                                <q-table :data="myIdeaData" row-key="name" :columns="myIdeaColumn"
                                    :pagination.sync="myPagination" :filter="filter" dense
                                    class="my-sticky-virtscroll-table" virtual-scroll
                                    :virtual-scroll-sticky-size-start="48" :rows-per-page-options="[0]">
                                    <template v-slot:top="props">
                                        <div class="text-h6" style="font-weight: 600;">安全隐患清单</div>
                                        <q-space></q-space>
                                        <!-- <q-input borderless dense debounce="300" v-model="filter" placeholder="Search"
                                            class="bg-indigo-1">
                                            <template v-slot:append>
                                                <q-icon name="search" />
                                            </template>
                        </q-input> -->
                                        <div style="font-weight: 600;">如需查看延期项目请点击-></div>
                                        <q-toggle v-model="adpotFilter_byQuery" color="green"
                                            :label="adpotFilter_byQuery ? '所有项目' : '延期项目'"
                                            style="font-weight: 600;margin-right: 20px;" />
                                        <q-input borderless dense debounce="300" v-model="filter" placeholder="Search"
                                            class="bg-indigo-1">
                                            <template v-slot:append>
                                                <q-icon name="search" />
                                            </template>
                                        </q-input>
                                        <q-btn color="primary" icon-right="archive" label="导出到CSV" no-caps
                                            @click="exportTable" />

                                        <q-btn flat round dense
                                            :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
                                            @click="props.toggleFullscreen" class="q-ml-md" label="全屏" />
                                    </template>

                                    <template v-slot:body="props">
                                        <q-tr :props="props" @click="toShow(props)"
                                            :style="new Date(props.row['审批时间']) < new Date().setDate(new Date().getDate() - (props.row['风险等级'] == '高' ? 7 : props.row['风险等级'] == '中' ? 14 : props.row['风险等级'] == '低' ? 30 : 0)) && props.row['流程状态'] != 'Completed' && props.row['流程状态'] != 'open' && props.row['流程状态'] != 'Rejected' ? 'background:#D32F2F;color:white' : ''">
                                            <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                                <div
                                                    :style="col.name == '隐患内容' || col.name == '整改人方案' ? 'width: 200px;white-space: normal;' : ''">
                                                    {{ col.value }}</div>
                                            </q-td>
                                        </q-tr>
                                    </template>
                                </q-table>
                            </div>
                        </q-tab-panel>
                    </q-tab-panels>

                    <q-tab-panels v-model="tab" animated>
                        <q-tab-panel name="inputSafty">
                            <div>
                                <div class="q-gutter-md row items-start">
                                    <q-select outlined v-model="inputData['department']"
                                        :options="dropDownData['department']" label-slot clearable style="width: 200px;"
                                        dense>
                                        <template v-slot:label>部门/生产线
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['employee_Name']"
                                        :options="dropDownData['employee_Name']" label-slot clearable
                                        style="width: 200px;" dense>
                                        <template v-slot:label>提交人
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['Safty_Type']"
                                        :options="dropDownData['Safty_Type']" label-slot clearable style="width: 200px;"
                                        dense>
                                        <template v-slot:label>隐患类别
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['Safty_DailyType']"
                                        :options="dropDownData['Safty_DailyType']" label-slot clearable
                                        style="width: 200px;" dense>
                                        <template v-slot:label>日常检查类型
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['Safty_Department']"
                                        :options="dropDownData['Safty_Department']" label-slot clearable
                                        style="width: 200px;" dense>
                                        <template v-slot:label>发生区域部门
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['Safty_Area']"
                                        :options="dropDownData['Safty_Area']" label-slot clearable style="width: 200px;"
                                        dense>
                                        <template v-slot:label>发生区域
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['Safty_Repeat']" :options="['是', '否']"
                                        label-slot clearable style="width: 200px;" dense>
                                        <template v-slot:label>是否重复发生
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>


                                </div>
                                <!-- <div class="q-gutter-md row items-start"></div> -->
                                <div class="q-gutter-md" style="margin-top: 10px;">
                                    <q-input outlined v-model="inputData['Safty_Problem']" label-slot clearable
                                        type="textarea" style="height: 100px;" dense>
                                        <template v-slot:label>请描述发现的隐患
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填
                                            </em>
                                        </template>
                                    </q-input>
                                </div>

                                <div class="q-gutter-md row items-start" style="margin-top: 0px;">
                                    <q-uploader batch multiple auto-upload dense :factory="factoryFn_Safty"
                                        style=" max-width: 300px;height:150px" @uploaded="handleUploadSuccess"
                                        label="隐患照片上传" />
                                </div>
                                <div class="q-gutter-md row items-start" style="margin-top: 0px;">
                                    <q-btn color="secondary" label="提交隐患" size="lg" @click="submitIdea()"
                                        style="margin-top: 10px;" />
                                    <q-btn color="purple" label="切换到点子银行" size="lg" @click="gotoIdea()"
                                        style="margin-top: 10px;" />
                                </div>

                            </div>
                        </q-tab-panel>

                        <q-tab-panel name="myApproval">
                            <div v-if="approveIdeaList">
                                <q-table :data="approveIdeaList" row-key="name" :pagination.sync="myPagination"
                                    :filter="filter1" dense class="my-sticky-virtscroll-table" virtual-scroll
                                    :virtual-scroll-sticky-size-start="48" :rows-per-page-options="[0]">
                                    <template v-slot:top="props">
                                        <div class="text-h6" style="font-weight: 600;">我的审批</div>
                                        <q-space></q-space>
                                        <div style="font-weight: 600;">如需查看所有项目请点击-></div>
                                        <q-toggle v-model="adpotFilter" color="green"
                                            :label="adpotFilter ? '未审批项目' : '所有项目'"
                                            style="font-weight: 600;margin-right: 20px;" />
                                        <q-input borderless dense debounce="300" v-model="filter1" placeholder="Search"
                                            class="bg-indigo-1">
                                            <template v-slot:append>
                                                <q-icon name="search" />
                                            </template>
                                        </q-input>

                                        <q-btn flat round dense
                                            :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
                                            @click="props.toggleFullscreen" class="q-ml-md" label="全屏" />
                                    </template>

                                    <template v-slot:body="props">
                                        <q-td :props="props">

                                        </q-td>

                                        <q-tr :props="props" @click="toApprove(props)">
                                            <template>
                                                <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                                    {{ col.value }}
                                                </q-td>
                                            </template>

                                        </q-tr>
                                    </template>
                                </q-table>
                            </div>
                        </q-tab-panel>
                    </q-tab-panels>
                </q-card>

            </div>
        </div>
    </base-content>
</template>
<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import { exportFile } from 'quasar'
import * as XLSX from 'xlsx';
import { get } from 'lodash';
import uploadURL from '../../utils/uploadURL'
function wrapCsvValue(val, formatFn) {
    let formatted = formatFn !== void 0
        ? formatFn(val)
        : val

    formatted = formatted === void 0 || formatted === null
        ? ''
        : String(formatted)

    formatted = formatted.split('"').join('""')
    // formatted = formatted.split('\r').join('\\r')
    /**
     * Excel accepts \n and \r in strings, but some other CSV parsers do not
     * Uncomment the next two lines to escape new lines
     */
    // .split('\n').join('\\n')
    // .split('\r').join('\\r')

    return `"${formatted}"`
}

export default {
    components: {
        BaseContent
    },
    data() {
        return {
            webURL_upload: '',
            filter: '',
            filter1: '',
            tab: 'inputSafty',
            myPagination: { rowsPerPage: 0 },
            employee_List: [],
            dropDownData: {
                department: [],
                employee_Name: [],
                Safty_Type: [],
                Safty_DailyType: [],
                Safty_Department: [],
                Safty_Area: [],
                assignedBy_Department: [],
                assignedBy: []
            },

            inputData: {
                department: '',
                employee_Name: '',
                date: '',
                Safty_Type: '',
                Safty_DailyType: '',
                Safty_Department: '',
                Safty_Area: '',
                Safty_Repeat: '',
                Safty_Problem: '',
                Safty_URL: '',

            },
            Safty_Department_RawData: [],
            myIdeaData: false,
            myIdeaRawData: false,
            myIdeaDropDownData: {
                department: [],
                employee_Name: [],
                process_status: ['open', 'In Progress', 'Wait Rectify', 'Completed', 'Rejected'],
                area: [],
            },
            myIdeaSelectedData: {
                部门: null,
                提交人: null,
                流程状态: null,
                隐患区域: null,
                startDate: null,
                endDate: null
            },
            approveIdeaList: false,
            approveRawIdeaList: false,
            approveIdea: false,
            approveIdeaDropdown: {
                Risk_Level: ['高', '中', '低'],
                assignedBy_Department: [],
                assignedBy: []
            },

            //visibleColumns: ['Date','Employee_Name','Owner_Adopt','Owner_Reject_Cause','Idea_Problem','Idea_Solution'],
            myIdeaColumn: [
                { name: '流程状态', align: 'left', label: '流程状态', field: '流程状态', sortable: true },
                { name: '提交日期', align: 'left', label: '提交日期', field: '提交日期', sortable: true, format: val => `${val.substring(0, 19).replace('T', ' ')}` },
                { name: '提交人', align: 'left', label: '提交人', field: '提交人', sortable: true },
                { name: '是否重复发生', align: 'left', label: '是否重复发生', field: '是否重复发生', sortable: true },
                { name: '是否采纳', align: 'left', label: '是否采纳', field: '是否采纳', sortable: true },
                { name: '风险等级', align: 'left', label: '风险等级', field: '风险等级', sortable: true },
                { name: '隐患区域', align: 'left', label: '隐患区域', field: '隐患区域', sortable: true },
                { name: '隐患内容', align: 'left', label: '隐患内容', field: '隐患内容', sortable: true, style: 'width: 100px' },
                { name: '整改人方案', align: 'left', label: '整改人方案', field: '整改人方案', sortable: true },
                { name: '审批时间', align: 'left', label: '审批时间', field: '审批时间', sortable: true }
            ],
            adpotFilter: true,
            adpotFilter_byQuery: true,
            firtDialog: false,
            showDetailDialog: false,
            showDetailData: false,
            processStatus: '',
            assignedInput: {
                assigned_Department: '',
                assigned_name: '',
                riskLevel: '',
                rejectedComment: '',
                ID: '',
                process_Status: '',
                owner_approval_status: '',
                P_Value: 0,
                S_Value: 0
            },
            P_Value_DropDown: [15, 8, 2, 0.03],
            S_Value_DropDown: [15, 10, 6, 4, 2, 0],
            rejectDialog: false,
            prompt_uploadFile: false,
            calRiskDialog: false,
        }
    },
    mounted() {
        console.log('this.$query', this.$route.query)
        this.webURL_upload = uploadURL
        this.getDepartment()
        this.getSaftyType()
        this.getEmployee()
        this.getDepartment_Owner_List()
        if (this.$route.query.function) {
            console.log('有参数')
            this.tab = this.$route.query.function
            this.myIdeaSelectedData['department'] = this.$route.query.department
        }
    },

    // computed: {
    //     currentDate() {
    //         const currentDate = new Date();
    //         currentDate.setDate(currentDate.getDate() - 7);
    //         return currentDate;
    //     }
    // },
    watch: {
        'inputData.department'(newValue, oldValue) {
            this.dropDownData['employee_Name'] = []
            this.inputData['employee_Name'] = ''
            this.filterEmployee('input')
        },
        'inputData.Safty_Department'(newValue, oldValue) {
            this.dropDownData['Safty_Area'] = []
            this.inputData['Safty_Area'] = ''
            this.filter_Safty_Department(this.inputData['Safty_Department'])
        },
        // 'inputData.Safty_Area'(newValue, oldValue) {

        // },
        'myIdeaSelectedData.部门'(newValue, oldValue) {
            this.myIdeaDropDownData['employee_Name'] = []
            this.myIdeaSelectedData['提交人'] = null
            this.filterEmployee('myIdea')
            this.getMyIdead()
            // this.filterApplicant()
        },
        'myIdeaSelectedData.提交人'(newValue, oldValue) {
            console.log('myIdeaSelectedData.提交人', this.myIdeaSelectedData.提交人)
            this.filterApplicant()
        },
        'myIdeaSelectedData.流程状态'(newValue, oldValue) {
            console.log('myIdeaSelectedData.流程状态', this.myIdeaSelectedData.流程状态)
            this.filterApplicant()
        },
        'myIdeaSelectedData.隐患区域'(newValue, oldValue) {
            console.log('myIdeaSelectedData.隐患区域', this.myIdeaSelectedData.隐患区域)
            this.filterApplicant()
        },
        // 'approveIdea.整改人部门'(newValue, oldValue) {
        //     console.log('approveIdea.整改人部门')
        //     console.log(this.approveIdea.整改人部门)
        //     this.approveIdeaDropdown['assignedBy'] = []
        //     // this.approveIdea['整改人'] = ''
        //     this.filterEmployee('approve')
        // },
        'assignedInput.assigned_Department'(newValue, oldValue) {
            this.approveIdeaDropdown['assignedBy'] = []
            // this.assignedInput['assigned_name'] = ''
            this.filterEmployee('approve')
        },
        'myIdeaSelectedData.startDate'(newValue, oldValue) {
            if (this.myIdeaSelectedData.startDate != '' && this.myIdeaSelectedData.endDate != '') {
                this.getMyIdead()
            }

        },
        'myIdeaSelectedData.endDate'(newValue, oldValue) {
            if (this.myIdeaSelectedData.startDate != '' && this.myIdeaSelectedData.endDate != '') {
                this.getMyIdead()
            }
        },

        adpotFilter(newValue, oldValue) {
            this.filterMyApproval()
        },

        adpotFilter_byQuery(newValue, oldValue) {
            console.log('adpotFilter_byQuery', this.adpotFilter_byQuery)
            this.filterApplicant()
        },

        tab() {
            if (this.tab == 'myApproval') {
                this.getMyApprovalIdea()
            }
        },

    },
    methods: {
        gotoIdea() {
            this.$router.push('/ideaBank/ideaBankApplicate')
        },
        async getDepartment() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getDepartment')
            console.log('getDepartment', res)
            _this.dropDownData['department'] = res
            _this.myIdeaDropDownData['department'] = res
            _this.approveIdeaDropdown['assignedBy_Department'] = res
        },
        async getEmployee() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getEmployee')
            console.log('getEmployee', res)
            _this.employee_List = res
            // _this.filterAssginedBy()
        },
        async getSaftyType() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getSaftyType')
            console.log('getSaftyType', res)
            _this.dropDownData['Safty_Type'] = res['Safty_Type']
            _this.dropDownData['Safty_DailyType'] = res['Safty_DailyType']
        },
        async getDepartment_Owner_List() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getDepartment_Owner_List')
            console.log('getDepartment_Owner_List', res)
            _this.Safty_Department_RawData = res
            // _this.Safty_Department_RawData = res
            _this.filter_Safty_Department('')
        },
        async getMyIdead() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`ideabank/querySafty?startDate=${_this.myIdeaSelectedData['startDate']}&endDate=${_this.myIdeaSelectedData['endDate']}`)
            console.log('queryIdea_ByDepartment', res)
            _this.myIdeaData = res
            _this.myIdeaRawData = res
            // const keys = Object.keys(res);
            // keys.forEach(key => {
            //     employeeList[key] = null;
            // });
            _this.filterApplicant()

        },
        async getMyApprovalIdea() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`ideabank/myApprovalSafty?employeeID=${localStorage.getItem('account')}&processType=saftyApprove`)
            console.log('myApprovalSafty', res)
            _this.approveIdeaList = res
            _this.approveRawIdeaList = res
            // const keys = Object.keys(res);
            // keys.forEach(key => {
            //     employeeList[key] = null;
            // });
            _this.filterMyApproval()
        },
        filter_Safty_Department(department) {
            const _this = this
            const data = _this.Safty_Department_RawData
            if (department == '') {
                _this.myIdeaDropDownData.area = []
                _this.dropDownData.Safty_Department = []
                for (let i = 0; i < data.length; i++) {
                    if (!_this.dropDownData.Safty_Department.includes(data[i]['Department'])) {
                        _this.dropDownData.Safty_Department.push(data[i]['Department'])
                    }
                    if (!_this.myIdeaDropDownData.area.includes(data[i]['Area'])) {
                        _this.myIdeaDropDownData.area.push(data[i]['Area'])
                    }

                }
            } else {
                _this.dropDownData.Safty_Area = []
                for (let i = 0; i < data.length; i++) {
                    if (!_this.dropDownData.Safty_Area.includes(data[i]['Area']) && data[i]['Department'] == _this.inputData.Safty_Department) {
                        _this.dropDownData.Safty_Area.push(data[i]['Area'])
                    }
                }
            }
            //console.log('_this.inputData.Safty_Department',_this.dropDownData.Safty_Department)
        },
        filterEmployee(fun) {
            const _this = this
            console.log('department', _this.inputData['department'])
            for (let i = 0; i < _this.employee_List.length; i++) {
                if (fun == 'input') {
                    if (_this.employee_List[i]['line'] === _this.inputData['department']) {
                        _this.dropDownData['employee_Name'].push(_this.employee_List[i]['Employee_PYName'])
                    }
                } else if (fun == 'myIdea') {
                    if (_this.employee_List[i]['line'] === _this.myIdeaSelectedData['部门']) {
                        _this.myIdeaDropDownData['employee_Name'].push(_this.employee_List[i]['Employee_PYName'])
                    }
                } else {
                    // if (_this.employee_List[i]['line'] === _this.approveIdea['整改人部门']) {
                    //     _this.approveIdeaDropdown['assignedBy'].push({ label: _this.employee_List[i]['Employee_PYName'], value: _this.employee_List[i]['Employee_ID'] })
                    // }
                    if (_this.employee_List[i]['line'] === _this.assignedInput['assigned_Department']) {
                        _this.approveIdeaDropdown['assignedBy'].push({ label: _this.employee_List[i]['Employee_PYName'], value: _this.employee_List[i]['Employee_ID'] })
                    }
                }

            }
            console.log('selectedEmployee', _this.approveIdeaDropdown['assignedBy'])
        },

        filterApplicant() {
            const _this = this
            console.log('_this.myIdeaSelectedData', _this.myIdeaSelectedData)
            // const employee_name = _this.myIdeaSelectedData['employee_Name']
            const filterArray = _this.myIdeaSelectedData
            _this.myIdeaData = []

            let filteredData = [..._this.myIdeaRawData]; // 将Data表中的所有数据复制到filteredData
            console.log('filteredData', filteredData)
            for (const key in _this.myIdeaSelectedData) {
                if (_this.myIdeaSelectedData[key] !== null) {
                    // console.log('key', key)
                    if (key != 'startDate' && key != 'endDate') {
                        filteredData = filteredData.filter(item => {
                            return item[key] === _this.myIdeaSelectedData[key]
                        });
                    }
                }


            }
            if (_this.adpotFilter_byQuery == false) {
                console.log('开始查询过期项目')
                const currentDate = new Date();
                filteredData = filteredData.filter(item => {
                    // 计算每个风险等级对应的过期天数
                    const expireDays = item['风险等级'] === '高' ? 7 :
                        item['风险等级'] === '中' ? 14 :
                            item['风险等级'] === '低' ? 30 : 0;
                    // console.log('expireDays', expireDays)
                    // 获取审批时间的日期对象，并计算过期日期
                    const approvalDate = new Date(item['审批时间']);
                    const expireDate = new Date(new Date().setDate(new Date().getDate() - expireDays));
                    // console.log('currentDate', new Date())
                    // console.log('expireDate', expireDate)
                    // console.log('approvalDate', approvalDate)
                    // console.log('isExpired', approvalDate < expireDate)

                    // 检查项目是否过期，且流程状态不符合条件
                    const isExpired = approvalDate < expireDate;
                    const statusExcluded = ['Completed', 'open', 'Rejected'].includes(item['流程状态']);
                    // 如果项目已过期且流程状态不被排除，则打印日志并过滤掉
                    if (isExpired && !statusExcluded) {
                        // console.log('过期项目', item);
                        return item; // 这里应该是返回false，因为我们想过滤掉这些项目

                    }
                })
            }
                // filteredData = filteredData.filter(item => {
                //     if (new Date(item['审批时间'] < new Date().setDate(new Date().getDate() - (item['风险等级'] == '高' ? 7 : item['风险等级'] == '中' ? 14 : item['风险等级'] == '低' ? 30 : 0)) )) {
                //         console.log('过期项目', item)
                //     }
                //     return (new Date(item['审批时间'] < new Date().setDate(new Date().getDate() - (item['风险等级'] == '高' ? 7 : item['风险等级'] == '中' ? 14 : item['风险等级'] == '低' ? 30 : 0)) && item['流程状态'] != 'Completed' && item['流程状态'] != 'open' && item['流程状态'] != 'Rejected'))
                // });
                //new Date(props.row['审批时间']) < new Date().setDate(new Date().getDate() - (props.row['风险等级'] == '高' ? 7 : props.row['风险等级'] == '中' ? 14 : props.row['风险等级'] == '低' ? 30 : 0)) && props.row['流程状态'] != 'Completed' && props.row['流程状态'] != 'open' && props.row['流程状态'] != 'Rejected' 
            
            console.log('filteredData', filteredData)
            _this.myIdeaData = filteredData
        },
        filterMyApproval() {
            const _this = this
            _this.approveIdeaList = []
            for (let i = 0; i < _this.approveRawIdeaList.length; i++) {
                if (_this.adpotFilter) {
                    if (_this.approveRawIdeaList[i]['是否采纳'] == null || _this.approveRawIdeaList[i]['流程状态'] == 'open' || _this.approveRawIdeaList[i]['流程状态'] == 'Final Check' || (_this.approveRawIdeaList[i]['流程状态'] == 'EHS Check' && localStorage.getItem('Position') == 'EHS经理')) {
                        _this.approveIdeaList.push(_this.approveRawIdeaList[i])
                    }
                } else {
                    _this.approveIdeaList = _this.approveRawIdeaList
                }
            }
        },

        filterMyApproval_byQuery() {
            const _this = this
            _this.approveIdeaList = []
            for (let i = 0; i < _this.approveRawIdeaList.length; i++) {
                if (_this.adpotFilter) {
                    if (_this.approveRawIdeaList[i]['是否采纳'] == null || _this.approveRawIdeaList[i]['流程状态'] == 'open' || _this.approveRawIdeaList[i]['流程状态'] == 'Final Check' || (_this.approveRawIdeaList[i]['流程状态'] == 'EHS Check' && localStorage.getItem('Position') == 'EHS经理')) {
                        _this.approveIdeaList.push(_this.approveRawIdeaList[i])
                    }
                } else {
                    _this.approveIdeaList = _this.approveRawIdeaList
                }
            }
        },
        assignedToMe() {
            const _this = this
            for (let i = 0; i < _this.employee_List.length; i++) {
                if (_this.employee_List[i]['Employee_ID'] == localStorage.getItem('account')) {
                    //assignedName = _this.employee_List[i]['Employee_PYName']
                    _this.assignedInput['assigned_Department'] = _this.employee_List[i]['line']
                    _this.assignedInput['assigned_name'] = { 'label': _this.employee_List[i]['Employee_PYName'], 'value': _this.employee_List[i]['Employee_ID'] }
                    // _this.assignedInput['assigned_Department']=_this.employee_List[i]['line']
                    console.log(' _this.assignedInput', _this.assignedInput)
                    break
                }
            }

        },
        submitIdea() {
            const _this = this
            console.log('_this.inputData', _this.inputData)
            if (_this.inputData['Safty_Type'] == '' || _this.inputData['Safty_DailyType'] == '' || _this.inputData['Safty_Department'] == '' || _this.inputData['Safty_Area'] == '' || _this.inputData['Safty_Repeat'] == '' || _this.inputData['Safty_Problem'] == '') {
                _this.$q.dialog({
                    title: '安全隐患无法提交',
                    message: '请填写完整必填项'
                })
                return
            }
            let Manager_ID = ''
            const data = _this.Safty_Department_RawData
            for (let i = 0; i < data.length; i++) {
                if (data[i]['Department'] == _this.inputData['Safty_Department'] && data[i]['Area'] == _this.inputData['Safty_Area']) {
                    Manager_ID = data[i]['Employee_ID']
                }
            }
            _this.$http.post('ideabank/insertSafty', { 'data': _this.inputData, 'Manager_ID': Manager_ID, 'Entry_By': localStorage.getItem('account') }).then(function (response) {
                console.log('response', response)
                if (response.data === '已发送') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `安全隐患提交成功！系统第一时间发送邮件给区域负责人`,
                        position: 'top'
                    })
                    _this.inputData['Safty_Type'] = ''
                    _this.inputData['Safty_DailyType'] = ''
                    _this.inputData['Safty_Department'] = ''
                    _this.inputData['Safty_Area'] = ''
                    _this.inputData['Safty_Repeat'] = ''
                    _this.inputData['Safty_Problem'] = ''
                    _this.inputData['Safty_URL'] = ''
                }
            })
        },
        toShow(props) {
            const _this = this
            _this.showDetailData = props.row
            console.log(_this.showDetailData)
            _this.showDetailDialog = true
        },


        toApprove(props) {
            console.log('toApprove', props)
            const _this = this
            _this.approveIdea = props.row
            _this.assignedInput = {
                'assigned_Department': '',
                'assigned_name': '',
                'riskLevel': props.row['风险等级'] ? props.row['风险等级'] : '',
                'rejectedComment': '',
                'ID': props.row['ID'],
                'process_Status': props.row['流程状态'],
                'owner_approval_status': props.row['是否采纳']
            }
            //let assignedName=''
            _this.processStatus = props.row['流程状态']
            console.log('整改人', _this.approveIdea)
            if (_this.approveIdea['整改人'] != null && _this.approveIdea['整改人'] != '') {
                console.log('level1')
                if (!_this.approveIdea['整改人'].hasOwnProperty('value')) {
                    for (let i = 0; i < _this.employee_List.length; i++) {
                        if (_this.employee_List[i]['Employee_ID'] == props.row['整改人']) {
                            //assignedName = _this.employee_List[i]['Employee_PYName']
                            //_this.approveIdea['整改人部门'] = _this.employee_List[i]['line']
                            //_this.approveIdea['整改人'] = { 'label': _this.employee_List[i]['Employee_PYName'], 'value': props.row['整改人'] }
                            _this.assignedInput['assigned_Department'] = _this.employee_List[i]['line']
                            _this.assignedInput['assigned_name'] = { 'label': _this.employee_List[i]['Employee_PYName'], 'value': props.row['整改人'] }
                            break
                        }
                    }
                    // _this.assignedInput['assigned_name']={ 'label':assignedName, 'value': props.row['整改人'] }
                }
            }
            console.log('开始弹窗')
            _this.firtDialog = true

        },
        async updateSafty(status) {
            const _this = this
            _this.rejectDialog = false
            //console.log('approveSafty->assignedInput', _this.assignedInput)
            if (status == 'No' && _this.assignedInput['rejectedComment'] == '') {
                _this.rejectDialog = true
                return
            }
            _this.assignedInput['owner_approval_status'] = status
            if (_this.assignedInput['process_Status'] !== 'Final Check' && _this.assignedInput['process_Status'] !== 'EHS Check') {
                if (status == 'Yes' && (_this.assignedInput['assigned_name'] == '' || _this.assignedInput['riskLevel'] == '' || _this.assignedInput['riskLevel'] == null || _this.assignedInput['assigned_name'] == null)) {
                    _this.$q.dialog({
                        title: '安全隐患无法采纳',
                        message: '由于没有填写分配人员或安全等级'
                    })
                    return
                }
            }

            console.log('_this.assignedInput', _this.assignedInput)
            _this.$http.post('ideabank/updateSafty', { 'rowData': _this.approveIdea, 'data': _this.assignedInput, 'approver': { "account": localStorage.getItem('account'), "username": localStorage.getItem('username') }, 'processType': 'approval' }).then(function (response) {
                console.log('response', response)
                if (response.data === "已发送") {
                    _this.$q.notify({
                        type: 'positive',
                        message: `隐患审批成功！`,
                        position: 'top'
                    })
                    _this.getMyApprovalIdea()
                    _this.firtDialog = false
                }
            })

        },
        handleUploadSuccess(response) {
            var _this = this
            var responseFileName = response['files'][0]['name']
            var responseURL = response['files'][0]['xhr']['responseText']
            _this.inputData['Safty_URL'] = responseURL
            _this.prompt_uploadFile = false
        },
        downloadFile(url) {
            console.log(url)
            window.open(url, '_blank');
        },
        factoryFn_Safty(files) {
            var _this = this
            _this.prompt_uploadFile = true
            return {
                url: _this.webURL_upload + '?system=Safty',
                method: 'POST'
            }
        },
        cal_risk() {
            const _this = this
            _this.calRiskDialog = true
        },
        cal_riskLevel() {
            let riskLevel = ''
            const R = this.assignedInput.P_Value * this.assignedInput.S_Value
            console.log('R', R)
            if (R >= 80) {
                riskLevel = '高'
            } else if (R >= 30 && R < 80) {
                riskLevel = '中'
            } else {
                riskLevel = '低'
            }
            this.assignedInput.riskLevel = riskLevel
            this.calRiskDialog = false
        },


        exportTable() {
            //naive encoding to csv format
            const content = [this.myIdeaColumn.map(col => wrapCsvValue(col.label))].concat(
                this.myIdeaData.map(row => this.myIdeaColumn.map(col => wrapCsvValue(
                    typeof col.field === 'function'
                        ? col.field(row)
                        : row[col.field === void 0 ? col.name : col.field],
                    col.format
                )).join(','))
            ).join('\r\n')
            const blob = new Blob([new Uint8Array([0xEF, 0xBB, 0xBF]), content], { type: 'text/csv;charset=utf-8;' });
            const downloadLink = document.createElement('a');
            downloadLink.href = URL.createObjectURL(blob);
            downloadLink.setAttribute('download', '安全隐患项目导出.csv');
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        },

    }
}
</script>

<style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */
  height: 500px
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0
</style>
