const _Base = require('../config/dbbase')
const crypto = require('crypto');

function sqlexec() {
  _Base.call(this);
}

sqlexec.prototype = new _Base()

/**
 * 密码加密函数
 * @param {string} password - 明文密码
 * @returns {string} - 加密后的密码
 */
function encryptPassword(password) {
  const secretKey = 'supply_chain_secret_key';
  const hash = crypto.createHmac('sha256', secretKey)
                    .update(password)
                    .digest('hex');
  return hash;
}

/**
 * 生成随机密码
 * @param {number} length - 密码长度
 * @returns {string} - 随机生成的密码
 */
function generateRandomPassword(length = 6) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

/**
 * 检查用户是否已存在
 * @param {string} employeeId - 员工ID
 * @param {function} callBack - 回调函数
 */
sqlexec.prototype.checkUserExists = function (employeeId, callBack) {
  const query = `SELECT Employee_ID FROM WebSite_login_List WHERE Employee_ID = '${employeeId}'`;

  sqlexec.prototype._query(query, function (err, result) {
    if (err) {
      return callBack({ success: false, error: err });
    }
    return callBack({
      success: true,
      exists: result.recordset.length > 0
    });
  });
}

/**
 * 注册新用户
 * @param {object} userData - 用户数据
 * @param {function} callBack - 回调函数
 */
sqlexec.prototype.registerUser = function (userData, callBack) {
  // 检查用户是否已存在
  this.checkUserExists(userData.employee_id, (checkResult) => {
    if (!checkResult.success) {
      return callBack({ success: false, error: checkResult.error });
    }

    if (checkResult.exists) {
      return callBack({
        success: false,
        error: '用户ID已存在'
      });
    }

    // 生成随机密码
    const randomPassword = generateRandomPassword();
    // 加密密码
    const encryptedPassword = encryptPassword(randomPassword);

    // 设置默认值
    const mill = userData.mill || '南京南厂';
    const relatedLine = userData.related_line || '';

    // 根据实际表结构插入所有字段
    const query = `INSERT INTO WebSite_login_List
                  (Employee_ID, Psw, Employee_Name, Mill, Related_Line)
                  VALUES ('${userData.employee_id}', '${encryptedPassword}',
                  '${userData.employee_name}', '${mill}', '${relatedLine}')`;
    console.log('执行SQL查询:', query)
    sqlexec.prototype._query(query, function (err, result) {
      if (err) {
        console.error('SQL错误:', err)
        return callBack({ success: false, error: err });
      }
      console.log('SQL执行结果:', result)
      return callBack({
        success: true,
        employee_id: userData.employee_id,
        temporary_password: randomPassword
      });
    });
  });
}

/**
 * 重置用户密码
 * @param {string} employeeId - 员工ID
 * @param {function} callBack - 回调函数
 */
sqlexec.prototype.resetPassword = function (employeeId, callBack) {
  // 检查用户是否存在
  this.checkUserExists(employeeId, (checkResult) => {
    if (!checkResult.success) {
      return callBack({ success: false, error: checkResult.error });
    }

    if (!checkResult.exists) {
      return callBack({
        success: false,
        error: '用户不存在'
      });
    }

    // 生成随机密码
    const randomPassword = generateRandomPassword();
    // 加密密码
    const encryptedPassword = encryptPassword(randomPassword);

    const query = `UPDATE WebSite_login_List
                  SET Psw = '${encryptedPassword}'
                  WHERE Employee_ID = '${employeeId}'`;

    sqlexec.prototype._query(query, function (err, result) {
      if (err) {
        return callBack({ success: false, error: err });
      }

      // 检查是否有行被更新
      if (result.rowsAffected[0] === 0) {
        return callBack({
          success: false,
          error: '密码重置失败，未找到用户'
        });
      }

      return callBack({
        success: true,
        employee_id: employeeId,
        new_password: randomPassword
      });
    });
  });
}

/**
 * 更新用户信息（部门和职位）
 * @param {object} userData - 用户数据
 * @param {function} callBack - 回调函数
 */
sqlexec.prototype.updateUserInfo = function (userData, callBack) {
  // 检查用户是否存在
  this.checkUserExists(userData.employee_id, (checkResult) => {
    if (!checkResult.success) {
      return callBack({ success: false, error: checkResult.error });
    }

    if (!checkResult.exists) {
      return callBack({
        success: false,
        error: '用户不存在'
      });
    }

    const query = `UPDATE WebSite_login_List
                  SET Department = '${userData.department}',
                      Title = '${userData.title}'
                  WHERE Employee_ID = '${userData.employee_id}'`;

    sqlexec.prototype._query(query, function (err, result) {
      if (err) {
        return callBack({ success: false, error: err });
      }

      // 检查是否有行被更新
      if (result.rowsAffected[0] === 0) {
        return callBack({
          success: false,
          error: '用户信息更新失败，未找到用户'
        });
      }

      return callBack({ success: true });
    });
  });
}

/**
 * 获取所有用户列表
 * @param {function} callBack - 回调函数
 */
sqlexec.prototype.getUsers = function (callBack) {
  const query = `SELECT Employee_ID, Employee_Name, Mill, Related_Line
                FROM WebSite_login_List`;

  sqlexec.prototype._query(query, function (err, result) {
    if (err) {
      console.error('获取用户列表错误:', err);
      return callBack({ success: false, error: err });
    }
    console.log('获取用户列表成功, 记录数:', result.recordset.length);
    return callBack({
      success: true,
      data: result.recordset
    });
  });
}

/**
 * 获取权限组和应用列表
 * @param {function} callBack - 回调函数
 */
sqlexec.prototype.getAccessGroups = function (callBack) {
  const query = `SELECT Group_Name, App_Name FROM Access_Group`;

  sqlexec.prototype._query(query, function (err, result) {
    if (err) {
      return callBack({ success: false, error: err });
    }
    return callBack({
      success: true,
      data: result.recordset
    });
  });
}

/**
 * 获取用户权限列表
 * @param {string} employeeId - 员工ID
 * @param {function} callBack - 回调函数
 */
sqlexec.prototype.getUserAccess = function (employeeId, callBack) {
  const query = `SELECT * FROM Access_List
                WHERE Employee_ID = '${employeeId}'`;

  sqlexec.prototype._query(query, function (err, result) {
    if (err) {
      return callBack({ success: false, error: err });
    }
    return callBack({
      success: true,
      data: result.recordset
    });
  });
}

/**
 * 设置用户权限
 * @param {object} accessData - 权限数据
 * @param {function} callBack - 回调函数
 */
sqlexec.prototype.setUserAccess = function (accessData, callBack) {
  // 生成Access_LinkID
  const accessLinkId = `${accessData.employee_id}-${accessData.access_group}-${accessData.app_name}`;

  // 检查权限是否已存在
  const checkQuery = `SELECT * FROM Access_List
                    WHERE Access_LinkID = '${accessLinkId}'`;

  sqlexec.prototype._query(checkQuery, function (err, checkResult) {
    if (err) {
      return callBack({ success: false, error: err });
    }

    // 如果权限已存在，返回成功
    if (checkResult.recordset.length > 0) {
      return callBack({ success: true, exists: true });
    }

    // 添加新权限
    const insertQuery = `INSERT INTO Access_List
                        (Employee_ID, Employee_Name, Access_Group, App_Name, Access_LinkID)
                        VALUES ('${accessData.employee_id}', '${accessData.employee_name}',
                        '${accessData.access_group}', '${accessData.app_name}', '${accessLinkId}')`;

    sqlexec.prototype._query(insertQuery, function (insertErr, insertResult) {
      if (insertErr) {
        return callBack({ success: false, error: insertErr });
      }
      return callBack({ success: true, exists: false });
    });
  });
}

/**
 * 移除用户权限
 * @param {string} accessLinkId - 权限关联ID
 * @param {function} callBack - 回调函数
 */
sqlexec.prototype.removeUserAccess = function (accessLinkId, callBack) {
  const query = `DELETE FROM Access_List
                WHERE Access_LinkID = '${accessLinkId}'`;

  sqlexec.prototype._query(query, function (err, result) {
    if (err) {
      return callBack({ success: false, error: err });
    }

    // 检查是否有行被删除
    if (result.rowsAffected[0] === 0) {
      return callBack({
        success: false,
        error: '移除权限失败，权限不存在'
      });
    }

    return callBack({ success: true });
  });
}

module.exports = sqlexec;