<template>
    <div class="container" style="width: 100%;">
        <q-dialog v-model="showDialog" persistent full-width>
            <div style="width: 100%;">
                <projectDetail :tasksData=" selectedTask"/>
            </div>
        </q-dialog>
        <!-- </q-dialog> -->
        <div ref="gantt" class="gantt-container"></div>
    </div>
</template>
<script>
import {
    gantt
} from 'dhtmlx-gantt'; //引入插件
import "dhtmlx-gantt/codebase/dhtmlxgantt.css"  //引入插件样式
import projectDetail from '../GanttChart/ProjectDetail.vue'
export default {
    props: {
        tasksData: {
            type: Array,
            default: []
        },
    },
    components: {
        projectDetail
    },
    name: 'gantt',
    data() {
        return {
            tasks: {
                data: []
            },
            showDialog: false,
            selectedTask: {},
        }
    },

    watch: {
        // value() {
        //     this.selectChange(this.value)
        // }
    },



    methods: {
        //开始时间-结束时间参数
        DateDifference: function (strDateStart, strDateEnd) {
            var begintime_ms = Date.parse(new Date(strDateStart.replace(/-/g, '/'))) //begintime 为开始时间
            var endtime_ms = Date.parse(new Date(strDateEnd.replace(/-/g, '/'))) // endtime 为结束时间
            var date3 = endtime_ms - begintime_ms //时间差的毫秒数
            var days = Math.floor(date3 / (24 * 3600 * 1000))
            return days
        },
        initData: function () {
            console.log('tasksData:', this.tasksData)
            this.tasks.data = this.tasksData.map(function (current, ind, arry) {
                var newObj = {}
                if (current.type) { //存在type字段 说明非一级菜单，判断阶段的具体类型 设置不同颜色
                    if (current.type == 1) { //冒烟
                        newObj = Object.assign({}, current, {
                            'color': '#fcca02'
                        })
                    } else if (current.type == 2) { //单元
                        newObj = Object.assign({}, current, {
                            'color': '#fec0dc'
                        })
                    } else if (current.type == 3) { //回归
                        newObj = Object.assign({}, current, {
                            'color': '#62ddd4'
                        })
                    } else if (current.type == 4) {
                        newObj = Object.assign({}, current, {
                            'color': '#d1a6ff'
                        })
                    }
                } else { //一级菜单是蓝色的
                    newObj = Object.assign({}, current, {
                        'color': '#f0e800'
                    })
                }

                return newObj
            })
        },
        formatChart() {
            //自适应甘特图的尺寸大小, 使得在不出现滚动条的情况下, 显示全部任务
            gantt.config.autosize = true
            //只读模式
            gantt.config.readonly = false
            //是否显示左侧树表格
            gantt.config.show_grid = true
            //表格列设置
            gantt.config.columns = [{
                name: 'text',
                label: '项目名字',
                // label: '分项工程名称',
                tree: true,
                width: '280',
                onrender: function (task, node) {
                    node.setAttribute("class", "gantt_cell gantt_last_cell gantt_cell_tree " + task.status);
                }
            },
                // {
                //     name: 'duration',
                //     label: '时长',
                //     align: 'center',
                //     template: function (obj) {
                //         return obj.duration + '天'
                //     },
                //     hide: false
                // },
                // {
                //     name: 'start_date',
                //     label: '开始日期',
                //     align: 'left',
                //     width: '280',
                //     template: function (obj) {
                //         return obj.start_date
                //     },
                //     hide: false
                // }
            ]
            var dayScaleTemplate = function (date) {
                var dateToStr = gantt.date.date_to_str("%Y-%m-%d");
                return dateToStr(date)
            };


            var weekScaleTemplate = function (date) {
                var dateToStr = gantt.date.date_to_str("%m %d");
                var endDate = gantt.date.add(gantt.date.add(date, 1, "week"), -1, "day");
                var weekNum = gantt.date.date_to_str("第 %W 周");
                return weekNum(date)
            };
            var daysStyle = function (date) {
                var dateToStr = gantt.date.date_to_str("%D");
                if (dateToStr(date) == "六" || dateToStr(date) == "日") return "weekend";
                return "";
            };
            gantt.config.subscales = [{
                unit: "week",
                step: 1,
                template: weekScaleTemplate
            },
                // {
                //     unit: "day",
                //     step: 1,
                //     format: "%d"
                // }
            ];

            gantt.plugins({
                tooltip: true
            });
            gantt.attachEvent("onGanttReady", function () {
                var tooltips = gantt.ext.tooltips;
                gantt.templates.tooltip_text = function (start, end, task) {
                                    const output = `<div style="font-size:14px;padding: 0 10px;width:250px;line-height:20px;white-space:normal;">
                    	<p style="font-weight:bold;margin: 8px 0;">${task.text}</p>
                    	<p style="margin: 4px 0;">持续时间：${task.duration}天</p>
                    	<p style="margin: 4px 0;">开始时间：${dayScaleTemplate(task.start_date)}</p>
                        <p style="margin: 4px 0;">下一阶段：${task.Project_Next_Phase}</p>
                        <p style="margin: 4px 0;">下一阶段时间：${task.Project_Next_Phase_Date}</p>
                        <p style="margin: 4px 0;">下一阶段结束时间：${task.Project_Next_Phase_End_Date}</p>
                    	<p style="margin: 4px 0;">项目状态：${task.status}</p>
                        <p style="margin: 4px 0;">修改人：${task.Modify_By}</p>
                        <p style="margin: 4px 0;">修改时间：${task.Modify_On}</p>
                    </div>`
                    //                 return output;
            //         const excludedKeys = ['id', 'text', 'open'];
            //         let output = `<div style="font-size:14px;padding: 0 10px;width:500px;line-height:20px;white-space:normal;">
            // <p style="font-weight:bold;margin: 8px 0;">${task.text}</p>`;
            //         // console.log('task', task)
            //         for (const [key, value] of Object.entries(task)) {
            //             if (!excludedKeys.includes(key) && !key.includes('$') && !key.includes('URL')) {
            //                 output += `<p style="margin: 4px 0;">${key}：${value}</p>`;
            //             }
            //         }

                    // output += '</div>';
                    return output;
                };
            });

            gantt.attachEvent("onTaskClick", function (id, e) {
                console.log("id:", id);
                console.log('dataArray:', this.tasks)
                this.selectedTask={}

                for (var i = 0; i < this.tasks.data.length; i++) {
                    console.log("this.tasks.data[i].id:", this.tasks.data[i].id)
                    if (this.tasks.data[i].id == id) {
                        console.log('Data:', this.tasks.data[i]);
                        // this.$store.commit('updateSelectedProjectData', this.tasks.data[i])
                        this.selectedTask = this.tasks.data[i]
                        this.showDialog = true
                        break;
                    }
                }
            }, { thisObject: this });


            //设置任务条进度内容
            // gantt.templates.progress_text = function (start, end, task) {
            //     return "<div style='text-align:left;color:black;padding-left:20px'>" + Math.round(task.progress * 100) +
            //         "% </div>";
            // };

            //任务条显示内容
            gantt.templates.task_text = function (start, end, task) {
                // return task.text + '(' + task.duration + '天)';
                return "<div style='text-align:left;color:black'>" + '' + task.duration + '天' +
                    "</div>";
            }


            // gantt.templates.scale_cell_class = function(date) {
            //     /*if(date.getDay()== 0 || date.getDay()== 6){
            //       return "weekend";
            //     }*/
            //     return 'weekend'
            // }


            //任务栏周末亮色
            gantt.templates.task_cell_class = function (item, date) {
                if (date.getDay() == 0 || date.getDay() == 6) {
                    return "weekend";
                }
            };


            //任务条上的文字大小 以及取消border自带样式
            gantt.templates.task_class = function (start, end, item) {
                return item.$level == 0 ? 'firstLevelTask' : 'secondLevelTask'
            }

            gantt.config.layout = {
                css: "gantt_container",
                cols: [{
                    width: 350,
                    min_width: 280,
                    rows: [{
                        view: "grid",
                        scrollX: "gridScroll",
                        scrollable: true,
                        scrollY: "scrollVer"
                    },
                    {
                        view: "scrollbar",
                        id: "gridScroll",
                        group: "horizontal"
                    }
                    ]
                },
                {
                    resizer: true,
                    width: 1
                },
                {
                    rows: [{
                        view: "timeline",
                        scrollX: "scrollHor",
                        scrollY: "scrollVer"
                    },
                    {
                        view: "scrollbar",
                        id: "scrollHor",
                        group: "horizontal"
                    }
                    ]
                },
                {
                    view: "scrollbar",
                    id: "scrollVer"
                }
                ]
            };

            //时间轴图表中，任务条形图的高度
            gantt.config.task_height = 28
            //时间轴图表中，甘特图的高度
            gantt.config.row_height = 50
            //时间轴图表中，如果不设置，只有行边框，区分上下的任务，设置之后带有列的边框，整个时间轴变成格子状。
            gantt.config.show_task_cells = true
            //当task的长度改变时，自动调整图表坐标轴区间用于适配task的长度
            gantt.config.fit_tasks = true
            gantt.config.min_column_width = 50;
            gantt.config.auto_types = true;
            gantt.config.xml_date = "%Y-%m-%d";
            gantt.config.scale_unit = "month";
            gantt.config.step = 1;
            gantt.config.date_scale = "%Y年%M";
            gantt.config.start_on_monday = true;
            gantt.config.scale_height = 90;
            gantt.config.autoscroll = true;
            gantt.config.calendar_property = "start_date";
            gantt.config.calendar_property = "end_date";
            gantt.config.readonly = true;
            gantt.i18n.setLocale('cn');

            // 初始化
            gantt.init(this.$refs.gantt)
            // 数据解析
            gantt.parse(this.tasks)
        },
        // selectChange(val) {
        //     console.log(val)
        //     //测试用例
        //     var obj = {
        //         toolTipsTxt: '新增任务',
        //         text: '新增任务', // 任务名
        //         start_date: '2024-04-15', // 开始时间
        //         id: 24, // 任务id
        //         duration: 2, // 任务时长，从start_date开始计算
        //         parent: 1, // 父任务ID
        //         type: 4,
        //         progress: 0.8,
        //         status: "popular"
        //     }
        //     this.tasks.data.push(obj)

        //     // 数据解析
        //     gantt.parse(this.tasks)
        //     // 刷新数据
        //     gantt.refreshData();
        // }
    },
    mounted() {
        const vm = this


        vm.$nextTick(() => {
            //vm.tasks.data = vm.tasksData
            console.log('vm.tasksData', vm.tasksData)
            if(vm.tasksData.length > 0){
               for(var i = 0; i < vm.tasksData.length; i++){
                vm.tasksData[i]['progress']=0
               }
            }
            vm.initData()
            vm.formatChart()
        })

    }
}
</script>
<style lang="scss">
.firstLevelTask {
    border: none;

    .gantt_task_content {
        font-size: 13px;
    }
}

.secondLevelTask {
    border: none;
}

.thirdLevelTask {
    border: 2px solid #da645d;
    color: #da645d;
    background: #da645d;
}

.milestone-default {
    border: none;
    background: rgba(0, 0, 0, 0.45);
}

.milestone-unfinished {
    border: none;
    background: #5692f0;
}

.milestone-finished {
    border: none;
    background: #84bd54;
}

.milestone-canceled {
    border: none;
    background: #da645d;
}

html,
body {
    margin: 0px;
    padding: 0px;
    height: 100%;
    overflow: hidden;
}

.container {
    height: 100%;
    width: 100%;
    position: relative;

    .gantt_grid_head_cell {
        padding-left: 20px;
        text-align: left !important;
        font-size: 14px;
        color: #333;
    }

    .select-wrap {
        position: absolute;
        top: 25px;
        z-index: 99;
        width: 90px;
        left: 180px;

        .el-input__inner {
            border: none;
        }
    }

    .left-container {
        height: 100%;
    }

    .parent {
        .gantt_tree_icon {
            &.gantt_folder_open {
                // background-image: url(assets/gantt-icon.svg) !important;
            }

            &.gantt_folder_closed {
                // background-image: url(assets/gantt-icon-up.svg) !important;
            }
        }
    }

    .green,
    .yellow,
    .pink,
    .popular {
        .gantt_tree_icon.gantt_file {
            background: none;
            position: relative;

            &::before {
                content: "";
                width: 10px;
                height: 10px;
                border-radius: 50%;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }

    .green {
        .gantt_tree_icon.gantt_file {
            &::before {
                background: #84bd54;
            }
        }
    }

    .yellow {
        .gantt_tree_icon.gantt_file {
            &::before {
                background: #fcca02;
            }
        }
    }

    .pink {
        .gantt_tree_icon.gantt_file {
            &::before {
                background: #da645d;
            }
        }
    }

    .popular {
        .gantt_tree_icon.gantt_file {
            &::before {
                background: #d1a6ff;
            }
        }
    }

}

.left-container {
    height: 100%;
}

.gantt_task_content {
    text-align: left;
    padding-left: 10px;
}
</style>