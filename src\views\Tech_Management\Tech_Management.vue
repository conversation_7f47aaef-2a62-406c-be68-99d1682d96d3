<template>
  <BaseContent>
    <q-dialog v-model="showDialog" persistent full-width>
      <projectDetail :tasksData="projectInputData" />
    </q-dialog>

    <div class="q-pa-md" >
      <div>
        <div class="row wrap justify-start  q-gutter-sm">
          <div style="font-size: 15px;line-height: 45px;font-weight: 600;">开始日期：</div>
          <q-input style="width: 200px;" v-model="parms['date_from']" filled type="date" label="开始日期" stack-label dense>
            <template v-slot:label>
              <em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
            </template>
          </q-input>
          <!-- <div style="font-size: 15px;line-height: 45px;font-weight: 600;">结束日期：</div>
          <q-input style="width: 200px;" v-model="parms['date_to']" filled type="date" label="结束日期" stack-label dense>
            <template v-slot:label>
              <em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
            </template>
          </q-input> -->
          <q-btn color="primary" class="btn-fixed-width  q-gutter-xs" label="查询"
            @click="Tech_Project_Data(parms['date_from'], parms['date_to'])" />
          <q-btn color="deep-orange" class="btn-fixed-width  q-gutter-xs " label="技术开发项目新增"
            @click="showDialog = true" />
        </div>
      </div>

      <q-card class="full-height-card full-width-card">
        <q-tabs v-model="tab" dense class="text-grey" active-color="primary" indicator-color="primary" align="justify"
          narrow-indicator>
          <q-tab name="Project_Summary" label="Project Summary" />
          <q-tab name="Project_Chat" label="Project Timeline" />
        </q-tabs>

        <q-separator />

        <q-tab-panels v-model="tab" animated class="full-height-panel">
          <q-tab-panel name="Project_Summary" class="full-height-panel">
            <div v-if="projectData" class="full-height-content">
              <q-table  :data="projectData" :columns="columns" :Pagination="initialPagination" :filter="filter"
                @row-click="onRowClick" :style="{ width: width + 'px', height: height + 'px' }">
                <template v-slot:top="props">
                  <q-space />
                  <q-input borderless dense debounce="300" color="primary" v-model="filter"
                    class="bg-grey-4 text-white">
                    <template v-slot:append>
                      <q-icon name="search" />
                    </template>
                  </q-input>
                  <q-btn color="light-green" icon-right="archive" label="导出csv" no-caps @click="exportTable" />
                        <q-btn
          flat round dense
          :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen"
          class="q-ml-md"
        />
                </template>
              </q-table>
            </div>
          </q-tab-panel>

          <q-tab-panel name="Project_Chat" class="full-height-panel">
            <div v-if="projectData" class="q-pa-md full-height-content">
              <GanttChart :tasksData="projectData" v-if="projectData" class="full-height-chart" />
            </div>
          </q-tab-panel>

        </q-tab-panels>
      </q-card>






    </div>

  </BaseContent>

</template>
<script>
import Papa from 'papaparse';
import BaseContent from '../../components/BaseContent/BaseContent'
import GanttChart from '../../components/componentsList/GanttChart/GanttChart'
import projectDetail from '../../components/componentsList/GanttChart/ProjectDetail.vue'
export default {
  name: 'gantt',
  components: {
    BaseContent,
    GanttChart,
    projectDetail
  },
  data() {
    return {
      parms: {
        date_from: '',
        date_to: ''
      },
      projectData: false,
      projectDropdownData: {
        Project_Next_Phase: ['RSR', 'MSR', 'CINS'],
        Porject_Status: ['方案评估中', '方案评估完成等待反馈中', '实验准备中', '商业化准备中']
      },
      projectInputData: {
        id: null,
        Project_Name: '',
        Project_Material_URL: '',
        Mechanical_Engineer: '',
        Electrical_Engineer: '',
        Process_Engineer: '',
        Packaging_Engineer: '',
        Porject_Start_Date: '',
        Project_End_Date: '',
        Project_Feature: '',
        Project_Proposal_URL: '',
        Project_Experiment_Report_URL: '',
        Project_Experiment_Cost: '',
        Project_Evaluation_Program_URL: '',
        Project_Capex_Forecast: '',
        Project_Capex_YTD_Actual: '',
        Project_Capex_Run_Rate: '',
        Project_Next_Phase: '',
        Project_Next_Phase_Date: '',
        Project_Progress: 0,
        Project_Status: '',
        Project_Parent_ID: null,
        Modify_By: ''
      },
      showDialog: false,
      selectedProjectData: {},
      tab: 'Project_Summary',
      columns: [
        {
          "name": "id",
          "label": "ID",
          "field": "id",
          "align": "left",
          sortable: true 
        },
        {
          "name": "Project_Name",
          "label": "项目名称",
          "field": "Project_Name",
          "align": "left"
        },
        {
          "name": "SupportTeam",
          "label": "支持团队",
          "field": "SupportTeam",
          "align": "left",
          "style": "min-width:200px ;white-space: normal;",
          
        },
        {
          "name": "Project_Start_Date",
          "label": "开始日期",
          "field": "Project_Start_Date",
          "align": "left",
          sortable: true 
        },

        {
          "name": "Project_Feature",
          "label": "项目特点",
          "field": "Project_Feature",
          "align": "left",
          "style": "max-width:300px ;overflow: hidden;text-overflow: ellipsis;white-space: normal;",
        },

        {
          "name": "Project_Experiment_Cost",
          "label": "项目实验成本",
          "field": "Project_Experiment_Cost",
          "align": "left",
          sortable: true 
        },

        {
          "name": "Project_Capex_Forecast",
          "label": "CAPEX Forecast",
          "field": "Project_Capex_Forecast",
          "align": "left",
          "style":"max-width:50px",
          sortable: true 
        },

        {
          "name": "Project_Capex_Run_Rate",
          "label": "CAPEX Run Rate",
          "field": "Project_Capex_Run_Rate",
          "align": "left",
          sortable: true 
        },
        {
          "name": "Project_Next_Phase",
          "label": "项目下一阶段",
          "field": "Project_Next_Phase",
          "align": "left",
          sortable: true 
        },
        {
          "name": "Project_Next_Phase_Date",
          "label": "项目下一阶段日期",
          "field": "Project_Next_Phase_Date",
          "align": "left",
          sortable: true 
        },
        {
          "name": "duration",
          "label": "持续时间",
          "field": "duration",
          "align": "left",
          sortable: true 
        },


        {
          "name": "Project_Progress",
          "label": "项目进度",
          "field": "Project_Progress",
          "align": "left",
          format: val => `${val * 100}%`,
          sortable: true 
        },
        {
          "name": "Project_Status",
          "label": "项目状态",
          "field": "Project_Status",
          "align": "left",
          sortable: true 
        },

        
      ],
      initialPagination: {
        sortBy: 'desc',
        descending: false,
        rowsPerPage: 20
        // rowsNumber: xx if getting data from a server
      },
      filter:'',
      width: window.innerWidth,
      height: window.innerHeight
    }
  },


  beforeDestroy() {
    window.removeEventListener('resize', this.updateSize)
  },
watch:{
// width(val){
//   console.log('width', val)
// },
// height(val){
//   console.log('height', val)
// }
},


  mounted() {
    const currentDate = new Date()
    this.parms['date_from'] = currentDate.getFullYear() + '-' + (currentDate.getMonth()<10? '0'+(currentDate.getMonth()) : currentDate.getMonth()) + '-' + currentDate.getDate()
    this.parms['date_to'] = currentDate.getFullYear() + '-' + (currentDate.getMonth() + 1? '0'+(currentDate.getMonth() + 1) : currentDate.getMonth() + 1) + '-' + currentDate.getDate()
    console.log('parms', this.parms)
    this.Tech_Project_Data(this.parms['date_from'], this.parms['date_to'])
    window.addEventListener('resize', this.updateSize)
  },
  methods: {
    
updateSize() {
      this.width = window.innerWidth
      this.height = window.innerHeight
    },
    async Tech_Project_Data(startDate, endDate) {
      const _this = this
      _this.projectData=false
      const {
        data: res
      } = await _this.$http.get(`tech_data/Tech_Project_Data?startDate=${startDate}&endDate=${endDate}`)
      console.log('Tech_Project_Data', res)
      _this.projectData = res
    },
    onRowClick(evt, row) {
      // evt: 事件对象
      // row: 被点击行的数据
      console.log('点击的行数据:', row);
      // 在这里添加你的逻辑，比如跳转到详情页或弹窗显示详情
      // this.$router.push({ name: 'projectDetail', params: { id: row.id } });
      this.projectInputData = row
      this.showDialog = true

    },
    async exportTable() {
      const csvData = Papa.unparse(this.projectData);
      const blob = new Blob(["\uFEFF" + csvData], { type: 'text/csv;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.download = '项目数据.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },







  },
} 
</script>
<style lang="sass" scoped>
.btn-fixed-width
  width: 200px
.full-width-container
  padding:0 !important
  width:100%
  max-width:100%
.full-width-card
  width:100%
  margin:0
  padding:0
  border-radius:0
.full-height-card
  height: calc(100vh - 180px)
  display: flex
  flex-direction: column
.full-height-panel
  flex:1
  display:flex
  flex-direction: column
  width:100%
.full-height-content
  height: 100%
  display: flex
  flex-direction: column
  width:100%
.full-height-table
  flex:1
  height:100%
  width:100%
.full-height-chart
  height:100%
  width:100%
</style>
