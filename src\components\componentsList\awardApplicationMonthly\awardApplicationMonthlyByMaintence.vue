<template>
    <div>
        <q-dialog v-model="summaryPrompt">
            <q-card style="width: 900px; max-width: 100vw;">
                <q-card-section class="row items-center q-pb-none">
                    <div class="text-h6">月度积分汇总</div>
                    <q-space />
                    <q-btn icon="close" flat round dense v-close-popup />
                </q-card-section>

                <q-card-section>
                    <div class="txt-h3">申请总金额：{{ summaryAwardPoint }}元</div>
                </q-card-section>

                <q-card-section>
                    <q-table :data="newAwardAnalysisRow" />
                </q-card-section>
                <q-card-actions>
                    <q-btn v-if="summaryAwardType == '提交'" class="full-width" color="primary"
                        @click="applyToMonthly()">提交月度审批</q-btn>
                </q-card-actions>



            </q-card>
        </q-dialog>

        <q-dialog v-model="addCrewDialog" transition-show="flip-down">
            <q-card style="width: 850px; max-width: 100vw;" v-if="addCrewDialog">
                <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                    <div class="text-h6 ">{{ addCrewTypeValue }}人员添加</div>
                    <q-space />
                    <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                </q-card-section>
                <q-card-section class="q-pa-md q-gutter-sm">
                    <!-- <q-card-section>
							<q-select color="teal" filled v-model="addCrewLine['value']" :options="addCrewLine['array']"
								label="机台" style="min-width: 150px; max-width: 300px" dense />
						</q-card-section> -->
                    <q-card-section>
                        <template v-for="crew in otherCrewList">
                            <q-checkbox style="width:250px" v-model="selectedOtherCrew" :val="crew"
                                :label="crew['Position'] + '-' + crew['Employee_PYName']" color="teal" />
                        </template>
                    </q-card-section>
                </q-card-section>
                <q-card-actions class="bg-white text-teal">
                    <q-space />
                    <q-btn style="width:150px" color="primary" label="确认" @click="addCrewToList()"
                        :disable="btnEnable" />
                </q-card-actions>
            </q-card>
        </q-dialog>


        <q-dialog v-model="modifyDialog" transition-show="flip-down">
            <q-card style="width: 800px; max-width: 100vw;" v-if="modifyAward_Row">
                <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                    <div class="text-h6 ">{{ modifyAward_Row['row']['Employee_PYName'] }}积分修正</div>
                    <q-space />
                    <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                </q-card-section>
                <q-card-section class="q-pa-md q-gutter-sm">
                    <q-card-section>
                        <template v-for="award in awardList">
                            <q-checkbox
                                v-if="award['Position'] != '工艺技师' && modifyAward_Row['row']['Position'] != '工艺技师'"
                                style="width:130px" v-model="clearAwardArray" :val="award['Award_Type']"
                                :label="award['Award_Type']" color="teal" />
                        </template>
                    </q-card-section>
                </q-card-section>
                <q-card-actions class="bg-white text-teal">
                    <q-space />
                    <q-btn style="width:150px" color="primary" label="积分清空" @click="clearAwardConfirm('清空积分')"
                        :disable="btnEnable" />
                    <q-btn style="width:150px" color="primary" label="删除人员" @click="clearAwardConfirm('清空人员')"
                        :disable="btnEnable" />
                </q-card-actions>
            </q-card>
        </q-dialog>


        <q-dialog v-model="firstDialog" transition-show="flip-down">
            <q-card style="width:1300px; max-width: 100vw;" v-if="selectedApplication">
                <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                    <div class="text-h6 ">{{ selectedApplication['Award_Type'] }}积分申请</div>
                    <q-space />
                    <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                </q-card-section>
                <q-card-section>

                </q-card-section>


                <q-card-section class="" horizontal>
                    <q-card-section class=""
                        v-if="selectedApplication['Award_Type'] !== '积分调整' && selectedApplication['Award_Type'] !== '质量' && selectedApplication['Award_Type'] !== 'PM' && selectedApplication['Award_Type'] !== '三连班' && selectedApplication['Award_Type'] !== '项目奖励'">
                        <q-markup-table>
                            <thead>
                                <tr>
                                    <th class="text-left">奖项</th>
                                    <th class="text-left">职位</th>
                                    <th class="text-left">奖励标准</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template v-for="item in awardBase_byPosition">
                                    <tr>
                                        <td class="text-left">{{ item['Award'] }}</td>
                                        <td class="text-left">{{ item['Position'] }}</td>
                                        <td class="text-left">{{ item['Point'] }}</td>
                                    </tr>
                                </template>
                            </tbody>
                        </q-markup-table>
                    </q-card-section>

                    <q-separator vertical />
                    <q-card-section class="">


                        <div class="text-weight-medium text-blue-grey-4">
                            {{ selectedApplication['Comment'] }}
                        </div>
                        <div class="row">
                            <template v-for="(value, key) in JSON.parse(selectedApplication.Award_Param)  ">
                                <!-- {{ key }}{{ value }} -->
                                <div style="" class="row">
                                    <div v-if="key != 'selectedCrew' && (key.indexOf('标准') === -1 || key == '积分调整标准' || key == '质量标准' || key == 'PM标准' || key == '三连班标准' || key == '项目奖励标准')"
                                        class="text-bold"
                                        :style="value == 'imageUpload' ? 'height:150px;line-height: 150px;width:100px' : 'height:50px;line-height: 50px;width:100px'">
                                        {{ key }}
                                    </div>
                                    <q-input
                                        v-if="value == 'input' && key.indexOf('标准') !== -1 && (key == '积分调整标准' || key == '质量标准' || key == 'PM标准' || key == '三连班标准' || key == '项目奖励标准')"
                                        filled style="min-width: 200px; max-width: 300px;height:50px"
                                        v-model="singleAward_base['value']" dense />
                                    <q-input v-if="value == 'input' && key.indexOf('标准') === -1" filled
                                        style="min-width: 200px; max-width: 300px;height:50px" autogrow
                                        v-model="singleAward_base['comment']" dense
                                        :readonly="key.indexOf('个数') !== -1" />
                                    <q-input v-if="value == 'view' && key.indexOf('换型个数') === -1" filled
                                        style="min-width: 200px; max-width: 300px;height:50px" autogrow
                                        v-model="singleAward_base['count']" dense readonly />
                                    <q-input v-if="value == 'view' && key.indexOf('换型个数') !== -1" filled
                                        style="width: 50px;height:50px" autogrow
                                        v-model="singleAward_base['count'][key]" dense readonly />
                                    <q-separator inset />
                                </div>
                                <div>
                                    <!-- {{ key }}{{ value }} -->
                                    <template v-if="key == 'selectedCrew' && value == 'true'">
                                        <div>请选择奖励人员</div>
                                        <div>
                                            <template v-for="  crew in opData['data']  ">
                                                <q-checkbox style="width:130px" v-model="selectionCrew" :val="crew"
                                                    :label="crew['Employee_PYName']" color="teal" />
                                            </template>
                                        </div>
                                        <!-- {{ selectionCrew }} -->
                                    </template>
                                </div>
                            </template>
                            <q-btn style="width:150px" color="primary" label="按职位分配积分"
                                v-if="selectedApplication['Award_Type'].includes('换型') || selectedApplication['Award_Type'] == '0Delay'"
                                @click="applyAward(selectedApplication['Award_Type'], selectedApplication['Position'], '更新')"
                                :disable="btnEnable" />
                        </div>

                        <div
                            v-if="selectedApplication['Award_Type'].includes('换型') || selectedApplication['Award_Type'] == '0Delay' || selectedApplication['Award_Type'] == '首班开机'">
                            <q-markup-table>
                                <thead>
                                    <tr>
                                        <th class="text-left">LinkID</th>
                                        <th class="text-left"
                                            v-if="selectedApplication['Award_Type'].includes('换型') || selectedApplication['Award_Type'] == '0Delay'">
                                            {{ selectedApplication['Award_Type'].includes('换型') ? '奖励类型' : '0Delay' }}
                                        </th>
                                        <th class="text-left"
                                            v-if="selectedApplication['Award_Type'].includes('换型') || selectedApplication['Award_Type'] == '0Delay'">
                                            {{ selectedApplication['Award_Type'].includes('换型') ? '换型类型' : '0Delay照片' }}
                                        </th>
                                        <th class="text-left" v-if="selectedApplication['Award_Type'].includes('换型')">
                                            换型子类型
                                        </th>
                                        <th class="text-left">Employee_PYName</th>
                                        <th class="text-left">Point
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template v-for="item in Award_Count_byQCORows">
                                        <tr>
                                            <td class="text-left">{{ item['LinkID'] }}</td>
                                            <td class="text-left"
                                                v-if="selectedApplication['Award_Type'].includes('换型') || selectedApplication['Award_Type'] == '0Delay'">
                                                {{ selectedApplication['Award_Type'].includes('换型') ?
                                                    item['奖励类型'] : item['0Delay'] }}</td>
                                            <td class="text-left"
                                                v-if="selectedApplication['Award_Type'].includes('换型') || selectedApplication['Award_Type'] == '0Delay'">
                                                <a v-if="selectedApplication['Award_Type'] == '0Delay'"
                                                    :href="item['照片']" target="_blank">{{ item['照片'] }}</a>
                                                <div v-else>
                                                    {{ item['换型类型'] }}
                                                </div>
                                            </td>
                                            <td class="text-left"
                                                v-if="selectedApplication['Award_Type'].includes('换型')">{{
                                                    item['换型子类型'] }}</td>
                                            <td class="text-left">{{ item['Employee_PYName'] }}</td>
                                            <td class="text-left">{{
                                                item['point'] }}</td>
                                        </tr>
                                    </template>
                                </tbody>
                            </q-markup-table>

                        </div>

                    </q-card-section>
                </q-card-section>
                <q-card-actions class="bg-white text-teal">
                    <q-space />
                    <q-btn style="width:150px" color="primary" label="按职位分配积分"
                        v-if="!selectedApplication['Award_Type'].includes('换型') && selectedApplication['Award_Type'] !== '0Delay'"
                        @click="applyAward(selectedApplication['Award_Type'], selectedApplication['Position'], '更新')"
                        :disable="btnEnable" />
                </q-card-actions>
            </q-card>
        </q-dialog>


        <q-banner inline-actions class="text-white bg-red" v-if="awardStatus">
            积分系统申请已提交，审批状态:{{ awardStatus[0]['Approval_Status'] }},无法进行修改。如需修改请删除正在审批项目
            <template v-slot:action>
                <q-btn flat color="white" label="转到积分申请" @click="gotoAwardPage" />
            </template>
        </q-banner>

        <q-tabs v-model="subtab" dense class="bg-grey-3" align="justify" narrow-indicator>
            <q-tab name="monthlyOPApplication" label="生产月度申请" />
            <q-tab name="monthlyMaintenanceApplication" label="维修月度申请" />
        </q-tabs>
        <q-tab-panels v-model="subtab" animated>
            <q-tab-panel name="monthlyOPApplication">
                <awardComponents moduleFunction="awardApplicationMonthly" />
            </q-tab-panel>
        </q-tab-panels>
        <q-tab-panels v-model="subtab" animated>
            <q-tab-panel name="monthlyMaintenanceApplication">
                <div class="q-gutter-md row items-start" style="margin-bottom: 10px;">
                    <q-select color="teal" filled v-model="yearMonth['value']" :options="yearMonth['array']" label="年月"
                        style="min-width: 150px; max-width: 300px" dense />
                    <q-select color="teal" filled v-model="Line['value']" :options="Line['array']" label="机台"
                        style="min-width: 150px; max-width: 300px" dense />
                    <q-btn push color="primary" class="btn-fixed-width" style="min-width:150px"
                        @click="getMonthlyCrewList()" :loading="loading">查询
                        <template v-slot:loading>
                            <q-spinner-hourglass class="on-left" />
                            数据获取中...
                        </template>
                    </q-btn>
                    <q-btn push color="purple" class="btn-fixed-width" style="min-width:150px"
                        @click="analysisSummary('分析')" :loading="loading">月度积分汇总
                    </q-btn>
                    <q-btn push color="secondary" class="btn-fixed-width" style="min-width:150px"
                        @click="analysisSummary('提交')" :loading="loading" :disable="btnEnable">提交月度积分
                    </q-btn>

                </div>
                <div>
                    <q-card v-if="opData && Performance_Summary_Monthly">
                        <q-table :data="opData['data']" :columns="opData['columns']" row-key="name"
                            :pagination.sync="myPagination" class="my-sticky-virtscroll-table" virtual-scroll
                            :virtual-scroll-sticky-size-start="80" :rows-per-page-options="[0]" dense>
                            <template v-slot:top>

                                <template v-for="item in Performance_Summary_Monthly">

                                    <q-input filled v-model="item['Result']" :label="item['Performance_Type']"
                                        stack-label disable style="width:80px;margin-right:10px" dense />
                                </template>
                                <q-input filled v-model="Performance_Summary_Monthly[0]['开机率']" label="开机率%" stack-label
                                    disable style="width:200px;margin-right:10px" dense />


                                <q-space />

                                <q-btn color="purple" class="btn-fixed-width" style="min-width:150px"
                                    @click="getSummaryAward_byDaily()" :loading="loading1"
                                    :disable="btnEnable">获取点子和BP积分
                                    <template v-slot:loading1>
                                        <q-spinner-hourglass class="on-left" />
                                        数据更新中...
                                    </template>
                                </q-btn>
                                <q-btn color="secondary" label="添加维修人员" class="q-ml-sm" style="width:150px"
                                    @click="addCrewType('工程')" :disable="btnEnable" />
                            </template>

                            <template v-slot:header="props">
                                <q-tr :props="props">
                                    <q-th auto-width class="bg-primary text-white">修正</q-th>
                                    <q-th v-for="  col in props.cols  " :key="col.name" :props="props">
                                        {{ col.name }}
                                    </q-th>
                                </q-tr>
                            </template>

                            <template v-slot:body="props">
                                <q-tr :props="props">
                                    <q-td auto-width>
                                        <!-- <q-btn color="purple" size="md" glossy icon="far fa-edit" @click=""
													:disable="btnModifyEnable" style="height:28px" /> -->
                                        <q-icon name="far fa-edit" size="26px" @click="modifyAward(props)"
                                            :disable="btnEnable" />
                                    </q-td>
                                    <q-td v-for="  col in props.cols  " :key="col.name" :props="props">
                                        <!-- <template v-if="col.name==='Employee_PYName'">
													<q-btn color="deep-orange" glossy icon="close" size="xs" />
												</template> -->
                                        {{ col.value }}
                                    </q-td>
                                </q-tr>
                            </template>

                            <template v-slot:bottom>
                                <template v-for="  award in awardList  ">
                                    <q-btn color="primary" :label="award['Award_Type']" class="q-ml-sm"
                                        @click="toApply_show(award)" :disable="btnEnable" />
                                </template>
                                <q-space />
                                <!-- <q-btn color="secondary" label="查看0Delay清单" class="q-ml-sm" @click="toApply_show(award)"
                                    :disable="btnEnable" />
                                <q-btn color="secondary" label="查看换型清单" class="q-ml-sm" @click="toApply_show(award)"
                                    :disable="btnEnable" /> -->
                            </template>
                        </q-table>
                    </q-card>
                </div>
            </q-tab-panel>
        </q-tab-panels>
    </div>
</template>

<script>
import awardComponents from '../../componentsList/awardApplicationMonthly/awardApplicationMonthly.vue'
export default {
    name: 'awardApplication',
    components: {
        awardComponents
    },
    mounted() {
        const _this = this
        _this.$nextTick(() => {
            _this.Line['array'] = localStorage.getItem('lineArray').split(',')
            // _this.addCrewLine['array'] = localStorage.getItem('lineArray').split(',')
            _this.AccessToBtn(localStorage.getItem('Position'))
            const currentDate = new Date();
            currentDate.setMonth(currentDate.getMonth() - 1); // 获取上一个月的日期
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth() + 1;
            _this.yearMonth['value'] = year.toString() + (month < 10 ? '0' + month : month.toString());
            _this.yearMonth['array'].push(year.toString() + (month < 10 ? '0' + month : month.toString()))
        })

    },
    watch: {
        subtab() {
            this.awardStatus = false
        }
    },
    data() {
        return {
            text: 'Yes',
            btnEnable: false,
            loading: false,
            loading1: false,
            tab: 'monthlyApplication',
            subtab: 'monthlyOPApplication',
            yearMonth: {
                array: [],
                value: ''
            },
            Line: {
                array: ['ND05', 'ND06'],
                value: ''
            },
            myPagination: { rowsPerPage: 0 },
            jsData: false,
            opData: false,
            awardList: false,
            awardStatus: false,
            clearAwardArray: [],
            addCrewTypeValue: '',
            otherCrewList: false,
            selectedOtherCrew: [],
            addCrewDialog: false,
            modifyDialog: false,
            firstDialog: false,
            modifyAward_Row: false,
            clearAwardArray: [],
            selectedApplication: false,
            selectionCrew: [],
            singleAward_base: '',
            awardBase_byPosition: [],
            Award_Count_byQCORows: [],
            Performance_Summary_Monthly: false,
            // Performance_summary_Product:false,
            summaryAwardPoint: 0,
            summaryAwardAnalysisRow: [],
            summaryPrompt: false,
            summaryAwardType: '',
            awardStatus: false,
            newAwardAnalysisRow: false
        }
    },

    methods: {
        async AccessToBtn(awardType) {
            this.btnEnable = await checkAccess(awardType, 'approve')
        },

        async Award_Application_Summary_Monthly_verify() {
            const _this = this
            _this.loading = true
            const {
                data: res
            } = await _this.$http.get(`approve/Award_Application_Summary_Monthly_verify?yearMonth=${_this.yearMonth['value']}&Line=${_this.Line['value']}&department=工程维修`)
            console.log(res)
            if (res !== '未申请') {
                _this.btnEnable = true
                _this.awardStatus = res
            } else {
                _this.awardStatus = false
                _this.AccessToBtn(localStorage.getItem('Position'))
            }

        },
        async getMonthlyCrewList() {
            var _this = this
            _this.loading = true
            const {
                data: res
            } = await _this.$http.get(`approve/getMonthlyCrewList?yearMonth=${_this.yearMonth['value']}&Line=${_this.Line['value']}&department=工程维修`)
            console.log('getMonthlyCrewList', res)
            _this.jsData = res.jsData
            _this.opData = res.opData
            _this.awardList = res.awardList
            await _this.getMonthlyApplicationAward()
            await _this.Award_Application_Summary_Monthly_verify()
            await _this.Award_Performance_Summary_Monthly()

            // 从本地存储中恢复新增申请方式的数据
            _this.restoreLocalAwardData('PM')
            _this.restoreLocalAwardData('三连班')
            _this.restoreLocalAwardData('项目奖励')

            _this.loading = false
        },

        async Award_Performance_Summary_Monthly() {
            const _this = this
            _this.loading = true
            _this.Performance_Summary_Monthly = false
            const {
                data: res
            } = await _this.$http.get(`approve/Award_Performance_Summary_Monthly?yearMonth=${_this.yearMonth['value']}&Line=${_this.Line['value']}`)
            console.log('Award_Performance_Summary_Monthly', res)
            if (res.length == 0) {
                _this.$q.notify({
                    type: 'negative',
                    message: `月度审批未完成，无法进行积分申请`,
                    position: 'top'
                })
                return
            }
            _this.Performance_Summary_Monthly = res
        },

        async getMonthlyApplicationAward() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`approve/getMonthlyApplicationAward?yearMonth=${_this.yearMonth['value']}&Line=${_this.Line['value']}&department=工程维修`)
            console.log('getMonthlyApplicationAward', res)
            const awardApplicationData = res
            const jsData = _this.jsData['data']
            const opData = _this.opData['data']
            if (awardApplicationData.length > 0) {
                for (var i = 0; i < awardApplicationData.length; i++) {
                    const employee_id = awardApplicationData[i]['Employee_ID']
                    const award_type = awardApplicationData[i]['Award_Type']
                    const award_Point = awardApplicationData[i]['Point']
                    for (var j = 0; j < opData.length; j++) {
                        if (opData[j]['Employee_ID'] == employee_id) {
                            opData[j][award_type] = award_Point
                            break
                        }
                    }
                    for (var j = 0; j < jsData.length; j++) {
                        if (jsData[j]['Employee_ID'] == employee_id) {
                            jsData[j][award_type] = award_Point
                            break
                        }
                    }
                }
            }
        },
        async addCrewType(addCrewType) {
            console.log(addCrewType)
            const _this = this
            _this.addCrewTypeValue = addCrewType
            _this.otherCrewList = false
            _this.selectedOtherCrew = []
            const {
                data: res
            } = await _this.$http.get(`approve/other_crew_list?Business_Title=技师&Line=工程`)
            _this.otherCrewList = false
            _this.selectedOtherCrew = []
            console.log('other_crew_list', _this.selectedOtherCrew)
            _this.otherCrewList = res
            _this.addCrewDialog = true
        },
        async addCrewToList() {
            const _this = this
            console.log(_this.selectedOtherCrew)
            const crewData = _this.opData['data']
            const selectedOtherCrew = _this.selectedOtherCrew
            console.log('other_crew_list', selectedOtherCrew)
            console.log('crewData', crewData)
            for (let i = 0; i < selectedOtherCrew.length; i++) {
                const newCrewList = { ...crewData[0] }
                for (let key in newCrewList) {
                    if (newCrewList.hasOwnProperty(key)) {
                        newCrewList[key] = ''
                    }
                }
                console.log(' selectedOtherCrew[i]', selectedOtherCrew[i]['Employee_ID'])
                newCrewList['Employee_ID'] = selectedOtherCrew[i]['Employee_ID']
                newCrewList['Employee_PYName'] = selectedOtherCrew[i]['Employee_PYName']
                newCrewList['Position'] = selectedOtherCrew[i]['Position']
                console.log('newCrewList', newCrewList)
                crewData.push(newCrewList)
                console.log('crewData', crewData)
            }
            // console.log('crewData',crewData)
            _this.$q.notify({
                type: 'positive',
                message: `添加人员成功`,
                position: 'top'
            })
            _this.addCrewDialog = false

        },
        modifyAward(row) {
            console.log(row)
            const _this = this
            _this.modifyAward_Row = row
            _this.clearAwardArray = []

            _this.modifyDialog = true
        },
        async clearAwardConfirm(operate) {
            //operate: 清空积分/清空人员
            const _this = this
            const modifyAward_Row = _this.modifyAward_Row
            const clearAwardArray = _this.clearAwardArray
            const rowIndex = modifyAward_Row['rowIndex']
            console.log('rowIndex', rowIndex)
            const postParam = {
                'yearMonth': _this.yearMonth['value'],
                'line': _this.Line['value'],
                'award_type': clearAwardArray,
                'selectedCrew': modifyAward_Row['row']['Employee_ID'],
                'operate': operate
            }
            _this.$http.post('approve/delete_Application_Monthly', postParam).then(function (response) {
                console.log(response)
                if (response.data === '更新成功') {
                    let awardData = _this.opData['data']
                    if (operate == '清空人员') {
                        awardData.splice(rowIndex, 1)
                    } else {
                        for (var s = 0; s < clearAwardArray.length; s++) {
                            awardData[rowIndex][clearAwardArray[s]] = ''
                        }
                    }
                    _this.$q.notify({
                        type: 'positive',
                        message: `清空操作成功`,
                        position: 'top'
                    })
                    _this.modifyDialog = false
                }

            })

        },
        async toApply_show(award) {
            const _this = this
            console.log('award', award)
            _this.selectedApplication = award
            _this.selectionCrew = []
            _this.singleAward_base = {
                array: [],
                value: '',
                comment: '',
                count: 0
            }
            const {
                data: res
            } = await _this.$http.get(`approve/getAward_Base?award_Type=${award['Award_Type']}&Line=${_this.Line['value']}&yearMonth=${_this.yearMonth['value']}&department=工程维修`)
            console.log('getAward_Base', res)
            _this.awardBase_byPosition = res
            // _this.singleAward_base['array'] = award['awardbaseArray']
            // _this.singleAward_base['value'] = award['awardbaseArray'][0]

            // 对于新增的申请方式，初始化默认值
            if (award['Award_Type'] == 'PM' || award['Award_Type'] == '三连班' || award['Award_Type'] == '项目奖励') {
                if (_this.awardBase_byPosition && _this.awardBase_byPosition.length > 0) {
                    _this.singleAward_base['value'] = _this.awardBase_byPosition[0]['Point']
                }
            }
            if (award['Award_Param'].indexOf('个数') !== -1) {
                const {
                    data: res
                } = await _this.$http.get(`approve/SP_API_Award_Count?yearMonth=${_this.yearMonth['value']}&Line=${_this.Line['value']}&awardType=${award['Award_Type']}&department=工程维修`)
                console.log('SP_API_Award_Count', res)
                if (award['Award_Type'].includes('换型')) {
                    _this.singleAward_base['count'] = res[0]
                } else {
                    _this.singleAward_base['count'] = res[0]['个数']
                }

            }
            if (award['Award_Type'].includes('换型') || award['Award_Type'] == '0Delay' || award['Award_Type'] == '首班开机') {
                const {
                    data: res1
                } = await _this.$http.get(`approve/SP_API_Award_Count_byDetail?yearMonth=${_this.yearMonth['value']}&Line=${_this.Line['value']}&department=工程维修&awardType=${award['Award_Type']}`)
                console.log('SP_API_Award_Count_byQCO', res1)
                _this.Award_Count_byQCORows = res1
            }
            _this.firstDialog = true
        },


        async applyAward(award_type, Position, operate) {
            const _this = this
            console.log('award_type', award_type)
            console.log('Position', Position)
            console.log('operate', operate)
            console.log('_this.singleAward_base', _this.singleAward_base)
            if (JSON.parse(_this.selectedApplication['Award_Param'])['selectedCrew'] == 'true' && _this.selectionCrew.length == 0) {
                _this.$q.notify({
                    type: 'negative',
                    message: `请选择奖励人员`,
                    position: 'top'
                })
                return
            }
            if (award_type == '生产OEE') {
                for (let per = 0; per < _this.Performance_Summary_Monthly.length; per++) {
                    if (_this.Performance_Summary_Monthly[per]['Performance_Type'] == '生产' && _this.Performance_Summary_Monthly[per]['Result'] == 'No') {
                        _this.$q.notify({
                            type: 'negative',
                            message: `生产未达标无法申请OEE奖励`,
                            position: 'top'
                        })
                        return
                    }
                }
            }
            let award_base = { ..._this.singleAward_base }
            console.log('award_base', award_base)
            if (_this.selectedApplication['Award_Param'].indexOf('个数') !== -1) {
                if (award_base['count'] == 0) {
                    _this.$q.notify({
                        type: 'negative',
                        message: `${award_type}个数为空，无法申请积分`,
                        position: 'top'
                    })
                    return
                }
            }
            let selectedData = _this.selectionCrew

            const selectedData_postData = [..._this.selectionCrew]
            const selectedExistsNum = selectedData.length
            if (selectedExistsNum == 0) {
                console.log('awardBase_byPosition', _this.awardBase_byPosition)
                for (var i = 0; i < _this.opData['data'].length; i++) {
                    let opInfo = { 'Employee_ID': _this.opData['data'][i]['Employee_ID'], 'Employee_PYName': _this.opData['data'][i]['Employee_PYName'], 'Position': _this.opData['data'][i]['Position'] }
                    for (let j = 0; j < _this.awardBase_byPosition.length; j++) {
                        if (_this.opData['data'][i]['Position'] == _this.awardBase_byPosition[j]['Position']) {
                            console.log('award_base.count', award_base['count'])
                            if (award_type == '0Delay') {
                                // if(_this.opData['data'][i]['Position'] !== '电气技师'){
                                if (_this.opData['data'][i]['Position'] !== '技师') {
                                    opInfo['award_base'] = _this.awardBase_byPosition[j]['Point'] * award_base['count']
                                    break;
                                }

                            } else if (award_type == '换型') {
                                if (_this.opData['data'][i]['Position'] !== '电气技师') {
                                    if (_this.awardBase_byPosition[j]['Award'] == '2类换型') {
                                        console.log('award_base.count.2换型', award_base['count']['2类换型个数'])
                                        if (!opInfo.hasOwnProperty('award_base')) {
                                            opInfo['award_base'] = 0;
                                        }
                                        opInfo['award_base'] += _this.awardBase_byPosition[j]['Point'] * award_base['count']['2类换型个数']
                                    }
                                    if (_this.awardBase_byPosition[j]['Award'] == '3类换型') {
                                        console.log('award_base.count.3换型', award_base['count']['3类换型个数'])
                                        if (!opInfo.hasOwnProperty('award_base')) {
                                            opInfo['award_base'] = 0;
                                        }
                                        opInfo['award_base'] += _this.awardBase_byPosition[j]['Point'] * award_base['count']['3类换型个数']
                                    }
                                }

                            } else if (award_type == '换型0delay') {
                                if (_this.opData['data'][i]['Position'] !== '电气技师') {
                                    console.log('找到了')
                                    if (_this.awardBase_byPosition[j]['Award'] == '1类换型换型0delay') {

                                        // console.log('award_base.count.1换型', award_base['count']['1类换型个数'])
                                        // console.log(_this.awardBase_byPosition[j]['Point'])
                                        if (!opInfo.hasOwnProperty('award_base')) {
                                            opInfo['award_base'] = 0;
                                        }
                                        opInfo['award_base'] += _this.awardBase_byPosition[j]['Point'] * award_base['count']['1类换型个数']
                                    }
                                    if (_this.awardBase_byPosition[j]['Award'] == '2类换型换型0delay') {
                                        // console.log('award_base.count.2换型', award_base['count']['2类换型个数'])
                                        // console.log(_this.awardBase_byPosition[j]['Point'])
                                        if (!opInfo.hasOwnProperty('award_base')) {
                                            opInfo['award_base'] = 0;
                                        }
                                        opInfo['award_base'] += _this.awardBase_byPosition[j]['Point'] * award_base['count']['2类换型个数']
                                    }
                                    if (_this.awardBase_byPosition[j]['Award'] == '3类换型换型0delay') {
                                        // console.log('award_base.count.3换型', award_base['count']['3类换型个数'])
                                        // console.log(_this.awardBase_byPosition[j]['Point'])
                                        // console.log('3类换型金额1', opInfo)
                                        if (!opInfo.hasOwnProperty('award_base')) {
                                            opInfo['award_base'] = 0;
                                        }
                                        opInfo['award_base'] += _this.awardBase_byPosition[j]['Point'] * award_base['count']['3类换型个数']
                                        // console.log('3类换型金额2', opInfo)
                                    }
                                }
                            } else if (award_type == '首班开机') {
                                if (_this.opData['data'][i]['Position'] !== '电气技师') {
                                    opInfo['award_base'] = _this.awardBase_byPosition[j]['Point'] * award_base['count']
                                    break;
                                }
                            }
                            else if (award_type == '积分调整' || award_type == '质量' || award_type == 'PM' || award_type == '三连班' || award_type == '项目奖励') {
                                // 对于这些申请方式，使用用户输入的值作为积分值
                                opInfo['award_base'] = _this.singleAward_base['value'] ? Number(_this.singleAward_base['value']) : Number(_this.awardBase_byPosition[j]['Point'])
                                break;
                            }else{
                               
                                opInfo['award_base'] = _this.awardBase_byPosition[j]['Point']
                                break
                            }


                        }
                    }
                    // if (_this.Award_Count_byQCORows.length > 0 && (award_type == '换型'||award_type == '0Delay') && _this.opData['data'][i]['Position'] == '电气技师') {
                    if (_this.Award_Count_byQCORows.length > 0 && (award_type.includes('换型') || award_type == '首班开机') && _this.opData['data'][i]['Position'] == '电气技师') {
                        for (let s = 0; s < _this.Award_Count_byQCORows.length; s++) {
                            console.log('_this.Award_Count_byQCORows', _this.Award_Count_byQCORows[s])
                            if (opInfo['Employee_ID'] == _this.Award_Count_byQCORows[s]['Employee_ID']) {
                                console.log('找到了：', _this.Award_Count_byQCORows[s]['Employee_ID'])
                                console.log('_this.Award_Count_byQCORows[s].point', _this.Award_Count_byQCORows[s]['point'])
                                if (_this.Award_Count_byQCORows[s]['point'] > 0) {
                                    if (!opInfo.hasOwnProperty('award_base')) {
                                        opInfo['award_base'] = 0
                                    }
                                    console.log('第一次award_base:', opInfo['award_base'])
                                    opInfo['award_base'] += _this.Award_Count_byQCORows[s]['point']
                                }
                            }
                        }

                        console.log('最终award_base:', opInfo)
                    }
                    selectedData_postData.push(opInfo)
                }
            }

            console.log('selectedData_postData', selectedData_postData)


            const postParam = {
                'yearMonth': _this.yearMonth['value'],
                'line': _this.Line['value'],
                'award_type': award_type,
                'selectedCrew': selectedData_postData,
                'award_base': _this.singleAward_base['value'] || 0,
                'award_comment': _this.singleAward_base['comment'],
                'modifyName': localStorage.getItem('account'),
                'operate': operate,
                'department': '工程维修'
            }
            console.log('postParam', postParam)
            // 添加调试信息，记录请求参数和响应
            console.log('award_type', award_type)
            console.log('award_base', _this.singleAward_base['value'])

            _this.$http.post('approve/SP_API_Award_Update_Application_Monthly', postParam).then(function (response) {
                console.log('response', response)
                // 添加调试信息，记录响应数据
                console.log('response.data', response.data)

                if (response.data === '更新成功') {
                    let awardData = _this.opData['data']
                    for (var s = 0; s < selectedData_postData.length; s++) {
                        const employeeID = selectedData_postData[s]['Employee_ID']
                        const award_base = selectedData_postData[s]['award_base']
                        for (var d = 0; d < awardData.length; d++) {
                            if (awardData[d]['Employee_ID'] == employeeID) {
                                // console.log()
                                awardData[d][award_type] = award_type == '积分调整' || award_type == '质量' || award_type == 'PM' || award_type == '三连班' || award_type == '项目奖励' ? _this.singleAward_base['value'] : award_base
                            }
                        }
                    }
                    _this.$q.notify({
                        type: 'positive',
                        message: `${award_type}申请成功！`,
                        position: 'top'
                    })
                    _this.firstDialog = false

                    // 在成功保存后立即将数据保存到本地存储中
                    localStorage.setItem(`award_${award_type}_${_this.yearMonth['value']}_${_this.Line['value']}`, JSON.stringify(selectedData_postData))
                }

            })
        },
        async getSummaryAward_byDaily() {
            const _this = this
            const postParam = {
                'yearMonth': _this.yearMonth['value'],
                'line': _this.Line['value'],
                'selectedCrew': _this.opData['data'],
                'modifyName': localStorage.getItem('account'),
                'department': '工程维修'
            }
            _this.$http.post('approve/SP_API_Award_SummaryAward_ByCrew', postParam).then(function (response) {
                console.log(response)
                const responseData = response.data
                if (responseData.length > 0) {
                    for (var i = 0; i < _this.opData['data'].length; i++) {
                        const employee_id = _this.opData['data'][i]['Employee_ID']
                        for (var j = 0; j < responseData.length; j++) {
                            if (employee_id == responseData[j]['Employee_ID']) {
                                for (const [key, value] of Object.entries(responseData[j])) {
                                    _this.opData['data'][i][key] = value;
                                }
                            }
                        }

                    }

                }
                _this.$q.notify({
                    type: 'positive',
                    message: `每日积分已更新`,
                    position: 'top'
                })

            })

        },
        analysisSummary(e) {
            const _this = this
            // console.log('totalOPData', this.opData['data'])
            // console.log('totalJSData', this.jsData['data'])
            // console.log('AwardData', this.awardList)
            const awardListArray = [..._this.awardList]
            let awardListDistinct = []
            console.log('awardListArray', awardListArray)
            for (let a = 0; a < awardListArray.length; a++) {
                if (awardListDistinct.indexOf(awardListArray[a]['Award_Type']) === -1) {
                    awardListDistinct.push(awardListArray[a]['Award_Type'])
                }
            }
            awardListDistinct.push('点子')
            awardListDistinct.push('BP')
            const opData = _this.opData['data']
            const Award_Summary_Row = []
            for (let i = 0; i < awardListDistinct.length; i++) {
                let award = awardListDistinct[i]
                let Award_Summary_Single_Row = {}
                Award_Summary_Single_Row = [award, 0, 0]
                console.log('opData', opData)
                console.log('opDataLength', opData.length)
                for (var op = 0; op < opData.length; op++) {
                    // console.log('subAward',award)
                    // console.log('opData[op][award]',opData[op][award])
                    console.log('opdataIndex', op)
                    if (opData[op][award] != "" && opData[op][award] !== null && opData[op].hasOwnProperty(award) && opData[op][award] !== undefined) {
                        Award_Summary_Single_Row[1] += parseInt(opData[op][award])
                        _this.summaryAwardPoint += parseInt(opData[op][award])
                        console.log('Award_Summary_Single_Row', Award_Summary_Single_Row)
                    }
                }
                Award_Summary_Row.push(Award_Summary_Single_Row)
            }
            console.log('Award_Summary_Row', Award_Summary_Row)
            _this.summaryAwardAnalysisRow = Award_Summary_Row
            const newAwardAnalysisRow = [{}]
            for (var x = 0; x < Award_Summary_Row.length; x++) {
                const Award_Summary_Row_single = Award_Summary_Row[x]
                newAwardAnalysisRow[0][Award_Summary_Row_single[0]] = Award_Summary_Row_single[1]
            }
            console.log('newAwardAnalysisRow', newAwardAnalysisRow)
            _this.newAwardAnalysisRow = newAwardAnalysisRow
            _this.summaryAwardType = e
            _this.summaryPrompt = true
        },
        applyToMonthly() {
            const _this = this
            console.log('confirmData', _this.summaryAwardAnalysisRow)
            const applyData = [..._this.summaryAwardAnalysisRow]
            const paramData = {
                "data": [],
                'modifyName': localStorage.getItem('account'),
                'line': _this.Line['value'],
                'yearMonth': _this.yearMonth['value'],
                'department': '工程维修'
            }
            for (let i = 0; i < applyData.length; i++) {
                const sum = applyData[i].slice(1).reduce((acc, curr) => acc + curr, 0);
                const result = [applyData[i][0], sum];
                paramData['data'].push(result)
            }
            console.log('paramData', paramData)
            _this.$http.post('approve/Award_Application_Summary_Monthly', paramData).then(function (response) {
                console.log(response)
                if (response.data === '更新成功') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `提交申请成功`,
                        position: 'top'
                    })
                    _this.btnEnable = true
                    _this.summaryPrompt = false
                }

            })

        },
        gotoAwardPage() {
            this.$router.push('/awardPage/award')
        },

        // 从本地存储中恢复新增申请方式的数据
        restoreLocalAwardData(award_type) {
            const _this = this
            const storageKey = `award_${award_type}_${_this.yearMonth['value']}_${_this.Line['value']}`
            const storedData = localStorage.getItem(storageKey)

            if (storedData) {
                try {
                    const parsedData = JSON.parse(storedData)
                    if (parsedData && parsedData.length > 0) {
                        const opData = _this.opData['data']

                        for (let i = 0; i < parsedData.length; i++) {
                            const employeeID = parsedData[i]['Employee_ID']
                            const award_base = parsedData[i]['award_base']

                            for (let j = 0; j < opData.length; j++) {
                                if (opData[j]['Employee_ID'] == employeeID) {
                                    // 如果数据中没有该申请方式的数据，则从本地存储中恢复
                                    if (!opData[j][award_type] || opData[j][award_type] === '') {
                                        opData[j][award_type] = _this.singleAward_base['value'] || award_base
                                    }
                                    break
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error(`解析${award_type}本地存储数据失败:`, error)
                }
            }
        }
    }

}
function checkAccess(award_type, btnType) {
    return new Promise((resolve, reject) => {
        const userRole = localStorage.getItem('user_role')
        const userPosition = localStorage.getItem('Position')
        let result = true
        console.log('userRole', userRole)
        console.log('userPosition', userPosition)
        if (btnType === 'approve') {
            if ((userRole.indexOf('积分系统_审批') !== -1 && userPosition.indexOf('机械') !== -1) || (userRole.indexOf('积分系统_审批') !== -1 && userPosition.indexOf('电气') !== -1) || userRole.indexOf('Admin') !== -1) {
                console.log('有权限')
                result = false
            }
            else {
                console.log('没权限')
            }
        }
        console.log(result)
        resolve(result)
    })
}
</script>