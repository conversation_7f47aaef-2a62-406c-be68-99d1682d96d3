<template>
    <base-content>

        <q-dialog v-model="notificationDialog" persistent transition-show="flip-down" transition-hide="flip-up">
            <q-card v-if="selectedLSWData.length > 0 && selectedIndex >= 0">
                <q-bar class="bg-teal text-white text-center">
                    <q-space />
                    <!-- <q-btn padding="xs" color="red" icon="clear" v-close-popup /> -->
                </q-bar>
                <q-card-section style="margin-left: 10px;">
                    <div class="fit row justify-start ">
                        <div class="fit row justify-start  text-h5 text-bold">
                            另行通知人员
                        </div>
                    </div>
                </q-card-section>

                <q-card-section>
                    <!-- {{ selectedLSWData[0] }} -->

                    <div class="q-gutter-sm">
                        <!-- Notification{{ selectedLSWData[0].hasOwnProperty('NotificationMail') }} -->

                        <template v-for="item in managerList">
                            <q-checkbox style="width:250px" v-model="selectedLSWData_NotificationMail"
                                :val="item['Employee_ID']" :label="item['DisplayName']" color="teal" />
                        </template>

                    </div>
                </q-card-section>

                <q-card-actions align="right">
                    <q-btn flat color="primary" label="确定提交" @click="submitLSWProblem_Data" />
                    <!-- <q-btn flat color="primary" label="取消" /> -->
                </q-card-actions>


            </q-card>

        </q-dialog>





        <q-dialog v-model="initShowDialog" persistent transition-show="flip-down" transition-hide="flip-up">
            <q-card>
                <q-bar class="bg-teal text-white text-center">
                    <q-space />
                    <!-- <q-btn padding="xs" color="red" icon="clear" v-close-popup /> -->
                </q-bar>

                <q-card-section style="margin-left: 10px;">
                    <div class="fit row justify-start ">
                        <div class="fit row justify-start  text-h5 text-bold">
                            <div style="color:dodgerblue">
                                Kimberly-Clark
                            </div>
                            <div>清洁标准及检查记录</div>
                        </div>
                    </div>
                </q-card-section>

                <q-card-section>
                    <div class="fit row justify-start ">
                        <div class="fit row justify-start  text-h5 text-bold">
                            <div class="row wrap justify-start">
                                <div style="font-size: 15px;line-height: 45px;font-weight: 600;">日期：</div>
                                <q-input style="width: 200px;" v-model="formData['Date']" filled type="date" label="日期"
                                    stack-label dense>
                                </q-input>
                            </div>
                            <div class="row wrap justify-start">
                                <div style="font-size: 15px;line-height: 45px;font-weight: 600;">班次：</div>
                                <q-select outlined v-model="formData['Shift']" :options="shiftArray" label-slot
                                    clearable style="width: 200px;" dense>
                                    <template v-slot:label>班次
                                        <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em>
                                    </template>
                                </q-select>
                            </div>
                        </div>
                    </div>
                    <div class="fit row justify-start ">
                        <div class="fit row justify-start  text-h5 text-bold">
                            <div class="row wrap justify-start">
                                <div style="font-size: 15px;line-height: 45px;font-weight: 600;">机台：</div>
                                <q-select outlined v-model="formData['Line']" :options="lineArray" label-slot clearable
                                    style="width: 200px;" dense>
                                    <template v-slot:label>机台
                                        <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em>
                                    </template>
                                </q-select>
                            </div>
                            <div class="row wrap justify-start">
                                <div style="font-size: 15px;line-height: 45px;font-weight: 600;">状态：</div>
                                <q-select outlined v-model="formData['MachineStatus']" :options="machineStatusArray"
                                    label-slot clearable style="width: 200px;" dense>
                                    <template v-slot:label>状态
                                        <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em>
                                    </template>
                                </q-select>
                            </div>
                            <!-- <div class="row wrap justify-start">
                                <div style="font-size: 15px;line-height: 45px;font-weight: 600;">班组：</div>
                                <q-select outlined v-model="formData['Crew']" :options="crewArray" label-slot clearable
                                    style="width: 200px;" dense>
                                    <template v-slot:label>班组
                                        <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em>
                                    </template>
                                </q-select>
                            </div> -->
                        </div>
                    </div>
                </q-card-section>

                <q-card-actions align="right">
                    <q-btn flat color="primary" label="开始检查" @click="initData" />
                    <!-- <q-btn flat color="primary" label="取消" /> -->
                </q-card-actions>
            </q-card>
        </q-dialog>




        <q-dialog v-model="showDetailDialog" persistent maximized transition-show="slide-up"
            transition-hide="slide-down">
            <q-card style="width:100%; max-width: 100vw;" v-if="selectedRow">
                <q-bar class="bg-teal text-white text-center">
                    <q-space />
                    <q-btn padding="xs" color="red" icon="clear" @click="closeCheckDialog" />
                </q-bar>
                <q-card-section style="margin-left: 10px;">
                    <div class="fit row justify-start ">
                        <div class="fit row justify-start  text-h5 text-bold">
                            <div style="color:dodgerblue">
                                Kimberly-Clark
                            </div>
                            <div>清洁标准及检查记录</div>
                        </div>
                    </div>
                    <div>
                        <div class="text-h6 text-bold">基本信息</div>

                        <q-markup-table separator="cell" flat bordered>
                            <thead>
                                <tr>
                                    <th class="text-center" style="width: 100px;">标准标号</th>
                                    <th class="text-center">专业</th>
                                    <th class="text-center">类型</th>
                                    <th class="text-center">设备状态</th>
                                    <th class="text-center">等级</th>
                                    <th class="text-center">单元</th>
                                    <th class="text-center">部件</th>
                                    <th class="text-center">用时(min)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-center" style="width: 100px;">{{ selectedRow['Standard_ID'] }}</td>
                                    <td class="text-center">{{ selectedStandard['Major'] }}</td>
                                    <td class="text-center">{{ selectedStandard['Type'] }}</td>
                                    <td class="text-center">{{ selectedStandard['Machine_Status'] }}</td>
                                    <td class="text-center">{{ selectedStandard['Level'] }}</td>
                                    <td class="text-center">{{ selectedStandard['Unit'] }}</td>
                                    <td class="text-center">{{ selectedStandard['Component'] }}</td>
                                    <td class="text-center">{{ selectedStandard['Spend_Time'] }}</td>
                                </tr>
                            </tbody>
                        </q-markup-table>

                    </div>
                    <div>
                        <div class="text-h6 text-bold">清洁要求</div>
                        <q-markup-table separator="cell" flat bordered>
                            <thead>
                                <tr>
                                    <th class="text-center">关键位置</th>
                                    <th class="text-center">清洁步骤</th>
                                    <th class="text-center">清洁工具</th>
                                    <th class="text-center">清洁标准图片</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-left text-wrap" style="width: 200px">
                                        <p v-html="selectedStandard['Standard_Description'].replace(/\n/g, '<br>')">
                                        </p>
                                    </td>
                                    <td class="text-left" style="width: 50px">
                                        <p v-html="selectedStandard['Operate_Process'].replace(/\n/g, '<br>')"></p>
                                    </td>
                                    <td class="text-left" style="width: 100px">
                                        <p v-html="selectedStandard['Tool'].replace(/\n/g, '<br>')"></p>
                                    </td>
                                    <td class="text-left">
                                        <q-img :src="selectedStandard['Standard_Image_URL']" :ratio="4 / 3"
                                            @click="gotoImage(selectedStandard['Standard_Image_URL'])" />
                                    </td>
                                </tr>
                            </tbody>
                        </q-markup-table>
                    </div>

                    <div>
                        <div class="text-h6 text-bold">自检记录</div>

                        <div class=" row justify-start text-bold q-gutter-sm">
                            <div class="q-pa-sm" style="width: 400px;">
                                <div class="row justify-start text-bold black_border  q-gutter-sm"
                                    style="height: 60px;">
                                    <div style="line-height: 40px;">自检结果：</div>
                                    <template v-if="selectedCheckData['Check_Result'] != ''">
                                        <div class="q-pa-sm" style="width: 200px;">
                                            <q-chip v-if="selectedCheckData['Check_Result'] == '合格'" color="positive"
                                                label="合格" />
                                            <q-chip v-else-if="selectedCheckData['Check_Result'] == '不合格'"
                                                color="negative" label="不合格" />
                                        </div>
                                    </template>
                                    <template v-else>
                                        <q-btn style="width: 80px;height: 40px;" color="primary" label="合格"
                                            @click="addProblem_self('合格')" />
                                        <q-btn style="width: 80px;height: 40px;" color="deep-orange" label="不合格"
                                            @click="addProblem_self('不合格')" />
                                    </template>
                                </div>

                                <div style="height: 60px;margin-top: 5px;"
                                    class="row justify-start text-bold black_border  q-gutter-sm">
                                    <div style="line-height: 40px;">问题类型：</div>
                                    <q-select filled v-model="selectedCheckData['Check_Category']"
                                        :options="problemCategory" clearable style="width: 260px; height: 40px;" dense
                                        :disable="selectedCheckData['Check_Result'] != ''">
                                    </q-select>
                                </div>

                                <div style=" height: 150px;margin-top: 5px;"
                                    class="row justify-start text-bold black_border  q-gutter-sm">
                                    <div style="line-height: 40px;">问题描述：</div>
                                    <q-input v-model="selectedCheckData['Check_Problem_Desc']"
                                        style="width: 260px; height: 140px;" filled type="textarea"
                                        :disable="selectedCheckData['Check_Result'] != ''" />
                                </div>

                                <div class="row justify-start text-bold black_border  q-gutter-sm"
                                    style="height: 60px; margin-top: 5px;">
                                    <div style=" line-height: 40px;">主管评定：</div>
                                    <template v-if="selectedCheckData_Asset['Check_Result'] != ''">
                                        <div class="q-pa-sm" style="width: 200px;">
                                            <q-chip v-if="selectedCheckData_Asset['Check_Result'] == '合格'"
                                                color="positive" label="合格" />
                                            <q-chip v-else-if="selectedCheckData_Asset['Check_Result'] == '不合格'"
                                                color="negative" label="不合格" />
                                        </div>
                                    </template>
                                    <template v-else>
                                        <q-btn v-if="login_position == '资产主管'" style="width: 80px;height: 40px;"
                                            color="primary" label="合格" @click="check_Asset('合格')" />
                                        <q-btn v-if="login_position == '资产主管'" style="width: 80px;height: 40px;"
                                            color="deep-orange" label="不合格" @click="check_Asset('不合格')" />
                                    </template>
                                </div>
                            </div>

                            <div>
                                <div class="black_border" style="height: 280px; width: 310px;">
                                    <div style="line-height: 40px;" class="text-center black_border">自检图片上传</div>
                                    <template v-if="selectedCheckData['Check_Image_URL'] != ''">
                                        <q-img :src="selectedCheckData['Check_Image_URL']" :ratio="4 / 3"
                                            style="height: 230px;"
                                            @click="gotoImage(selectedCheckData['Check_Image_URL'])" />
                                    </template>

                                    <template v-else>
                                        <q-uploader style="width: 100%;height: 230px;" batch auto-upload dense
                                            :factory="factoryFn" @uploaded="handleUploadSuccess_Problem_self" />
                                    </template>
                                </div>
                            </div>


                        </div>
                    </div>


                    <div>
                        <div class="text-h6 text-bold">LSW检查记录
                            <q-btn style="width:150px;height: 40px;" color="primary" label="新增问题" @click="addProblem" />
                            <!-- <q-btn style="width: 80px;height: 40px;" color="deep-orange" label="合格" /> -->
                        </div>

                        <div v-if="selectedLSWData.length > 0">
                            <template v-for="(item, index) in selectedLSWData">
                                <div class=" row justify-start text-bold q-gutter-sm">
                                    <div class="q-pa-sm" style="width: 400px;">
                                        <div class="row justify-start text-bold black_border  q-gutter-sm"
                                            style="height: 60px;">
                                            <div style="line-height: 40px;">问题类型：</div>
                                            <q-select filled v-model="item['problem_category']"
                                                :options="lswProblemCategory" clearable
                                                style="width: 260px; height: 40px;" dense :disable="!item['selected']">
                                            </q-select>
                                        </div>

                                        <div style="height: 150px;margin-top: 5px;"
                                            class="row justify-start text-bold black_border  q-gutter-sm">
                                            <div style="line-height: 40px;">问题描述：</div>
                                            <q-input v-model="item['problemContent']"
                                                style="width: 260px; height: 140px;" filled type="textarea"
                                                :disable="!item['selected']" />
                                        </div>

                                        <div style="height: 60px;margin-top: 5px;"
                                            class="row justify-start text-bold black_border q-gutter-sm">
                                            <q-space />
                                            <q-btn v-if="item['selected']"
                                                style="width: 200px;height: 40px;margin-top: 5px;" color="primary"
                                                label="提交问题" @click="notificationShow(index)" />
                                        </div>
                                    </div>

                                    <div>
                                        <div class="black_border  " style="height: 280px; width: 300px;">
                                            <div style="line-height: 40px;" class="text-center black_border">
                                                问题图片或视频上传
                                            </div>
                                            <template v-if="!item['selected']">
                                                <q-img :src="item['image_URL']" :ratio="4 / 3" style="height: 230px;"
                                                    @click="gotoImage(item['image_URL'])" />
                                            </template>

                                            <template v-else>
                                                <q-uploader style="width: 100%;height: 230px;" batch auto-upload dense
                                                    :factory="factoryFn_lsw"
                                                    @uploaded="handleUploadSuccess_Problem_lsw" />
                                            </template>
                                        </div>
                                    </div>

                                </div>
                                <!-- <q-separator inset /> -->
                                <div style="width: 100%;height: 3px;background:black;"></div>
                            </template>
                        </div>


                    </div>
                </q-card-section>

            </q-card>
        </q-dialog>







        <div class="fit row wrap justify-around items-start content-start" v-if="!initShowDialog">
            <div>
                <div class="text-h5 text-bold" style="margin-left: 10px;margin-top: 10px;">
                    <div style="color:dodgerblue">
                        <!-- <img src="http://172.21.118.163:3003/Web_image/Logo.png"
                style="margin-top: 5px;width: 30%;height: 80px;"> -->
                        Kimberly-Clark
                    </div>
                    <div>机台清洁管理系统</div>
                </div>
                <div style="margin-left: 10px;">
                    <div class="row wrap justify-start">
                        <div style="font-size: 15px;line-height: 45px;font-weight: 600;">日期：</div>
                        <q-input style="width: 150px;" v-model="formData['Date']" filled type="date" label="日期"
                            stack-label dense disable>
                        </q-input>
                    </div>
                    <div class="row wrap justify-start">
                        <div style="font-size: 15px;line-height: 45px;font-weight: 600;">班次：</div>
                        <q-select outlined v-model="formData['Shift']" :options="shiftArray" label-slot clearable
                            style="width: 150px;" dense disable>
                            <template v-slot:label>班次
                                <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                    style="font-size: 12px;">必填</em>
                            </template>
                        </q-select>
                    </div>
                </div>
            </div>
            <div style="margin-top: 72px;margin-left: 10px;">
                <div class="row wrap justify-start">
                    <div style="font-size: 15px;line-height: 45px;font-weight: 600;">机台：</div>
                    <q-select outlined v-model="formData['Line']" :options="lineArray" label-slot clearable
                        style="width: 150px;" dense disable>
                        <template v-slot:label>机台
                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                style="font-size: 12px;">必填</em>
                        </template>
                    </q-select>
                </div>
                <div class="row wrap justify-start">
                    <div style="font-size: 15px;line-height: 45px;font-weight: 600;">状态：</div>
                    <q-select outlined v-model="formData['MachineStatus']" :options="machineStatusArray" label-slot
                        clearable style="width: 150px;" dense disable>
                        <template v-slot:label>状态
                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                style="font-size: 12px;">必填</em>
                        </template>
                    </q-select>
                </div>
                <!-- <div class="row wrap justify-start">
                    <div style="font-size: 15px;line-height: 45px;font-weight: 600;">班组：</div>
                    <q-select outlined v-model="formData['Crew']" :options="crewArray" label-slot clearable
                        style="width: 150px;" dense disable>
                        <template v-slot:label>班组
                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                style="font-size: 12px;">必填</em>
                        </template>
                    </q-select>
                </div> -->
            </div>
            <div style="margin-top: 30px;margin-left: 10px;width:300px;">
                <div class="text-center "
                    style="font-size: 20px;line-height: 45px;font-weight: 600; border: 1px solid black;">
                    问题统计
                </div>
                <div class="row wrap justify-around items-center text-center">
                    <div style="width: 100px;border: 1px solid black;font-size: 18px;line-height: 45px;">总数
                    </div>
                    <div style="width: 100px;border: 1px solid black;font-size: 18px;line-height: 45px;">主机
                    </div>
                    <div style="width: 100px;border: 1px solid black;font-size: 18px;line-height: 45px;">外围
                    </div>
                </div>
                <div class="row wrap justify-around items-center text-center">
                    <div style="width: 100px;border: 1px solid black;font-size: 18px;line-height: 40px;">
                        {{ HK_Problem_Statistics['Total'] }}</div>
                    <div style="width: 100px;border: 1px solid black;font-size: 18px;line-height: 40px;">
                        {{ HK_Problem_Statistics['主机'] }}</div>
                    <div style="width: 100px;border: 1px solid black;font-size: 18px;line-height: 40px;"> {{
                        HK_Problem_Statistics['外围'] }}
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top:5px" v-if="!initShowDialog">
            <q-table :pagination.sync="pagination" :data="hk_Check_data[formData['Area']]" :columns="hk_Check_columns"
                row-key="name" dense :filter="formData['position'] == 'All' ? '' : formData['position']">
                <template v-slot:top>
                    <div style="font-size: 20px;font-weight: 600;">清洁任务清单</div>
                    <div class="row wrap justify-start" style="margin-left: 30px;">
                        <div style="font-size: 15px;line-height: 45px;font-weight: 600;">区域选择：</div>
                        <q-select outlined v-model="formData['Area']" :options="AreaList" style="width: 150px;" dense>
                        </q-select>
                    </div>

                    <div class="row wrap justify-start" style="margin-left: 30px;">
                        <div style="font-size: 15px;line-height: 45px;font-weight: 600;">岗位选择：</div>
                        <q-select outlined v-model="formData['position']" :options="positionList" style="width: 150px;"
                            dense>
                        </q-select>
                    </div>
                </template>

                <template v-slot:body="props">
                    <q-tr :props="props" @click="toDetail(props.row)">
                        <template>

                        </template>
                        <q-td v-for="col in props.cols" :key="col.name" :props="props"
                            :style="col.field == 'checkResult' || col.field == 'checkItem' ? col.field == 'checkResult' ? col.value == '合格' ? 'background:#0EA06F;color:white' : col.value == '不合格' ? 'background:#D32F2F;color:white' : '' : col.value == null ? '' : 'background:#D32F2F;color:white' : ''">
                            <div>
                                {{ col.value }}</div>
                        </q-td>
                    </q-tr>
                </template>

            </q-table>
        </div>




    </base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import qs from 'qs'
export default {
    components: {
        BaseContent
    },
    data() {
        return {
            Line: 'ND06',
            LinkID: 'ND06-LMU-PM-Q1-H',
            lineArray: ['ND05', 'ND06'],
            shiftArray: ['白班', '夜班'],
            crewArray: ['A', 'B', 'C', 'D', 'E', 'F'],
            machineStatusArray: ['Normal Production', 'PM', 'RSR', 'Grade Change'],
            formData: {
                LinkID: '',
                Line: '',
                Date: '',
                Shift: '',
                Crew: '',
                MachineStatus: '',
                Area: '主机',
                position: 'All',
            },
            hk_Check_columns: [
                { name: 'row_num', align: 'left', label: '序号', field: 'row_num', sortable: true },
                { name: 'Area', align: 'left', label: '区域', field: 'Area', sortable: true },
                { name: 'Task', align: 'left', label: '任务描述', field: 'Task', sortable: true },
                { name: 'Cycle', align: 'left', label: '频次', field: 'Cycle', sortable: true },
                { name: 'Executive_Position', align: 'left', label: '岗位', field: 'Executive_Position', sortable: true },
                { name: 'Standard_ID', align: 'left', label: '清洁标准', field: 'Standard_ID', sortable: true },
                { name: 'checkResult', align: 'left', label: '自检结果', field: 'checkResult', sortable: true },
                { name: 'checkItem', align: 'left', label: 'LSW个数', field: 'checkItem', sortable: true }
            ],
            hk_Check_data: [
                { 序号: 1, 机器状态: '正常', 区域: '机房', 任务描述: '机房清洁', 频次: '每日', 岗位: '清洁工', 清洁标准: '清洁工标准', 自检结果: '合格', 检查: '无' },
                { 序号: 2, 机器状态: '正常', 区域: '机房', 任务描述: '机房清洁', 频次: '每日', 岗位: '清洁工', 清洁标准: '清洁工标准', 自检结果: '合格', 检查: '无' },
                { 序号: 3, 机器状态: '正常', 区域: '机房', 任务描述: '机房清洁', 频次: '每日', 岗位: '清洁工', 清洁标准: '清洁工标准', 自检结果: '合格', 检查: '无' },
                { 序号: 4, 机器状态: '正常', 区域: '机房', 任务描述: '机房清洁', 频次: '每日', 岗位: '清洁工', 清洁标准: '清洁工标准', 自检结果: '合格', 检查: '无' },
                { 序号: 5, 机器状态: '正常', 区域: '机房', 任务描述: '机房清洁', 频次: '每日', 岗位: '清洁工', 清洁标准: '清洁工标准', 自检结果: '合格', 检查: '无' },
                { 序号: 6, 机器状态: '正常', 区域: '机房', 任务描述: '机房清洁', 频次: '每日', 岗位: '清洁工', 清洁标准: '清洁工标准', 自检结果: '合格', 检查: '无' },
            ],
            AreaList: ['主机', '外围'],
            pagination: {
                rowsPerPage: 0
            },
            positionList: ['All', 'Bagger', 'MOP', 'OP1', 'OP2', '班长'],
            selectedRow: false,
            selectedStandard: false,
            selectedCheckData: {
                "Task_ID": '',
                "Date": '',
                "Line": '',
                "Employee_ID": '',
                "Employee_Name": '',
                "Check_DateTime": '',
                "Check_Result": '',
                "Check_Category": '',
                "Check_Problem_Desc": '',
                "Check_Image_URL": '',
                "ID": ''
            },
            selectedCheckData_Asset: {
                "Task_ID": '',
                "Date": '',
                "Line": '',
                "Employee_ID": '',
                "Employee_Name": '',
                "Check_DateTime": '',
                "Check_Result": '',
            },
            selectedLSWData: [],
            showDetailDialog: false,
            problemCategory: ['风险提报', '新标准提报', '老标准提升'],
            lswProblemCategory: ['风险提报', '新标准提报', '老标准提升'],
            LSWID: '',
            OwnerID: '',
            initShowDialog: false,
            HK_Problem_Statistics: false,
            managerList: false,
            notificationDialog: false,
            selectedIndex: -1,
            selectedLSWData_NotificationMail: [],
            login_position: ''
        }
    },
    mounted() {
        console.log('mounted', this.$route.query)
        this.CI_HK_Machine_Status()
        const parma = this.$route.query
        this.getLine()
        this.formData['Line'] = parma['Line']
        this.formData['MachineStatus'] = parma['MachineStatus']
        this.formData['Date'] = this.formatCurrentDate();
        const now = new Date();
        const hour = now.getHours();
        this.formData['Shift'] = hour >= 8 && hour < 20 ? '白班' : '夜班';
        this.getManagerList()
        this.initShowDialog = true;
        this.login_position = localStorage.getItem('Position')
        // this.login_position = '资产主管'
    },
    watch: {

    },

    methods: {
        async getLine() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`approve/getLine?mill=${localStorage.getItem('mill')}`)
            console.log('line', res)
            _this.lineArray = res
        },
        async CI_HK_Machine_Status() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`ci/CI_HK_Machine_Status`)
            console.log('CI_HK_Machine_Status', res)
            _this.machineStatusArray = res
        },
        async getManagerList() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get('lsw/getManagerList')
            console.log('getManagerList', res)
            _this.managerList = res
        },

        initData() {
            if (this.formData['Line'] == '' || this.formData['MachineStatus'] == '' || this.formData['Date'] == '' || this.formData['Shift'] == '') {
                this.$q.notify({
                    type: 'negative',
                    message: '请选择机台、状态、日期、班次、班组',
                    timeout: 2000,
                    position: 'top'
                })
                return
            }
            this.CI_HK_Task_List(this.formData['Line'], this.formData['MachineStatus'], this.formData['Date'], this.formData['Shift'])
            this.CI_HK_LSW_ID()
            this.CI_HK_LSW_OwnerName(this.formData['Line'])
            this.CI_HK_Problem_Statistics(this.formData['Line'], this.formData['MachineStatus'], this.formData['Date'], this.formData['Shift'])
            this.initShowDialog = false;
        },

        toDetail(row) {
            console.log('row', row)
            this.selectedRow = row
            this.selectedCheckData = {
                "Task_ID": row['Task_ID'],
                "Date": this.formData['Date'],
                "Line": this.formData['Line'],
                "Shift": this.formData['Shift'],
                "Employee_ID": localStorage.getItem('account'),
                "Employee_Name": localStorage.getItem('username'),
                "Check_DateTime": '',
                "Check_Result": '',
                "Check_Category": '',
                "Check_Problem_Desc": '',
                "Check_Image_URL": '',
                "ID": ''
            }
            this.selectedCheckData_Asset = {
                "Task_ID": row['Task_ID'],
                "Date": this.formData['Date'],
                "Line": this.formData['Line'],
                "Shift": this.formData['Shift'],
                "Employee_ID": localStorage.getItem('account'),
                "Employee_Name": localStorage.getItem('username'),
                "Check_DateTime": '',
                "Check_Result": '',
            }
            console.log(' this.selectedCheckData', this.selectedCheckData)
            this.selectedLSWData = []

            this.CI_HK_Standard(row['Standard_ID'])
            this.CI_HK_Check_Data(row['Task_ID'], this.formData['Date'], this.formData['Line'], this.formData['Shift'])
            this.CI_HK_Check_Data_Asset(row['Task_ID'], this.formData['Date'], this.formData['Line'], this.formData['Shift'])
            this.CI_HK_LSW_Problem_Data(this.formData['Date'] + '-' + this.formData['Line'] + '-' + this.formData['Shift'] + '-' + row['Task_ID'])
        },
        async CI_HK_Task_List(Line, MachineStatus, Date, Shift) {
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`ci/CI_HK_Task_List?Line=${Line}&MachineStatus=${MachineStatus}&Date=${Date}&Shift=${Shift}`)
            console.log('CI_HK_Task_List', res)
            _this.hk_Check_data = res
        },
        async CI_HK_Problem_Statistics(Line, MachineStatus, Date, Shift) {
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`ci/CI_HK_Problem_Statistics?Line=${Line}&MachineStatus=${MachineStatus}&Date=${Date}&Shift=${Shift}`)
            console.log('CI_HK_Problem_Statistics', res)
            _this.HK_Problem_Statistics = res
        },
        async CI_HK_LSW_ID() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`ci/CI_HK_LSW_ID?Employee_ID=${localStorage.getItem('account')}`)
            console.log('CI_HK_LSW_ID', res)
            if (res.length > 0) {
                _this.LSWID = res[0]['LSW_Base_ID']
            }

        },
        async CI_HK_LSW_OwnerName(Line) {
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`ci/CI_HK_LSW_OwnerName?Line=${Line}`)
            console.log('CI_HK_LSW_OwnerName', res)
            if (res.length > 0) {
                _this.OwnerID = res[0]['Employee_ID']
            }
        },




        async CI_HK_Standard(Standard_ID) {
            const _this = this
            _this.selectedStandard = false
            const {
                data: res
            } = await _this.$http.get(`ci/CI_HK_Standard?Standard_ID=${Standard_ID}`)
            console.log('CI_HK_Standard', res)
            _this.selectedStandard = res[0]
            _this.showDetailDialog = true
        },



        gotoImage(url) {
            window.open(url, '_blank');
        },
        async CI_HK_Check_Data(Task_ID, Date, Line, Shift) {
            const _this = this
            _this.selectedStandard = false
            const {
                data: res
            } = await _this.$http.get(`ci/CI_HK_Check_Data?Task_ID=${Task_ID}&Date=${Date}&Line=${Line}&Shift=${Shift}`)
            console.log('CI_HK_Check_Data', res)
            if (res.length > 0) {
                _this.selectedCheckData = res[0]
            }
            // this.showDetailDialog = true
        },

        formatCurrentDate() {
            const now = new Date(); // 获取当前日期
            const currentHour = now.getHours(); // 获取当前小时

            // 根据需要格式化日期，例如转换为YYYY-MM-DD格式
            // const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
            // 如果当前时间在00:00:00到07:59:59之间，则返回前一天的日期
            if (currentHour < 8) {
                now.setDate(now.getDate() - 1); // 减去一天
            }
            // 格式化日期为YYYY-MM-DD格式
            const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
            return formattedDate;
        },
        formatCurrentDateTime() {
            const now = new Date(); // 获取当前日期
            // 根据需要格式化日期，例如转换为YYYY-MM-DD格式
            const formattedDateTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
            return formattedDateTime;
        },
        async CI_HK_LSW_Problem_Data(relateID) {
            const _this = this
            _this.selectedStandard = false
            const {
                data: res
            } = await _this.$http.get(`ci/CI_HK_LSW_Problem_Data?RelateID=${relateID}`)
            console.log('CI_HK_LSW_Problem_Data', res)
            if (res.length > 0) {
                _this.selectedLSWData = res
            }
            // this.showDetailDialog = true
        },
        addProblem() {
            const _this = this
            _this.selectedLSWData.unshift({
                mill: '南京南厂',
                name: localStorage.getItem('username'),
                account: localStorage.getItem('account'),
                'dept[label]': _this.formData['Line'],
                'dept[value]': _this.formData['Line'],
                'dept[ownerID]': _this.OwnerID,
                problemContent: '',
                LSW_Base_ID: _this.LSWID,
                fileNameArray: [],
                Priority: '',
                problem_category: '',
                fileURLArray: {},
                NotificationMail: [],
                relateID: _this.formData['Date'] + '-' + _this.formData['Line'] + '-' + _this.formData['Shift'] + '-' + _this.selectedCheckData['Task_ID'],
                image_URL: '',
                selected: true
            })
            console.log('this.selectedLSWData', _this.selectedLSWData)

        },

        addProblem_self(result) {
            const _this = this
            if (_this.selectedCheckData['Check_Image_URL'] == '' && result == '合格') {
                _this.$q.dialog({
                    title: '请上传图片',
                    message: `请上传自检的图片`,
                    ok: {
                        label: '确定',
                        color: 'primary'
                    },
                })
                return
            }
            if ((_this.selectedCheckData['Check_Category'] == '' || _this.selectedCheckData['Check_Problem_Desc'] == '') && result == '不合格') {
                _this.$q.dialog({
                    title: '请填写问题类型和问题描述',
                    message: `由于没有填写问题类别或问题描述，请填写后再提交`,
                    ok: {
                        label: '确定',
                        color: 'primary'
                    },
                })
                return
            }
            _this.selectedCheckData['Check_Result'] = result
            _this.selectedCheckData['Check_DateTime'] = _this.formatCurrentDateTime()
            _this.$http.post('ci/Insert_CI_HK_Check_Data', _this.selectedCheckData).then(function (response) {
                console.log('response', response)
                if (response.data === '已添加') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `添加成功`,
                        position: 'top'
                    })
                }
            })

        },
        notificationShow(index) {
            const _this = this
            _this.selectedLSWData_NotificationMail = []
            _this.selectedIndex = index
            console.log('_this.selectedIndex', _this.selectedIndex)
            _this.notificationDialog = true
        },
        async submitLSWProblem_Data() {
            const _this = this
            _this.submitLSWProblem(_this.selectedIndex)
        },

        submitLSWProblem(index) {
            const _this = this
            console.log('item', index)
            console.log('this.selectedLSWData', this.selectedLSWData[index])
            _this.selectedLSWData[index].fileURLArray = JSON.stringify(_this.selectedLSWData[index].fileURLArray)
            _this.selectedLSWData[index].fileNameArray = _this.selectedLSWData[index].fileNameArray.join(",")

            if (_this.selectedLSWData_NotificationMail.length > 0) {
                for (let s = 0; s < _this.selectedLSWData_NotificationMail.length; s++) {
                    _this.selectedLSWData[index].NotificationMail += _this.selectedLSWData_NotificationMail[s] + "@kcc.com;"
                }
            }
            _this.$http.post('lsw/insert_LSW_Problem_Data', qs.stringify(_this.selectedLSWData[index])).then(function (response) {
                console.log(response)
                _this.$q.dialog({
                    title: '保存成功',
                    message: '您的问题已提交，系统会第一时间已邮件方式发给区域负责人'
                }).onOk(() => {
                    // console.log('OK')
                    // _this.prompt_input_problem = false
                    // _this.getProblemList(_this.form.LSW_Base_ID)
                    _this.selectedLSWData[index]['selected'] = false
                    _this.notificationDialog = false
                }).onCancel(() => {
                    // console.log('Cancel')
                }).onDismiss(() => {
                    // console.log('I am triggered on both OK and Cancel')
                })

            })
        },

        handleUploadSuccess_Problem_self(response) {
            console.log('FileUploadResponse', response)
            var _this = this
            //response.files[0].xlr.responseText
            var responseURL = response['files'][0]['xhr']['responseText']
            _this.selectedCheckData['Check_Image_URL'] = responseURL
        },
        handleUploadSuccess_Problem_lsw(response) {
            console.log('FileUploadResponse', response)
            var _this = this
            var responseFileName = response['files'][0]['xhr']['responseText']
            var responseURL = response['files'][0]['xhr']['responseText']
            for (var i = 0; i < _this.selectedLSWData.length; i++) {
                if (_this.selectedLSWData[i]['selected']) {
                    responseFileName = responseFileName.replace("http://172.21.65.192:3003/lsw/", "")
                    _this.selectedLSWData[i].fileNameArray.push(responseFileName)
                    _this.selectedLSWData[i].fileURLArray[responseFileName] = responseURL
                    _this.selectedLSWData[i].image_URL = responseURL
                    break
                }
            }
            // if (_this.problem_Image_Type == 'before') {
            //     _this.problemData[_this.problem_Index]['Before_Image'] = responseURL
            // }
            // if (_this.problem_Image_Type == 'after') {
            //     _this.problemData[_this.problem_Index]['After_Image'] = responseURL
            // }
            // console.log('resultData:', _this.problemData[_this.problem_Index])
            // _this.ImageShow = true
            // _this.problemData[_this.problem_Index][_this.problem_Image_Type == 'before' ? 'Before_Image' :'After_Image'] = responseURL

        },
        factoryFn(files) {
            var _this = this
            // _this.prompt_uploadFile = true
            return {
                url: 'http://172.21.65.192:3001/upload?system=hk',
                method: 'POST'
            }
        },
        factoryFn_lsw(files) {
            var _this = this
            // _this.prompt_uploadFile = true
            return {
                url: 'http://172.21.65.192:3001/upload?system=lsw',
                method: 'POST'
            }
        },
        closeCheckDialog() {
            this.CI_HK_Task_List(this.formData['Line'], this.formData['MachineStatus'], this.formData['Date'], this.formData['Shift'])
            this.CI_HK_Problem_Statistics(this.formData['Line'], this.formData['MachineStatus'], this.formData['Date'], this.formData['Shift'])
            this.showDetailDialog = false
        },
        check_Asset(result) {
            const _this = this
            _this.selectedCheckData_Asset['Check_Result'] = result
            _this.selectedCheckData_Asset['Check_DateTime'] = _this.formatCurrentDateTime()
            _this.$http.post('ci/Insert_CI_HK_Check_Data_Asset', _this.selectedCheckData_Asset).then(function (response) {
                console.log('response', response)
                if (response.data === '已添加') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `添加成功`,
                        position: 'top'
                    })
                }
            })

        },
        async CI_HK_Check_Data_Asset(Task_ID, Date, Line, Shift) {
            const _this = this
            _this.selectedStandard = false
            const {
                data: res
            } = await _this.$http.get(`ci/CI_HK_Check_Data_Asset?Task_ID=${Task_ID}&Date=${Date}&Line=${Line}&Shift=${Shift}`)
            console.log('CI_HK_Check_Data_Asset', res)
            if (res.length > 0) {
                _this.selectedCheckData_Asset = res[0]
            }
            // this.showDetailDialog = true
        },


    }


}
</script>


<style>
.black_border {
    border: 1px solid #E8E8E8;

}
</style>