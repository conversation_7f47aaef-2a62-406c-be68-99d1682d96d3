<template>
  <div style="width: 100%;">


    <!-- 页面标题 -->
    <div class="text-center q-mb-md">
      <!-- <img src="a3-logo.png" alt="A3 Problem Solving" class="q-mb-sm"> -->
      <div class="text-h4 text-weight-bold">
        <span>{{ reportType === '蓝带问题解决5WHY报告' ? '5WHY报告' : 'A3报告' }}{{ mode == 'create' ? '录入' : mode == 'edit' ? '编辑' : '查看' }}</span>
        <!-- <span class="q-mx-md">A3报告录入</span>
        <span style="color: red">根本原因分析</span> -->
      </div>
    </div>

    <!-- 报告类型选择 -->
    <div class="q-pa-md">
      <q-select v-model="reportType" :options="reportTypeOptions" label="报告类型" outlined dense
        :readonly="mode === 'query'" @update:model-value="handleReportTypeChange" />
    </div>

    <!-- 问题定义部分 -->
    <div class="q-pa-md q-mt-lg">
      <q-card class="q-mb-md" bordered>
        <q-card-section>
          <div class="text-h6">问题定义</div>

          <div class="row q-col-gutter-sm ">
            <div class="col-3">
              <q-select
                v-model="problemDefinition.factory"
                :options="factoryOptions"
                bg-color="grey-3"
                outlined
                color="yellow"
                :rules="[val => !!val || '请选择工厂']"
                dense
                :readonly="mode === 'query'"
                :error="!problemDefinition.factory && showValidationErrors"
                error-message="工厂为必填项"
                label-slot
              >
                <template v-slot:label>工厂
                  <em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
                </template>
              </q-select>
            </div>
            <div class="col-3">
              <q-select
                v-model="problemDefinition.department"
                :options="departmentOptions"
                bg-color="grey-3"
                outlined
                color="yellow"
                :rules="[val => !!val || '请选择部门']"
                dense
                :readonly="mode === 'query'"
                :error="!problemDefinition.department && showValidationErrors"
                error-message="部门为必填项"
                label-slot
              >
                <template v-slot:label>部门
                  <em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
                </template>
              </q-select>
            </div>
            <div class="col-3">
              <q-select
                v-model="problemDefinition.machine"
                :options="machineOptions"
                bg-color="grey-3"
                outlined
                color="yellow"
                :rules="[val => !!val || '请选择机台']"
                dense
                :readonly="mode === 'query'"
                :error="!problemDefinition.machine && showValidationErrors"
                error-message="机台为必填项"
                label-slot
              >
                <template v-slot:label>机台
                  <em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
                </template>
              </q-select>
            </div>
            <div class="col-3">
              <q-select
                v-model="problemDefinition.unit"
                :options="unitOptions"
                bg-color="grey-3"
                outlined
                color="yellow"
                :rules="[val => !!val || '请选择设备单元']"
                dense
                :readonly="mode === 'query'"
                :error="!problemDefinition.unit && showValidationErrors"
                error-message="设备单元为必填项"
                label-slot
              >
                <template v-slot:label>设备单元
                  <em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
                </template>
              </q-select>
            </div>
            <div class="col-3">
              <q-select
                v-model="problemDefinition.category"
                :options="categoryOptions"
                bg-color="grey-3"
                outlined
                color="yellow"
                :rules="[val => !!val || '请选择问题分类']"
                dense
                :readonly="mode === 'query'"
                :error="!problemDefinition.category && showValidationErrors"
                error-message="问题分类为必填项"
                label-slot
              >
                <template v-slot:label>问题分类
                  <em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
                </template>
              </q-select>
            </div>
            <div class="col-3">
              <q-input v-model="problemDefinition.teamLeader" label="Team leader" bg-color="grey-3" outlined dense
                :rules="[val => !!val || '此项为必填项']" :readonly="mode === 'query'" />
            </div>
            <div class="col-3">
              <q-input v-model="problemDefinition.teamMembers" label="Team members" bg-color="grey-3" outlined dense
                :rules="[val => !!val || '此项为必填项']" :readonly="mode === 'query'" />
            </div>
          </div>

          <q-input v-model="problemDefinition.problemDescription" label="问题名称简述" type="textarea" class=""
            bg-color="grey-3" outlined dense :rules="[val => !!val || '此项为必填项']" :readonly="mode === 'query'" />

          <div class="row q-col-gutter-sm">
            <div class="col-3">
              <q-input v-model="problemDefinition.whyImportant" label="Why important" bg-color="grey-3" outlined dense
                :readonly="mode === 'query'" />
            </div>
            <div class="col-3">
              <q-input v-model="problemDefinition.when" label="When" bg-color="grey-3" outlined dense
                :readonly="mode === 'query'" />
            </div>
            <div class="col-3">
              <q-input v-model="problemDefinition.howOften" label="How often" bg-color="grey-3" outlined dense
                :readonly="mode === 'query'" />
            </div>
            <div class="col-3">
              <q-input v-model="problemDefinition.where" label="Where" bg-color="grey-3" outlined dense
                :readonly="mode === 'query'" />
            </div>
          </div>

          <!-- 右侧指标区域 -->
          <div class="q-mt-sm">
            <div class="bg-blue text-white q-pa-sm text-weight-bold">
              衡量指标
              <q-btn round dense color="positive" icon="add" size="sm" class="float-right" @click="addMetric" />
            </div>
            <div class="row q-col-gutter-xs q-mt-xs">
              <div class="col-2 text-center">Metrics</div>
              <div class="col-2 text-center">Baseline</div>
              <div class="col-2 text-center">Goal</div>
              <div class="col-2 text-center">Actual</div>
              <div class="col-2 text-center">Units</div>
            </div>
            <div v-for="(metric, index) in metrics" :key="index"
              class="row q-col-gutter-xs q-mt-xs bg-grey-3 q-pa-xs items-center">
              <div class="col-2">
                <q-input v-model="metric.name" dense outlined :readonly="mode === 'query'" />
              </div>
              <div class="col-2">
                <q-input v-model="metric.baseline" dense outlined :readonly="mode === 'query'" />
              </div>
              <div class="col-2">
                <q-input v-model="metric.goal" dense outlined :readonly="mode === 'query'" />
              </div>
              <div class="col-2">
                <q-input v-model="metric.actual" dense outlined :readonly="mode === 'query'" />
              </div>
              <div class="col-2">
                <q-input v-model="metric.unit" dense outlined :readonly="mode === 'query'" />
              </div>
              <div class="col-1">
                <q-btn round dense color="negative" icon="remove" size="sm" @click="removeMetric(index)" v-if="mode !== 'query'" />
              </div>
            </div>
          </div>
          <!-- 照片/视频上传区域 -->
          <div class="q-mt-md">
            <div class="bg-grey-3 q-pa-md">
              <div class="row items-center">
                <q-icon name="add" size="24px" color="yellow" class="q-mr-sm" />
                <span>问题现象照片或视频，可以添加</span>
              </div>
              <q-uploader style="width:40%;height: 230px;" batch auto-upload dense :factory="factoryFn_Problem_Picture"
                @uploaded="handleUploadSuccess_Problem_Picture" label="问题现象照片或视频" v-if="mode !== 'query' && mode !== 'myReport'" />

              <div v-if="mode === 'query' || (mode === 'myReport' && !problemPictureEditMode)" class="row items-center">
                <q-img v-if="problemDefinition.url" :src="problemDefinition.url" style="width:40%;height: 230px;" @click="openFile(problemDefinition.url)" />
                <div v-else class="bg-grey-4 flex flex-center" style="width:40%;height: 230px;">
                  <span class="text-grey-7">暂无照片</span>
                </div>
                <q-btn v-if="mode === 'myReport'" color="primary" icon="edit" label="修改照片" class="q-ml-md" @click="enableProblemPictureEdit" />
              </div>

              <div v-if="mode === 'myReport' && problemPictureEditMode" class="q-mt-md">
                <q-uploader style="width:40%;height: 230px;" batch auto-upload dense :factory="factoryFn_Problem_Picture"
                  @uploaded="handleUploadSuccess_Problem_Picture" label="更新问题现象照片或视频" />
                <q-btn color="negative" label="取消编辑" @click="cancelProblemPictureEdit" class="q-ml-md" />
              </div>
              <q-input v-model="problemDefinition.photoDescription" label="照片内容简要说明" class="q-mt-md" bg-color="white"
                outlined :rules="[val => !!val || '此项为必填项']" :readonly="mode === 'query'" />
            </div>
          </div>

          <!-- 问题定义附件上传区域 -->
          <div class="q-mt-md">
            <div class="bg-grey-3 q-pa-md">
              <div class="row items-center">
                <q-icon name="attach_file" size="24px" color="yellow" class="q-mr-sm" />
                <span>问题定义附件上传，可以添加多个附件</span>
              </div>
              <q-uploader style="width:100%;height: 150px;" batch multiple auto-upload dense :factory="factoryFn_Problem_Attachments"
                @uploaded="handleUploadSuccess_Problem_Attachments" label="问题定义附件" v-if="mode !== 'query' && mode !== 'myReport'" />

              <div v-if="mode === 'query' || (mode === 'myReport' && !attachmentUploadMode)" class="q-mt-md">
                <div v-if="problemDefinition.attachments && problemDefinition.attachments.length > 0">
                  <div v-for="(attachment, index) in problemDefinition.attachments" :key="index" class="q-mb-sm row items-center">
                    <q-btn color="primary" :label="attachment.name" @click="openFile(attachment.url)" icon="attachment" />
                    <q-btn v-if="mode === 'myReport'" round dense color="negative" icon="delete" class="q-ml-md" @click="removeAttachment(index)" />
                  </div>
                </div>
                <div v-else class="text-grey-7 q-mb-md">暂无附件</div>
                <q-btn v-if="mode === 'myReport'" color="primary" icon="add" label="添加附件" @click="enableAttachmentUpload" class="q-mt-md" />
              </div>

              <div v-if="mode === 'myReport' && attachmentUploadMode" class="q-mt-md">
                <q-uploader style="width:100%;height: 150px;" batch multiple auto-upload dense :factory="factoryFn_Problem_Attachments"
                  @uploaded="handleUploadSuccess_Problem_Attachments" label="添加附件" />
                <q-btn color="negative" label="完成添加" @click="cancelAttachmentUpload" class="q-mt-sm" />
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>


    </div>

    <!-- 工作原理介绍部分 -->
    <div class="q-pa-md" v-if="reportType === '绿带问题解决A3报告'">
      <div v-for="(principle, index) in workPrinciples" :key="index">
        <q-card class="q-mb-md" bordered>
          <q-card-section>
            <div class="row justify-between items-center q-mb-md">
              <div class="text-h6">工作原理 {{ index + 1 }}</div>
              <q-btn round dense color="negative" icon="remove" size="sm" @click="removeWorkPrinciple(index)" />
            </div>
            <div class="row q-col-gutter-md">
              <div class="col-6">
                <q-input v-model="principle.name" label="名称" bg-color="grey-3" outlined :readonly="mode === 'query'" />
              </div>
            </div>

            <!-- 照片/视频区域 -->
            <div class="q-mt-md">
              <div class="row q-gutter-sm">
                <q-btn v-for="(file, idx) in principle.mediaFiles" :key="idx" color="primary" :label="file.name"
                  @click="openFile(file.url)" icon="attachment">
                  <q-tooltip>点击查看文件</q-tooltip>
                  <q-btn round dense color="negative" icon="close" size="xs" class="q-ml-xs"
                    @click.stop="removeFile(principle, idx)" />
                </q-btn>
                <q-btn color="primary" icon="add" label="添加照片/视频" @click="openUploadDialog(index)"
                  v-if="mode !== 'query'" />
              </div>
              <q-dialog v-model="uploadDialog" position="top">
                <q-card style="max-width: 90vw; width: 800px;">
                  <q-card-section>
                    <div class="text-h6">上传照片/视频</div>
                  </q-card-section>
                  <q-card-section>
                    <q-uploader batch auto-upload :factory="factoryFn_Principle_Picture"
                      @uploaded="handleUploadSuccess_Principle_Picture" />
                  </q-card-section>
                </q-card>
              </q-dialog>
              <q-input v-model="principle.mediaDescription" label="照片内容简要说明，包括流程说明，设定介绍等" class="q-mt-md"
                bg-color="white" outlined :rules="[val => !!val || '此项为必填项']" />
            </div>

            <!-- 关键设定和标准说明 -->
            <div class="q-mt-md">
              <div class="bg-blue text-white q-pa-sm text-weight-bold">
                关键设定 & 标准说明
                <q-btn round dense color="positive" icon="add" size="sm" class="float-right"
                  @click="addSettingAndDescription(index)" />
              </div>
              <div v-for="(item, idx) in principle.settingsAndDescriptions" :key="idx"
                class="bg-grey-3 q-pa-md q-mt-sm">
                <div class="text-weight-bold q-mb-sm">关键设定</div>
                <div class="row q-col-gutter-sm">
                  <div class="col-4">
                    <q-input v-model="item.parameter" label="参数" dense outlined :readonly="mode === 'query'" />
                  </div>
                  <div class="col-4">
                    <q-input v-model="item.currentValue" label="当前值" dense outlined :readonly="mode === 'query'" />
                  </div>
                  <div class="col-4">
                    <q-input v-model="item.standardValue" label="标准值" dense outlined :readonly="mode === 'query'" />
                  </div>
                </div>

                <div class="text-weight-bold q-mt-md q-mb-sm">标准说明</div>
                <div class="row q-col-gutter-sm">
                  <div class="col-6">
                    <q-input v-model="item.item" label="说明项" dense outlined :readonly="mode === 'query'" />
                  </div>
                  <div class="col-6">
                    <q-input v-model="item.description" label="详细说明" dense outlined :readonly="mode === 'query'" />
                  </div>
                </div>

                <div class="row justify-end q-mt-sm">
                  <q-btn round dense color="negative" icon="remove" size="sm"
                    @click="removeSettingAndDescription(index, idx)" />
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div class="row q-mt-md">
        <q-btn color="positive" icon="add" label="添加工作原理" size="lg" class="q-mr-md" @click="addWorkPrinciple"
          v-if="mode !== 'query'" />
      </div>
    </div>

    <!-- 根本原因分析部分 -->
    <div class="q-pa-md q-mt-lg" v-if="reportType === '绿带问题解决A3报告'">
      <q-card class="q-mb-md" bordered>
        <q-card-section>
          <div class="text-h6">鱼骨图分析</div>
          <div class="row q-col-gutter-md q-mt-md">
            <div class="col">
              <div class="text-black text-weight-bold">人</div>
              <div v-for="(issue, index) in rootCauseAnalysis.human" :key="index" class="q-mb-sm">
                <div class="row items-center">
                  <div class="col">
                    <q-input v-model="issue.description" label="直接问题" bg-color="grey-3" outlined
                      :readonly="mode === 'query'" />
                  </div>
                  <div class="col-auto q-pl-sm">
                    <q-btn round dense color="negative" icon="remove" size="sm" @click="removeIssue('human', index)" />
                  </div>
                </div>
              </div>
              <q-btn color="positive" icon="add" label="添加问题" @click="addIssue('human')" v-if="mode !== 'query'" />
            </div>

            <div class="col">
              <div class="text-black text-weight-bold">机</div>
              <div v-for="(issue, index) in rootCauseAnalysis.machine" :key="index" class="q-mb-sm">
                <div class="row items-center">
                  <div class="col">
                    <q-input v-model="issue.description" label="直接问题" bg-color="grey-3" outlined
                      :readonly="mode === 'query'" />
                  </div>
                  <div class="col-auto q-pl-sm">
                    <q-btn round dense color="negative" icon="remove" size="sm"
                      @click="removeIssue('machine', index)" />
                  </div>
                </div>
              </div>
              <q-btn color="positive" icon="add" label="添加问题" @click="addIssue('machine')" v-if="mode !== 'query'" />
            </div>

            <div class="col">
              <div class="text-black text-weight-bold">料</div>
              <div v-for="(issue, index) in rootCauseAnalysis.material" :key="index" class="q-mb-sm">
                <div class="row items-center">
                  <div class="col">
                    <q-input v-model="issue.description" label="直接问题" bg-color="grey-3" outlined
                      :readonly="mode === 'query'" />
                  </div>
                  <div class="col-auto q-pl-sm">
                    <q-btn round dense color="negative" icon="remove" size="sm"
                      @click="removeIssue('material', index)" />
                  </div>
                </div>
              </div>
              <q-btn color="positive" icon="add" label="添加问题" @click="addIssue('material')" v-if="mode !== 'query'" />
            </div>

            <div class="col">
              <div class="text-black text-weight-bold">法</div>
              <div v-for="(issue, index) in rootCauseAnalysis.method" :key="index" class="q-mb-sm">
                <div class="row items-center">
                  <div class="col">
                    <q-input v-model="issue.description" label="直接问题" bg-color="grey-3" outlined
                      :readonly="mode === 'query'" />
                  </div>
                  <div class="col-auto q-pl-sm">
                    <q-btn round dense color="negative" icon="remove" size="sm" @click="removeIssue('method', index)" />
                  </div>
                </div>
              </div>
              <q-btn color="positive" icon="add" label="添加问题" @click="addIssue('method')" v-if="mode !== 'query'" />
            </div>

            <div class="col">
              <div class="text-black text-weight-bold">环</div>
              <div v-for="(issue, index) in rootCauseAnalysis.environment" :key="index" class="q-mb-sm">
                <div class="row items-center">
                  <div class="col">
                    <q-input v-model="issue.description" label="直接问题" bg-color="grey-3" outlined
                      :readonly="mode === 'query'" />
                  </div>
                  <div class="col-auto q-pl-sm">
                    <q-btn round dense color="negative" icon="remove" size="sm"
                      @click="removeIssue('environment', index)" />
                  </div>
                </div>
              </div>
              <q-btn color="positive" icon="add" label="添加问题" @click="addIssue('environment')"
                v-if="mode !== 'query'" />
            </div>
          </div>

          <div class="q-mt-md">
            <div class="text-black text-weight-bold">Top 问题总结</div>
            <div v-for="(issue, index) in rootCauseAnalysis.topIssues" :key="index" class="q-mb-sm">
              <div class="row items-center">
                <div class="col">
                  <q-input v-model="issue.description" :label="`直接问题 ${index + 1}`" bg-color="grey-3" outlined
                    :readonly="mode === 'query'" />
                </div>
                <div class="col-2">
                  <q-input v-model="issue.percentage" label="百分比" bg-color="grey-3" outlined
                    :readonly="mode === 'query'" />
                </div>
                <div class="col-auto q-pl-sm">
                  <q-btn round dense color="negative" icon="remove" size="sm" @click="removeTopIssue(index)" />
                </div>
              </div>
            </div>
            <q-btn color="positive" icon="add" label="添加问题" @click="addTopIssue" v-if="mode !== 'query'"  />
          </div>
        </q-card-section>
      </q-card>
    </div>





    <!-- 5why分析模块 -->
    <div class="q-pa-md q-mt-sm">
      <q-card class="q-mb-md" bordered>
        <q-card-section>
          <div class="text-h6">5why分析</div>
          <div class="q-mt-md">
            <q-uploader style="width:100%;height: 230px;" batch multiple auto-upload dense :factory="factoryFn_WhyAnalysis"
              @uploaded="handleUploadSuccess_WhyAnalysis" label="上传5why分析图片（可上传多张）" v-if="mode !== 'query' && mode !== 'myReport'" />

            <div v-if="mode === 'query' || (mode === 'myReport' && !whyAnalysisUploadMode)">
              <div v-if="rootCauseAnalysis.whyAnalysis.images && rootCauseAnalysis.whyAnalysis.images.length > 0">
                <div v-for="(image, index) in rootCauseAnalysis.whyAnalysis.images" :key="index" class="q-mb-md row items-center">
                  <q-img :src="image" style="width:40%;height: 230px;" @click="openFile(image)" />
                  <q-btn v-if="mode === 'myReport'" round dense color="negative" icon="delete" class="q-ml-md" @click="removeWhyAnalysisImage(index)" />
                </div>
              </div>
              <div v-else class="text-grey-7 q-my-md">暂无5why分析图片</div>
              <q-btn v-if="mode === 'myReport'" color="primary" icon="add" label="添加5why分析图片" @click="enableWhyAnalysisUpload" class="q-mt-md" />
            </div>

            <div v-if="mode === 'myReport' && whyAnalysisUploadMode" class="q-mt-md">
              <q-uploader style="width:100%;height: 230px;" batch multiple auto-upload dense :factory="factoryFn_WhyAnalysis"
                @uploaded="handleUploadSuccess_WhyAnalysis" label="添加5why分析图片" />
              <q-btn color="negative" label="完成添加" @click="cancelWhyAnalysisUpload" class="q-mt-sm" />
            </div>
            <!-- <q-input v-model="rootCauseAnalysis.whyAnalysis.description" label="图片说明" class="q-mt-md" bg-color="white"
              outlined :rules="[val => !!val || '此项为必填项']" :readonly="mode === 'query'" /> -->
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 改进方案模块 -->
    <div class="q-pa-md q-mt-md">
      <q-card class="q-mb-md" bordered>
        <q-card-section>
          <div class="bg-blue text-white q-pa-sm text-weight-bold">
            改进方案
            <q-btn round dense color="positive" icon="add" size="sm" class="float-right" @click="addImprovementPlan" v-if="mode !== 'query'" />
          </div>
          <div class="row q-col-gutter-xs q-mt-xs">
            <div class="col-2 text-center">问题描述</div>
            <div class="col-2 text-center">行动方案</div>
            <div class="col-1 text-center">Effort分数</div>
            <div class="col-1 text-center">Benefit分数</div>
            <div class="col-1 text-center">优先级</div>
            <div class="col-1 text-center">负责人</div>
            <div class="col-1 text-center">完成日期</div>
            <div class="col-1 text-center">改善前照片</div>
            <div class="col-1 text-center">改善后照片</div>
            <div class="col-1 text-center">改善结果</div>
            <div class="col-1 text-center">操作</div>
          </div>
          <div v-for="(plan, index) in improvementPlans" :key="index"
            class="row q-col-gutter-xs q-mt-xs bg-grey-3 q-pa-xs items-center">
            <div class="col-2">
              <q-input v-model="plan.description" dense outlined :readonly="mode === 'query'" />
            </div>
            <div class="col-2">
              <q-input v-model="plan.actionPlan" dense outlined :readonly="mode === 'query'" />
            </div>
            <div class="col-1">
              <q-input v-model="plan.effort" dense outlined :readonly="mode === 'query'" />
            </div>
            <div class="col-1">
              <q-input v-model="plan.benefit" dense outlined :readonly="mode === 'query'" />
            </div>
            <div class="col-1">
              <q-select v-model="plan.totalScore" :options="['A','B','C','D']" dense outlined
                :readonly="mode === 'query'" />
            </div>
            <div class="col-1">
              <q-input v-model="plan.responsible" dense outlined :readonly="mode === 'query'" @click="showResponsibleDialog(index)" />
            </div>
            <div class="col-1">
              <q-input v-model="plan.dueDate" dense outlined :readonly="mode === 'query'" type="date" />
            </div>
            <div class="col-1">
              <q-btn v-if="!plan.beforeImage && (mode !== 'query' || mode === 'myReport')" color="primary" icon="add_photo_alternate" size="sm" @click="uploadImprovementImage(index, 'before')" />
              <div v-else class="row items-center">
                <q-img :src="plan.beforeImage" style="height: 50px;" @click="openFile(plan.beforeImage)" />
                <q-btn v-if="mode === 'myReport'" round dense color="negative" icon="delete" size="xs" class="q-ml-xs" @click="removeImprovementImage(index, 'before')" />
              </div>
            </div>
            <div class="col-1">
              <q-btn v-if="!plan.afterImage && (mode !== 'query' || mode === 'myReport')" color="primary" icon="add_photo_alternate" size="sm" @click="uploadImprovementImage(index, 'after')" />
              <div v-else class="row items-center">
                <q-img :src="plan.afterImage" style="height: 50px;" @click="openFile(plan.afterImage)" />
                <q-btn v-if="mode === 'myReport'" round dense color="negative" icon="delete" size="xs" class="q-ml-xs" @click="removeImprovementImage(index, 'after')" />
              </div>
            </div>
            <div class="col-1">
              <q-input v-model="plan.improvementResult" dense outlined :readonly="mode === 'query'" type="textarea" autogrow rows="2" />
              <div class="row justify-end q-mt-xs">
                <q-btn v-if="plan.improvementResult" color="primary" icon="edit" size="xs" @click="showImprovementResult(index)" label="编辑" flat dense />
                <q-btn v-else color="primary" icon="add" size="xs" @click="showImprovementResult(index)" label="添加" flat dense />
              </div>
            </div>
            <div class="col-1">
              <div class="row q-gutter-xs">
                <q-btn round dense color="negative" icon="remove" size="sm" @click="removeImprovementPlan(index)" v-if="mode !== 'query'" />
                <q-btn round dense color="primary" icon="arrow_forward" size="sm" @click="moveToAssetMeeting(index)" title="移入到资产会议" />
              </div>
            </div>

            <!-- 改善结果对话框 -->
            <q-dialog v-model="improvementResultDialog.show">
              <q-card style="width: 700px; max-width: 80vw;">
                <q-card-section>
                  <div class="text-h6">改善结果</div>
                </q-card-section>
                <q-card-section>
                  <q-input v-model="improvementPlans[improvementResultDialog.index].improvementResult"
                    type="textarea"
                    rows="5"
                    label="请输入改善结果详情"
                    :readonly="mode === 'query'"
                    outlined
                  />
                </q-card-section>
                <q-card-actions align="right">
                  <q-btn color="primary" label="确定" v-close-popup />
                </q-card-actions>
              </q-card>
            </q-dialog>

            <!-- 负责人选择对话框 -->
            <q-dialog v-model="responsibleDialog.show">
              <q-card style="width: 500px; max-width: 80vw;">
                <q-card-section class="bg-primary text-white">
                  <div class="text-h6">选择负责人</div>
                </q-card-section>
                <q-card-section>
                  <div class="row q-col-gutter-md q-mb-md">
                    <div class="col-12">
                      <q-select
                        :options="position_list"
                        v-model="responsibleDialog.Owner_Dept"
                        label="负责人职位"
                        outlined
                        @update:model-value="filterResponsibleAssignedBy"
                        :loading="!position_list || position_list.length === 0"
                        :disable="!position_list || position_list.length === 0"
                      >
                        <template v-slot:no-option>
                          <q-item>
                            <q-item-section class="text-grey">
                              正在加载职位列表...
                            </q-item-section>
                          </q-item>
                        </template>
                      </q-select>
                    </div>
                    <div class="col-12">
                      <q-select
                        :options="filteredNames.length > 0 ? filteredNames : responsibleDialog.assignedby_List"
                        v-model="responsibleDialog.Owner"
                        label="负责人"
                        outlined
                        :loading="false"
                        :disable="!responsibleDialog.Owner_Dept"
                        use-input
                        hide-selected
                        fill-input
                        input-debounce="0"
                        @filter="filterNames"
                        @click="ensureResponsibleList"
                      >
                        <template v-slot:no-option>
                          <q-item>
                            <q-item-section class="text-grey">
                              {{ responsibleDialog.Owner_Dept ? '正在加载负责人列表...' : '请先选择负责人职位' }}
                            </q-item-section>
                          </q-item>
                        </template>
                      </q-select>
                    </div>
                  </div>
                </q-card-section>
                <q-card-actions align="right">
                  <q-btn color="primary" label="确定" @click="confirmResponsible" />
                  <q-btn color="negative" label="取消" v-close-popup />
                </q-card-actions>
              </q-card>
            </q-dialog>
          </div>

          <!-- 改善照片上传对话框 -->
          <q-dialog v-model="improvementImageDialog.show">
            <q-card style="max-width: 90vw; width: 800px;">
              <q-card-section>
                <div class="text-h6">上传{{ improvementImageDialog.type === 'before' ? '改善前' : '改善后' }}照片</div>
              </q-card-section>
              <q-card-section>
                <q-uploader batch auto-upload :factory="factoryFn_Improvement_Picture"
                  @uploaded="handleUploadSuccess_Improvement_Picture" />
              </q-card-section>
            </q-card>
          </q-dialog>
        </q-card-section>
      </q-card>
    </div>

    <!-- 绩效达成总结模块 -->
    <div class="q-pa-md q-mt-md" v-if="reportType === '绿带问题解决A3报告'">
      <q-card class="q-mb-md" bordered>
        <q-card-section>
          <div class="text-h6">绩效达成总结</div>
          <div class="q-mt-md">
            <q-input v-model="performanceSummary.description" label="达成结果说明" type="textarea" rows="4"
              bg-color="grey-3" outlined :readonly="mode === 'query'" />

            <div class="q-mt-md">
              <div class="row items-center">
                <q-icon name="photo_library" size="24px" color="yellow" class="q-mr-sm" />
                <span>绩效照片上传（可上传多张）</span>
              </div>
              <q-uploader style="width:100%;height: 150px;" batch multiple auto-upload dense :factory="factoryFn_Performance_Images"
                @uploaded="handleUploadSuccess_Performance_Images" label="绩效照片" v-if="mode !== 'query' && mode !== 'myReport'" />

              <div v-if="mode === 'query' || (mode === 'myReport' && !performanceImageUploadMode)">
                <div v-if="performanceSummary.images && performanceSummary.images.length > 0" class="row q-col-gutter-md q-mt-md">
                  <div v-for="(image, index) in performanceSummary.images" :key="index" class="col-3 relative-position">
                    <q-img :src="image" style="height: 150px;" @click="openFile(image)" />
                    <q-btn v-if="mode === 'myReport'" round dense color="negative" icon="delete" class="absolute-top-right" @click="removePerformanceImage(index)" />
                  </div>
                </div>
                <div v-else class="text-grey-7 q-my-md">暂无绩效照片</div>
                <q-btn v-if="mode === 'myReport'" color="primary" icon="add" label="添加绩效照片" @click="enablePerformanceImageUpload" class="q-mt-md" />
              </div>

              <div v-if="mode === 'myReport' && performanceImageUploadMode" class="q-mt-md">
                <q-uploader style="width:100%;height: 150px;" batch multiple auto-upload dense :factory="factoryFn_Performance_Images"
                  @uploaded="handleUploadSuccess_Performance_Images" label="添加绩效照片" />
                <q-btn color="negative" label="完成添加" @click="cancelPerformanceImageUpload" class="q-mt-sm" />
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 必填字段提示 -->
    <div v-if="mode === 'create'" class="q-pa-md bg-blue-1 rounded-borders q-mb-md">
      <div class="row items-center">
        <q-icon name="info" color="primary" size="24px" class="q-mr-sm" />
        <div class="text-body2">
          <strong>提交前请确保以下必填项已填写：</strong>
          <span class="text-primary q-ml-sm">工厂、部门、机台、设备单元、问题分类</span>
        </div>
      </div>
    </div>

    <div class="row q-gutter-md">
      <q-btn
        @click="submitForm"
        color="primary"
        label="提交"
        class="submit-button"
        icon="send"
        v-if="mode !== 'query' || mode === 'myReport'"
      />
      <q-btn
        @click="shareReport"
        color="secondary"
        icon="share"
        label="分享报告"
        v-if="mode === 'query' || mode === 'myReport'"
      />
    </div>

    <!-- 资产会议录入对话框 -->
    <q-dialog v-model="assetMeetingDialog.show" persistent>
      <q-card style="width: 80%; max-width: 1000px;">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">移入到资产会议</div>
        </q-card-section>

        <q-card-section class="q-pa-md">
          <div class="row q-col-gutter-md">
            <div class="col-4">
              <q-select :options="machineOptions" v-model="assetMeetingDialog.data.line" label="机台" outlined dense />
            </div>
            <div class="col-4">
              <q-select :options="departmentOptions" v-model="assetMeetingDialog.data.Submitter_Dept" label="团队" outlined dense />
            </div>
            <div class="col-4">
              <q-input v-model="assetMeetingDialog.data.Submitter" label="提交人" outlined dense />
            </div>
          </div>

          <div class="row q-col-gutter-md q-mt-md">
            <div class="col-4">
              <q-select :options="categoryOptions" v-model="assetMeetingDialog.data.Problem_category" label="问题分类" outlined dense />
            </div>
            <div class="col-4">
              <q-select :options="['自然劣化', '强制劣化']" v-model="assetMeetingDialog.data.Deterioration_Type" label="劣化分类" outlined dense />
            </div>
          </div>

          <div class="q-mt-md">
            <q-input v-model="assetMeetingDialog.data.Problem_Desc" label="问题描述" type="textarea" outlined autogrow />
          </div>

          <div class="q-mt-md">
            <q-input v-model="assetMeetingDialog.data.Problem_Rootcuase" label="根本原因" type="textarea" outlined autogrow />
          </div>

          <div class="q-mt-md">
            <q-input v-model="assetMeetingDialog.data.Problem_Solution" label="解决方案" type="textarea" outlined autogrow />
          </div>

          <div class="q-mt-md">
            <q-input v-model="assetMeetingDialog.data.Problem_Prevention" label="预防措施" type="textarea" outlined autogrow />
          </div>

          <div class="row q-col-gutter-md q-mt-md">
            <div class="col-4">
              <q-select
                :options="position_list"
                v-model="assetMeetingDialog.data.Owner_Dept"
                label="负责人职位"
                outlined
                dense
                @update:model-value="filterAssetMeetingAssignedBy"
                :loading="!position_list || position_list.length === 0"
                :disable="!position_list || position_list.length === 0"
              >
                <template v-slot:no-option>
                  <q-item>
                    <q-item-section class="text-grey">
                      正在加载职位列表...
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>
            <div class="col-4">
              <q-select
                :options="assetMeetingDialog.filteredNames.length > 0 ? assetMeetingDialog.filteredNames : assetMeetingDialog.assignedby_List"
                v-model="assetMeetingDialog.data.Owner"
                label="负责人"
                outlined
                dense
                :loading="false"
                :disable="!assetMeetingDialog.data.Owner_Dept"
                use-input
                hide-selected
                fill-input
                input-debounce="0"
                @filter="filterAssetMeetingNames"
                @click="ensureAssetMeetingResponsibleList"
              >
                <template v-slot:no-option>
                  <q-item>
                    <q-item-section class="text-grey">
                      {{ assetMeetingDialog.data.Owner_Dept ? '正在加载负责人列表...' : '请先选择负责人职位' }}
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>
            <div class="col-4">
              <q-select :options="['中', '低', '高', 'T']" v-model="assetMeetingDialog.data.Priority" label="优先级" outlined dense />
            </div>
          </div>

          <div class="row q-col-gutter-md q-mt-md">
            <div class="col-4">
              <q-input v-model="assetMeetingDialog.data.Completed_Date" label="计划完成日期" outlined dense type="date" />
            </div>
            <div class="col-4">
              <q-select :options="['完成', '进行中', '未开始', '删除']" v-model="assetMeetingDialog.data.Status" label="状态" outlined dense />
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn color="primary" label="提交" @click="submitAssetMeeting" />
          <q-btn color="negative" label="取消" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<style scoped>
/* 必填字段标识样式 */
:deep(.q-field__label) {
  font-weight: 500;
}

/* 必填字段标识中的星号样式 */
:deep(.q-field__label:contains("*必须")) {
  color: #1976d2;
}

/* 错误状态下的必填字段样式 */
:deep(.q-field--error .q-field__label) {
  color: #c10015 !important;
}

/* 必填字段提示样式 */
.required-field-hint {
  color: #c10015;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
}

.required-field-hint .q-icon {
  margin-right: 4px;
}

/* 提交按钮样式优化 */
.submit-button {
  min-width: 120px;
  font-weight: 600;
}
</style>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import uploadURL from '../../utils/uploadURL'
export default {
  name: 'CI_Management',
  components: {
    BaseContent,
  },
  props: {
    A3_ID: {
      type: String,
      default: ''
    },
    mode: {
      type: String,
      default: 'create'
    }
  },
  data() {
    return {
      webURL_upload: '',
      reportType: '绿带问题解决A3报告',
      reportTypeOptions: ['蓝带问题解决5WHY报告', '绿带问题解决A3报告'],
      problemPictureEditMode: false,
      attachmentUploadMode: false,
      whyAnalysisUploadMode: false,
      performanceImageUploadMode: false,
      showValidationErrors: false, // 控制是否显示验证错误
      problemDefinition: {
        factory: null,
        department: null,
        machine: null,
        unit: null,
        category: null,
        teamLeader: '',
        teamMembers: '',
        problemDescription: '',
        whyImportant: '',
        when: '',
        howOften: '',
        where: '',
        photoDescription: '',
        url: '',
        attachments: [],
        CreateBy: localStorage.getItem('account'),
        ID:null
      },
      metrics: [],
      workPrinciples: [],
      workPrinciplesSelectedIndex: 0,
      uploadDialog: false,
      improvementImageDialog: {
        show: false,
        type: 'before',
        index: 0
      },
      improvementResultDialog: {
        show: false,
        index: 0
      },
      responsibleDialog: {
        show: false,
        index: 0,
        Owner_Dept: '',
        Owner: '',
        assignedby_List: []
      },
      filteredNames: [],
      improvementPlans: [],
      performanceSummary: {
        description: '',
        images: []
      },
      assetMeetingDialog: {
        show: false,
        index: 0,
        assignedby_List: [],
        filteredNames: [],
        initializing: false, // 添加初始化标志
        data: {
          line: '',
          Submitter: '',
          Submitter_Dept: '',
          Problem_category: '',
          Deterioration_Type: '自然劣化',
          Problem_Desc: '',
          Problem_Rootcuase: '',
          Problem_Solution: '',
          Problem_Prevention: '',
          Owner_Dept: '',
          Owner: '',
          Priority: '中',
          Completed_Date: '',
          Status: '未开始'
        }
      },
      position_list: [],
      employee_List: [],
      rootCauseAnalysis: {
        human: [],
        machine: [],
        material: [],
        method: [],
        environment: [],
        topIssues: [],
        whyAnalysis: {
          images: [],
          description: ''
        },
      },
      factoryOptions: [
        '南京南厂',
      ],
      departmentOptions: [
        '生产运作',
        '工程维修',
        '质量部门',
        '物流部门',
      ],
      machineOptions: [
        'ND05',
        'ND06',
        'ND07',
        'ND08',
        'ND09',
        'ND34'
      ],
      unitOptions: [
        '单元1',
        '单元2',
        '单元3'
      ],
      categoryOptions: [
        '机械问题',
        '电气问题',
        '胶水问题',
        '工艺问题'
      ]
    }
  },
  mounted() {
    this.webURL_upload = uploadURL
    this.machineOptions = localStorage.getItem('lineArray').split(',')
    this.$nextTick(() => {
      console.log('mode', this.mode)
      console.log('A3_ID', this.A3_ID)
      this.getMachineUnit()
      this.getPositionList()
      this.getEmployeeList()
      if (this.mode === 'edit' || this.mode === 'query' || this.mode==='myReport') {
        this.getA3Report()
      }
    })

  },



  methods: {
    getMachineUnit() {
      const _this = this
      this.$http.get('ciProject/getMachineUnit').then(function (response) {
        console.log('response', response)
        if (response.data.success) {
          const data = response.data.data
          console.log('data:', data)
          _this.unitOptions =data
        } else {
          _this.$q.notify({
            type: 'negative',
            message: response.data.message || '获取设备单元失败',
            position: 'top'
          })
        }
      })
    },

    getA3Report() {
      const _this = this
      this.$http.get('ciProject/a3_report/' + this.A3_ID).then(function (response) {
        console.log('response', response)
        if (response.data.success) {
          const data = response.data.data
          _this.reportType = data.reportType || '绿带问题解决A3报告'
          _this.problemDefinition = data.problemDefinition
          _this.metrics = data.metrics
          _this.workPrinciples = data.workPrinciples
          _this.rootCauseAnalysis = data.rootCause
          _this.improvementPlans = data.improvementPlans || []
          _this.performanceSummary = data.performanceSummary || { description: '', images: [] }
          console.log('加载的改进方案数据:', _this.improvementPlans)
          console.log('加载的绩效达成总结数据:', _this.performanceSummary)
        } else {
          _this.$q.notify({
            type: 'negative',
            message: response.data.message || '获取A3报告失败',
            position: 'top'
          })
        }
      }).catch(function (error) {
        console.error('获取A3报告失败:', error)
        _this.$q.notify({
          type: 'negative',
          message: '获取A3报告失败，请稍后重试',
          position: 'top'
        })
      })
    },
    handleReportTypeChange() {
      // 根据报告类型显示/隐藏相应模块
      console.log('报告类型变更为:', this.reportType)

      // 蓝带报告只需要问题定义、5Why分析和改进方案
      if (this.reportType === '蓝带问题解决5WHY报告') {
        // 清空工作原理数据
        this.workPrinciples = []
        // 清空鱼骨图分析数据
        this.rootCauseAnalysis.human = []
        this.rootCauseAnalysis.machine = []
        this.rootCauseAnalysis.material = []
        this.rootCauseAnalysis.method = []
        this.rootCauseAnalysis.environment = []
        // 清空绩效达成总结
        this.performanceSummary = {
          description: '',
          images: []
        }
      }
    },

    addMetric() {
      this.metrics.push({
        name: '',
        baseline: '',
        goal: '',
        actual: '',
        unit: ''
      })
    },
    removeMetric(index) {
      this.metrics.splice(index, 1)
    },
    addWorkPrinciple() {
      this.workPrinciples.push({
        id: Date.now(),
        name: '',
        mediaFiles: [],
        mediaDescription: '',
        settingsAndDescriptions: []
      })
    },
    addSettingAndDescription(index) {
      this.workPrinciples[index].settingsAndDescriptions.push({
        parameter: '',
        currentValue: '',
        standardValue: '',
        item: '',
        description: ''
      })
    },
    removeSettingAndDescription(principleIndex, itemIndex) {
      this.workPrinciples[principleIndex].settingsAndDescriptions.splice(itemIndex, 1)
    },
    addIssue(type) {
      this.rootCauseAnalysis[type].push({ description: '' })
    },
    removeIssue(type, index) {
      this.rootCauseAnalysis[type].splice(index, 1)
    },
    addTopIssue() {
      this.rootCauseAnalysis.topIssues.push({ description: '', percentage: '0' })
    },
    removeTopIssue(index) {
      this.rootCauseAnalysis.topIssues.splice(index, 1)
    },
    removeWorkPrinciple(index) {
      this.workPrinciples.splice(index, 1)
    },
    factoryFn_Problem_Picture(files) {
      var _this = this
      _this.prompt_uploadFile = true
      return {
        url: this.webURL_upload + '?system=A3_Problem_Solve',
        method: 'POST'
      }
    },
    handleUploadSuccess_Problem_Picture(response) {
      const _this = this
      console.log('handleUploadSuccess_Problem_Picture', response)
      // 只取第一个文件，因为问题现象照片只需要一张
      // 服务器直接返回了URL字符串，不需要解析response对象
      const responseURL = response.files[0].xhr.responseText
      _this.problemDefinition.url = responseURL
    },
    factoryFn_Principle_Picture(files) {
      var _this = this
      _this.prompt_uploadFile = true
      return {
        url: this.webURL_upload + '?system=A3_Work_Principle',
        method: 'POST'
      }
    },

    factoryFn_WhyAnalysis(files) {
      var _this = this
      _this.prompt_uploadFile = true
      return {
        url: this.webURL_upload + '?system=A3_Why_Analysis',
        method: 'POST'
      }
    },
    factoryFn_Problem_Attachments(files) {
      var _this = this
      _this.prompt_uploadFile = true
      return {
        url: this.webURL_upload + '?system=A3_Problem_Attachments',
        method: 'POST'
      }
    },
    handleUploadSuccess_Problem_Attachments(response) {
      const _this = this
      console.log('handleUploadSuccess_Problem_Attachments', response)
      // 处理多个文件上传
      response.files.forEach(file => {
        // 服务器直接返回了URL字符串，不需要解析response对象
        const responseURL = file.xhr.responseText
        const fileName = file.name
        if (!Array.isArray(_this.problemDefinition.attachments)) {
          _this.problemDefinition.attachments = []
        }
        _this.problemDefinition.attachments.push({ name: fileName, url: responseURL })
      })
    },
    handleUploadSuccess_WhyAnalysis(response) {
      const _this = this
      console.log('handleUploadSuccess_WhyAnalysis', response)
      // 处理多个文件上传
      response.files.forEach(file => {
        // 服务器直接返回了URL字符串，不需要解析response对象
        const responseURL = file.xhr.responseText
        if (!Array.isArray(_this.rootCauseAnalysis.whyAnalysis.images)) {
          _this.rootCauseAnalysis.whyAnalysis.images = []
        }
        _this.rootCauseAnalysis.whyAnalysis.images.push(responseURL)
      })
    },
    addImprovementPlan() {
      this.improvementPlans.push({
        description: '',
        actionPlan: '',
        effort: 0,
        benefit: 0,
        totalScore: 'C',
        responsible: '',
        dueDate: '',
        beforeImage: '',
        afterImage: '',
        improvementResult: '',
        responsibleData: {
          position: '',
          name: ''
        }
      })
    },

    showImprovementResult(index) {
      this.improvementResultDialog.index = index
      this.improvementResultDialog.show = true
    },
    removeImprovementPlan(index) {
      this.improvementPlans.splice(index, 1)
    },
    uploadImprovementImage(index, type) {
      this.improvementImageDialog.index = index
      this.improvementImageDialog.type = type
      this.improvementImageDialog.show = true
    },
    factoryFn_Improvement_Picture(files) {
      var _this = this
      _this.prompt_uploadFile = true
      return {
        url: this.webURL_upload + '?system=A3_Improvement_Pictures',
        method: 'POST'
      }
    },
    handleUploadSuccess_Improvement_Picture(response) {
      const _this = this
      console.log('handleUploadSuccess_Improvement_Picture', response)
      // 只取第一个文件，因为改进方案的前后照片只需要一张
      // 服务器直接返回了URL字符串，不需要解析response对象
      const responseURL = response.files[0].xhr.responseText
      if (_this.improvementImageDialog.type === 'before') {
        _this.improvementPlans[_this.improvementImageDialog.index].beforeImage = responseURL
      } else {
        _this.improvementPlans[_this.improvementImageDialog.index].afterImage = responseURL
      }
      _this.improvementImageDialog.show = false
    },
    handleUploadSuccess_Principle_Picture(response) {
      const _this = this
      console.log('handleUploadSuccess_Principle_Picture', response)
      // 处理多个文件上传
      response.files.forEach(file => {
        // 服务器直接返回了URL字符串，不需要解析response对象
        const responseURL = file.xhr.responseText
        const fileName = file.name
        _this.workPrinciples[this.workPrinciplesSelectedIndex]['mediaFiles'].push({ "name": fileName, "url": responseURL })
      })
      this.uploadDialog = false
    },
    openUploadDialog(index) {
      // this.$set(this.uploadDialog, principle.id, true)
      this.workPrinciplesSelectedIndex = ''
      this.workPrinciplesSelectedIndex = index
      this.uploadDialog = true
    },
    removeFile(principle, index) {
      this.$q.dialog({
        title: '确认删除',
        message: '确定要删除这个文件吗？',
        cancel: true,
        persistent: true
      }).onOk(() => {
        principle.mediaFiles.splice(index, 1)
      })
    },
    openFile(url) {
      window.open(url, '_blank')
    },

    // 分享报告方法
    shareReport() {
      // 生成分享链接
      const currentHost = window.location.origin
      const shareLink = `${currentHost}/#/CI_Management?id=${this.A3_ID}&mode=query&mail=true`

      // 创建对话框显示分享链接
      this.$q.dialog({
        title: '分享报告',
        message: '你可以复制下面的链接分享给他人，点击链接可以直接查看报告：',
        html: true,
        prompt: {
          model: shareLink,
          type: 'text',
          readonly: true
        },
        ok: {
          label: '复制链接',
          flat: true,
          color: 'primary'
        },
        cancel: {
          label: '取消',
          flat: true,
          color: 'negative'
        },
        persistent: true
      }).onOk(() => {
        // 复制链接到剪贴板 - 使用兼容性更好的方法
        this.copyToClipboard(shareLink)
      })
    },

    // 兼容性更好的复制到剪贴板方法
    copyToClipboard(text) {
      // 首先尝试使用现代的 Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
          console.log('使用 Clipboard API 复制成功')
          this.$q.notify({
            type: 'positive',
            message: '链接已复制到剪贴板',
            position: 'top'
          })
        }).catch(err => {
          console.error('Clipboard API 复制失败:', err)
          // 如果 Clipboard API 失败，使用备选方案
          this.fallbackCopyTextToClipboard(text)
        })
      } else {
        // 如果不支持 Clipboard API，直接使用备选方案
        console.log('不支持 Clipboard API，使用备选方案')
        this.fallbackCopyTextToClipboard(text)
      }
    },

    // 备选的复制方法，使用传统的 execCommand
    fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text

      // 避免在页面上显示
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      textArea.style.opacity = '0'

      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        if (successful) {
          console.log('使用 execCommand 复制成功')
          this.$q.notify({
            type: 'positive',
            message: '链接已复制到剪贴板',
            position: 'top'
          })
        } else {
          throw new Error('execCommand 返回 false')
        }
      } catch (err) {
        console.error('execCommand 复制失败:', err)
        this.$q.notify({
          type: 'negative',
          message: '复制链接失败，请手动复制链接',
          position: 'top'
        })
      } finally {
        document.body.removeChild(textArea)
      }
    },

    // 问题现象照片编辑相关方法
    enableProblemPictureEdit() {
      this.problemPictureEditMode = true
    },
    cancelProblemPictureEdit() {
      this.problemPictureEditMode = false
    },

    // 问题定义附件相关方法
    enableAttachmentUpload() {
      this.attachmentUploadMode = true
    },
    cancelAttachmentUpload() {
      this.attachmentUploadMode = false
    },
    removeAttachment(index) {
      this.$q.dialog({
        title: '确认删除',
        message: '确定要删除这个附件吗？',
        cancel: true,
        persistent: true
      }).onOk(() => {
        this.problemDefinition.attachments.splice(index, 1)
      })
    },

    // 5WHY分析图片相关方法
    enableWhyAnalysisUpload() {
      this.whyAnalysisUploadMode = true
    },
    cancelWhyAnalysisUpload() {
      this.whyAnalysisUploadMode = false
    },
    removeWhyAnalysisImage(index) {
      this.$q.dialog({
        title: '确认删除',
        message: '确定要删除这张图片吗？',
        cancel: true,
        persistent: true
      }).onOk(() => {
        this.rootCauseAnalysis.whyAnalysis.images.splice(index, 1)
      })
    },

    // 改进方案照片相关方法
    removeImprovementImage(index, type) {
      this.$q.dialog({
        title: '确认删除',
        message: '确定要删除这张图片吗？',
        cancel: true,
        persistent: true
      }).onOk(() => {
        if (type === 'before') {
          this.improvementPlans[index].beforeImage = ''
        } else {
          this.improvementPlans[index].afterImage = ''
        }
      })
    },

    // 绩效达成总结照片相关方法
    enablePerformanceImageUpload() {
      this.performanceImageUploadMode = true
    },
    cancelPerformanceImageUpload() {
      this.performanceImageUploadMode = false
    },
    removePerformanceImage(index) {
      this.$q.dialog({
        title: '确认删除',
        message: '确定要删除这张图片吗？',
        cancel: true,
        persistent: true
      }).onOk(() => {
        this.performanceSummary.images.splice(index, 1)
      })
    },
    factoryFn_Performance_Images(files) {
      var _this = this
      _this.prompt_uploadFile = true
      return {
        url: this.webURL_upload + '?system=A3_Performance_Images',
        method: 'POST'
      }
    },
    handleUploadSuccess_Performance_Images(response) {
      const _this = this
      console.log('handleUploadSuccess_Performance_Images', response)
      // 处理多个文件上传
      response.files.forEach(file => {
        // 服务器直接返回了URL字符串，不需要解析response对象
        const responseURL = file.xhr.responseText
        if (!Array.isArray(_this.performanceSummary.images)) {
          _this.performanceSummary.images = []
        }
        _this.performanceSummary.images.push(responseURL)
      })
    },

    submitForm() {
      // 验证必填字段
      const requiredFields = [
        { field: 'factory', name: '工厂' },
        { field: 'department', name: '部门' },
        { field: 'machine', name: '机台' },
        { field: 'unit', name: '设备单元' },
        { field: 'category', name: '问题分类' }
      ]

      const missingFields = []

      // 检查每个必填字段
      requiredFields.forEach(({ field, name }) => {
        if (!this.problemDefinition[field]) {
          missingFields.push(name)
        }
      })

      // 如果有未填写的必填字段，显示错误提示
      if (missingFields.length > 0) {
        this.showValidationErrors = true

        this.$q.notify({
          type: 'negative',
          message: `请填写以下必填项：${missingFields.join('、')}`,
          position: 'top',
          timeout: 5000,
          actions: [
            {
              label: '确定',
              color: 'white'
            }
          ]
        })

        // 滚动到页面顶部，让用户看到错误的字段
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        })

        return
      }

      // 重置验证错误状态
      this.showValidationErrors = false

      // 转换metrics字段名称以匹配API期望格式
      const transformedMetrics = this.metrics.map(metric => ({
        MetricName: metric.name,
        Baseline: metric.baseline,
        Goal: metric.goal,
        Actual: metric.actual,
        Units: metric.unit
      }))

      const postParam = {
        reportType: this.reportType,
        problemDefinition: this.problemDefinition,
        Metrics: transformedMetrics,
        WorkPrinciples: this.workPrinciples,
        RootCauseAnalysis: this.rootCauseAnalysis,
        ImprovementPlans: this.improvementPlans,
        PerformanceSummary: this.performanceSummary
      }

      const _this = this
      console.log('postParam', postParam)
      this.$http.post('ciProject/a3report', postParam).then(function (response) {
        console.log('response', response)
        if (response.data.success) {
          _this.$q.notify({
            type: 'positive',
            message: `A3报告提交成功`,
            position: 'top'
          })

          // 提交成功后跳转到"我的报告"页面
          _this.navigateToMyReports()
        } else {
          _this.$q.notify({
            type: 'negative',
            message: response.data.message || '提交失败',
            position: 'top'
          })
        }
      }).catch(function (error) {
        console.error('提交失败:', error)
        _this.$q.notify({
          type: 'negative',
          message: '提交失败，请稍后重试',
          position: 'top'
        })
      })
    },

    // 跳转到"我的报告"页面
    navigateToMyReports() {
      // 如果是在对话框中，关闭对话框
      if (this.$parent && this.$parent.dialog) {
        this.$parent.dialog = false
      }

      // 查找根组件（CI_Management.vue）
      let rootComponent = this;
      while (rootComponent.$parent && rootComponent.$parent.$options.name !== 'CI_Management') {
        rootComponent = rootComponent.$parent;
      }

      // 如果找到了根组件，直接设置其tab属性
      if (rootComponent.$parent && rootComponent.$parent.$options.name === 'CI_Management') {
        console.log('找到根组件，设置tab为myReport');
        rootComponent.$parent.tab = 'myReport';
      } else {
        console.log('未找到根组件，尝试通过路由跳转');
        // 如果无法找到根组件，则通过路由跳转并强制刷新页面
        window.location.href = '/#/CI_Management?tab=myReport';
      }

      // 通知用户已跳转
      this.$q.notify({
        type: 'info',
        message: '已跳转到"我的报告"页面',
        position: 'top',
        timeout: 2000
      })
    },

    // 资产会议相关方法
    moveToAssetMeeting(index) {
      const plan = this.improvementPlans[index]
      this.assetMeetingDialog.index = index

      // 获取负责人职位信息
      let ownerPosition = '';
      let ownerName = plan.responsible || '';

      // 如果存在保存的职位信息，则使用它
      if (plan.responsibleData && plan.responsibleData.position) {
        ownerPosition = plan.responsibleData.position;
        ownerName = plan.responsibleData.name || plan.responsible;
      }

      console.log('移入资产会议 - 负责人职位:', ownerPosition);
      console.log('移入资产会议 - 负责人姓名:', ownerName);

      // 设置初始化标志，防止在筛选时清空负责人
      this.assetMeetingDialog.initializing = true;

      // 映射数据
      this.assetMeetingDialog.data = {
        line: this.problemDefinition.machine || '',
        Submitter: localStorage.getItem('account') || '',
        Submitter_Dept: this.problemDefinition.department || '',
        Problem_category: this.mapProblemCategory(this.problemDefinition.category),
        Deterioration_Type: '自然劣化',
        Problem_Desc: plan.description || '',
        Problem_Rootcuase: '',
        Problem_Solution: plan.actionPlan || '',
        Problem_Prevention: plan.improvementResult || '',
        Owner_Dept: ownerPosition,
        Owner: ownerName,
        Priority: this.mapPriority(plan.totalScore),
        Completed_Date: plan.dueDate || '',
        Status: '未开始'
      }

      // 如果有职位信息，立即筛选负责人列表
      if (ownerPosition) {
        this.$nextTick(() => {
          // 筛选负责人列表
          this.filterAssetMeetingAssignedBy();

          // 确保负责人在列表中
          if (!this.assetMeetingDialog.assignedby_List.includes(ownerName)) {
            this.assetMeetingDialog.assignedby_List.push(ownerName);
          }

          // 强制更新UI
          this.$forceUpdate();

          // 重置初始化标志
          setTimeout(() => {
            this.assetMeetingDialog.initializing = false;
          }, 500);
        });
      } else {
        // 重置初始化标志
        setTimeout(() => {
          this.assetMeetingDialog.initializing = false;
        }, 500);
      }

      this.assetMeetingDialog.show = true
    },

    mapProblemCategory(category) {
      // 映射问题分类
      const categoryMap = {
        '机械问题': '效率',
        '电气问题': '效率',
        '胶水问题': '质量',
        '工艺问题': '质量'
      }
      return categoryMap[category] || '效率'
    },

    mapPriority(score) {
      // 映射优先级
      const priorityMap = {
        'A': '高',
        'B': '中',
        'C': '低',
        'D': '低'
      }
      return priorityMap[score] || '中'
    },

    getPositionList() {
      const _this = this
      this.$http.get('forminput/getDepartment').then(function (response) {
        console.log('getDepartment', response)
        if (response.data) {
          _this.position_list = response.data

          // 如果负责人选择对话框是打开的，确保UI更新
          if (_this.responsibleDialog.show) {
            // 强制更新UI
            _this.$forceUpdate()
          }
        }
      }).catch(function (error) {
        console.error('获取职位列表失败:', error)
      })
    },

    getEmployeeList() {
      const _this = this
      this.$http.get('forminput/getEmployee').then(function (response) {
        console.log('getEmployee', response)
        if (response.data) {
          _this.employee_List = response.data
        }
      }).catch(function (error) {
        console.error('获取员工列表失败:', error)
      })
    },

    filterAssetMeetingAssignedBy() {
      const _this = this
      _this.assetMeetingDialog.assignedby_List = []

      // 保存当前负责人
      const currentOwner = _this.assetMeetingDialog.data.Owner;

      // 只有在用户手动选择职位时才清空负责人，而不是在初始化时
      if (!_this.assetMeetingDialog.initializing) {
        _this.assetMeetingDialog.data.Owner = '' // 清空负责人
      }

      _this.assetMeetingDialog.filteredNames = [] // 清空筛选后的名称列表

      if (!_this.assetMeetingDialog.data.Owner_Dept) {
        console.log('未选择职位，不进行筛选')
        return
      }

      console.log('筛选资产会议负责人，职位:', _this.assetMeetingDialog.data.Owner_Dept)
      console.log('当前负责人:', currentOwner)

      // 如果员工列表为空，直接返回
      if (!_this.employee_List || _this.employee_List.length === 0) {
        console.log('员工列表为空，无法筛选')
        return
      }

      // 首先尝试按职位匹配
      let hasMatched = false;
      for (let i = 0; i < _this.employee_List.length; i++) {
        const emp = _this.employee_List[i]
        if (emp && emp.Employee_Name && emp.Position === _this.assetMeetingDialog.data.Owner_Dept) {
          _this.assetMeetingDialog.assignedby_List.push(emp.Employee_Name)
          hasMatched = true;
        }
      }

      // 如果没有匹配到任何员工，则添加所有员工作为备选
      if (!hasMatched) {
        console.log('未找到匹配职位的员工，添加所有员工作为备选')
        for (let i = 0; i < _this.employee_List.length; i++) {
          const emp = _this.employee_List[i]
          if (emp && emp.Employee_Name) {
            _this.assetMeetingDialog.assignedby_List.push(emp.Employee_Name)
          }
        }
      }

      console.log('筛选后的资产会议负责人列表:', _this.assetMeetingDialog.assignedby_List)

      // 如果列表仍为空，添加一些测试数据
      if (_this.assetMeetingDialog.assignedby_List.length === 0) {
        console.log('资产会议负责人列表为空，添加测试数据')
        _this.assetMeetingDialog.assignedby_List = [
          '测试员工1',
          '测试员工2',
          '测试员工3',
          '测试员工4'
        ]
      }

      // 如果是初始化阶段，并且有预设的负责人，则恢复负责人
      if (_this.assetMeetingDialog.initializing && currentOwner) {
        console.log('恢复预设的负责人:', currentOwner);
        _this.assetMeetingDialog.data.Owner = currentOwner;

        // 确保负责人在列表中
        if (!_this.assetMeetingDialog.assignedby_List.includes(currentOwner)) {
          _this.assetMeetingDialog.assignedby_List.push(currentOwner);
        }
      }

      // 强制更新UI
      _this.$forceUpdate()
    },

    // 确保资产会议负责人列表已加载
    ensureAssetMeetingResponsibleList() {
      // 如果负责人列表为空，但已选择职位，则重新筛选
      if (this.assetMeetingDialog.data.Owner_Dept &&
          (!this.assetMeetingDialog.assignedby_List || this.assetMeetingDialog.assignedby_List.length === 0)) {
        console.log('资产会议负责人列表为空，重新筛选')
        this.filterAssetMeetingAssignedBy()
      }

      // 如果负责人列表仍为空，添加测试数据
      if (!this.assetMeetingDialog.assignedby_List || this.assetMeetingDialog.assignedby_List.length === 0) {
        console.log('资产会议负责人列表仍为空，添加测试数据')
        this.assetMeetingDialog.assignedby_List = [
          '测试员工1',
          '测试员工2',
          '测试员工3',
          '测试员工4'
        ]
      }
    },

    // 筛选资产会议负责人名称
    filterAssetMeetingNames(val, update) {
      // 确保负责人列表已加载
      this.ensureAssetMeetingResponsibleList()

      if (!this.assetMeetingDialog.assignedby_List || this.assetMeetingDialog.assignedby_List.length === 0) {
        return
      }

      if (val === '') {
        update(() => {
          this.assetMeetingDialog.filteredNames = this.assetMeetingDialog.assignedby_List
        })
        return
      }

      update(() => {
        const needle = val.toLowerCase()
        this.assetMeetingDialog.filteredNames = this.assetMeetingDialog.assignedby_List.filter(
          v => v.toLowerCase().indexOf(needle) > -1
        )
      })
    },

    submitAssetMeeting() {
      const _this = this

      // 验证必填字段
      if (!this.assetMeetingDialog.data.line ||
          !this.assetMeetingDialog.data.Problem_Desc ||
          !this.assetMeetingDialog.data.Problem_category) {
        this.$q.notify({
          type: 'negative',
          message: '请填写必要的字段（机台、问题分类、问题描述）',
          position: 'top'
        })
        return
      }

      // 提交到资产会议系统
      this.$http.post('forminput/insert_asset_meeting', this.assetMeetingDialog.data).then(function (response) {
        console.log('response', response)
        if (response.data === '添加成功') {
          _this.$q.notify({
            type: 'positive',
            message: '已成功移入到资产会议',
            position: 'top'
          })
          _this.assetMeetingDialog.show = false
        } else {
          _this.$q.notify({
            type: 'negative',
            message: '移入资产会议失败',
            position: 'top'
          })
        }
      }).catch(function (error) {
        console.error('移入资产会议失败:', error)
        _this.$q.notify({
          type: 'negative',
          message: '移入资产会议失败，请稍后重试',
          position: 'top'
        })
      })
    },

    // 负责人选择相关方法
    showResponsibleDialog(index) {
      if (this.mode === 'query') return

      this.responsibleDialog.index = index
      this.responsibleDialog.Owner_Dept = ''
      this.responsibleDialog.Owner = ''
      this.responsibleDialog.assignedby_List = []
      this.filteredNames = []

      // 确保已加载员工列表
      if (!this.employee_List || this.employee_List.length === 0) {
        this.getEmployeeList()
      }

      // 确保已加载职位列表
      if (!this.position_list || this.position_list.length === 0) {
        this.getPositionList()
      }

      // 添加一些测试数据，以防API调用失败
      if (this.employee_List.length === 0) {
        console.log('添加测试员工数据')
        this.employee_List = [
          { Employee_Name: '测试员工1', Position: '资产主管' },
          { Employee_Name: '测试员工2', Position: '电气工程师' },
          { Employee_Name: '测试员工3', Position: '机械工程师' },
          { Employee_Name: '测试员工4', Position: '工艺工程师' }
        ]
      }

      this.responsibleDialog.show = true
    },

    async filterResponsibleAssignedBy() {
      const _this = this
      _this.responsibleDialog.assignedby_List = []
      _this.responsibleDialog.Owner = '' // 清空负责人
      _this.filteredNames = [] // 清空筛选后的名称列表

      if (!_this.responsibleDialog.Owner_Dept) {
        console.log('未选择职位，不进行筛选')
        return
      }

      console.log('筛选负责人，职位:', _this.responsibleDialog.Owner_Dept)

      // 如果员工列表为空或未定义，先获取员工列表
      if (!_this.employee_List || _this.employee_List.length === 0) {
        console.log('员工列表为空，正在获取...')
        try {
          const response = await _this.$http.get('forminput/getEmployee')
          console.log('getEmployee 响应:', response)
          if (response.data) {
            _this.employee_List = response.data
            console.log('获取到员工列表:', _this.employee_List)
          }
        } catch (error) {
          console.error('获取员工列表失败:', error)
        }

        // 如果获取后仍为空，添加测试数据
        if (!_this.employee_List || _this.employee_List.length === 0) {
          console.log('添加测试员工数据')
          _this.employee_List = [
            { Employee_Name: '测试员工1', Position: '资产主管' },
            { Employee_Name: '测试员工2', Position: '电气工程师' },
            { Employee_Name: '测试员工3', Position: '机械工程师' },
            { Employee_Name: '测试员工4', Position: '工艺工程师' }
          ]
        }
      }

      console.log('开始筛选，员工列表长度:', _this.employee_List.length)
      console.log('员工列表数据:', JSON.stringify(_this.employee_List))

      // 首先尝试按职位匹配
      let hasMatched = false;
      for (let i = 0; i < _this.employee_List.length; i++) {
        const emp = _this.employee_List[i]
        if (emp && emp.Employee_Name && emp.Position === _this.responsibleDialog.Owner_Dept) {
          _this.responsibleDialog.assignedby_List.push(emp.Employee_Name)
          hasMatched = true;
        }
      }

      // 如果没有匹配到任何员工，则添加所有员工作为备选
      if (!hasMatched) {
        console.log('未找到匹配职位的员工，添加所有员工作为备选')
        for (let i = 0; i < _this.employee_List.length; i++) {
          const emp = _this.employee_List[i]
          if (emp && emp.Employee_Name) {
            _this.responsibleDialog.assignedby_List.push(emp.Employee_Name)
          }
        }
      }

      console.log('筛选后的负责人列表:', _this.responsibleDialog.assignedby_List)

      // 如果列表仍为空，添加一些测试数据
      if (_this.responsibleDialog.assignedby_List.length === 0) {
        console.log('负责人列表为空，添加测试数据')
        _this.responsibleDialog.assignedby_List = [
          '测试员工1',
          '测试员工2',
          '测试员工3',
          '测试员工4',
          '张三',
          '李四',
          '王五',
          '赵六'
        ]
      }

      // 强制更新UI
      _this.$forceUpdate()
    },

    // 确保负责人列表已加载
    ensureResponsibleList() {
      // 如果负责人列表为空，但已选择职位，则重新筛选
      if (this.responsibleDialog.Owner_Dept &&
          (!this.responsibleDialog.assignedby_List || this.responsibleDialog.assignedby_List.length === 0)) {
        console.log('负责人列表为空，重新筛选')
        this.filterResponsibleAssignedBy()
      }

      // 如果负责人列表仍为空，添加测试数据
      if (!this.responsibleDialog.assignedby_List || this.responsibleDialog.assignedby_List.length === 0) {
        console.log('负责人列表仍为空，添加测试数据')
        this.responsibleDialog.assignedby_List = [
          '测试员工1',
          '测试员工2',
          '测试员工3',
          '测试员工4',
          '张三',
          '李四',
          '王五',
          '赵六'
        ]
      }
    },

    filterNames(val, update) {
      // 确保负责人列表已加载
      this.ensureResponsibleList()

      if (!this.responsibleDialog.assignedby_List || this.responsibleDialog.assignedby_List.length === 0) {
        return
      }

      if (val === '') {
        update(() => {
          this.filteredNames = this.responsibleDialog.assignedby_List
        })
        return
      }

      update(() => {
        const needle = val.toLowerCase()
        this.filteredNames = this.responsibleDialog.assignedby_List.filter(
          v => v.toLowerCase().indexOf(needle) > -1
        )
      })
    },

    confirmResponsible() {
      if (!this.responsibleDialog.Owner_Dept || !this.responsibleDialog.Owner) {
        this.$q.notify({
          type: 'negative',
          message: '请选择负责人职位和负责人',
          position: 'top'
        })
        return
      }

      // 更新改进方案中的负责人，只显示姓名
      this.improvementPlans[this.responsibleDialog.index].responsible = this.responsibleDialog.Owner

      // 在改进方案中保存职位信息，但不显示
      if (!this.improvementPlans[this.responsibleDialog.index].responsibleData) {
        this.improvementPlans[this.responsibleDialog.index].responsibleData = {}
      }
      this.improvementPlans[this.responsibleDialog.index].responsibleData.position = this.responsibleDialog.Owner_Dept
      this.improvementPlans[this.responsibleDialog.index].responsibleData.name = this.responsibleDialog.Owner

      this.responsibleDialog.show = false
    },

    async getEmployeeList() {
      try {
        const _this = this
        console.log('开始获取员工列表')
        const response = await this.$http.get('forminput/getEmployee')
        console.log('getEmployee 响应:', response)

        if (response.data) {
          console.log('员工数据:', response.data)
          _this.employee_List = response.data

          // 直接在控制台输出员工列表，检查数据结构
          console.table(_this.employee_List)

          // 如果员工列表为空，添加测试数据
          if (!_this.employee_List || _this.employee_List.length === 0) {
            console.log('员工列表为空，添加测试数据')
            _this.employee_List = [
              { Employee_Name: '测试员工1', Position: '资产主管' },
              { Employee_Name: '测试员工2', Position: '电气工程师' },
              { Employee_Name: '测试员工3', Position: '机械工程师' },
              { Employee_Name: '测试员工4', Position: '工艺工程师' }
            ]
          }

          // 如果负责人选择对话框是打开的，立即筛选负责人列表
          if (_this.responsibleDialog.show && _this.responsibleDialog.Owner_Dept) {
            console.log('对话框打开中，立即筛选负责人')
            _this.filterResponsibleAssignedBy()
          }

          // 如果资产会议对话框是打开的，立即筛选负责人列表
          if (_this.assetMeetingDialog.show && _this.assetMeetingDialog.data.Owner_Dept) {
            _this.filterAssetMeetingAssignedBy()
          }

          // 强制更新UI
          _this.$forceUpdate()
        } else {
          console.error('获取员工列表失败: 响应数据为空')
          // 添加测试数据
          _this.employee_List = [
            { Employee_Name: '测试员工1', Position: '资产主管' },
            { Employee_Name: '测试员工2', Position: '电气工程师' },
            { Employee_Name: '测试员工3', Position: '机械工程师' },
            { Employee_Name: '测试员工4', Position: '工艺工程师' }
          ]
        }
      } catch (error) {
        console.error('获取员工列表失败:', error)
        // 添加测试数据
        this.employee_List = [
          { Employee_Name: '测试员工1', Position: '资产主管' },
          { Employee_Name: '测试员工2', Position: '电气工程师' },
          { Employee_Name: '测试员工3', Position: '机械工程师' },
          { Employee_Name: '测试员工4', Position: '工艺工程师' }
        ]
      }
    }
  }
}
</script>
