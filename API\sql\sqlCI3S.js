const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.Insert_CI_3S_Summary = function (data, callBack) {
    sqlexec.prototype.
        _query(`insert into CI_3S_Summary([Date]
      ,[Line]
      ,[Crew]
      ,[Unit]
      ,[Start_Time]
      ,[End_Time]
      ,[Team_Image]
      ,[Red_Area_Image]
      ,[Problem_Analysis_Image]
      ,[LinkID]
      ,[Process_Status]) values('${data['Date']}','${data['Line']}','${data['Crew']}','${data['Unit']}',
                        '${data['Start_Time']}','${data['End_Time']}','${data['Team_Image']}','${data['Red_Area_Image']}','${data['Problem_Analysis_Image']}',
                        '${data['LinkID']}','open')`, function (err, result) {

            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.Insert_CI_3S_Red_Statistics = function (data, callBack) {
    console.log(`insert into CI_3S_Red_Statistics values('${data['LinkID']}','${data['OP']}','${data['Red_Count']}','${data['Resolved_Count']}',
                        '${data['Pending_Count']}')`)
    sqlexec.prototype.
        _query(`insert into CI_3S_Red_Statistics values('${data['LinkID']}','${data['OP']}','${data['Red_Count']}','${data['Resolved_Count']}',
                        '${data['Pending_Count']}')`, function (err, result) {

            if (err) {
            }
            return callBack('已更新')
        })
}
sqlexec.prototype.Insert_CI_3S_Problem_List = function (linkid, data, callBack) {
    sqlexec.prototype.
        _query(`insert into CI_3S_Problem_List values('${linkid}','${data['Problem_Category']}','${data['Problem_Desc']}','${data['Root_Cause']}',
                        '${data['Corrective_Action']}','${data['Preventive_Measure']}','${data['Before_Image']}','${data['After_Image']}')`, function (err, result) {

            if (err) {
            }
            return callBack('已更新')
        })
}

//2024-7-29 新增Report_URL字段
sqlexec.prototype.query_CI_3S_Summary = function (line, callBack) {
    sqlexec.prototype.
        _query(`SELECT Report_URL,LinkID,format(Date,'yyyy-MM-dd')Date,Line,Crew,Unit,Start_Time,End_Time,Approver,Process_Status  FROM [CI_3S_Summary] where Line='${line}'`, function (err, result) {

            if (err) {
            }
            return callBack(result.recordset)
        })
}

//查询所有AM报告
sqlexec.prototype.query_CI_3S_Summary_All = function (callBack) {
    sqlexec.prototype.
        _query(`SELECT Report_URL,LinkID,format(Date,'yyyy-MM-dd')Date,Line,Crew,Unit,Start_Time,End_Time,Approver,Process_Status  FROM [CI_3S_Summary] ORDER BY Date DESC`, function (err, result) {

            if (err) {
            }
            return callBack(result.recordset)
        })
}

//2024-7-29 新增Report_URL字段
sqlexec.prototype.query_CI_3S_Summary_byLinkid = function (linkid, callBack) {
    sqlexec.prototype.
        _query(`SELECT format([Date],'yyyy-MM-dd')Date
      ,[Line]
      ,[Crew]
      ,[Unit]
      ,[Start_Time]
      ,[End_Time]
      ,[Team_Image]
      ,[Red_Area_Image]
      ,[Problem_Analysis_Image]
      ,[LinkID],Report_URL  FROM [dbo].[CI_3S_Summary] where LinkID='${linkid}'`, function (err, result) {

            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.query_CI_3S_Area_Base = function (callBack) {
    sqlexec.prototype.
        _query(`SELECT * FROM [dbo].[CI_3S_Area_Base]`, function (err, result) {

            if (err) { }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.query_CI_3S_Red_Statistics_byLinkid = function (linkid, callBack) {
    sqlexec.prototype.
        _query(`select  * from [dbo].[CI_3S_Red_Statistics]where LinkID='${linkid}'`, function (err, result) {

            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.query_CI_3S_Problem_List_byLinkid = function (linkid, callBack) {
    sqlexec.prototype.
        _query(`select  * from [dbo].[CI_3S_Problem_List]where LinkID='${linkid}'`, function (err, result) {

            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.query_CI_3S_Score_byLinkid = function (linkid, callBack) {
    sqlexec.prototype.
        _query(`select  * from [dbo].[CI_3S_Score]where LinkID='${linkid}'`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}


sqlexec.prototype.Insert_CI_3S_Score = function (data, callBack) {
    sqlexec.prototype.
        _query(`insert into CI_3S_Score values('${data['LinkID']}','${data['item']}','${data['score']}')`, function (err, result) {

            if (err) {
            }
            return callBack('已更新')
        })
}

sqlexec.prototype.update_CI_3S_Summary_byApprover = function (linkid,approver, callBack) {
    sqlexec.prototype.
        _query(`update CI_3S_Summary set Approver='${approver}',Process_Status='Approved' where LinkID='${linkid}'`, function (err, result) {

            if (err) {
            }
            return callBack('已更新')
        })
}

//2024-7-29 新增模块，用于更新报告地址
sqlexec.prototype.update_CI_3S_Report_URL = function (linkid, Report_URL, callBack) {
    sqlexec.prototype.
        _query(`update CI_3S_Summary set Report_URL='${Report_URL}' where LinkID='${linkid}'`, function (err, result) {

            if (err) {
            }
            return callBack('已更新')
        })
}

//HK_SYSTEM

sqlexec.prototype.CI_HK_Task_List = function (Line, Machine_Status, Date, Shift, callBack) {
       sqlexec.prototype.
           _query(`  select ROW_NUMBER() over (order by a.Task_ID asc) row_num,a.*,iif(b.check_Result is null,'待检',b.check_Result) checkResult,c.totalLSW checkItem from CI_HK_Task_List as a
left join CI_HK_Check_Data as b on a.Task_ID=b.Task_ID  and b.Date='${Date}' and b.Shift='${Shift}'
left join (select RelateID,count(*) totalLSW from LSW_Problem_Data group by RelateID) as c on c.RelateID=CONCAT_WS('-','${Date}','${Line}','${Shift}',a.Task_ID)
where a.Line='${Line}' and a.Machine_Status='${Machine_Status}' order by a.Task_ID asc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.CI_HK_Standard = function (Standard_ID, callBack) {
    sqlexec.prototype.
        _query(`select * from CI_HK_Standard where Standard_ID='${Standard_ID}'`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.CI_HK_LSW_ID = function (Employee_ID, callBack) {
    sqlexec.prototype.
        _query(`SELECT TOP 1 [LSW_Base_ID]
  FROM [dbo].[LSW_Check_The_Checker_Base]
  where Employee_ID='${Employee_ID}' and LSW_category='HK'`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.CI_HK_LSW_OwnerName = function (Line, callBack) {
    sqlexec.prototype.
        _query(`select top 1 Employee_ID from Department_Owner_List where Area = '${Line}'`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.CI_HK_Check_Data = function (Task_ID, Date, Line, Shift, callBack) {
    sqlexec.prototype.
        _query(`SELECT *  FROM [dbo].[CI_HK_Check_Data] where Date='${Date}' and Shift='${Shift}' and Line='${Line}' and Task_ID='${Task_ID}'`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.Insert_CI_HK_Check_Data = function (data, callBack) {
    sqlexec.prototype.
        _query(`insert into [CI_HK_Check_Data] values ('${data['Task_ID']}','${data['Date']}','${data['Line']}','${data['Shift']}','${data['Employee_ID']}',
            '${data['Employee_Name']}','${data['Check_DateTime']}','${data['Check_Result']}','${data['Check_Category']}','${data['Check_Problem_Desc']}',
            '${data['Check_Image_URL']}')`, function (err, result) {
            if (err) {
            }
            return callBack('已添加')
        })
}
sqlexec.prototype.CI_HK_LSW_Problem_Data = function (relateID, callBack) {
    sqlexec.prototype.
        _query(`SELECT  RelateID,Problem_Category as problem_category,Problem_Desc as problemContent,JSON_VALUE(Problem_File_URL_Array, CONCAT('$."', Problem_File_Name_Array, '"')) image_URL
                FROM [dbo].[LSW_Problem_Data] where RelateID='${relateID}' order by ID desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.CI_HK_Problem_Statistics = function (Line, Machine_Status, Date, Shift, callBack) {

    sqlexec.prototype.
        _query(`select a.Area,count(c.RelateID) problemCount from CI_HK_Task_List as a
                    left join CI_HK_Check_Data as b on a.Task_ID=b.Task_ID and b.Date='${Date}' and b.Shift='${Shift}'
                    left join [LSW_Problem_Data] as c on c.RelateID=CONCAT_WS('-','${Date}','${Line}','${Shift}',a.Task_ID)

                    where a.Line='${Line}' and a.Machine_Status='${Machine_Status}' and c.RelateID is not null
                    group by a.Area`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.CI_HK_Machine_Status = function (callBack) {

    sqlexec.prototype.
        _query(`select distinct Machine_Status from CI_HK_Task_List order by Machine_Status asc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.Insert_CI_HK_Check_Data_Asset = function (data, callBack) {
    sqlexec.prototype.
        _query(`insert into [CI_HK_Check_Data_Asset] values ('${data['Task_ID']}','${data['Date']}','${data['Line']}','${data['Shift']}','${data['Employee_ID']}',
            '${data['Employee_Name']}','${data['Check_DateTime']}','${data['Check_Result']}')`, function (err, result) {
            if (err) {
            }
            return callBack('已添加')
        })
}

sqlexec.prototype.CI_HK_Check_Data_Asset = function (Task_ID, Date, Line, Shift, callBack) {
    sqlexec.prototype.
        _query(`SELECT *  FROM [dbo].[CI_HK_Check_Data_Asset] where Date='${Date}' and Shift='${Shift}' and Line='${Line}' and Task_ID='${Task_ID}'`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}




module.exports = sqlexec;