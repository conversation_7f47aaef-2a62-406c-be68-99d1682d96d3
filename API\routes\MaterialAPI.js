var express = require('express')
var router = express.Router()
var sqlexec_grade = require('../sql/sqlMaterial')
var moment = require('moment')
const { resolve } = require('q')



router.get("/lineList", (req, res) => {
    // var y = "'" + req.query.y + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.lineList(function (result) {
        var returnLine = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnLine.push(result[i].line)
            }
            res.send(returnLine)

        } else {
            res.send("获取失败")
        }
    })
})


router.get("/OPList", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.OPList(Line, function (result) {
        var returnOPList = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnOPList.push(result[i].Employee_PYName)
            }
            res.send(returnOPList)

        } else {
            res.send("获取失败")
        }
    })
})

router.get("/RM", (req, res) => {
    var RM_Code = "'" + req.query.RM_Code + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.RM(RM_Code, function (result) {
        var returnRM = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnRM.push(result[i])
            }
            res.send(returnRM)
        } else {
            res.send("获取失败")
        }
    })
})

router.get("/PalletLine", (req, res) => {
    // var y = "'" + req.query.y + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.PalletLine(function (result) {
        var returnLine = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnLine.push(result[i].line)
            }
            res.send(returnLine)

        } else {
            res.send("获取失败")
        }
    })
})

router.get("/PalletSumLine", (req, res) => {
    // var y = "'" + req.query.y + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.PalletSumLine(function (result) {
        var returnSumLine = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnSumLine.push(result[i].line)
            }
            res.send(returnSumLine)

        } else {
            res.send("获取失败")
        }
    })
})

router.get("/PalletSKU", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.PalletSKU(Line, function (result) {
        var returnPalletSKU = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnPalletSKU.push(result[i].SKU)
            }
            res.send(returnPalletSKU)

        } else {
            res.send("获取失败")
        }
    })
})

router.get("/PalletSumSKU", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    var Date1 = "'" + req.query.Date + "'"
    var Shift = "'" + req.query.Shift + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.PalletSumSKU(Line, Date1, Shift, function (result) {
        var returnPalletSumSKU = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnPalletSumSKU.push(result[i].SKU)

            }
            res.send(returnPalletSumSKU)

        } else {
            res.send("获取失败")
        }
    })
})

router.get("/PalletData", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    var SKU = "'" + req.query.SKU + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.PalletData(Line, SKU, function (result) {
        var returnPalletData = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnPalletData.push(result[i])
            }
            res.send(returnPalletData)

        } else {
            res.send("获取失败")
        }
    })
})

router.get("/PalletSumData", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    var SKU = "'" + req.query.SKU + "'"
    var Date = "'" + req.query.Date + "'"
    var Shift = "'" + req.query.Shift + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.PalletSumData(Line, SKU, Date, Shift, function (result) {
        var returnPalletSumData = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnPalletSumData.push(result[i])
            }
            res.send(returnPalletSumData)
        } else {
            res.send("获取失败")
        }
    })
})

router.get("/ScanPalletQTY", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    var SKU = "'" + req.query.SKU + "'"
    var Date1 = "'" + req.query.Date + "'"
    var Shift = "'" + req.query.Shift + "'"
    console.log('参数', Line, SKU, Date1, Shift)
    var returnQuery = new sqlexec_grade()
    returnQuery.ScanPalletQTY(Line, SKU, Date1, Shift, function (result) {
        var returnScanPalletQTY = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnScanPalletQTY.push(result[i])
            }
            res.send(returnScanPalletQTY)

        } else {
            res.send("获取失败")
        }
    })
})

router.post("/API_Execute_PalletID", (req, res) => {
    // console.log(req.body.awardID)
    var pallet = req.body
    var Line = pallet.Line
    console.log('pallet', pallet)
    var returnQuery = new sqlexec_grade()
    returnQuery.API_Execute_PalletID(pallet, function (result) {
        res.send(result)
    })
})

router.get("/PalletSum", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    var SKU = "'" + req.query.SKU + "'"
    var ShiftStart = "'" + req.query.ShiftStart + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.PalletSum(Line, SKU, ShiftStart, function (result) {
        var returnPalletSum = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnPalletSum.push(result[i])
            }
            res.send(returnPalletSum)

        } else {
            res.send("获取失败")
        }
    })
})

router.post("/API_Execute_RM_Return_Insert", (req, res) => {
    // console.log(req.body.awardID)
    var RM_Ruturn = req.body
    console.log('RM_Ruturn', RM_Ruturn)
    var returnQuery = new sqlexec_grade()
    returnQuery.API_Execute_RM_Return_Insert(RM_Ruturn, function (result) {
        res.send(result)
    })
})

router.get("/MESMachineWaste", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    const ArrayStart = req.query.Start.split('/')
    const convertStart = ArrayStart.join('-')
    var Start = "'" + convertStart + "'"
    const ArrayEnd = req.query.End.split('/')
    const convertEnd = ArrayEnd.join('-')
    var End = "'" + convertEnd + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.MachineWaste(Line, Start, End, function (result) {
        var returnMachineWaste = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnMachineWaste.push(result[i])
            }
            res.send(returnMachineWaste)

        } else {
            res.send("获取失败")
        }
    })
})

router.get("/MESQCOWaste", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    const ArrayStart = req.query.Start.split('/')
    const convertStart = ArrayStart.join('-')
    var Start = "'" + convertStart + "'"
    const ArrayEnd = req.query.End.split('/')
    const convertEnd = ArrayEnd.join('-')
    var End = "'" + convertEnd + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.QCOWaste(Line, Start, End, function (result) {
        var returnMachineWaste = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnMachineWaste.push(result[i])
            }
            res.send(returnMachineWaste)

        } else {
            res.send("获取失败")
        }
    })
})

router.get("/TotalCut_Waste", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    const ArrayStart = req.query.Start.split('/')
    const convertStart = ArrayStart.join('-')
    var Start = "'" + convertStart + "'"
    const ArrayEnd = req.query.End.split('/')
    const convertEnd = ArrayEnd.join('-')
    var End = "'" + convertEnd + " 23:00:00'"
    var returnQuery = new sqlexec_grade()
    returnQuery.TotalCut_Waste(Line, Start, End, function (result) {
        var returnTotalCut_Waste = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnTotalCut_Waste.push(result[i])
            }
            res.send(returnTotalCut_Waste)

        } else {
            res.send("获取失败")
        }
    })
})

router.get("/TierSize", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.TierSize(Line, function (result) {
        var returnTierSize = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnTierSize.push(result[i])
            }
            res.send(returnTierSize)

        } else {
            res.send("获取失败")
        }
    })
})

router.get("/OPForQA", (req, res) => {
    console.log('req', req.query)
    var Mill = "'" + req.query.Mill + "'"
    var Start = "'" + req.query.Start + "'"
    var End = "'" + req.query.End + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.OPForQA(Mill, Start, End, function (result) {
        var returnOPForQA = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnOPForQA.push(result[i])
            }
            res.send(returnOPForQA)
        } else {
            res.send("获取失败")
        }
    })
})

router.get("/CLTierSize", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    var SKU = req.query.SKUList
    var returnQuery = new sqlexec_grade()
    console.log('CLTierSize', Line, SKU)
    returnQuery.CLTierSize(Line, SKU, function (result) {
        var returnCLTierSize = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnCLTierSize.push(result[i])
            }
            res.send(returnCLTierSize)

        } else {
            res.send("获取失败")
        }
    })
})

router.get("/DailyKPI", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    const ArrayStart = req.query.Start.split('/')
    const convertStart = ArrayStart.join('-')
    var Start = "'" + convertStart + "'"
    // const ArrayEnd = req.query.End.split('/')
    // const convertEnd = ArrayEnd.join('-')
    // var End = "'" + convertEnd + "'"
    const ArrayEnd = req.query.End.split('/');
    // 将数组转换为日期字符串
    const convertEnd = ArrayEnd.join('-');
    // 将日期字符串转换为日期对象
    const dateEnd = new Date(convertEnd);
    // 日期对象加一天
    dateEnd.setDate(dateEnd.getDate() + 1);
    // 格式化日期为'YYYY-MM-DD'字符串
    const year = dateEnd.getFullYear();
    const month = String(dateEnd.getMonth() + 1).padStart(2, '0');
    const day = String(dateEnd.getDate()).padStart(2, '0');
    const newEnd = `${year}-${month}-${day}`;
    // 将日期字符串包裹在单引号中
    var End = `'${newEnd}'`
    var returnQuery = new sqlexec_grade()
    returnQuery.DailyKPI(Line, Start, End, function (result) {
        var returnDailyKPI = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnDailyKPI.push(result[i])
            }
            res.send(returnDailyKPI)

        } else {
            res.send("获取失败")
        }
    })
})

router.get("/WasteTop3", (req, res) => {
    var Line = "'" + req.query.Line + "'"
    const ArrayStart = req.query.Start.split('/')
    const convertStart = ArrayStart.join('-')
    var Start = "'" + convertStart + "'"
    // 获取传入的End参数，并将其拆分为日期数组
    const ArrayEnd = req.query.End.split('/');
    // 将数组转换为日期字符串
    const convertEnd = ArrayEnd.join('-');
    // 将日期字符串转换为日期对象
    const dateEnd = new Date(convertEnd);
    // 日期对象加一天
    dateEnd.setDate(dateEnd.getDate() + 1);
    // 格式化日期为'YYYY-MM-DD'字符串
    const year = dateEnd.getFullYear();
    const month = String(dateEnd.getMonth() + 1).padStart(2, '0');
    const day = String(dateEnd.getDate()).padStart(2, '0');
    const newEnd = `${year}-${month}-${day}`;
    // 将日期字符串包裹在单引号中
    var End = `'${newEnd}'`;
    var returnQuery = new sqlexec_grade()
    returnQuery.WasteTop3(Line, Start, End, function (result) {
        var returnWasteTop3 = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnWasteTop3.push(result[i])
            }
            res.send(returnWasteTop3)

        } else {
            res.send("获取失败")
        }
    })
})
module.exports = router