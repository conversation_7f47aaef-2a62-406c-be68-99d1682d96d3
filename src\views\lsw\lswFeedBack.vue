<template>
    <base-content>

        <div class="q-pa-md">
            <q-dialog v-model="firtDialog" transition-show="flip-down">
                <q-card style="width:1000px; max-width: 100vw;" v-if="showDetailData">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6 ">LSW详情</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>
                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template
                                v-if="showDetailData['Process_Status'] == 'In Progress' || showDetailData['Process_Status'] == 'Final Check' ">
                                <q-input outlined v-model="approvalData['rootCase']" label-slot clearable
                                    type="textarea" filled style="height: 100px;width: 300px;">
                                    <template v-slot:label>根本问题 <em
                                            class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em></template>
                                </q-input>
                                <q-input outlined v-model="approvalData['problemSolution']" label-slot clearable
                                    type="textarea" filled style="height: 100px;width: 300px;">
                                    <template v-slot:label>解决方案 <em
                                            class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em></template>
                                </q-input>

                                <q-input v-model="approvalData['completeDate']" filled type="date" stack-label clearable
                                    label-slot style="width: 250px;">
                                    <template v-slot:prepend>
                                        <q-icon name="event" />
                                    </template>
                                    <template v-slot:label>预计整改完成时间
                                        <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em>
                                    </template>
                                </q-input>
                                <q-input outlined v-model="approvalData['rejectedReason']" label-slot clearable
                                    type="textarea" style="height: 100px;margin-top: 10px;">
                                    <template v-slot:label>拒绝理由</template>
                                </q-input>
                                <q-btn style="width:150px" label="反馈问题" color="purple"
                                    @click="updateLSWFeedback('Yes')" />
                                <q-btn style="width:150px" label="退回分配者" color="red" @click="updateLSWFeedback('No')" />
                            </template>
                        </div>
                    </q-card-section>
                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template v-for="(value, key) in showDetailData">
                                <div v-if="key == 'Problem_Desc' || key == 'Problem_Root_Case' || key == 'Problem_Solution' || key == '拒绝理由'"
                                    style="width: 100%;">
                                    <q-input type="textarea" filled v-model="showDetailData[key]" dense stack-label
                                        :label="key" readonly style="height: 100px;" />
                                </div>
                                <div v-else-if="key == 'Problem_File_URL_Array'" style="width: 100%;">
                                    <div>Problem_File_URL_Array</div>
                                    <template>
                                        <a :href="JSON.parse(showDetailData[key])[showDetailData['Problem_File_Name_Array']]"
                                            target="_blank">{{
                                            JSON.parse(showDetailData[key])[showDetailData['Problem_File_Name_Array']]
                                            }}</a>
                                    </template>
                                </div>
                                <div v-else>
                                    <q-input filled v-model="showDetailData[key]" dense stack-label :label="key"
                                        readonly style="width:300px" />
                                </div>
                            </template>
                        </div>
                    </q-card-section>
                </q-card>
            </q-dialog>


            <div v-if="approveIdeaList">
                <q-table :data="approveIdeaList" row-key="name" :pagination.sync="myPagination" :filter="filter1" dense
                    class="my-sticky-virtscroll-table" virtual-scroll :virtual-scroll-sticky-size-start="48"
                    :rows-per-page-options="[0]">
                    <template v-slot:top="props">
                        <div class="text-h6" style="font-weight: 600;">LSW分配给我的任务</div>
                        <q-space></q-space>
                        <div style="font-weight: 600;">如需查看所有所有反馈项目请点击-></div>
                        <q-toggle v-model="adpotFilter" color="green" :label="adpotFilter ? '未反馈项目' : '所有反馈项目'"
                            style="font-weight: 600;margin-right: 20px;" />
                        <q-input borderless dense debounce="300" v-model="filter1" placeholder="Search"
                            class="bg-indigo-1">
                            <template v-slot:append>
                                <q-icon name="search" />
                            </template>
                        </q-input>

                        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
                            @click="props.toggleFullscreen" class="q-ml-md" label="全屏" />
                    </template>

                    <template v-slot:body="props">
                        <q-tr :props="props" @click="toApprove(props)">
                            <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                {{ col.value }}
                            </q-td>
                        </q-tr>
                    </template>
                </q-table>
            </div>
        </div>




    </base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
export default {
    components: {
        BaseContent
    },
    data() {
        return {
            approveIdeaList: false,
            approveRawIdeaList: false,
            firtDialog: false,
            approveIdea: false,
            adpotFilter: true,
            filter1: '',
            myPagination: { rowsPerPage: 0 },
            showDetailData: false,
            approvalData: {
                Adopt: null,
                id: null,
                rootCase: null,
                problemSolution: null,
                completeDate: null,
                rejectedReason: null,
                process_status:null
            },
            
            
        }
    },
    mounted() {
        this.getMyApprovalIdea()

    },
    watch: {
        adpotFilter(newValue, oldValue) {
            this.filterMyApproval()
        },
    },

    methods: {
        async getMyApprovalIdea() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`lsw/getLSW_Assign_List?employeeID=${localStorage.getItem('account')}`)
            console.log('getLSW_Assign_List', res)
            _this.approveIdeaList = res
            _this.approveRawIdeaList = res
            _this.filterMyApproval()
        },
        toApprove(props) {
            console.log('toApprove', props)
            const _this = this
            _this.approvalData['Adopt'] = null
            _this.approvalData['id'] = null
            _this.approvalData['rootCase'] = null
            _this.approvalData['problemSolution'] = null
            _this.approvalData['completeDate'] = null
            _this.approvalData['rejectedReason'] = null
            _this.approvalData['process_status'] = null
            _this.showDetailData = props.row
            _this.approvalData['id'] = props.row['ID']
            _this.firtDialog = true
        },
        filterMyApproval() {
            const _this = this
            _this.approveIdeaList = []
            for (let i = 0; i < _this.approveRawIdeaList.length; i++) {
                if (_this.adpotFilter) {
                    if (_this.approveRawIdeaList[i]['Process_Status'] == 'In Progress') {
                        _this.approveIdeaList.push(_this.approveRawIdeaList[i])
                    }
                } else {
                    _this.approveIdeaList = _this.approveRawIdeaList
                }
            }

        },
        updateLSWFeedback(status) {
            const _this = this
            // console.log('approveIdea', _this.approveIdea)
            if (status == 'No' && _this.approvalData['rejectedReason'] == null) {
                _this.$q.dialog({
                    title: '拒绝LSW反馈无法保存',
                    message: '拒绝LSW任务需要写拒绝原因。请填写后继续点击拒绝'
                })
                return
            }
            if (status == 'Yes' && (_this.approvalData['problemSolution'] == null || _this.approvalData['completeDate'] == null || _this.approvalData['rootCase'] == null)) {
                _this.$q.dialog({
                    title: '无法反馈LSW任务',
                    message: '由于没有填写根本问题、解决方案或者预计完成时间，无法反馈。'
                })
                return
            }
            _this.approvalData['process_status'] = status == 'Yes' ? 'Final Check' : 'open'
            _this.approvalData['Adopt'] = status
            console.log('_this.approvalData', _this.approvalData)
            _this.$http.post('lsw/updateLSW_Problem_AssignFeedBack', { 'data': _this.approvalData, 'rowData': _this.showDetailData, 'approver': localStorage.getItem('username')}).then(function (response) {
                console.log('response', response)
                if (response.data === "已发送") {
                    _this.$q.notify({
                        type: 'positive',
                        message: `LSW任务反馈成功！系统第一时间发送邮件给分配人`,
                        position: 'top'
                    })
                    _this.getMyApprovalIdea()
                    _this.firtDialog = false
                }
            })

        }
    }
}
</script>

<style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */
  height: 500px
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0
</style>