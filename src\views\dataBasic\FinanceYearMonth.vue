<template>
	<base-content>
		<div>
			<q-dialog v-model="firstDialog" transition-show="flip-down">
				<q-card style="width: 800px; max-width: 100vw;">
					<q-card-section class=" bg-teal text-white fit row  justify-between  ">
						<div class="text-h6 ">财务月维护</div>
						<q-space />
						<q-btn padding="xs" color="red" icon="clear" v-close-popup />
					</q-card-section>

					<!-- 循环遍历所有字段及值 -->
					<q-card-section>
						<div style="width: 40%;"
							class="full-width row  justify-start items-start content-start q-col-gutter-sm">
							<template v-for="(item, key) in selectRow">
								<!-- {{key}} -->
								<template v-if="key == 'Start_Date' || key == 'End_Date'">
									<q-input filled v-model="selectRow[key]" type="date" label-slot stack-label
										style="width: 180px;" :label="key">
										<!-- <template v-slot:label>
											<q-label for="date-input" class="cursor-pointer" style="font-size: 13px;">
												{{ key }}
												<em class="q-px-sm bg-deep-orange text-white rounded-borders"
													style="font-size: 10px;">必填</em>
											</q-label>
										</template> -->
										<template v-slot:prepend>
											<q-icon name="event" />
											<!-- <em class="q-px-sm bg-deep-orange text-white rounded-borders"
													style="font-size: 10px;">必填</em> -->
										</template>
									</q-input>
								</template>

								<template v-else>
									<q-input filled v-model="selectRow[key]" :label="key"
										:disable="key != 'Start_Date' && key != 'End_Date'" />
								</template>



							</template>
						</div>
					</q-card-section>



					<q-card-actions align="right">
						<q-btn style="width:150px" label="新增" color="purple" @click="CreateFinanceYearMonth()" />
						<q-btn style="width:150px" label="删除" color="red" @click="DeleteFinanceYearMonth()" />
						<q-btn style="width:150px" label="取消" color="primary" v-close-popup />
					</q-card-actions>
				</q-card>
			</q-dialog>
		</div>
		<div dense style="font-size: 20px;color: black;text-align: center;margin-top: 5px;">财务月查询及设置</div>
		<div class="q-pa-md">
			<!--  选择框及按钮 -->
			<div class="q-gutter-md row">
				<q-select outlined dense v-model="Year" :options="YearArray" style="width: 150px;margin-top: -10px;"
					label-slot clearable>
					<template v-slot:label>年份
						<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
					</template>
				</q-select>

				<div class="q-pa-md q-gutter-sm">
					<q-btn :loading="loading1" color="primary" @click="getFinanceYearMonth()" label="查询"
						style="width: 120px;height: 40px;margin-left:-10px;margin-top: -28px;" />

					<q-btn :loading="loading1" color="primary" @click="InsertFinanceYearMonth()" label="新增财务月"
						style="width: 120px;height: 40px;margin-left:10px;margin-top: -28px;" />
				</div>
			</div>
		</div>
		<!--  显示列表区域 -->
		<div class="q-pa-md" v-if="YearmonthList">
			<q-table title="财务年月详情" dense :data="YearmonthList" :rows-per-page-options="[20]"
				style="margin-top: -20px;">
				<template v-slot:body="props">
					<!-- {{ props.row }} -->
					<!-- {{ props.cols }} -->
					<!-- 点击后,弹出的窗口,字段遍历显示 -->
					<q-tr :props="props" @click="detail(props)">
						<q-td v-for="item in props.cols" :key="item.name" :props="props">
							{{ props.row[item.name] }}
						</q-td>
					</q-tr>
				</template>
			</q-table>
		</div>
	</base-content>
</template>
<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import moment from 'moment'
import Quagga from 'quagga'


export default {
	components: {
		BaseContent,
		moment,
		Quagga
	},
	mounted() {
		// 获取当前年份
		const currentYear = new Date().getFullYear();
		// 创建一个数组来存储前5年的年份
		const yearArray = [];
		// 循环获取前5年的年份并存储到数组中
		for (let i = 0; i < 4; i++) {
			const year = currentYear - i;
			yearArray.push(year);
		}
		this.YearArray = yearArray;
		// 设置初始年份值为数组中的第一个年份
		this.Year = yearArray[0];
		// 打印前5年的年份
		console.log(yearArray);
		// this.getFinanceYearMonth();

		// const vm = this
		// vm.$nextTick(() => {
		// 	// 获取当前年份
		// 	const currentYear = new Date().getFullYear();
		// 	// 创建一个数组来存储前5年的年份
		// 	const yearArray = [];
		// 	// 循环获取前5年的年份并存储到数组中
		// 	for (let i = 0; i < 4; i++) {
		// 		const year = currentYear - i;
		// 		yearArray.push(year);
		// 	}
		// 	vm.YearArray = yearArray;
		// 	// 设置初始年份值为数组中的第一个年份
		// 	vm.Year = yearArray[0];
		// 	// 打印前5年的年份
		// 	console.log(yearArray);
			
		// 	// vm.getFinanceYearMonth();
		// })
	},
	data() {
		return {
			YearArray: [],
			Year: '',
			YearmonthList: false,
			loading1: false,
			selectRow: false,
			firstDialog: false,
		}
	},
	watch: {

		'selectRow.End_Date'(newValue, oldValue) {
			console.log('selectRow', this.selectRow)
			const startMoment = moment(this.selectRow.Start_Date);
			const endMoment = moment(this.selectRow.End_Date);
			// 财务月的总时长是两个日期相减结果+1天的分钟数
			const minutes = endMoment.diff(startMoment, 'minutes') + 24 * 60;
			console.log('minutes', minutes)
			this.selectRow.Calendar_Time = minutes
			const formattedDate = moment(this.selectRow.End_Date).format('YYYYMM');
			this.selectRow.Fin_YearMonth = formattedDate
			const month = moment(this.selectRow.End_Date).format('MM');
			console.log('Month', month)
			this.selectRow.Month = month
		},

	},

	methods: {

		async getFinanceYearMonth() {
			this.YearmonthList=false
			var _this = this
			const { data: res } = await _this.$http.get(`data_base/FinanceYearMonth?Year=` + _this.Year)
			console.log('YearMonth', res)
			_this.YearmonthList = res
			// this.YearmonthList = _this.YearmonthList
		},
		async detail(prop) {
			var _this = this
			_this.selectRow = prop.row
			_this.firstDialog = true
			console.log('Prop', prop)
		},
		async InsertFinanceYearMonth() {
			const _this = this
			// _this.employeeDataValue=_this.employeeData
			// _this.opType="新增"
			_this.selectRow = {
				Fin_YearMonth: '',
				Start_Date: '',
				End_Date: '',
				Calendar_Time: '',
				Year: _this.Year,
				Month: '',
			}

			_this.firstDialog = true
		},
		async CreateFinanceYearMonth() {
			const _this = this
			_this.$http.post('data_base/insert_FinYearMonth', _this.selectRow).then(function (response) {
				console.log('response', response)

				if (response.data === '添加成功') {
					_this.$q.notify({
						type: 'positive',
						message: `添加成功`,
						position: 'top'
					})
					// _this.getEmployee_List()
					_this.firstDialog = false
					_this.getFinanceYearMonth()
				}
			})

		},
		async DeleteFinanceYearMonth() {
			const _this = this
			_this.$http.post('data_base/Del_FinYearMonth', _this.selectRow).then(function (response) {
				console.log('response', response)
				if (response.data === '更新成功') {
					_this.$q.notify({
						type: 'positive',
						message: `删除成功`,
						position: 'top'
					})
					// _this.getEmployee_List()
					_this.firstDialog = false
					_this.getFinanceYearMonth()
				}
			})

		}

	},



}
</script>


<style lang="css" scoped>
.text_type {
	color: yellow;
	font-size: 20px;
	font-weight: bold
}
</style>
