<template>

    <base-content>
        <q-dialog v-model="firstDialog" transition-show="flip-down">
            <q-card style="width: 60%; max-width: 100vw;">
                <q-card-section class=" bg-primary text-white fit row  justify-between  ">
                    <div class="text-h5 ">资产会议事项跟踪和修改</div>
                    <q-space />
                    <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                </q-card-section>
                <q-card-section>
                    <div class="row content-center q-gutter-xs" style="margin-top: 10px;">
                        <q-input filled v-model="shiftissuerow.Problem_Desc" label="问题描述" style="width: 100%;"
                            type="textarea" />
                        <q-input filled v-model="shiftissuerow.Problem_Rootcuase" label="根本原因" style="width: 100%;"
                            type="textarea" />
                        <q-input filled v-model="shiftissuerow.Problem_Solution" label="解决方案" style="width: 100%;"
                            type="textarea" />
                        <q-input filled v-model="shiftissuerow.Problem_Prevention" label="预防措施" style="width: 100%;"
                            type="textarea" />
                        <q-select :options="position_list" v-model="shiftissuerow.Owner_Dept" label="负责人职位"
                            style="width: 230px" />
                        <q-select :options="assignedby_List" v-model="shiftissuerow.Owner" label="负责人"
                            style="width: 230px"></q-select>

                        <q-select :options="priority_list" v-model="shiftissuerow.Priority" label="优先级"
                            style="width: 150px" />
                        <q-input filled v-model="shiftissuerow.Completed_Date" label="计划完成日期" style="width: 150px" />
                        <q-select :options="status_list" v-model="shiftissuerow.Status" label="状态"
                            style="width: 150px" />
                    </div>
                </q-card-section>

                <q-card-actions align="right">
                    <q-btn style="width:150px" label="更新" color="primary" @click="update('更新')" />
                    <q-btn style="width:150px" label="删除" color="primary" @click="del('删除')" />
                    <q-btn style="width:150px" label="取消" color="primary" v-close-popup />
                </q-card-actions>
            </q-card>
        </q-dialog>

        <table style="font-size: 20px;border-bottom: 1px solid blue;width:95%;margin-top:20px">
            <tr>
                <td colspan="8">
                    <div style="width: 100%;height: 100px;" class="bg-primary">
                        <div style="font-size: 40px;color:white;margin-left: 30px">资产会议事项跟踪</div>
                        <div style="color: white;margin-left: 30px">
                            <q-radio keep-color v-model="times" val="t1" label="资产会议事项录入" color="white" />
                            <q-radio keep-color v-model="times" val="t2" label="资产会议事项查询并修改" color="white" />
                        </div>
                    </div>
                </td>
            </tr>
            <tr v-if="times == 't1'">
                    <!-- <td align="right" class="white-space: nowrap;">机台:</td> -->
                    <td><q-select :options="line_List" v-model="line" label="机台:" style="width: 230px;"></q-select></td>

                <!-- <td align="right">团队:</td> -->
                <td><q-select :options="Dept_list" v-model="Submitter_Dept" label="团队:" style="width: 230px; "
                        v-if="times == 't1'"></q-select>
                </td>
                <!-- <td align="right">提交人:</td> -->
                <td><q-input v-model="Submitter" label="提交人:" style="width: 230px; "></q-input></td>
                <!-- <td align="right">问题分类:</td> -->
                <td><q-select :options="problem_list" v-model="Problem_category" label="问题分类:"
                        style="width: 230px;"></q-select></td>
                <!-- <td align="right">劣化分类:</td> -->
                <td><q-select :options="deterioration_list" v-model="Deterioration_Type" label="劣化分类:"
                        style="width: 230px;"></q-select></td>
            </tr>

            <tr v-if="times == 't1'">
                <!-- <td align="right">问题描述:</td> -->
                <td colspan="7"><q-input v-model="Problem_Desc" label="问题描述:" style="width: 97%; "></q-input>
                </td>

            </tr>

            <tr v-if="times == 't1'">
                <!-- <td align="right">根本原因:</td> -->
                <td colspan="7"><q-input v-model="Problem_Rootcuase" label="根本原因:" style="width: 97%; "></q-input></td>
            </tr>
            <tr v-if="times == 't1'">
                <!-- <td align="right">解决方案:</td> -->
                <td colspan="7"><q-input v-model="Problem_Solution" label="解决方案:" style="width: 97%; "></q-input></td>
            </tr>
            <tr v-if="times == 't1'">
                <!-- <td align="right">预防措施:</td> -->
                <td colspan="7"><q-input v-model="Problem_Prevention" label="预防措施:" style="width: 97%; "></q-input></td>
            </tr>
            <tr v-if="times == 't1'">
                <!-- <td align="right">负责人职位:</td> -->
                <td><q-select :options="position_list" v-model="Owner_Dept" label="负责人职位:" style="width: 230px;"></q-select>
                </td>
                <!-- <td align="right">负责人:</td> -->
                <td><q-select :options="assignedby_List" v-model="Owner" label="负责人:" style="width: 230px;"></q-select></td>
                <!-- <td align="right">优先级:</td> -->
                <td><q-select :options="priority_list" v-model="Priority" label="优先级:" style="width: 230px;"></q-select>
                </td>
                <!-- <td align="right">计划完成日期:</td>
                <td><q-input v-model="Completed_Date" label="" style="width: 230px; "></q-input></td> -->
                <td>
                    <q-input filled v-model="Completed_Date" mask="date" :rules="['date']" label="计划完成日期"
                        style="margin-top: 25px;width: 230px">
                        <template>
                            <q-icon name="event" class="cursor-pointer">
                                <q-popup-proxy ref="qDateProxy" transition-show="scale" transition-hide="scale">
                                    <q-date v-model="Completed_Date">
                                        <div class="row items-center justify-end">
                                        </div>
                                    </q-date>
                                </q-popup-proxy>
                            </q-icon>
                        </template>
                    </q-input>
                </td>
            </tr>
            <tr v-if="times == 't1'">
                <!-- <td align="right">状态:</td> -->
                <td><q-select :options="status_list" v-model="Status" label="状态:" style="width: 230px;"></q-select></td>
                <td></td>
                <td></td>
                <q-btn @click="submit()" color="primary" label="提交" class="btn-fixed-width"
                    style="margin-right: 30px; width: 200px;height:50px"></q-btn>

            </tr>
        </table>


        <table style="font-size: 20px; border-bottom: 1px solid blue; width: 95%; margin-top: 20px;"
            v-if="times == 't2'">
            <tr class="row content-center q-gutter-xs" style="margin-top: 10px;">
                <td>
                    <q-select standout="bg-green text-white" v-model="line" :options="line_List" label="机台" color="red"
                        style="width: 130px; font-size: 10px;margin-top: 23px;"></q-select>
                </td>
                <td>
                    <q-input filled v-model="startdate" mask="date" :rules="['date']" label="开始日期"
                        style="margin-top: 25px;">
                        <template v-slot:append>
                            <q-icon name="event" class="cursor-pointer">
                                <q-popup-proxy ref="qDateProxy" transition-show="scale" transition-hide="scale">
                                    <q-date v-model="startdate">
                                        <div class="row items-center justify-end">
                                            <q-btn v-close-popup label="Close" color="primary" flat />
                                        </div>
                                    </q-date>
                                </q-popup-proxy>
                            </q-icon>
                        </template>
                    </q-input>
                </td>
                <td>
                    <q-input filled v-model="enddate" mask="date" :rules="['date']" label="结束日期"
                        style="margin-top: 25px;">
                        <template v-slot:append>
                            <q-icon name="event" class="cursor-pointer">
                                <q-popup-proxy ref="qDateProxy" transition-show="scale" transition-hide="scale">
                                    <q-date v-model="enddate">
                                        <div class="row items-center justify-end">
                                            <q-btn v-close-popup label="Close" color="primary" flat />
                                        </div>
                                    </q-date>
                                </q-popup-proxy>
                            </q-icon>
                        </template>
                    </q-input>
                </td>
                <td>
                    <q-select standout="bg-green text-white" v-model="Status" :options="status_list" label="状态"
                        color="red" style="width: 130px; font-size: 10px; margin-top: 23px;"></q-select>
                </td>
                <td>
                    <q-btn @click="query()" align="center" class="btn-fixed-width" color="primary" label="查询和修改"
                        style="margin-top: 23px; width: 200px; height: 55px;font-size: 20px;"></q-btn>
                </td>
                <td>
                    <q-btn @click="email()" align="center" class="btn-fixed-width" color="primary" label="发送邮件"
                        style="margin-top: 23px; width: 200px; height: 55px;font-size: 20px;"></q-btn>
                </td>
            </tr>
        </table>
        <div class="q-pa-md">
            <!-- 把getdata里数据给表格 :columns="columns"标准写法-->
            <q-table :data="getdata" row-key="name" :columns="columns" wrap-cells v-if="times == 't2' && getdata">
                <template v-slot:body="props">
                    <q-tr :props="props" @click="detail(props)">
                        <q-td v-for="item in props.cols" :key="item.name" :props="props">
                            {{ props.row[item.name] }}
                        </q-td>
                    </q-tr>
                </template>
                <!-- props把子级给父级 -->
            </q-table>
        </div>
    </base-content>
</template>

<script>
import { date } from 'quasar'
import BaseContent from '../../components/BaseContent/BaseContent'

export default {
    name: 'Home',
    components: {
        BaseContent,
    },
    mounted() {
        //页面进入后自动执行的方法,即登录到此页面后，就够api一个指令去获取 getdepartment 和getemployee
        this.getDepartment()
        this.getEmployee()
        this.line_List = localStorage.getItem('lineArray').split(',')

    },
    watch: {
        // 当Owner_Dept属性的值发生改变时，自动调用filterAssginedBy()函数，从而实现根据新的Owner_Dept值重新筛选并更新assignedby_List数组中的数据
        Owner_Dept() {
            this.filterAssginedBy()
        },
        'shiftissuerow.Owner_Dept'() {
            this.filterAssginedBy()
        },
    },



    data() {
        return {
            line: '',
            Submitter: '',
            Submitter_Dept: '',
            Problem_category: '',
            Deterioration_Type: '',
            Problem_Desc: '',
            Problem_Rootcuase: '',
            Problem_Solution: '',
            Problem_Prevention: '',
            Owner_Dept: '',
            Owner: '',
            Priority: '',
            Completed_Date: '',
            Status: '未开始',
            Dept_list: ['生产', '工艺', '电气', '机械'],
            line_List: ['ND05', 'ND06', 'ND07', 'ND08', 'ND09', 'ND34', 'ND37'],
            priority_list: ['中', '低', '高', 'T'],
            status_list: ['完成', '进行中', '未开始', '删除'],
            owner_list: ['资产主管', '电气工程师', '机械工程师', '工艺工程师'],
            problem_list: ['安全', '质量', '成本', '效率'],
            deterioration_list: ['自然劣化', '强制劣化'],
            times: 't1',
            startdate: '',
            enddate: '',
            shiftissuerow: '',
            firstDialog: false,
            getdata: false,
            columns: [
                // 利用columns把数据库里英文名称改为中文名称，注意name 和field里的的Date要的数据库大小写保持一致
                { name: 'Date', align: 'left', label: '日期', field: 'Date', sortable: true },
                { name: 'Line', align: 'left', label: '机台', field: 'Line', sortable: true },
                { name: 'Submitter', align: 'left', label: '提交人', field: 'Submitter', sortable: true },
                { name: 'Submitter_Dept', align: 'left', label: '团队', field: 'Submitter_Dept', sortable: true },
                { name: 'Problem_category', align: 'left', label: '问题分类', field: 'Problem_category', sortable: true },
                { name: 'Deterioration_Type', align: 'left', label: '劣化分类', field: 'Deterioration_Type', sortable: true },
                { name: 'Problem_Desc', align: 'left', label: '问题描述', field: 'Problem_Desc', sortable: true },
                { name: 'Problem_Rootcuase', align: 'left', label: '根本原因', field: 'Problem_Rootcuase', sortable: true },
                { name: 'Problem_Solution', align: 'left', label: '解决方案', field: 'Problem_Solution', sortable: true },
                { name: 'Problem_Prevention', align: 'left', label: '预防措施', field: 'Problem_Prevention', sortable: true },
                { name: 'Owner_Dept', align: 'left', label: '负责人职位', field: 'Owner_Dept', sortable: true },
                { name: 'Owner', align: 'left', label: '负责人', field: 'Owner', sortable: true },
                { name: 'Priority', align: 'left', label: '优先级', field: 'Priority', sortable: true },
                { name: 'Completed_Date', align: 'left', label: '计划完成日期', field: 'Completed_Date', sortable: true },
                { name: 'Status', align: 'left', label: '状态', field: 'Status', sortable: true },
            ],

            position_list: [],
            assignedby_List: [],
            employee_List: [],

        }
    },
    methods: {
        // 如下是从api获取数据

        async submit() {
            var _this = this
            const postParam = {

                'line': _this.line,
                'Submitter': _this.Submitter,
                'Submitter_Dept': _this.Submitter_Dept,
                'Problem_category': _this.Problem_category,
                'Deterioration_Type': _this.Deterioration_Type,
                'Problem_Desc': _this.Problem_Desc,
                'Problem_Rootcuase': _this.Problem_Rootcuase,
                'Problem_Solution': _this.Problem_Solution,
                'Problem_Prevention': _this.Problem_Prevention,
                'Owner_Dept': _this.Owner_Dept,
                'Owner': _this.Owner,
                'Priority': _this.Priority,
                'Completed_Date': _this.Completed_Date,
                'Status': _this.Status,

            }
            _this.$http.post('forminput/insert_asset_meeting', postParam).then(function (response) {
                console.log('response', response)
                if (response.data == '添加成功') {
                    _this.$q.notify({
                        message: '添加成功',
                        icon: 'done_outline',
                        position: 'top',
                        type: 'positive',
                    })
                    _this.line = ''
                    _this.Submitter = ''
                    _this.Submitter_Dept = ''
                    _this.Problem_category = ''
                    _this.Deterioration_Type = ''
                    _this.Problem_Desc = ''
                    _this.Problem_Rootcuase = ''
                    _this.Problem_Solution = ''
                    _this.Problem_Prevention = ''
                    _this.Owner_Dept = ''
                    _this.Owner = ''
                    _this.Priority = ''
                    _this.Completed_Date = ''
                    _this.Status = ''
                }
            })
        },

        detail(e) {
            console.log(e)
            const _this = this
            _this.shiftissuerow = e.row
            _this.firstDialog = true
        },
        async query() {

            var _this = this // 重命名变量
            const {
                data: res
            } = await _this.$http.get(`forminput/query_asset_meeting?line=${_this.line}&startdate=${_this.startdate}&enddate=${_this.enddate}&Status=${_this.Status}`)
            _this.getdata = res // 把查询出来的数据给data里的getdata
            console.log('getdata', res)
        },
        async del() {
            const _this = this;
            try {
                const response = await _this.$http.post('forminput/delete_asset_meeting', _this.shiftissuerow);
                console.log('response', response);
                if (response.data === '删除成功') {
                    _this.$q.notify({
                        type: 'positive',
                        message: '删除成功',
                        position: 'top'
                    });

                    _this.firstDialog = false
                } else {
                    // 处理更新失败情况
                }
            } catch (error) {
                console.error('删除失败：', error);
                // 处理异常情况
            }
        },

        async update() {
            const _this = this;
            try {
                const response = await _this.$http.post('forminput/update_asset_meeting', _this.shiftissuerow);
                console.log('response', response);
                if (response.data === '更新成功') {
                    _this.$q.notify({
                        type: 'positive',
                        message: '更新成功',
                        position: 'top'
                    });
                    // 把post 的结果给this.shiftissuerow 字典 然后更新时读取 字典里的date,问题分类和问题描述
                    _this.firstDialog = false
                } else {
                    // 处理更新失败情况
                }
            } catch (error) {
                console.error('更新失败：', error);
                // 处理异常情况
            }
        },


        async email() {
            var _this = this
            const postParam = {
                'line': _this.line,
                'startdate': _this.startdate,
                'enddate': _this.enddate,
            }
            _this.$http.post('forminput/email_asset_meeting', postParam).then(function (response) {
                console.log('response', response)
                if (response.data == '发送成功') {
                    _this.$q.notify({
                        message: '发送成功',
                        icon: 'done_outline',
                        position: 'top',
                        type: 'positive',
                    })

                }
            })
        },
        async getDepartment() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('forminput/getDepartment')
            console.log('getDepartment', res)
            _this.position_list = res
            // 用此方法获取Position list, 并把输出结果给 data里 position_list

        },
        async getEmployee() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('forminput/getEmployee')
            console.log('getEmployee', res)
            _this.employee_List = res
            _this.filterAssginedBy()
        },

        filterAssginedBy() {
            // filterassingedby的作用是根据特定条件（员工的Position等于Owner_Dept）从employee_List中筛选员工，并将符合条件的员工姓名和 ID 存储在assignedby_List数组中。
            const _this = this
            _this.assignedby_List = []
            _this.Owner = '' // 清空assignedby_list and owner list
            for (let i = 0; i < _this.employee_List.length; i++) {
                if (_this.times == 't1' && _this.employee_List[i]['Position'] == _this.Owner_Dept) {
                    // _this.assignedby_List.push({ label: _this.employee_List[i]['Employee_Name'], value: _this.employee_List[i]['Employee_ID'] })
                    _this.assignedby_List.push(_this.employee_List[i]['Employee_Name']);
                }
                if (_this.times == 't2' && _this.employee_List[i]['Position'] == _this.shiftissuerow.Owner_Dept) {
                    // _this.assignedby_List.push({ label: _this.employee_List[i]['Employee_Name'], value: _this.employee_List[i]['Employee_ID'] })
                    _this.assignedby_List.push(_this.employee_List[i]['Employee_Name']);
                }
            }
        },


    }


}
</script>

<!-- <style scoped>
::v-deep .q-field__label {
  font-size: 40px; /* 你可以根据需要调整字体大小 */
  color: rgb(0, 255, 30);
}
</style> -->