<template>
    <!-- 写HTML -->
    <base-content>
 
        <div class="q-pa-md q-gutter-sm">
            <div class="fit row wrap justify-start items-start q-gutter-sm  ">
                <q-select standout="bg-teal text-white" v-model="line" :options="lineList" label="请选择机台"
                    style="width: 200px;" />
                <q-select standout="bg-teal text-white" v-model="sku" :options="skuList" label="SKU"
                    style="width: 500px;" />
            </div>
            <q-btn color="primary" label="Primary" @click="getSKU()" />
            <div>选择的机台值是：{{ line }}</div>
 
            <div>选择框选择的值是：{{ sku }}</div>
            <!-- <div class="fit row wrap justify-start items-start q-gutter-sm ">
                <q-input filled v-model="text" label="Standard" style="width: 100%;" />
            </div> -->
        </div>
 
        <!-- {{ text }} -->
    </base-content>
</template>
 
<script>
import BaseContent from '../../components/BaseContent/BaseContent'
 
export default {
    name: 'Home',
    components: {
        BaseContent,
    },
    watch: {
        line(newValue,oldValue){
            console.log('oldValue->',oldValue)
            console.log('newValue->',newValue)
            this.getSKU()
        },
        sku(newValue,oldValue){
            console.log('oldValue->',oldValue)
            console.log('newValue->',newValue)
        }
    },
    mounted() {
 
    },
 
    data() {
        return {
            line: '',
            sku: '',
            lineList: ['ND05', 'ND06', 'ND07', 'ND08', 'ND09'],
            skuList: []
        }
    },
    methods: {
        //方法
        async getSKU() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`material/getSKU?Line=${_this.line}`)
            console.log('getSKU', res)
            _this.skuList = res
        }
 
    }
}
</script>