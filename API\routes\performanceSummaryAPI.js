var express = require('express')
var router = express.Router()
var sqlexec = require('../sql/sqlPerformanceSummary')
var moment = require('moment')

// 获取绩效汇总数据
router.get("/getSummaryData", (req, res) => {
    var startDate = "'" + req.query.startDate + "'"
    var endDate = "'" + req.query.endDate + "'"
    
    var returnQuery = new sqlexec()
    returnQuery.getPerformanceSummaryData(startDate, endDate, function (result) {
        // 计算OEE_T, OEE_A, Waste指标
        const processedData = processPerformanceData(result);
        res.json(processedData);
    })
})

// 获取所有生产线列表
router.get("/getProductionLines", (req, res) => {
    var returnQuery = new sqlexec()
    returnQuery.getProductionLines(function (result) {
        const lines = result.map(item => item.Line);
        res.json(lines);
    })
})

// 处理性能数据，计算OEE_T, OEE_A, Waste指标
function processPerformanceData(data) {
    // 按日期分组
    const groupedByDate = groupByDate(data);
    
    // 按生产线分组
    const groupedByLine = groupByLine(data);
    
    // 计算MTD (Month to Date) 数据
    const mtdData = calculateMTD(data);
    
    // 计算YTD (Year to Date) 数据
    const ytdData = calculateYTD(data);
    
    return {
        dailyData: groupedByDate,
        lineData: groupedByLine,
        mtdData: mtdData,
        ytdData: ytdData
    };
}

// 按日期分组数据
function groupByDate(data) {
    const grouped = {};
    
    data.forEach(item => {
        const date = moment(item.Date).format('YYYY-MM-DD');
        
        if (!grouped[date]) {
            grouped[date] = [];
        }
        
        grouped[date].push(item);
    });
    
    return grouped;
}

// 按生产线分组数据
function groupByLine(data) {
    const grouped = {};
    
    data.forEach(item => {
        if (!grouped[item.Line]) {
            grouped[item.Line] = [];
        }
        
        grouped[item.Line].push(item);
    });
    
    return grouped;
}

// 计算MTD (Month to Date) 数据
function calculateMTD(data) {
    const currentDate = moment();
    const startOfMonth = moment().startOf('month');
    
    // 过滤当月数据
    const mtdData = data.filter(item => {
        const itemDate = moment(item.Date);
        return itemDate.isSameOrAfter(startOfMonth) && itemDate.isSameOrBefore(currentDate);
    });
    
    // 按生产线分组
    const groupedByLine = {};
    mtdData.forEach(item => {
        if (!groupedByLine[item.Line]) {
            groupedByLine[item.Line] = [];
        }
        
        groupedByLine[item.Line].push(item);
    });
    
    // 计算每条生产线的OEE_T, OEE_A, Waste指标
    const result = {};
    Object.keys(groupedByLine).forEach(line => {
        const lineData = groupedByLine[line];
        
        // 计算OEE_T
        const oeeT = calculateOEE_T(lineData);
        
        // 计算OEE_A
        const oeeA = calculateOEE_A(lineData);
        
        // 计算Waste
        const waste = calculateWaste(lineData);
        
        result[line] = {
            OEE_T: oeeT,
            OEE_A: oeeA,
            Waste: waste
        };
    });
    
    return result;
}

// 计算YTD (Year to Date) 数据
function calculateYTD(data) {
    const currentDate = moment();
    const startOfYear = moment().startOf('year');
    
    // 过滤当年数据
    const ytdData = data.filter(item => {
        const itemDate = moment(item.Date);
        return itemDate.isSameOrAfter(startOfYear) && itemDate.isSameOrBefore(currentDate);
    });
    
    // 按生产线分组
    const groupedByLine = {};
    ytdData.forEach(item => {
        if (!groupedByLine[item.Line]) {
            groupedByLine[item.Line] = [];
        }
        
        groupedByLine[item.Line].push(item);
    });
    
    // 计算每条生产线的OEE_T, OEE_A, Waste指标
    const result = {};
    Object.keys(groupedByLine).forEach(line => {
        const lineData = groupedByLine[line];
        
        // 计算OEE_T
        const oeeT = calculateOEE_T(lineData);
        
        // 计算OEE_A
        const oeeA = calculateOEE_A(lineData);
        
        // 计算Waste
        const waste = calculateWaste(lineData);
        
        result[line] = {
            OEE_T: oeeT,
            OEE_A: oeeA,
            Waste: waste
        };
    });
    
    return result;
}

// 计算OEE_T
// OEE_T计算公式: DIVIDE(sum([Plan_Time])-sum([Delay_Time])-sum([MachineWasteMin])-sum([Speed_Loss_T_Min]),sum('[Plan_Time]));
function calculateOEE_T(data) {
    let sumPlanTime = 0;
    let sumDelayTime = 0;
    let sumMachineWasteMin = 0;
    let sumSpeedLossTMin = 0;
    
    data.forEach(item => {
        sumPlanTime += item.Plan_Time || 0;
        sumDelayTime += item.Delay_Time || 0;
        sumMachineWasteMin += item.MachineWasteMin || 0;
        sumSpeedLossTMin += item.Speed_Loss_T_Min || 0;
    });
    
    if (sumPlanTime === 0) {
        return 0;
    }
    
    return (sumPlanTime - sumDelayTime - sumMachineWasteMin - sumSpeedLossTMin) / sumPlanTime;
}

// 计算OEE_A
// OEE_A计算公式: DIVIDE(sum([Plan_Time])-sum([Delay_Time])-sum([MachineWasteMin])-sum([Speed_Loss_A_Min]),sum('[Plan_Time]));
function calculateOEE_A(data) {
    let sumPlanTime = 0;
    let sumDelayTime = 0;
    let sumMachineWasteMin = 0;
    let sumSpeedLossAMin = 0;
    
    data.forEach(item => {
        sumPlanTime += item.Plan_Time || 0;
        sumDelayTime += item.Delay_Time || 0;
        sumMachineWasteMin += item.MachineWasteMin || 0;
        sumSpeedLossAMin += item.Speed_Loss_A_Min || 0;
    });
    
    if (sumPlanTime === 0) {
        return 0;
    }
    
    return (sumPlanTime - sumDelayTime - sumMachineWasteMin - sumSpeedLossAMin) / sumPlanTime;
}

// 计算Waste
// Waste计划公式: DIVIDE([CullCuts],[TotalCuts]);
function calculateWaste(data) {
    let sumCullCuts = 0;
    let sumTotalCuts = 0;
    
    data.forEach(item => {
        sumCullCuts += item.CullCuts || 0;
        sumTotalCuts += item.TotalCuts || 0;
    });
    
    if (sumTotalCuts === 0) {
        return 0;
    }
    
    return sumCullCuts / sumTotalCuts;
}

module.exports = router;
