<template>
	<base-content>

		<div class="q-pa-md">
			<q-dialog v-model="firtDialog" transition-show="flip-down">
				<q-card style="width:1200px; max-width: 100vw;" v-if="approveIdea">
					<q-card-section class=" bg-teal text-white fit row  justify-between  ">
						<div class="text-h6 ">点子方案反馈</div>
						<q-space />
						<q-btn padding="xs" color="red" icon="clear" v-close-popup />
					</q-card-section>

					<q-card-section>
						<div class="q-gutter-md row items-start">

							<q-input v-model="approveIdea.row['部门']" standout disable filled autogrow label="部门/生产线"
								style="width: 150px;" />
							<q-input v-model="approveIdea.row['提交人']" standout disable filled autogrow label="提交人"
								style="width: 150px;" />
							<q-input v-model="approveIdea.row['点子类型']" standout disable filled autogrow label="点子类型"
								style="width: 150px;" />
							<q-input v-model="approveIdea.row['点子子类型']" standout disable filled autogrow label="点子子类型"
								style="width: 150px;" />
							<q-input v-model="approveIdea.row['点子价值']" standout disable filled autogrow label="点子价值"
								style="width: 150px;" />
							<q-input v-model="approveIdea.row['审批人']" standout disable filled autogrow label="审批人"
								style="width: 150px;" />

							<!-- <q-select outlined v-model="approveIdea.row['Assigned_By']"
                                :options="approveIdeaDropdown['assignedBy']" label-slot clearable style="width: 300px;">
                                <template v-slot:label>建议改善责任人
                                </template>
                            </q-select> -->
						</div>
						<!-- <div class="q-gutter-md row items-start"></div> -->
						<div class="q-gutter-md" style="margin-top: 10px;">
							<q-input outlined v-model="approveIdea.row['发现的问题']" label-slot clearable type="textarea"
								disable filled style="height: 100px;">
								<template v-slot:label>发现的问题</template>
							</q-input>
							<q-input outlined v-model="approveIdea.row['建议解决方案']" label-slot clearable type="textarea"
								disable filled style="height: 100px;">
								<template v-slot:label>提交人建议的方案</template>
							</q-input>
							<q-input outlined v-model="approveIdea.row['整改人提交方案']" label-slot clearable type="textarea"
								filled style="height: 100px;">
								<template v-slot:label>您的行动方案
									<em class="q-px-sm bg-deep-orange text-white rounded-borders"
										style="font-size: 12px;">必填</em>
								</template>
							</q-input>
						</div>
						<div class="q-gutter-md row items-start" style="margin-top: 10px; ">
							<q-input v-model="approveIdea.row['预计整改完成时间']" filled type="date" stack-label label-slot
								style="width: 250px;">
								<template v-slot:prepend>
									<q-icon name="event" />
								</template>
								<template v-slot:label>预计整改完成时间
									<em class="q-px-sm bg-deep-orange text-white rounded-borders"
										style="font-size: 12px;">必填</em>
								</template>
							</q-input>

						</div>
						<q-input outlined v-model="approveIdea.row['整改人拒绝理由']" label-slot clearable type="textarea"
							style="height: 100px;margin-top: 10px;">
							<template v-slot:label>拒绝理由</template>
						</q-input>
					</q-card-section>
					<q-card-actions align="right" v-if="approveIdea.row['流程状态']=='In progress'">
						<q-btn style="width:150px" label="反馈确认" color="purple" @click="updateIdea('Yes')" />
						<q-btn style="width:150px" label="退回分配人" color="red" @click="updateIdea('No')" />
					</q-card-actions>
				</q-card>
			</q-dialog>


			<div v-if="approveIdeaList">
				<q-table :data="approveIdeaList" row-key="name" :pagination.sync="myPagination" :filter="filter1" dense
					class="my-sticky-virtscroll-table" virtual-scroll :virtual-scroll-sticky-size-start="48"
					:rows-per-page-options="[0]">
					<template v-slot:top="props">
						<div class="text-h6" style="font-weight: 600;">分配给我的任务</div>
						<q-space></q-space>
						<div style="font-weight: 600;">如需查看所有所有反馈项目请点击-></div>
						<q-toggle v-model="adpotFilter" color="green" :label="adpotFilter ? '未反馈项目' : '所有反馈项目'"
							style="font-weight: 600;margin-right: 20px;" />
						<q-input borderless dense debounce="300" v-model="filter1" placeholder="Search" class="bg-indigo-1">
							<template v-slot:append>
								<q-icon name="search" />
							</template>
						</q-input>

						<q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
							@click="props.toggleFullscreen" class="q-ml-md" label="全屏" />
					</template>

					<template v-slot:body="props">
						<q-tr :props="props" @click="toApprove(props)">
							<q-td v-for="col in props.cols" :key="col.name" :props="props">
								{{ col.value }}
							</q-td>
						</q-tr>
					</template>
				</q-table>
			</div>
		</div>




	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
export default {
	components: {
		BaseContent
	},
	data() {
		return {
			approveIdeaList: false,
			approveRawIdeaList: false,
			firtDialog: false,
			approveIdea: false,
			adpotFilter: true,
			filter1: '',
			myPagination: { rowsPerPage: 0 },
		}
	},
	mounted() {
		this.getMyApprovalIdea()

	},
	watch: {
		adpotFilter(newValue, oldValue) {
			this.filterMyApproval()
		},
	},

	methods: {
		async getMyApprovalIdea() {
			const _this = this
			const {
				data: res
			} = await _this.$http.get(`ideabank/myApprovalIdea?employeeID=${localStorage.getItem('account')}&processType=feedback`)
			console.log('getMyApprovalIdead——feedback', res)
			_this.approveIdeaList = res
			_this.approveRawIdeaList = res
			_this.filterMyApproval()
		},
		toApprove(props) {
			console.log('toApprove', props)
			const _this = this
			_this.approveIdea = props
			_this.firtDialog = true
		},
		filterMyApproval() {
			const _this = this
			_this.approveIdeaList = []
			for (let i = 0; i < _this.approveRawIdeaList.length; i++) {
				if (_this.adpotFilter) {
					if (_this.approveRawIdeaList[i]['流程状态'] == 'In progress') {
						_this.approveIdeaList.push(_this.approveRawIdeaList[i])
					}
				} else {
					_this.approveIdeaList = _this.approveRawIdeaList
				}
			}

		},
		updateIdea(status) {
			const _this = this
			// console.log('approveIdea', _this.approveIdea)
			if (status == 'No' && _this.approveIdea.row['整改人拒绝理由'] == null) {
				_this.$q.dialog({
					title: '拒绝点子无法保存',
					message: '拒绝点子需要写拒绝原因。请填写后继续点击拒绝'
				})
				return
			}
			if (status == 'Yes' && (_this.approveIdea.row['整改人提交方案'] == null || _this.approveIdea.row['预计整改完成时间'] == null)) {
				_this.$q.dialog({
					title: '无法反馈点子',
					message: '由于没有填写行动方案或者预计完成时间，无法反馈。'
				})
				return
			}
			_this.approveIdea.row['流程状态']=status=='Yes'?'Final Check':'open'
			console.log('_this.approveIdea.row',_this.approveIdea.row)
			_this.$http.post('ideabank/updateIdea', { 'data': _this.approveIdea.row, 'approver': localStorage.getItem('username'), 'processType': 'feedback' }).then(function (response) {
				console.log('response', response)
				if (response.data === "已发送") {
					_this.$q.notify({
						type: 'positive',
						message: `点子反馈成功！系统第一时间发送邮件给分配人`,
						position: 'top'
					})
					_this.getMyApprovalIdea()
					_this.firtDialog = false
				}
			})

		}
	}
}
</script>

<style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */
  height: 500px
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0
</style>