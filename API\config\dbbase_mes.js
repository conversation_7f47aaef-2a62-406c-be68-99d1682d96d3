var config = require('./config_mes');
const sql=require('mssql/msnodesqlv8')
var Q = require('q');

function _Base() {
}

//利用config账户登陆SQL
_Base.prototype._connect = function (callback) {
  var defer = Q.defer()
  var connection = new sql.ConnectionPool(
  {server: 'CNNBAS70', // You can use 'localhost\instance' to connect to named instance
    database: 'UMDB',
    driver:"msnodesqlv8",
    options: {
    trustedConnection: true // 使用Windows身份验证
  }
}
  )
  defer.resolve(connection)
  return defer.promise.nodeify(callback)
}

//query字符串的回调
_Base.prototype._query = async function (sqlstr, callBack) {
  if (!sqlstr) {
    var err = new Error('sql is empty!')
    var defer = Q.defer()
    return defer.reject(err)
  }

  if (config.debug) {
    console.log('[SQL:]', sqlstr, '[:SQL]')
  }
  return this._connect().then(function (connection) {
    return connection.connect().then(function () {
      var request = new sql.Request(connection)
      return request.query(sqlstr).then(function (recordset) {
        connection.close()
        return callBack(err, recordset)
      }).catch(function (err) {
        connection.close()
        return err
      })
    })
  })
}


module.exports = _Base