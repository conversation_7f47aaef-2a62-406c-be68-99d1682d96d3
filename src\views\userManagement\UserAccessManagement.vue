<template>
  <div class="user-access-management">
    <h2 class="text-h6 q-mb-md">用户权限管理</h2>
    
    <!-- 用户选择 -->
    <div class="row q-mb-md">
      <q-select
        filled
        v-model="selectedUser"
        :options="userOptions"
        label="选择用户"
        option-label="label"
        option-value="value"
        emit-value
        map-options
        class="col-xs-12 col-sm-6"
        @input="fetchUserAccess"
      >
        <template v-slot:no-option>
          <q-item>
            <q-item-section class="text-grey">
              无匹配用户
            </q-item-section>
          </q-item>
        </template>
      </q-select>
    </div>
    
    <!-- 用户权限列表 -->
    <div v-if="selectedUser">
      <div class="row q-col-gutter-md">
        <!-- 当前权限列表 -->
        <div class="col-xs-12 col-md-6">
          <q-card>
            <q-card-section class="bg-primary text-white">
              <div class="text-h6">当前权限</div>
            </q-card-section>
            
            <q-card-section>
              <div v-if="loading" class="text-center q-pa-md">
                <q-spinner color="primary" size="3em" />
                <div class="q-mt-md">加载中...</div>
              </div>
              
              <div v-else-if="userAccess.length === 0" class="text-center q-pa-md">
                <q-icon name="info" color="grey" size="3em" />
                <div class="q-mt-md text-grey">该用户暂无权限</div>
              </div>
              
              <q-list bordered separator v-else>
                <q-item v-for="access in userAccess" :key="access.access_link_id">
                  <q-item-section>
                    <q-item-label>{{ access.app_name }}</q-item-label>
                    <q-item-label caption>角色: {{ access.access_group }}</q-item-label>
                  </q-item-section>
                  
                  <q-item-section side>
                    <q-btn flat round dense color="negative" icon="delete" @click="removeAccess(access)" />
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>
        
        <!-- 添加新权限 -->
        <div class="col-xs-12 col-md-6">
          <q-card>
            <q-card-section class="bg-primary text-white">
              <div class="text-h6">添加权限</div>
            </q-card-section>
            
            <q-card-section>
              <q-form @submit="addAccess" class="q-gutter-md">
                <q-select
                  filled
                  v-model="newAccess.access_group"
                  :options="accessGroupOptions"
                  label="选择权限组 *"
                  @input="filterAppOptions"
                  :rules="[val => !!val || '请选择权限组']"
                />
                
                <q-select
                  filled
                  v-model="newAccess.app_name"
                  :options="filteredAppOptions"
                  label="选择应用 *"
                  :disable="!newAccess.access_group"
                  :rules="[val => !!val || '请选择应用']"
                />
                
                <div class="row justify-end q-mt-md">
                  <q-btn label="添加权限" type="submit" color="primary" :loading="submitting" />
                </div>
              </q-form>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
    
    <div v-else class="text-center q-pa-xl">
      <q-icon name="person" color="grey" size="4em" />
      <div class="text-h6 q-mt-md text-grey">请选择一个用户进行权限管理</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserAccessManagement',
  data() {
    return {
      loading: false,
      submitting: false,
      selectedUser: null,
      users: [],
      userOptions: [],
      accessGroups: [],
      accessGroupOptions: [],
      appOptions: [],
      filteredAppOptions: [],
      userAccess: [],
      newAccess: {
        access_group: null,
        app_name: null
      }
    }
  },
  mounted() {
    this.fetchUsers()
    this.fetchAccessGroups()
  },
  methods: {
    async fetchUsers() {
      try {
        const response = await this.$http.get('/userManagement_api/users')
        console.log('用户列表响应:', response.data)
        
        if (response.data && response.data.status === 200) {
          let userData = response.data.data || []
          
          // 输出原始数据，用于调试
          console.log('原始用户数据:', JSON.stringify(userData))
          
          if (Array.isArray(userData)) {
            // 处理数组格式的数据
            this.users = userData.map(user => ({
              employee_id: user.Employee_ID || user.employee_id || '',
              employee_name: user.Employee_Name || user.employee_name || '',
              department: user.Department || user.department || '',
              title: user.Title || user.title || ''
            }))
          } else if (userData && typeof userData === 'object') {
            // 处理单个用户对象
            console.log('用户数据是对象格式，尝试转换为数组')
            const userItem = {
              employee_id: userData.Employee_ID || userData.employee_id || '',
              employee_name: userData.Employee_Name || userData.employee_name || '',
              department: userData.Department || userData.department || '',
              title: userData.Title || userData.title || ''
            }
            this.users = [userItem]
          } else {
            console.error('返回的用户数据格式无法识别:', userData)
            this.$q.notify({
              color: 'negative',
              message: '获取用户列表数据格式错误',
              icon: 'error'
            })
            this.users = []
          }
          
          // 更新用户选项
          if (this.users.length > 0) {
            this.userOptions = this.users.map(user => ({
              label: `${user.employee_name} (${user.employee_id})`,
              value: user.employee_id,
              employee_name: user.employee_name
            }))
            console.log('处理后的用户选项:', this.userOptions)
          } else {
            this.userOptions = []
          }
        } else {
          this.$q.notify({
            color: 'negative',
            message: response.data?.message || '获取用户列表失败',
            icon: 'error'
          })
          this.users = []
          this.userOptions = []
        }
      } catch (error) {
        console.error('获取用户列表错误：', error)
        this.$q.notify({
          color: 'negative',
          message: '获取用户列表失败',
          icon: 'error'
        })
        this.users = []
        this.userOptions = []
      }
    },
    
    async fetchAccessGroups() {
      try {
        const response = await this.$http.get('/userManagement_api/access-groups')
        console.log('获取权限组响应:', response.data)
        
        if (response.data && response.data.status === 200) {
          let groupsData = response.data.data || []
          
          // 输出原始数据，用于调试
          console.log('原始权限组数据:', JSON.stringify(groupsData))
          
          if (Array.isArray(groupsData)) {
            // 处理数组格式的数据
            this.accessGroups = groupsData.map(group => ({
              group_name: group.Group_Name || group.group_name || '未定义',
              app_name: group.App_Name || group.app_name || '未定义'
            }))
            
            // 提取唯一的权限组名称
            const uniqueGroups = [...new Set(this.accessGroups.map(item => item.group_name))]
            this.accessGroupOptions = uniqueGroups.filter(group => group !== '未定义')
            
            // 处理应用选项
            this.appOptions = this.accessGroups
              .filter(item => item.group_name !== '未定义' && item.app_name !== '未定义')
              .map(item => ({
                label: item.app_name,
                value: item.app_name,
                group: item.group_name
              }))
            
            console.log('处理后的权限组:', uniqueGroups)
            console.log('处理后的应用选项:', this.appOptions)
          } else if (groupsData && typeof groupsData === 'object') {
            // 处理单个权限组对象
            console.log('权限组数据是对象格式，尝试转换为数组')
            const groupItem = {
              group_name: groupsData.Group_Name || groupsData.group_name || '未定义',
              app_name: groupsData.App_Name || groupsData.app_name || '未定义'
            }
            this.accessGroups = [groupItem]
            
            if (groupItem.group_name !== '未定义') {
              this.accessGroupOptions = [groupItem.group_name]
              
              if (groupItem.app_name !== '未定义') {
                this.appOptions = [{
                  label: groupItem.app_name,
                  value: groupItem.app_name,
                  group: groupItem.group_name
                }]
              } else {
                this.appOptions = []
              }
            } else {
              this.accessGroupOptions = []
              this.appOptions = []
            }
          } else {
            console.error('返回的权限组数据格式无法识别:', groupsData)
            this.$q.notify({
              color: 'negative',
              message: '获取权限组数据格式错误',
              icon: 'error'
            })
            this.accessGroups = []
            this.accessGroupOptions = []
            this.appOptions = []
          }
        } else {
          this.$q.notify({
            color: 'negative',
            message: response.data?.message || '获取权限组列表失败',
            icon: 'error'
          })
          this.accessGroups = []
          this.accessGroupOptions = []
          this.appOptions = []
        }
      } catch (error) {
        console.error('获取权限组列表错误：', error)
        this.$q.notify({
          color: 'negative',
          message: '获取权限组列表失败',
          icon: 'error'
        })
        this.accessGroups = []
        this.accessGroupOptions = []
        this.appOptions = []
      }
    },
    
    async fetchUserAccess() {
      if (!this.selectedUser) return
      
      this.loading = true
      this.userAccess = []
      
      try {
        const response = await this.$http.get(`/userManagement_api/user-access?employee_id=${this.selectedUser}`)
        console.log('用户权限响应:', response.data)
        
        // 检查响应格式
        if (response.data && response.data.status === 200) {
          let accessData = response.data.data || []
          
          // 输出原始数据格式，用于调试
          console.log('原始权限数据:', JSON.stringify(accessData))
          
          if (Array.isArray(accessData)) {
            // 处理数组格式的数据
            this.userAccess = accessData.map(access => {
              // 确保即使字段为null或undefined也会返回空字符串
              return {
                access_link_id: access.Access_LinkID || access.access_link_id || '',
                employee_id: access.Employee_ID || access.employee_id || '',
                employee_name: access.Employee_Name || access.employee_name || '',
                access_group: access.Access_Group || access.access_group || '未定义',
                app_name: access.App_Name || access.app_name || '未定义'
              }
            })
          } else if (accessData && typeof accessData === 'object') {
            // 处理对象格式的数据（可能是单个用户权限）
            console.log('权限数据是对象格式，尝试转换为数组')
            const accessItem = {
              access_link_id: accessData.Access_LinkID || accessData.access_link_id || '',
              employee_id: accessData.Employee_ID || accessData.employee_id || '',
              employee_name: accessData.Employee_Name || accessData.employee_name || '',
              access_group: accessData.Access_Group || accessData.access_group || '未定义',
              app_name: accessData.App_Name || accessData.app_name || '未定义'
            }
            this.userAccess = [accessItem]
          } else {
            console.error('返回的权限数据格式无法识别:', accessData)
            this.$q.notify({
              color: 'warning',
              message: '获取用户权限数据格式错误',
              icon: 'warning'
            })
            this.userAccess = []
          }
          
          console.log('处理后的用户权限数据:', this.userAccess)
        } else {
          this.$q.notify({
            color: 'negative',
            message: response.data?.message || '获取用户权限失败',
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('获取用户权限错误：', error)
        this.$q.notify({
          color: 'negative',
          message: '获取用户权限失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    
    filterAppOptions() {
      if (!this.newAccess.access_group) {
        this.filteredAppOptions = []
        return
      }
      
      // 根据所选权限组筛选应用选项
      this.filteredAppOptions = this.appOptions
        .filter(app => app.group === this.newAccess.access_group)
        .map(app => app.value)
    },
    
    async addAccess() {
      if (!this.selectedUser || !this.newAccess.access_group || !this.newAccess.app_name) {
        this.$q.notify({
          color: 'negative',
          message: '请完成所有必填字段',
          icon: 'warning'
        })
        return
      }
      
      this.submitting = true
      
      // 获取当前选择用户的信息
      const selectedUserInfo = this.users.find(user => user.employee_id === this.selectedUser) || {}
      const employeeName = selectedUserInfo.employee_name || selectedUserInfo.Employee_Name || ''
      
      console.log('添加权限请求数据:', {
        employee_id: this.selectedUser,
        employee_name: employeeName,
        access_group: this.newAccess.access_group,
        app_name: this.newAccess.app_name
      })
      
      try {
        const response = await this.$http.post('/userManagement_api/set-access', {
          employee_id: this.selectedUser,
          employee_name: employeeName,
          access_group: this.newAccess.access_group,
          app_name: this.newAccess.app_name
        })
        
        if (response.data.status === 200) {
          if (response.data.message === '权限已存在') {
            this.$q.notify({
              color: 'warning',
              message: '该权限已存在',
              icon: 'info'
            })
          } else {
            this.$q.notify({
              color: 'positive',
              message: '权限添加成功',
              icon: 'check'
            })
          }
          
          // 重新获取用户权限
          this.fetchUserAccess()
          
          // 重置表单
          this.newAccess = {
            access_group: null,
            app_name: null
          }
        } else {
          this.$q.notify({
            color: 'negative',
            message: response.data.message || '添加权限失败',
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('添加权限错误：', error)
        this.$q.notify({
          color: 'negative',
          message: '网络请求错误，请稍后重试',
          icon: 'error'
        })
      } finally {
        this.submitting = false
      }
    },
    
    async removeAccess(access) {
      this.$q.dialog({
        title: '确认移除权限',
        message: `确定要移除用户 ${access.employee_name} 的 ${access.app_name} (${access.access_group}) 权限吗？`,
        cancel: true,
        persistent: true
      }).onOk(async () => {
        try {
          console.log('移除权限请求数据:', {
            access_link_id: access.access_link_id
          })
          
          const response = await this.$http.post('/userManagement_api/remove-access', {
            access_link_id: access.access_link_id
          })
          
          if (response.data.status === 200) {
            this.$q.notify({
              color: 'positive',
              message: '权限移除成功',
              icon: 'check'
            })
            
            // 重新获取用户权限
            this.fetchUserAccess()
          } else {
            this.$q.notify({
              color: 'negative',
              message: response.data.message || '移除权限失败',
              icon: 'error'
            })
          }
        } catch (error) {
          console.error('移除权限错误：', error)
          this.$q.notify({
            color: 'negative',
            message: '网络请求错误，请稍后重试',
            icon: 'error'
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.user-access-management {
  max-width: 1000px;
  margin: 0 auto;
}
</style> 