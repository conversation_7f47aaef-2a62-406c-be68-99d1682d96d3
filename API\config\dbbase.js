var config = require('./config');
var sql = require('mssql');
var Q = require('q');

function _Base() {
}

//利用config账户登陆SQL
_Base.prototype._connect = function (callback) {
  var defer = Q.defer()
  var connection = new sql.ConnectionPool({
    // user: config.user,
    // password: config.password,
    // server: config.server,
    // database: config.database
    user: config.user,
    password: config.password,
    server: config.server,
    database: config.database,
    options: {
      encrypt: false,
      trustServerCertificate: true,
      enableArithAbort: true
    }
    })
  defer.resolve(connection)
  return defer.promise.nodeify(callback)
}

//query字符串的回调
_Base.prototype._query = async function (sqlstr, callBack) {
  if (!sqlstr) {
    var err = new Error('sql is empty!')
    var defer = Q.defer()
    return defer.reject(err)
  }

  if (config.debug) {
    console.log('[SQL:]', sqlstr, '[:SQL]')
  }
  return this._connect().then(function (connection) {
    return connection.connect().then(function () {
      var request = new sql.Request(connection)
      return request.query(sqlstr).then(function (recordset) {
        connection.close()
        return callBack(null, recordset)
      }).catch(function (err) {
        connection.close()
        return callBack(err)
      })
    })
  })
}


module.exports = _Base