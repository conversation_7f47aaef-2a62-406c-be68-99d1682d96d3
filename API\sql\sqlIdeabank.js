const _Base = require('../config/dbbase')
const EHS_List = require('../config/EHSAccount');

function sqlexec() {
    _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.getDepartment = function (callBack) {
    sqlexec.prototype.
        _query("select distinct line from dbo.Employee_List ", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.getEmployee = function (callBack) {
    sqlexec.prototype.
        _query(`select line,Employee_ID,Employee_PYName,Position,Business_Title,Manager_ID from dbo.Employee_List  order by Employee_PYName asc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.getIdeaType = function (callBack) {
    sqlexec.prototype.
        _query(`select * from dbo.Idea_Bank_Type`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.getBPType = function (callBack) {
    sqlexec.prototype.
        _query(`select * from dbo.Best_Practice_Type`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.getSaftyType = function (callBack) {
    sqlexec.prototype.
        _query(`select * from dbo.Safe_Incident_Type`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.getDepartment_Owner_List = function (callBack) {
    sqlexec.prototype.
        _query(`select * from dbo.Department_Owner_List where Apply_System='安全隐患'  order by area asc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.queryIdea_ByDepartment = function (department, callBack) {
    sqlexec.prototype.
        _query(`select
        Process_Status 流程状态,
        a.Date 提交日期,
        a.Department 部门,
        a.Employee_Name 提交人,
        a.Idea_Type 点子类型,
        a.Idea_Sub_Type 点子子类型,
        A.Idea_Problem 发现的问题,
        a.Idea_Solution 建议解决方案,
        a.Assigned_Action_Plan 整改人提交方案,
        a.Owner_Adopt 是否采纳,
        a.Sharing 点子价值,
        a.Owner_Rectify_Confirm 整改确认,
        a.Owner_Reject_Cause 拒绝理由,
        b.Employee_PYName 审批人,
        c.Employee_Name 整改人,
        a.Action_Complete_Date 预计整改完成时间,
        a.Assigned_Reject_Cause 整改人拒绝理由,
        a.Self_Complete 是否自主完成,
        a.Owner_Name,
        a.Assigned_By
         from Idea_Bank as a
                left join Employee_List as b on a.Owner_Name=b.Employee_ID
                left join Employee_List as c on a.Assigned_By=c.Employee_ID
                left join Employee_List as d on a.Employee_Name=d.Employee_PYName
                where a.Department='${department}'
                order by Date desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.querySafty = function (startDate,endDate, callBack) {
    sqlexec.prototype.
        _query(`  select
        Process_Status 流程状态
       ,a.[Department] 部门
       ,a.[Employee_Name] 提交人
       ,[Date] 提交日期
       ,[Incident_Daily_Type] 日常检查类型
       ,[Incident_Type] 隐患类型
       ,[Incident_Dept] 隐患区域部门
       ,[Incident_Area] 隐患区域
       ,[Incident_Repeat] 是否重复发生
       ,[Incident_Problem] 隐患内容
       ,[Incident_Image_URL] 隐患照片
       ,[Risk_Level] 风险等级
       ,[Owner_Adopt] 是否采纳
       ,b.Employee_Name 审批人
       ,[Owner_Confirm_Date] 审批时间
       ,[Owner_Rejected_Cause] 拒绝理由
       ,[Owner_Rectify_Confirm] 区域整改确认
       ,[Owner_Rectify_Confirm_Date] 区域整改确认时间
       ,[Owner_Return_Reason] 整改退回原因
       ,[Assigned_By] 整改人
       ,c.Employee_Name 整改人姓名
       ,[Assigned_Rectify_Confirm] 整改人确认
       ,[Assigned_Rectify_Confirm_Date] 整改人确认时间
       ,[Assigned_Rectify_Comment] 整改人方案
       ,[Assigned_Temporary_Solution] 临时方案
       ,[Assigned_Reject_Reason] 整改人拒绝理由
       ,[Assigned_Rectify_Image_URL] 整改照片
       ,[Owner_Name]
       ,a.ID
          from [Safe_Incident_Data] as a
          left join Employee_List as b on a.Owner_Name=b.Employee_ID
          left join Employee_List as c on a.Assigned_By=c.Employee_ID
                 where a.Date between '${startDate}' and '${endDate}'
                 order by Date desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.queryIdea = function (callBack) {
    sqlexec.prototype.
        _query(`select
        Process_Status 流程状态,
        format(a.Date,'yyyy-MM-dd') 提交日期,
        a.Department 部门,
        a.Employee_Name 提交人,
        a.Idea_Type 点子类型,
        a.Idea_Sub_Type 点子子类型,
        A.Idea_Problem 发现的问题,
        a.Idea_Solution 建议解决方案,
        a.Assigned_Action_Plan 整改人提交方案,
        a.Owner_Adopt 是否采纳,
        a.Sharing 点子价值,
        a.Owner_Rectify_Confirm 整改确认,
        a.Owner_Reject_Cause 拒绝理由,
        b.Employee_PYName 审批人,
        c.Employee_Name 整改人,
        a.Action_Complete_Date 预计整改完成时间,
        a.Assigned_Reject_Cause 整改人拒绝理由,
        a.Self_Complete 是否自主完成,
        a.Owner_Name,
        a.Assigned_By
         from Idea_Bank as a
                left join Employee_List as b on a.Owner_Name=b.Employee_ID
                left join Employee_List as c on a.Assigned_By=c.Employee_ID
                left join Employee_List as d on a.Employee_Name=d.Employee_PYName
                where a.Date >='2023-12-29'
                order by Date desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.queryBP_ByDepartment = function (department, callBack) {
    sqlexec.prototype.
        _query(`select
        a.Date 提交日期,
        a.Department 部门,
        a.Employee_Name 提交人,
        a.BP_Type 点子类型,
		a.Improve_Line 改善机台,
		a.Owner_Adopt 是否采纳,
		a.Sharing 点子价值,
        A.BP_Problem 发现的问题,
        a.BP_Solution 建议解决方案,
        a.BP_URL BP链接,
        a.BPEquipment_Upgrading 是否变更设备,
        a.BPdrawing_URL 变更设备图纸,
        a.Owner_Reject_Cause 拒绝理由,
        b.Employee_PYName 审批人,
        a.Owner_Name,
		a.ID
         from Best_Practice as a
         left join Employee_List as b on a.Owner_Name=b.Employee_ID
                where a.Department='${department}'
                order by Date desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.queryBP = function (callBack) {
    sqlexec.prototype.
        _query(`select
        format(a.Date,'yyyy-MM-dd') 提交日期,
        a.Department 部门,
        a.Employee_Name 提交人,
        a.BP_Type 点子类型,
		a.Improve_Line 改善机台,
		a.Owner_Adopt 是否采纳,
		a.Sharing 点子价值,
        A.BP_Problem 发现的问题,
        a.BP_Solution 建议解决方案,
        a.BP_URL BP链接,
        a.BPEquipment_Upgrading 是否变更设备,
        a.BPdrawing_URL 变更设备图纸,
        a.Owner_Reject_Cause 拒绝理由,
        b.Employee_PYName 审批人,
        a.Owner_Name,
		a.ID
         from Best_Practice as a
         left join Employee_List as b on a.Owner_Name=b.Employee_ID
                where a.Date >='2023-12-29'
                order by Date desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.querySafty = function (startDate,endDate, callBack) {
    sqlexec.prototype.
        _query(`  select
        Process_Status 流程状态
       ,a.[Department] 部门
       ,a.[Employee_Name] 提交人
       ,[Date] 提交日期
       ,[Incident_Daily_Type] 日常检查类型
       ,[Incident_Type] 隐患类型
       ,[Incident_Dept] 隐患区域部门
       ,[Incident_Area] 隐患区域
       ,[Incident_Repeat] 是否重复发生
       ,[Incident_Problem] 隐患内容
       ,[Incident_Image_URL] 隐患照片
       ,[Risk_Level] 风险等级
       ,[Owner_Adopt] 是否采纳
       ,b.Employee_Name 审批人
       ,[Owner_Confirm_Date] 审批时间
       ,[Owner_Rejected_Cause] 拒绝理由
       ,[Owner_Rectify_Confirm] 区域整改确认
       ,[Owner_Rectify_Confirm_Date] 区域整改确认时间
       ,[Owner_Return_Reason] 整改退回原因
       ,[Assigned_By] 整改人
       ,c.Employee_Name 整改人姓名
       ,[Assigned_Rectify_Confirm] 整改人确认
       ,[Assigned_Rectify_Confirm_Date] 整改人确认时间
       ,[Assigned_Rectify_Comment] 整改人方案
       ,[Assigned_Temporary_Solution] 临时方案
       ,[Assigned_Reject_Reason] 整改人拒绝理由
       ,[Assigned_Rectify_Image_URL] 整改照片
       ,[Owner_Name]
       ,a.ID
          from [Safe_Incident_Data] as a
          left join Employee_List as b on a.Owner_Name=b.Employee_ID
          left join Employee_List as c on a.Assigned_By=c.Employee_ID
                 where a.Date between '${startDate}' and '${endDate}'
                 order by Date desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.myApprovalIdea = function (employeeID, processType, callBack) {
    sqlexec.prototype.
        _query(`select
        Process_Status 流程状态,
        a.Date 提交日期,
        a.Department 部门,
        a.Employee_Name 提交人,
        a.Idea_Type 点子类型,
        a.Idea_Sub_Type 点子子类型,
        A.Idea_Problem 发现的问题,
        a.Idea_Solution 建议解决方案,
        a.Assigned_Action_Plan 整改人提交方案,
        a.Owner_Adopt 是否采纳,
        a.Sharing 点子价值,
        a.Owner_Rectify_Confirm 整改确认,
        a.Owner_Reject_Cause 拒绝理由,
        b.Employee_PYName 审批人,
        c.Employee_Name 整改人,
        a.Action_Complete_Date 预计整改完成时间,
        a.Assigned_Reject_Cause 整改人拒绝理由,
        a.Self_Complete 是否自主完成,
        a.Owner_Name,
        a.Assigned_By,
        a.ID
         from Idea_Bank as a
                left join Employee_List as b on a.Owner_Name=b.Employee_ID
                left join Employee_List as c on a.Assigned_By=c.Employee_ID
                left join Employee_List as d on a.Employee_Name=d.Employee_PYName
        where  ${processType == 'ideaApprove' ? "Owner_Name='" + employeeID + "'" : "Assigned_By='" + employeeID + "' and Owner_Adopt='Yes'"} order by Date desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.myApprovalIdea_BP = function (employeeID, callBack) {
    sqlexec.prototype.
        _query(`select
        a.Date 提交日期,
        a.Department 部门,
        a.Employee_Name 提交人,
        a.BP_Type BP类型,
		a.Improve_Line 改善机台,
		a.Owner_Adopt 是否采纳,
		a.Sharing BP价值,
        A.BP_Problem 发现的问题,
        a.BP_Solution 建议解决方案,
        a.BP_URL BP链接,
        a.BPEquipment_Upgrading 是否变更设备,
        a.BPdrawing_URL 变更设备图纸,
        a.Owner_Reject_Cause 拒绝理由,
        b.Employee_PYName 审批人,
        a.Owner_Name,
		a.ID
         from Best_Practice as a
         left join Employee_List as b on a.Owner_Name=b.Employee_ID
                where a.owner_Name='${employeeID}'
                order by Date desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.myApprovalSafty = function (employeeID, processType, callBack) {
    sqlexec.prototype.
        _query(`select
        [Process_Status] 流程状态
        ,a.[Department] 部门
       ,a.[Employee_Name] 提交人
       ,format([Date],'yyyy-MM-dd') 提交日期
       ,[Owner_Adopt] 是否采纳
       ,[Risk_Level] 风险等级
       ,[Incident_Type] 隐患类型
       ,[Incident_Area] 隐患区域
       ,[Incident_Repeat] 是否重复发生
       ,[Incident_Problem] 隐患内容
       ,[Incident_Image_URL] 隐患照片
       ,[Incident_Dept] 隐患区域部门
       ,[Incident_Daily_Type] 日常检查类型
       ,b.Employee_Name 审批人
       ,[Owner_Confirm_Date] 审批时间
       ,[Owner_Rejected_Cause] 拒绝理由
       ,[Owner_Rectify_Confirm] 区域整改确认
       ,[Owner_Rectify_Confirm_Date] 区域整改确认时间
       ,[Owner_Return_Reason] 整改退回原因
       ,c.Employee_PYName 整改人
       ,[Assigned_Rectify_Confirm] 整改人完成整改确认
       ,[Assigned_Rectify_Confirm_Date] 整改人完成整改时间
       ,[Assigned_Rectify_Comment] 整改人方案
       ,[Assigned_Temporary_Solution] 临时方案
       ,Assigned_Rectify_Date 预计完成时间
       ,[Assigned_Reject_Reason] 整改人拒绝理由
       ,[Assigned_Rectify_Image_URL] 整改照片
        ,a.Assigned_By
       ,[Owner_Name]
       ,a.ID
          from [Safe_Incident_Data] as a
          left join Employee_List as b on a.Owner_Name=b.Employee_ID
          left join Employee_List as c on a.Assigned_By=c.Employee_ID
            where  ${processType == 'saftyApprove' ? "Owner_Name='" + employeeID + "' or  (Process_Status='EHS Check' and '" + EHS_List.managerAccount + "'='" + employeeID +"')" : "Assigned_By='" + employeeID + "' and Owner_Adopt='Yes'"} order by Date desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}



sqlexec.prototype.insertIdea = function (data, callBack) {
    sqlexec.prototype.
        _query(`insert into dbo.Idea_Bank(Mill,Department,Employee_Name,Date,Idea_Type,Idea_Problem,Idea_Solution,Owner_Name,Assigned_By,Process_Status,Self_Complete)
        select '南京南厂','${data['department']}','${data['employee_Name']}',getdate(),'${data['ideaType']}','${data['ideaProblem']}','${data['ideaSolution']}', Manager_ID,'${data['assignedBy']['value']}','open','${data['selfComplete']}'
        from Employee_List where Employee_PYName='${data['employee_Name']}'`, function (err, result) {
            if (err) {
            }
            console.log('result', result)
            return callBack('已添加')
        })
}
sqlexec.prototype.insertBP = function (data, Manager_ID, callBack) {
    sqlexec.prototype.
        _query(`insert into dbo.Best_Practice(Mill,Department,Employee_Name,Date,BP_Type,Improve_Line,BP_Problem,BP_Solution,BP_URL,BPEquipment_Upgrading,BPdrawing_URL,Owner_Name)
        values( '南京南厂','${data['department']}','${data['employee_Name']}',getdate(),
        '${data['BP_Type']}','${data['Improve_Line']}','${data['BP_Problem']}',
        '${data['BP_Solution']}','${data['BP_URL']}','${data['BPEquipment_Upgrading']}',
        ${data['BPdrawing_URL'] == null ? 'null' : "'" + data['BPdrawing_URL'] + "'"}, '${Manager_ID}')`, function (err, result) {
            if (err) {
            }
            return callBack('已添加')
        })
}

sqlexec.prototype.insertSafty = function (data, Manager_ID, Entry_By, callBack) {
    sqlexec.prototype.
        _query(`insert into dbo.Safe_Incident_Data(
            Mill,Department,Employee_Name,Date,Incident_Daily_Type,Incident_Type,Incident_Dept,Incident_Area,Incident_Repeat
            ,Incident_Problem,Incident_Image_URL,Owner_Name,Process_Status,Entry_By,Entry_On
            )
            values('南京南厂','${data['department']}','${data['employee_Name']}',getdate(),'${data['Safty_DailyType']}','${data['Safty_Type']}','${data['Safty_Department']}','${data['Safty_Area']}','${data['Safty_Repeat']}','${data['Safty_Problem']}','${data['Safty_URL']}','${Manager_ID}','open','${Entry_By}',getdate())`, function (err, result) {
            if (err) {
            }
            return callBack('已添加')
        })
}


sqlexec.prototype.updateIdea = function (data, processType, callBack) {
    let sqlstr = ''
    if (processType == 'approval') {
        if (data['流程状态'] == 'open') {
            sqlstr = `update dbo.Idea_Bank set Idea_Sub_Type='${data['点子子类型']}',Owner_Adopt='${data['是否采纳']}',Owner_Confirm_Date=getdate(),
            Owner_Reject_Cause=${data['拒绝理由'] == null ? 'null' : "'" + data['拒绝理由'] + "'"},
            Sharing=${data['点子价值'] == null ? 'null' : "'" + data['点子价值'] + "'"},
            Points=${data['是否采纳'] == 'Yes' && data['点子价值'] == '铂金' ? "(select Point  from Award_Base where Award='点子铂金')" : data['是否采纳'] == 'Yes' && data['点子价值'] == '金' ? "(select Point  from Award_Base where Award='点子金')" : data['是否采纳'] == 'Yes' && data['点子价值'] == '银' ? "(select Point  from Award_Base where Award='点子银')" : 0},
            Assigned_By='${data['Assigned_By'].hasOwnProperty('value') ? data['Assigned_By']['value'] : data['Assigned_By']}',
            Process_Status='${data['是否采纳'] == 'Yes' ? 'In progress' : 'Rejected'}',Assigned_Reject_Cause=null
            where ID=${data['ID']}`
        } else if (data['流程状态'] == 'Completed') {
            sqlstr = `update dbo.Idea_Bank set
            Owner_Rectify_Confirm='${data['整改确认']}',
            Process_Status='Completed'
            where ID=${data['ID']}`
        } else if (data['流程状态'] == 'In progress') {
            sqlstr = `update dbo.Idea_Bank set
            Owner_Rectify_Confirm='${data['整改确认']}',
            Process_Status='In progress'
            where ID=${data['ID']}`
        }

    } else if (processType == 'feedback') {
        if (data['流程状态'] == 'Final Check') {
            sqlstr = `update dbo.Idea_Bank set
            Assigned_Action_Plan='${data['整改人提交方案']}',
            Action_Complete_Date='${data['预计整改完成时间']}',
            Process_Status='Final Check'
            where ID=${data['ID']}`
        } else {
            sqlstr = `update dbo.Idea_Bank set
            Assigned_Reject_Cause='${data['整改人拒绝理由']}',
            Process_Status='open'
            where ID=${data['ID']}`
        }
    }
    console.log('sqlstr', sqlstr)
    sqlexec.prototype.
        _query(sqlstr, function (err, result) {
            if (err) {
            }
            return callBack('已更新')
        })
}
sqlexec.prototype.updateBP = function (data, callBack) {
    sqlexec.prototype.
        _query(`update dbo.Best_Practice set Owner_Adopt='${data['是否采纳']}',Owner_Confirm_Date=getdate(),
        Owner_Reject_Cause=${data['拒绝理由'] == null ? 'null' : "'" + data['拒绝理由'] + "'"},
        Sharing=${data['BP价值'] == null ? 'null' : "'" + data['BP价值'] + "'"},
        Points=${data['是否采纳'] == 'Yes' ? "(select Point  from Award_Base where Award='BP" + data['BP价值'] + "')" : 0}
        where ID=${data['ID']}`, function (err, result) {
            if (err) {
            }
            return callBack('已更新')
        })
}

sqlexec.prototype.updateSafty = function (data, Entry_By, processType, callBack) {
    let sqlstr = ''
    if (data['process_Status'] == 'open' || data['process_Status'] == 'In progress' && processType == 'approval') {
        if (data['owner_approval_status'] == 'Yes') {
            sqlstr = `update [Safe_Incident_Data] set Owner_Adopt='${data['owner_approval_status']}',Owner_Confirm_Date=getdate(),
                    Risk_Level='${data['riskLevel']}',Assigned_By='${data['assigned_name']['value']}',Process_Status='In progress',
                    Modified_by='${Entry_By}',Modified_On=getdate()
                    where ID=${data['ID']}`
        } else {
            sqlstr = `update [Safe_Incident_Data] set Owner_Adopt='${data['owner_approval_status']}',Owner_Confirm_Date=getdate(),
                    Owner_Rejected_Cause='${data['rejectedComment']}',Modified_by='${Entry_By}',Modified_On=getdate(),Process_Status='Rejected'
                    where ID=${data['ID']}`
        }
    } else if ((data['process_Status'] == 'Final Check' || data['process_Status'] == 'EHS Check') && processType == 'approval') {
        if (data['owner_approval_status'] == 'Yes') {
            sqlstr = `update [Safe_Incident_Data] set Owner_Rectify_Confirm='${data['owner_approval_status']}',Owner_Rectify_Confirm_Date=getdate(),
            Process_Status='${data['riskLevel'] == '高' && data['process_Status'] == 'Final Check' ? 'EHS Check' : 'Completed'}',Owner_Return_Reason=null,
            Modified_by='${Entry_By}',Modified_On=getdate()
            where ID=${data['ID']}`
        } else {
            sqlstr = `update [Safe_Incident_Data] set Owner_Rectify_Confirm='${data['owner_approval_status']}',Owner_Rectify_Confirm_Date=getdate(),
            Owner_Return_Reason='${data['rejectedComment']}',Process_Status='In progress',Assigned_Rectify_Confirm=null,
            Modified_by='${Entry_By}',Modified_On=getdate()
            where ID=${data['ID']}`
        }
    } else if (data['process_Status'] == 'In progress' && processType == 'feedback') {
        if (data['assigned_approval_status'] == 'Yes') {
            sqlstr = `update [Safe_Incident_Data] set Assigned_Rectify_Comment='${data['solution']}',Assigned_Temporary_Solution='${data['temporary_Solution']}',
            Assigned_Rectify_Date='${data['assigned_Rectify_Date']}',Assigned_Reject_Reason=null,
            Process_Status='Wait Rectify',
            Modified_by='${Entry_By}',Modified_On=getdate()
            where ID=${data['ID']}`
        } else {
            sqlstr = `update [Safe_Incident_Data] set Assigned_Reject_Reason='${data['assigned_Reject_Comment']}',
            Process_Status='open',
            Modified_by='${Entry_By}',Modified_On=getdate()
            where ID=${data['ID']}`
        }
    } else if (data['process_Status'] == 'Wait Rectify' && processType == 'feedback') {
        if (data['assigned_Rectify_Confirm'] == 'Yes') {
            sqlstr = `update [Safe_Incident_Data] set Assigned_Rectify_Confirm='Yes',Assigned_Rectify_Confirm_Date=getdate(),
            Assigned_Rectify_Image_URL='${data['rectify_image_url']}',
            Process_Status='Final Check',
            Modified_by='${Entry_By}',Modified_On=getdate()
            where ID=${data['ID']}`
        } else {
            sqlstr = `update [Safe_Incident_Data] set Assigned_Reject_Reason='${data['assigned_Reject_Comment']}',
            Process_Status='open',
            Modified_by='${Entry_By}',Modified_On=getdate()
            where ID=${data['ID']}`
        }

    }
    //
    console.log(sqlstr)
    sqlexec.prototype.
        _query(sqlstr, function (err, result) {
            if (err) {
            }
            return callBack('已更新')
        })
}





module.exports = sqlexec;