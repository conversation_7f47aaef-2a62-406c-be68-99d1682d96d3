<template>

    <div>
        <q-dialog v-model="prompt_addWork_Principle" transition-show="flip-down">
            <q-card style="width: 80%; max-width: 100vw;">
                <q-card-section class="row items-center q-pb-none bg-primary text-white ">
                    <div class="text-h6 " style="width: 90%;">工作原理添加</div>
                    <q-space />
                    <q-btn icon="close" flat round dense v-close-popup />
                </q-card-section>

                <q-card-section>
                    <div class="row wrap justify-start">
                        <div style="font-size: 15px;line-height: 45px;font-weight: 600;">工作原理名称：</div>
                        <q-input outlined v-model="Work_Principle_data[Work_Principle_Index]['Work_Principle_Name']"
                            clearable style="width:88%;" dense filled>
                        </q-input>
                    </div>
                    <div class="row wrap justify-start q-gutter-xs">
                        <q-uploader style="width:40%;height: 230px;" batch auto-upload dense
                            :factory="factoryFn_Work_Principle" @uploaded="handleUploadSuccess_Work_Principle"
                            label="工作原理图或视频" />
                        <div style="width:57%;height: 230px;">
                            <div style="font-size: 15px;line-height: 45px;font-weight: 600;">
                                照片内容简要说明，包括流程说明，设定介绍等
                            </div>
                            <div>
                                <q-input
                                    v-model="Work_Principle_data[Work_Principle_Index]['Work_Principle_Description']"
                                    filled type="textarea" clearable>
                                </q-input>
                            </div>
                        </div>

                    </div>
                    <div class="text-h6" style="font-size: 17px;line-height: 45px;font-weight: 600;">
                        <q-checkbox
                            v-model="Work_Principle_data[Work_Principle_Index]['Key_Setting_selected']" />关键设定指标
                        <q-btn padding="none" color="primary" icon="add" @click="addWork_Principle_Key_Setting()"
                            v-if="Work_Principle_data[Work_Principle_Index]['Key_Setting_selected']" />
                    </div>
                    <div v-if="Work_Principle_data[Work_Principle_Index]['Key_Setting_selected']">
                        <div v-for="(Key_Setting, index) in Work_Principle_data[Work_Principle_Index]['Key_Setting']"
                            :key="index">

                            <div class="row wrap justify-start q-gutter-sm text-body2 text-center">
                                <div>
                                    <div>Remove</div>
                                    <div style="line-height:35px;">
                                        <q-btn padding="none" color="negative" icon="horizontal_rule"
                                            @click="removeWork_Principle_Key_Setting(index)"
                                            v-if="Work_Principle_data[Work_Principle_Index]['Key_Setting_selected']" />
                                    </div>

                                </div>

                                <div>
                                    <div>关键设定名称</div>
                                    <q-input outlined v-model="Key_Setting.Key_Setting_Name" dense filled
                                        style="width: 200px;">
                                    </q-input>
                                </div>
                                <div>
                                    <div>标准说明</div>
                                    <q-input outlined v-model="Key_Setting.Key_Setting_Description" dense filled
                                        style="width: 600px;">
                                    </q-input>
                                </div>
                            </div>
                        </div>
                    </div>
                </q-card-section>
                <q-card-actions>
                    <q-btn color="primary" label="确定" @click="saveWork_Principle()" />
                    </q-card-actions>
            </q-card>
        </q-dialog>



        <div class="text-h6">
            <q-checkbox v-model="displayVisible" />是否涉及到工作原理 <q-btn padding="none"
                color="primary" icon="add" @click="addWork_Principle_Line()"
                v-if="displayVisible" />
        </div>
        <div v-if="displayVisible">
            <div v-for="(Work_Principle, index) in Work_Principle_data" :key="index">
                <div class="row wrap justify-start q-gutter-sm text-body2 text-center">
                    <div>
                        <div>Remove</div>
                        <div style="line-height:30px;">
                            <q-btn padding="none" color="negative" icon="horizontal_rule"
                                @click="removeWork_Principle(index)" v-if="displayVisible" />
                        </div>

                    </div>
                    <div>
                        <div>工作原理名称</div>
                        <q-btn color="primary" icon="east"
                            :label="Work_Principle.Work_Principle_Name == '' ? '添加工作原理名称' : Work_Principle.Work_Principle_Name"
                            @click="addWork_Principle(index)">
                        </q-btn>
                    </div>
                </div>
            </div>
        </div>
    </div>


</template>

<script>
export default {
    props: {
        opType: {
            type: String,
            default: '新增'
        },
        visible: {
            type: Boolean,
            default:false  
        },
        linkid: {
            type: String,
            default: false
        }
    },
    mounted() {
        console.log('Work_Principle_子组件opType:', this.opType)
        console.log('Work_Principle_子组件LinkID:', this.linkid)
        this.LinkID = this.linkid
        if (this.opType == '查询' || this.opType == 'Approver') {

        }

    },
    // watch: {
    //     visible(newValue, oldValue) {
    //         this.displayVisible = newValue
    //         console.log('oldValue:', oldValue)
    //         console.log('newValue:', newValue)
    //     },
    //     LinkID(newValue, oldValue) {
    //             console.log('LinkID——oldValue:', oldValue)
    //             console.log('LinkID——newValue:', newValue)
    //     }
    // },
    // computed: {
    //     displayVisible() {
    //         // 这里可以根据需要处理 visible
    //         return this.visible;
    //     }
    // },

    data() {
        return {
            displayVisible:true,
            LinkID:'',
            Work_Principle_Index: 0,
            prompt_addWork_Principle: false,
            Work_Principle_data: [
                {
                    "Work_Principle_Name": '',
                    "Work_Principle_Description": '',
                    "Work_Principle_URL": '',
                    "Key_Setting_selected": false,
                    "Key_Setting": [
                        {
                            "Key_Setting_Name": '',
                            "Key_Setting_Description": '',
                        }
                    ],
                }
            ],
        }
    },

    computed: {

    },
    watch: {
        Work_Principle_data: {
            handler(newVal) {
                console.log('updateWork_Principle_data 更新:', newVal);
                this.$store.commit('updateWork_Principle_data', newVal)
            },
            deep: true
        },
    },


    methods: {
        factoryFn_Work_Principle(files) {
            var _this = this
            _this.prompt_uploadFile = true
            return {
                url: 'http://*************:3001/upload?system=A3_Work_Principle',
                method: 'POST'
            }
        },
        handleUploadSuccess_Work_Principle(response) {
            // console.log('FileUploadResponse', response)
            const _this = this
            //response.files[0].xlr.responseText
            const responseURL = response['files'][0]['xhr']['responseText']
            _this.Work_Principle_data[_this.Work_Principle_Index]['Work_Principle_URL'] = responseURL
        },
        addWork_Principle_Line() {
            this.Work_Principle_data.push({
                "Work_Principle_Name": '',
                "Work_Principle_Description": '',
                "Work_Principle_URL": '',
                "Key_Setting_selected": false,
                "Key_Setting": [
                    {
                        "Key_Setting_Name": '',
                        "Key_Setting_Description": '',
                    }
                ],
            }
            )

        },
        removeWork_Principle(index) {
            this.Work_Principle_data.splice(index, 1)
        },
        addWork_Principle(index) {
            const _this = this
            _this.Work_Principle_Index = index
            _this.prompt_addWork_Principle = true
        },
        removeWork_Principle_Key_Setting(index) {
            this.Work_Principle_data[this.Work_Principle_Index]['Key_Setting'].splice(index, 1)
        },
        addWork_Principle_Key_Setting() {
            this.Work_Principle_data[this.Work_Principle_Index]['Key_Setting'].push({
                "Key_Setting_Name": '',
                "Key_Setting_Description": '',
            })
        },

        saveWork_Principle() {
            const _this = this
            _this.prompt_addWork_Principle = false
        }

    }
}


</script>

<!-- <style>
.black_border {
    border: 1px solid black;
}
</style> -->
