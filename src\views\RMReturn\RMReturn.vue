<template>
	<base-content>
		<div style="margin-left: 250px;" class="q-pa-md">
			<div class="q-gutter-sm" style="margin-bottom: -8px;">
				<q-radio keep-color v-model="RM_Type" val="合格材料" label="合格材料退料" color="teal"
					style="font-size: 25px;font-weight: 400;color:teal" />
				<q-radio keep-color v-model="RM_Type" val="NCR不合格材料" label="NCR不合格材料退料" color="red"
					style="font-size: 25px;color: red;font-weight: 400;" />
			</div>
			<div style="font-size: 15px;margin-left: 100px;margin-top: 8px;">
				你将进行退料的是: <strong style="font-size: 20px;" :style="{ color: RM_Type === '合格材料' ? 'teal' : 'red' }">{{
					RM_Type }}</strong>
			</div>
		</div>
		<div dense class="q-pa-md" style="margin-top: -30px;">
			<div class="q-gutter-md row " style="margin-left: 83px;margin-top: 0px;">
				<q-select outlined v-model="Line" :options="LineList" label="产线" style="width: 100px" />
				<q-input outlined v-model="RM" style="width: 180px;" label-slot clearable>
					<template v-slot:label>材料编码
						<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
					</template>
				</q-input>
				<q-input v-model="RMData[0].RM_Desc" standout disable filled autogrow label="材料描述"
					style="width: 350px;" />
				<q-btn color="primary" @click="startPrinting('single')" label="打印单卷标签" class="print-button"
					style="width: 120px;height: 45px;" />
			</div>
			<div style="margin-left: 83px;margin-top: 5px;" class="q-gutter-md row">
				<div style="width: 100px;">
					<q-input v-if="RMData[0].NW === 'Y'" v-model="Weightdata[2].RollQTY" standout disable filled
						autogrow label="参考单卷重量" style="width: 100%;" />
				</div>
				<div style="width: 180px;">
					<q-input outlined v-model="QTY" style="width: 100%;" label-slot clearable>
						<template v-slot:label>退料数量
							<em class="q-px-sm bg-deep-orange text-white rounded-borders"
								style="font-size: 12px;">必填</em>
						</template>
					</q-input>
				</div>
				<q-input v-model="RMData[0].UOM" standout disable filled autogrow label="单位" style="width: 350px;" />
				<q-btn color="primary" @click="PalletPrint = true" label="打印整托标签" class="print-button"
					style="width: 120px;height: 45px;" />
			</div>
		</div>
		<q-dialog v-model="PalletPrint">
			<q-card style="width: 450px; max-width: 80vw;">
				<q-card-section>
					<div class="q-pa-md ">
						<div class="q-gutter-md row">
							<q-input outlined type="number" v-model="palletQTY" label="整托数量"
								style="width: 180px;font-size: 20px;height: 40px;" />
							<q-btn dense color="primary" label="打印" @click="startPrinting('pallet')"
								style="width: 70px;" />
							<q-btn dense color="primary" label="取消" @click="PalletPrint = false" style="width: 70px;" />
						</div>
					</div>
				</q-card-section>
			</q-card>
		</q-dialog>
		<div style="margin-left: 215px;margin-top: 5px;" class="q-gutter-md row ">
			<q-select outlined v-model="OP" :options="OPList" style="width: 180px" label-slot clearable>
				<template v-slot:label>退料人
					<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
				</template>
			</q-select>
			<q-input outlined v-model="Reason" label="退料原因" style="width: 350px;" />
		</div>
		<div style="margin-left: 215px;margin-top: 5px;" class="q-gutter-md row ">
			<q-input v-model="RMRutrn_Date" label="退料时间" style="width: 180px;" standout disable filled autogrow />
			<q-input outlined v-model="Vendor_Lot" style="width: 350px;" label-slot clearable>
				<template v-slot:label>供应商生产日期
					<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
				</template>
			</q-input>
		</div>


		<div class="q-pa-md q-gutter-sm" style="width: 600px;margin: 0 auto;"></div>
		<div style="text-align: center;">{{ qrCodeContent }}</div>
		<div style="text-align: center;">
			<div style="display: inline-block;">
				<canvas id="qrcode"></canvas>
			</div>
		</div>

	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import Quagga from 'quagga'
import QRCode from 'qrcode'


export default {

	components: {
		BaseContent,
		Quagga,
	},
	data() {
		return {
			RM_Type: "合格材料",
			PalletPrint: false,
			printType: 'single',
			User_ID: "",
			RM: "",
			QTY: "",
			palletQTY: "",
			Line: "",
			OP: "",
			RMRutrn_Date: "",
			Reason: "生产完成",
			Vendor_Lot: "",
			qrCodeContent: "",
			RollQTY: "",
			LineList: [],
			OPList: [],
			RMData: [{
				"RM_Desc": " ",
				"UOM": " ",
				"materialGroup": "",
				"NW": ""
			}],
			Weightdata: [{
				"Line": " ",
				"RM_Code": " ",
				"RollQTY": ""
			}]
		};
	},
	watch: {
		RM: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.cleardata()
			this.getRM(newVal)
			this.getWeightdata(newVal)
			this.CreateQR('single')
		},
		QTY: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.CreateQR('single')
		},
		Vendor_Lot: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.CreateQR('single')
		},
		Line: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.getOPList()
		}
	},
	mounted() {
		console.log("mounted", this.$route.query)
		this.User_ID = localStorage.getItem('account')
		this.Line = localStorage.getItem('Related_Line')
		// this.getLine()
		this.LineList = localStorage.getItem('lineArray').split(',')
		this.getOPList()

	},
	methods: {
		// async getLine() {
		// 	var _this = this
		// 	const {
		// 		data: res
		// 	} = await _this.$http.get(`material/lineList`)
		// 	console.log('LineList', res)
		// 	_this.LineList = res
		// },

		async getOPList() {
			var _this = this
			var Line = this.Line
			const {
				data: res
			} = await _this.$http.get(`material/OPList?Line=${Line}`)
			console.log('OPList', res)
			_this.OPList = res
		},

		async getRM(RM_Code) {
			var _this = this
			if (RM_Code.length === 8) {
				const {
					data: res
				} = await _this.$http.get(`material/RM?RM_Code=${RM_Code}`)
				console.log('RM', res)
				// _this.skuList=res.map(item=>item.SKU_order)
				_this.RMData = res

			}
		},
		async getWeightdata(RM_Code) {
			var _this = this
			var Line = this.Line
			if (RM_Code.length === 8) {
				const {
					data: res
				} = await _this.$http.get(`mes/RollWeight?Line=${Line}&RM_Code=${RM_Code}`)
				console.log('Weight', res)
				// _this.skuList=res.map(item=>item.SKU_order)
				_this.Weightdata = res

			}
		},
		async cleardata() {
			this.QTY = "";
			this.palletQTY = "";
			this.Vendor_Lot = "";
			this.qrCodeContent = "";
			this.RMData[0].RM_Desc = "";
			this.RMData[0].UOM = "";
			this.Weightdata[2].RollQTY = "";
			this.RMData[0].NW = "";
		},


		CreateQR(printType) {
			//生成二维码
			var currentDate = new Date();
			var date = currentDate.toISOString("zh-CN", { timeZone: "Asia/Shanghai", hour12: false }).slice(0, 10).replace(/-/g, "");
			var datetime = currentDate.toLocaleString("zh-CN", { timeZone: "Asia/Shanghai", hour12: false })
				.replace(/\//g, "")
				.replace(/\s/g, "_")
				.replace(/:/g, "");
			if (this.RM && this.QTY && this.Vendor_Lot) {
				let qrCodeStr;
				if (printType === 'single') {
					if (this.Vendor_Lot === "") {
						qrCodeStr = `${this.RM},${this.QTY},${date},${datetime}`;
						this.qrCodeContent = qrCodeStr;
					} else {
						qrCodeStr = `${this.RM},${this.QTY},${this.Vendor_Lot},${datetime}`;
						this.qrCodeContent = qrCodeStr;
					}
				}
				else if (printType === 'pallet') {
					if (this.Vendor_Lot === "") {
						qrCodeStr = `${this.RM},${this.palletQTY},${date},${datetime}`;
						this.qrCodeContent = qrCodeStr;
					} else {
						qrCodeStr = `${this.RM},${this.palletQTY},${this.Vendor_Lot},${datetime}`;
						this.qrCodeContent = qrCodeStr;
					}
				}
				console.log('退料日期', date)
				this.RMRutrn_Date = date
				console.log('QR', qrCodeStr)
				const canvas = document.getElementById('qrcode');
				if (canvas) {
					QRCode.toCanvas(canvas, this.qrCodeContent, (error) => {
						if (error) {
							console.error('QR code generation error:', error);
						}
					});
				} else { console.error('Canvas element not found'); }
			} else {
				console.error('Please enter RM and QTY and OP');
			}
		},
		isValidDate(dateStr) {
			const regex = /^\d{4}\d{2}\d{2}$/; // 正则表达式匹配 YYYYMMDD 格式
			return regex.test(dateStr);
		},

		startPrinting(printType) {
			// if (!this.isValidDate(this.Vendor_Lot)) {
			// 	this.$q.notify({
			// 		icon: 'announcement',
			// 		message: '供应商生产日期格式错误，应为 YYYYMMDD',
			// 		color: 'red',
			// 		position: 'top',
			// 		timeout: 3000
			// 	})
			// 	return;
			// }

			var RM_QTY = ""
			if (printType === 'single' && this.RM && this.OP && this.QTY && this.Vendor_Lot) {
				if (this.isValidDate(this.Vendor_Lot)) {
					RM_QTY = this.QTY; // 单卷标签打印： RM_QTY等于this.QTY
					this.startPrinting_1(RM_QTY, printType);

				} else {
					// 如果 Vendor_Lot 格式不正确，显示错误通知
					this.$q.notify({
						icon: 'warning',
						message: '供应商生产日期格式错误，应为 YYYYMMDD',
						color: 'red',
						position: 'top',
						timeout: 3000
					});
				}
				// RM_QTY = this.QTY; // 单卷标签打印： RM_QTY等于this.QTY
				// this.startPrinting_1(RM_QTY, printType)
			} else if (printType === 'pallet' && this.RM && this.OP && this.palletQTY && this.Vendor_Lot) {
				if (this.isValidDate(this.Vendor_Lot)) {
					RM_QTY = this.palletQTY; // 整托标签打印： RM_QTY等于this.PalletQTY
					this.PalletPrint = false;
					this.startPrinting_1(RM_QTY, printType);

				} else {
					this.$q.notify({
						icon: 'warning',
						message: '供应商生产日期格式错误，应为 YYYYMMDD',
						color: 'red',
						position: 'top',
						timeout: 3000
					});
				}
				// RM_QTY = this.palletQTY; // 整托标签打印： RM_QTY等于this.PalletQTY
				// this.PalletPrint = false
				// this.startPrinting_1(RM_QTY, printType)
			} else {
				this.$q.notify({
					icon: 'announcement',
					message: '请填写材料编码 & 退料数量 & 退料人 & 供应商生产日期',
					color: 'red',
					position: 'top',
					timeout: 3000
				})
			}
		},

		startPrinting_1(RM_QTY, printType) {
			this.CreateQR(printType);
			var _this = this
			const timeoutId =
				setTimeout(() => {
					const printWindow = window.open('', '_blank');
					const qrCodeImage = document.createElement('img');
					const Line = this.Line;
					const RM_Code = this.RM;
					const RM_Desc = this.RMData[0].RM_Desc;
					const RMRutrn_Date = this.RMRutrn_Date;
					const UOM = this.RMData[0].UOM;
					const OP = this.OP;
					const Reason = this.Reason;
					const RM_QR = this.qrCodeContent;
					const RM_Type = this.RM_Type;
					qrCodeImage.src = document.getElementById('qrcode').toDataURL(); // 获取二维码的DataURL
					qrCodeImage.onload = () => {
						const printContent = `<html>
      <html>
		  <style>
            table {
              max-width:9.5cm;
			  max-height:9.5cm;
              border-collapse: collapse;
			  table-layout:auto
            }
            th, td {
              border: 1px solid black;
              text-align: center;
			  line-height: 25px;
  			  vertical-align: middle;
  			  white-space: normal;
			  overflow-wrap: break-word;
			  word-break:break-word;
			  font-size: 12px;
            }
			th {
				width: 80px;
			}
			@media print {
			thead {
			display: none;
			}
			
			tfoot {
			display: none;
			}
			@page {
			size: auto;
			margin: 0;
			transform: scale(0.9);
		 	}
			body {
			display: flex;
			justify-content: center;
			align-items: center;
			}
		}
          </style>
        <body onload="window.print(); window.close();">
			<table>
				<tr>
                <td colspan="4" style="padding:0;margin:0;height:30px;width:30px">
				<div class="centered">
					<strong style="font-size:23px">
					 ${RM_Type === '合格材料' ? '合格' : 'NCR不合格'} 
					</strong> 退料标签</div>
				<tr>
					<th>机台</th>
					<td>${Line}</td>
					<th>材料编码</th>
					<td>${RM_Code}</td>
				</tr>
				<tr>
					<th>材料名称</th>
				    <td colspan="3">${RM_Desc}</td>
				</tr>	
				<tr>
					<th>退料日期</th>
				    <td>${RMRutrn_Date}</td>
					<th>退料数量</th>
					<td>${RM_QTY} ${UOM}</td>
				</tr>
				<tr>
					<th>退料人</th>
					<td colspan="3">${OP}</td>
				</tr>
				<tr>
					<th>退料原因</th>
					<td colspan="3">${Reason}</td>
				</tr>
				<tr>
                	<td colspan="4" style="padding:0;margin:0;">
				    <div>${RM_QR}</div>
          		    <div >
					<img src="${qrCodeImage.src}" dense style="height:80px;width:80px;margin-top:0px;margin-bottom: 0px;">
					</div>
        		</td>
      			</tr>
				  <tr>
			
			</tr>
          </table>

        </body>		
     </html>`;
						// 天津要在table后加此文件号
						// <td colspan="4" style="font-size: 10px;border: none;">文件编号：FORM-53135，文件版本：0，生效日期：Nov3, 2022</td>
						// printWindow.document.open();
						printWindow.document.write(printContent);
						printWindow.document.close();
						var RM_Ruturn = {
							Return_Type: RM_Type === '合格材料' ? '合格' : '不合格',
							Pallet_Lable: printType === 'single' ? 'NO' : 'YES',
							Line: Line,
							RM_Code: RM_Code,
							RM_Desc: RM_Desc,
							RM_Lot_No: this.RMRutrn_Date,
							RM_QTY: RM_QTY,
							Operator: OP,
							Comment: Reason,
							QR_Code: RM_QR,
							Entry_By: this.User_ID,
							Raw_Lot: this.Vendor_Lot === "" ? 'Null' : this.Vendor_Lot
						}
						console.log('RM_Ruturn', RM_Ruturn)
						_this.$http.post('material/API_Execute_RM_Return_Insert', RM_Ruturn)
							.then(response => {
								console.log('response', response);
								let status = response.data
								_this.$q.notify({
									icon: status == '成功' ? 'insert_emoticon' : 'announcement',
									message: '退料信息写入' + status,
									color: status == '成功' ? 'green' : 'red',
									position: 'top',
									timeout: 3000
								});
							})
					};
				}, 100)
		}
	},
}




</script>

<style lang="css" scoped>
.red-label {
	color: red;
	font-size: 20px;
}
</style>
