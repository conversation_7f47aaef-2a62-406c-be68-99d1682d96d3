<template>
	<base-content>

		<div class="q-pa-md" style="max-width: 100%;">


			<q-card>
				<q-tabs v-model="tab" dense class="text-grey" active-color="primary" indicator-color="primary"
					align="justify">
					<q-tab name="dailyApplication" label="每日申请" />
					<q-tab name="dailyPerformance" label="每日绩效审批" />
					<q-tab name="monthlyApplication" label="月度申请" />
				</q-tabs>

				<q-separator />

				<q-tab-panels v-model="tab" animated>
					<q-tab-panel name="dailyApplication" animated transition-prev="fade" transition-next="fade">
						<awardComponents moduleFunction="awardApplicationDaily" />
					</q-tab-panel>

					<q-tab-panel name="dailyPerformance" class="q-pa-none" animated transition-prev="fade"
						transition-next="fade">
						<awardComponents moduleFunction="awardPerformanceDaily" />
					</q-tab-panel>
					<q-tab-panel name="monthlyApplication" class="q-pa-none" animated transition-prev="fade"
						transition-next="fade">
						<awardComponents moduleFunction="awardApplicationMonthlyByMaintence" />
					</q-tab-panel>
				</q-tab-panels>
			</q-card>
		</div>
		<div></div>
	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import awardComponents from '../../components/componentsList/index.vue'
export default {
	name: 'awardApplication',
	components: {
		BaseContent,
		awardComponents
	},
	mounted(e) {
		const _this=this
		console.log('网页参数',_this.$route.query)
		if(_this.$route.query.hasOwnProperty('tab')){
			_this.tab=_this.$route.query.tab
		}
	},
	watch: {

	},
	data() {
		return {
			tab: 'dailyApplication',
		}
	},

	methods: {

	}
}


</script>

<style lang="sass">

</style>