var express = require('express')
var router = express.Router()
var sqlexec = require('../sql/sql_userInfo')
var moment = require('moment')

router.post("/modifyPsw", (req, res) => {
    console.log(req.body)
    const { username, oldPassword, newPassword } = req.body
    
    var login = new sqlexec()
    login.checkAccount(username, oldPassword, function (result) {
        console.log(result)
        let msg = {}
        if (result.length > 0) {
            login.modifyPSW(username, newPassword, function (result1) {
                console.log(result1)
                if (result1 == '修改成功') {
                    msg = {
                        msg: '修改密码成功',
                    }
                    res.json(msg)
                } else {
                    msg = {
                        msg: '修改密码失败',
                    }
                    res.json(msg)
                }
            })
        } else {
            msg = {
                msg: '修改密码失败,原密码错误',
            }
            res.json(msg)
        }
    })
})

router.post("/login", (req, res) => {
    console.log(req.body)
    const { account, password } = req.body
    
    var login = new sqlexec()
    login.checkAccount(account, password, function (result) {
        console.log('result', result)
        let msg = {}
        if (result.length > 0) {
            msg = {
                msg: '登陆成功',
                username: result[0],
                Access_Group: []
            }
            for (var i = 0; i < result.length; i++) {
                msg.Access_Group.push(result[i]['Access_Group'])
            }
            console.log(msg)
            res.json(msg)
        } else {
            msg = {
                msg: '登陆失败',
            }
            res.json(msg)
        }
    })
})


module.exports = router


