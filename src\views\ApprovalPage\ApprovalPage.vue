<template>
	<base-content>
		<q-dialog v-model="alert">
			<q-card style="min-width:99%">
				<q-card-actions align="right">
					<q-btn flat label="关闭" color="primary" v-close-popup />
				</q-card-actions>
				<awardComponents moduleFunction="awardMonthlyApproval" :rows="selectedRows" ></awardComponents>
				<q-card-actions align="right">
					<q-btn flat label="关闭" color="primary" v-close-popup />
				</q-card-actions>
			</q-card>
		</q-dialog>

		<div class="q-pa-md">

			<q-tabs v-model="tab" class="bg-grey-3" align="justify">
				<q-tab name="approve" label="未审批项目" />
				<q-tab name="approved" label="已审批项目" />
			</q-tabs>
			<div>
				<q-markup-table>
					<thead>
						<tr>
							<th> </th>
							<!-- <th class="text-left">审批系统</th> -->
							<th class="text-left">申请部门</th>
							<th class="text-left">申请单号</th>
							<th class="text-left">审批人姓名</th>
							<th class="text-left">审批状态</th>
							<th class="text-left">审批时间</th>
							<th class="text-left">id</th>
							<th class="text-left">层级ID</th>
						</tr>
					</thead>
					<tbody>
						<template v-for="item in data[tab]">
							<tr>
								<td class="text-left" width="100">
									<q-btn @click="updateStatus({'id':item.id,'status':'已批准','department':item.Department,'billID':item.Bill_Code})" style="margin-right:10px"
									 color="primary" label="批准" />
									<q-btn @click="updateStatus({'id':item.id,'status':'已拒绝','department':item.Department,'billID':item.Bill_Code})" color="deep-orange" label="拒绝" />
								</td>
								<td @click="gotoDetail(item)" class="text-left">{{item.Department}}</td>
								<td @click="gotoDetail(item)" class="text-left">{{item.Bill_Code}}</td>
								<td @click="gotoDetail(item)" class="text-left">{{item.Approver_Name}}</td>
								<td @click="gotoDetail(item)" class="text-left">{{item.Approval_Status}}</td>
								<td @click="gotoDetail(item)" class="text-left">{{item.Approval_Date}}</td>
								<td @click="gotoDetail(item)" class="text-left">{{item.id}}</td>
								<td @click="gotoDetail(item)" class="text-left">{{item.Approval_Level}}</td>
							</tr>
						</template>
					</tbody>
				</q-markup-table>
			</div>
		</div>
		<!-- 		        <div class="full-width row flex-center text-accent q-gutter-sm" v-if="data[tab].length=0">
		          <q-icon size="2em" name="insert_emoticon" />
		          <span class="text-h8">
		            没有您需要审批的项目
		          </span>

		        </div> -->

	</base-content>
</template>

<script>
	import BaseContent from '../../components/BaseContent/BaseContent'
	import awardComponents from '../../components/componentsList/index.vue'
	export default {
		components: {
			BaseContent,
			awardComponents
		},
		data() {
			return {
				data: [],
				tab: 'approve',
				billID: '202103ND09',
				system: '积分系统',
				alert: false,
				selectedRows:false,
			}
		},
		mounted() {
			this.getApproveList()
		},

		methods: {
			async getApproveList() {
				var _this = this
				var username = localStorage.getItem('account')
				const {
					data: res
				} = await _this.$http.get('approve/getMyApproveList?username=' + username)
				console.log('res',res)
				_this.data = res
			},

			async updateStatus(e) {
				var _this = this
				console.log('e',e)
				var data = {
					"id": e.id,
					"approveStatus": e.status,
					"billID": e.billID,
					"department":e.department
				}
				_this.$http.post('approve/updateApproveStatus', data).then(function(response) {
					console.log('response', response)
					_this.getApproveList()
				})
			},

			gotoDetail(e) {
				console.log(e)
				this.selectedRows={
					'Fin_YearMonth':e['Bill_Code'].substring(0,6),
					'Line':e['Bill_Code'].substring(6,10),
					'Department':e['Department']
				}
				console.log('this.selectedRows',this.selectedRows)
				// this.system = e[0]
				// this.billID = e[1]
				this.alert = true

			}


		}
	}
</script>

<style scoped>
</style>
