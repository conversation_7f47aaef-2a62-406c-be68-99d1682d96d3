var express = require('express')
var router = express.Router()
var sqlexec_form = require('../sql/sqlforminput')
var moment = require('moment')
const { resolve } = require('q')
const web_url = require('../config/webURL');
const request = require('request')
// web_url,request 发邮件

// router.post("/insert_quality_inspection", (req, res) => {
//     console.log('接收到的参数', req.body)
//     // post 用body, get 用query
//     const Crew = req.body.Crew
//     const line = req.body.line
//     const Technician = req.body.Technician
//     const JZJJY = req.body.JZJJY
//     const HJTJL = req.body.HJTJL
//     const LUYJL = req.body.LUYJL
//     const LUGF = req.body.LUGF
//     const Pass = req.body.Pass
//     const LYG = req.body.LYG
//     const HCCP = req.body.HCCP
//     const DPD = req.body.DPD
//     const Teams = req.body.Teams
//     const PF = req.body.PF
//     const Comments = req.body.Comments
//     var returnQuery = new sqlexec_form()
//     returnQuery.insert_quality_inspection(Crew, line, Technician, JZJJY, HJTJL, LUYJL,
//         LUGF, Pass, LYG, HCCP, DPD, Teams, PF, Comments, function (result) {
//             res.send(result)
//         })
// })

// router.get("/query_shift_change", (req, res) => {
//     console.log(req.query)
//     const line = req.query.line
//     const startdate = req.query.startdate
//     const enddate = req.query.enddate
//     var returnQuery = new sqlexec_form()
//     returnQuery.query_shift_change(line, startdate, enddate, function (result) {
//         res.send(result)
//     })
// })


// router.post("/update_shift_change", (req, res) => {
//     console.log('接收到的参数', req.body);
//     const Date = req.body.Date
//     const Category = req.body.Issue_Category
//     const Desc = req.body.Issue_Desc
//     const id = req.body.id
//     const Display_Days = req.body.Display_Days
//     var returnQuery = new sqlexec_form();
//     returnQuery.update_shift_change(Date, Category, Desc, Display_Days, id, function (result) {
//         res.send(result);
//     });
// });

// router.post("/delete_shift_change", (req, res) => {
//     console.log('接收到的参数', req.body);
//     const id = req.body.id
//     var returnQuery = new sqlexec_form();
//     returnQuery.delete_shift_change(id, function (result) {
//         res.send(result);
//     });
// });
// router.post("/insert_shfit_change", (req, res) => {
//     console.log('接收到的参数', req.body)
//     // post 用body, get 用query
//     const line = req.body.line
//     const Submitter = req.body.Submitter
//     const Issue_Category = req.body.Issue_Category
//     const Issue_Desc = req.body.Issue_Desc
//     const Display_Days = req.body.Display_Days
//     // const { line, Submitter, Issue_Category, Issue_Desc,  } = req.body
//     var returnQuery = new sqlexec_form()
//     returnQuery.insert_shfit_change(line, Submitter, Issue_Category, Issue_Desc, Display_Days, function (result) {
//             res.send(result)
//         })
// })


router.post("/insert_asset_meeting", (req, res) => {
    console.log('接收到的参数', req.body);

    const { line, Submitter, Submitter_Dept, Problem_category, Deterioration_Type, Problem_Desc, Problem_Rootcuase, Problem_Solution, Problem_Prevention, Owner_Dept, Owner, Priority, Completed_Date, Status } = req.body;
    var returnQuery = new sqlexec_form();
    returnQuery.insert_asset_meeting(line, Submitter, Submitter_Dept, Problem_category, Deterioration_Type, Problem_Desc, Problem_Rootcuase, Problem_Solution, Problem_Prevention, Owner_Dept, Owner, Priority, Completed_Date, Status, function (result) {
        if (result.error) {
            return res.status(500).send("数据库插入错误");
        }
        res.send(result);
    })
})

router.get("/query_asset_meeting", (req, res) => {
    const { line, startdate, enddate, Status } = req.query
    if (!line || !startdate || !enddate || !Status) {
        res.status(400).send("Missing parameters")
        return
    }
    var returnQuery = new sqlexec_form()
    returnQuery.query_asset_meeting(line, startdate, enddate, Status, function (result) {
        res.send(result)
    })
})

router.post("/update_asset_meeting", (req, res) => {
    console.log('接收到的参数', req.body);
    const { Problem_Desc, Problem_Rootcuase, Problem_Solution, Problem_Prevention, Owner_Dept, Owner, Priority, Completed_Date, Status, id, Deterioration_Type } = req.body
    var returnQuery = new sqlexec_form();
    returnQuery.update_asset_meeting(Problem_Desc, Problem_Rootcuase, Problem_Solution, Problem_Prevention, Owner_Dept, Owner, Priority, Completed_Date, Status, id, Deterioration_Type, function (result) {
        res.send(result);
    })
})

router.post("/delete_asset_meeting", (req, res) => {
    console.log('接收到的参数', req.body);
    const id = req.body.id
    var returnQuery = new sqlexec_form();
    returnQuery.delete_asset_meeting(id, function (result) {
        res.send(result);
    });
});

router.post("/email_asset_meeting", (req, res) => {
    console.log('接收到的参数', req.body);
    const { startdate, enddate, line } = req.body
    var returnQuery = new sqlexec_form();
    returnQuery.email_asset_meeting(startdate, enddate, line, async function (result) {
        console.log('response', result);
        const repsonse = await sendMailtoApprover(result[0]['recipientsMail'], result[0]['subjectTitle'], result[0]['strHtml'], result[0]['fromName'])
        res.send(repsonse)
        // 和152 顺序一致
    })
})
router.get("/getDepartment", (req, res) => {
    var returnQuery = new sqlexec_form()
    returnQuery.getDepartment(function (result) {
        let returnDepartment = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnDepartment.push(result[i].position)
            }
            res.send(returnDepartment)
        } else {
            res.send("获取失败")
        }
    })
})

router.get("/getEmployee", (req, res) => {
    console.log(req.query)
    var returnQuery = new sqlexec_form()
    returnQuery.getEmployee(function (result) {
        res.send(result)
    })
})

function sendMailtoApprover(to, subject, htmlbody, fromName) {
    return new Promise((resolve, reject) => {
        // 定义 API 接口地址
        const apiUrl = web_url.mailAPI
        const data = {
            'to': to,
            'subject': subject,
            'htmlbody': htmlbody,
            'fromName': fromName
        }
        console.log('data', data)
        request({
            method: "POST",
            url: apiUrl,
            form: data
        }, (err, response, data) => {
            // if(err){resolve(err)}
            // console.log('response',response.statusCode)
            if (response.statusCode == 200) {
                resolve('已发送')
            } else {
                resolve('发送失败')
            }

        })
    })
}

// router.post("/insert_Mes_Crew_Data", (req, res) => {
//     console.log('接收到的参数', req.body);
//     const { CQDate, Line, Shift, Crew, Position, Full_Name, Employee_ID,} = req.body;
//     var returnQuery = new sqlexec_form();
//     returnQuery.insert_Mes_Crew_Data(CQDate, Line, Shift, Crew, Position, Full_Name, Employee_ID, function (result) {
//         if (result.error) {
//             return res.status(500).send("数据库插入错误");
//         }
//         res.send(result);
//     })
// })

// router.get("/getEmployeename", (req, res) => {
//     console.log(req.query)

//     var returnQuery = new sqlexec_form()
//     returnQuery.getEmployeename(function (result) {
//         res.send(result)
//     })
// })

// router.get("/query_Mes_Crew_Data", (req, res) => {
//     const { Line, date, Shift} = req.query
//     // if (!Line || !date || !Shift) {
//     //     res.status(400).send("Missing parameters")
//     //     return
//     // }
//     var returnQuery = new sqlexec_form()
//     returnQuery.query_Mes_Crew_Data(Line, date, Shift, function (result) {
//         res.send(result)
//     })
// })


// router.post("/update_Mes_Crew_Data", (req, res) => {
//     console.log('接收到的参数', req.body);
//     const { date,Line,Shift,Position,Full_Name,Employee_ID,Crew } = req.body
//     var returnQuery = new sqlexec_form();
//     returnQuery.update_Mes_Crew_Data(date,Line,Shift,Position,Full_Name,Employee_ID,Crew, function (result) {
//         res.send(result);
//     })
// })
// router.post("/insert_EHS_Shift_Turnover", (req, res) => {
//     console.log('接收到的参数', req.body);
//     const { shift, Asset, Shift_Leader, CQQK, JSZT, PPE, SafetyIssue_Previousshift, EHSHighlight_Currentshift, EHS_Status, Communication,
//         ProcetiveCover_Status, CS_Status, Chemicals_Status, TravellingCrane_Status, PMDL_Status, FLBY_Status, Ladder_Status, Comments,} = req.body;
//     var returnQuery = new sqlexec_form();
//     returnQuery.insert_EHS_Shift_Turnover(shift, Asset, Shift_Leader, CQQK, JSZT, PPE, SafetyIssue_Previousshift, EHSHighlight_Currentshift, EHS_Status, Communication,
//         ProcetiveCover_Status, CS_Status, Chemicals_Status, TravellingCrane_Status, PMDL_Status, FLBY_Status, Ladder_Status, Comments, function (result) {
//         if (result.error) {
//             return res.status(500).send("数据库插入错误");
//         }
//         res.send(result);
//     })
// })

// router.get("/query_EHS_Shift_Turnover", (req, res) => {
//     console.log(req.query)
//     const Asset = req.query.Asset
//     const startdate = req.query.startdate
//     const enddate = req.query.enddate
//     var returnQuery = new sqlexec_form()
//     returnQuery.query_EHS_Shift_Turnover(Asset, startdate, enddate, function (result) {
//         console.log(result)
//         res.send(result)
//     })
// })

module.exports = router