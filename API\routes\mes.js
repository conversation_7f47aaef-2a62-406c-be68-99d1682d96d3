var express = require('express')
var router = express.Router()
var sqlexec = require('../sql/sqlExcute_mes')
var moment = require('moment')
const { resolve } = require('q')


function returnFloat(value) {
	var value = Math.round(parseFloat(value) * 100) / 100;
	var s = value.toString().split(".");
	if (s.length == 1) {
		value = value.toString() + ".00";
		return value;
	}
	if (s.length > 1) {
		if (s[1].length < 2) {
			value = value.toString() + "0";
		}
		return value;
	}
}

function returnFloat1(value) {
	var value = Math.round(parseFloat(value) * 100) / 100;
	var s = value.toString().split(".");
	console.log(s)
	if (s.length == 1) {
		value = value.toString() + ".0";
		return value;
	}
	if (s.length > 1) {
		return value;
	}
}


router.get("/getLine", (req, res) => {
	var returnQuery = new sqlexec()
	returnQuery.getLine(function (result) {
		res.send(result)
	})
})

router.get("/getGCData", (req, res) => {
	var line = req.query.line
	var returnQuery = new sqlexec()
	returnQuery.getGCData(line, function (result) {
		res.send(result)
	})
})

router.get("/SKUList", (req, res) => {
	var line = "'" + req.query.line + "'"
	console.log(line)
	var returnQuery = new sqlexec()
	returnQuery.SKUList(line, function (result) {
		console.log(result)
		res.send(result)
	})
})

router.get("/SKU1", (req, res) => {
	var line = "'" + req.query.line + "'"
	console.log(line)
	var returnQuery = new sqlexec()
	returnQuery.SKU1(line, function (result) {
		console.log(result)
		res.send(result)
	})
})

router.get("/SKU2", (req, res) => {
	var line = "'" + req.query.line + "'"
	console.log(line)
	var returnQuery = new sqlexec()
	returnQuery.SKU2(line, function (result) {
		console.log(result)
		res.send(result)
	})
})

router.get("/BOM1List", (req, res) => {
	var BOM1 = "'" + req.query.BOM1 + "'"
	var BOM2 = "'" + req.query.BOM2 + "'"
	var returnQuery = new sqlexec()
	returnQuery.BOM1List(BOM1, BOM2, function (result) {
		console.log(result)
		res.send(result)
	})
})

router.get("/BOM2List", (req, res) => {
	var BOM1 = "'" + req.query.BOM1 + "'"
	var BOM2 = "'" + req.query.BOM2 + "'"

	var returnQuery = new sqlexec()
	returnQuery.BOM2List(BOM1, BOM2, function (result) {
		console.log(result)
		res.send(result)
	})
})

router.get("/SameBOMList", (req, res) => {
	var BOM1 = "'" + req.query.BOM1 + "'"
	var BOM2 = "'" + req.query.BOM2 + "'"
	var returnQuery = new sqlexec()
	returnQuery.SameBOMList(BOM1, BOM2, function (result) {
		console.log(result)
		res.send(result)
	})
})

router.get("/GCPOData", (req, res) => {
	var BOM = "'" + req.query.BOM + "'"
	var returnQuery = new sqlexec()
	returnQuery.GCPOData(BOM, function (result) {
		console.log(result)
		res.send(result)
	})
})

router.get("/MESCaseQTY", (req, res) => {
	var Line = "'" + req.query.Line + "'"
	var SKU = "'" + req.query.SKU + "'"
	const dateArray = req.query.Date.split('/')
	const convertDate = dateArray.join('-')
	var Date1 = "'" + convertDate + "'"
	var Shift = "'" + req.query.Shift + "'"
	console.log('MES参数1', Line, SKU, Date1, Shift)
	var returnQuery = new sqlexec()
	returnQuery.MESCaseQTY(Line, SKU, Date1, Shift, function (result) {
		var returnMESCaseQTY = []
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				returnMESCaseQTY.push(result[i])
			}
			res.send(returnMESCaseQTY)

		} else {
			res.send("获取失败")
		}
	})
})

router.get("/Centerline", (req, res) => {
	var Line = "'" + req.query.Line + "'"
	const StartArray = req.query.Start.split('/')
	const convertStart = StartArray.join('-')
	var Start = "'" + convertStart + " 00:00:00'"
	// 获取传入的End参数，并将其拆分为日期数组
	const ArrayEnd = req.query.End.split('/');
	// 将数组转换为日期字符串
	const convertEnd = ArrayEnd.join('-');
	// 将日期字符串转换为日期对象
	const dateEnd = new Date(convertEnd);
	// 日期对象加一天
	dateEnd.setDate(dateEnd.getDate() + 1);
	// 格式化日期为'YYYY-MM-DD'字符串
	const year = dateEnd.getFullYear();
	const month = String(dateEnd.getMonth() + 1).padStart(2, '0');
	const day = String(dateEnd.getDate()).padStart(2, '0');
	const newEnd = `${year}-${month}-${day}`;
	// 将日期字符串包裹在单引号中
	var End_1 = `${newEnd}`;
	var End = "'" + End_1 + " 00:00:00'"
	console.log('Centerline参数API', Line, Start, End)
	var returnQuery = new sqlexec()
	returnQuery.Centerline(Line, Start, End, function (result) {
		var returnCenterline = []
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				returnCenterline.push(result[i])
			}
			res.send(returnCenterline)

		} else {
			res.send("获取失败")
		}
	})
})

router.get("/ShiftChange_MESKPI", (req, res) => {
	var Line1 = "'" + req.query.Line + "%Conver%'"
	var Line2 = "'" + req.query.Line + "%Pallet%'"
	const DateArray = req.query.Date.split('/')
	const convertDate = DateArray.join('-')
	const startDate = new Date(convertDate)
	// 将日期增加一天
	const endDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
	// 格式化为需要的日期字符串格式 yyyy-m-d
	const End_1 = endDate.getFullYear() + '-' + (endDate.getMonth() + 1) + '-' + endDate.getDate();
	const Shift = req.query.Shift
	if (Shift === 'Day') {
		Start = "'" + convertDate + " 00:00:00'"
		End = "'" + convertDate + " 12:00:00'"
	} else {
		Start = "'" + convertDate + " 12:00:00'"
		End = "'" + End_1 + " 00:00:00'"
	}
	console.log('ShiftChange_MESKPI参数', Line1, Line2, Start, End)
	var returnQuery = new sqlexec()
	returnQuery.ShiftChange_MESKPI(Line1, Line2, Start, End, function (result) {
		var returnShiftChangeKPI = []
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				returnShiftChangeKPI.push(result[i])
			}
			res.send(returnShiftChangeKPI)
		} else {
			res.send("获取失败")
		}
	})
})

router.get("/ShiftChange_MESWaste?", (req, res) => {
	var Line = "'" + req.query.Line + "%Conver%'"
	const DateArray = req.query.Date.split('/')
	const convertDate = DateArray.join('-')
	const startDate = new Date(convertDate)
	// 将日期增加一天
	const endDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
	// 格式化为需要的日期字符串格式 yyyy-m-d
	const End_1 = endDate.getFullYear() + '-' + (endDate.getMonth() + 1) + '-' + endDate.getDate();
	const Shift = req.query.Shift
	if (Shift === 'Day') {
		Start = "'" + convertDate + " 00:00:00'"
		End = "'" + convertDate + " 12:00:00'"
	} else {
		Start = "'" + convertDate + " 12:00:00'"
		End = "'" + End_1 + " 00:00:00'"
	}
	console.log('ShiftChange_MESWaste参数', Line, Start, End)
	var returnQuery = new sqlexec()
	returnQuery.ShiftChange_MESWaste(Line, Start, End, function (result) {
		var returnShiftChangeWaste = []
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				returnShiftChangeWaste.push(result[i])
			}
			res.send(returnShiftChangeWaste)
		} else {
			res.send("获取失败")
		}
	})
})

router.get("/DailyCLKPI", (req, res) => {
	var Line = "'" + req.query.Line + "'"
	const StartArray = req.query.Start.split('/')
	const convertStart = StartArray.join('-')
	var Start = "'" + convertStart + " 00:00:00'"
	const EndArray = req.query.End.split('/')
	const convertEnd = EndArray.join('-')
	var End = "'" + convertEnd + " 00:00:00'"
	console.log('日报CLKPI', Line, Start, End)
	var returnQuery = new sqlexec()
	returnQuery.DailyCLKPI(Line, Start, End, function (result) {
		var returnDailyCLKPI = []
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				returnDailyCLKPI.push(result[i])
			}
			res.send(returnDailyCLKPI)
		} else {
			res.send("获取失败")
		}
	})
})

router.get("/RollWeight", (req, res) => {
	var Line = "'" + req.query.Line + "%Converter%'"
	var RM_Code = "'" + req.query.RM_Code + "'"
	const currentDate = new Date();
	// 减去12小时
	const start = new Date(currentDate.getTime() - 12 * 60 * 60 * 1000);
	// 格式化日期和时间
	function formatDate(date) {
		const year = date.getUTCFullYear();
		const month = ('0' + (date.getUTCMonth() + 1)).slice(-2); // 补零
		const day = ('0' + date.getUTCDate()).slice(-2); // 补零
		const hours = ('0' + date.getUTCHours()).slice(-2); // 补零
		const minutes = ('0' + date.getUTCMinutes()).slice(-2); // 补零
		const seconds = ('0' + date.getUTCSeconds()).slice(-2); // 补零
		return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
	}
	// 应用格式化
	const Start = "'" + formatDate(start) + "'";
	const End = "'" + formatDate(currentDate) + "'";
	console.log('单卷重量', Line, RM_Code, Start, End)
	var returnQuery = new sqlexec()
	returnQuery.RollWeight(Line, RM_Code, Start, End, function (result) {
		var returnRollWeight = []
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				returnRollWeight.push(result[i])
			}
			res.send(returnRollWeight)
		} else {
			res.send("获取失败")
		}
	})
})

router.get("/ActivePO", (req, res) => {
	var returnQuery = new sqlexec()
	returnQuery.ActivePO(function (result) {
		var returnActivePO = []
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				returnActivePO.push(result[i])
			}
			res.send(returnActivePO)
		} else {
			res.send("获取失败")
		}
	})
})

router.get("/NextPO", (req, res) => {
	var returnQuery = new sqlexec()
	returnQuery.NextPO(function (result) {
		var returnNextPO = []
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				returnNextPO.push(result[i])
			}
			res.send(returnNextPO)
		} else {
			res.send("获取失败")
		}
	})
})

router.get("/MWComment", (req, res) => {
	var Line = "'%" + req.query.Line + "%Convert%'"
	const StartArray = req.query.Start.split('/')
	const convertStart = StartArray.join('-')
	var Start = "'" + convertStart + " 00:00:00'"
	const EndArray = req.query.End.split('/')
	const convertEnd = EndArray.join('-')
	var End = "'" + convertEnd + " 23:59:59'"
	console.log('废品备注', Line, Start, End)
	var returnQuery = new sqlexec()
	returnQuery.MWComment(Line, Start, End, function (result) {
		var MWComment = []
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				MWComment.push(result[i])
			}
			res.send(MWComment)
		} else {
			res.send("获取失败")
		}
	})
})

router.get("/RSR_Order", (req, res) => {
	var Line = "'%" + req.query.Line + "%'"
	const StartArray = req.query.Start.split('/')
	const convertStart = StartArray.join('-')
	var Start = "'" + convertStart + " 00:00:00'"
	const EndArray = req.query.End.split('/')
	const convertEnd = EndArray.join('-')
	var End = "'" + convertEnd + " 23:59:59'"
	console.log('RSR_List', Line, Start, End)
	var returnQuery = new sqlexec()
	returnQuery.RSR_List(Line, Start, End, function (result) {
		var RSR_List = []
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				RSR_List.push(result[i])
			}
			res.send(RSR_List)
		} else {
			res.send("获取失败")
		}
	})
})

router.get("/RSR_BOM_Detail", (req, res) => {
	var pp_id = "'" + req.query.pp_id + "'"
	var returnQuery = new sqlexec()
	returnQuery.RSR_BOM_Detail(pp_id, function (result) {
		console.log(result)
		res.send(result)
	})
})

module.exports = router



