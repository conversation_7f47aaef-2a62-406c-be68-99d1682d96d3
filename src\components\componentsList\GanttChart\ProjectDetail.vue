<template>
    <div style="width:100%">
        <!-- <q-dialog v-model="showDialog" persistent full-width> -->
        <q-card>
            <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                <div class="text-h6 text-bold">技术开发项目新增</div>
                <q-space />
                <q-btn icon="close" flat round dense v-close-popup />
            </q-card-section>

            <q-card-section>
                <div class="row q-gutter-xs">
                    <template v-for="(value, key) in projectInputData">
                        <div class="row q-gutter-xs">

                            <div style="font-size: 15px;font-weight: 600; width:250px"
                                v-if="key != 'Project_Parent_ID' && key != 'Modify_By' && key != 'id' ">
                                {{ key }}：
                                <em v-if="isRequiredField(key)" class="q-px-sm bg-deep-orange text-white rounded-borders"
                                    style="font-size: 12px;">必填</em>
                            </div>

                            <div v-if="key.includes('Date')">
                                <!-- {{ taskRawData[key] }} -->
                                <q-input style="width: 500px;" v-model="taskRawData[key]" type="date" filled dense
                                    :readonly="opType == 'view'" :label-slot="isRequiredField(key)">
                                    <template v-if="isRequiredField(key)" v-slot:label>
                                        {{ key }}
                                        <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em>
                                    </template>
                                </q-input>
                            </div>
                            <div v-else-if="key == 'Project_Capex_Run_Rate'">
                                <q-input style="width: 500px;" v-model="taskRawData[key]" filled dense disable />
                            </div>

                            <div v-else-if="key == 'Project_Capex_Cost'">
                                <q-input style="width: 500px;" v-model="taskRawData[key]" type="number" filled dense
                                    :readonly="opType == 'view'" />
                            </div>
                            <div v-else-if=" key=='Project_Next_Phase' || key=='Project_Status'">
                                <q-select filled dense v-model=" taskRawData[key]" placeholder="请选择"
                                    :options="projectDropdownData[key]" style="width: 500px;"
                                    :readonly="opType == 'view'" :label-slot="isRequiredField(key)">
                                    <template v-if="isRequiredField(key)" v-slot:label>
                                        {{ key }}
                                        <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em>
                                    </template>
                                </q-select>
                            </div>

                            <div v-else-if="key == 'Project_Progress' ">
                                <div style="width: 500px;">
                                    <div style="margin-bottom: 8px;">
                                        {{ key }}
                                        <em v-if="isRequiredField(key)" class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em>
                                    </div>
                                    <q-slider v-model="taskRawData[key]" :min="0" :max="1" :step="0.01" label
                                        :label-value="taskRawData[key] * 100 + '%'" label-always color="purple"
                                        style="width: 100%;" :disable="opType == 'view'" />
                                </div>
                            </div>

                            <div v-else-if="key.includes('URL')">
                                <q-input style="width: 500px;" v-model="taskRawData[key]" filled dense
                                    :readonly="opType == 'view'">
                                    <template v-slot:after>
                                        <q-btn round dense flat icon="send" @click="openUrl(taskRawData[key])" />
                                    </template>
                                </q-input>
                            </div>

                            <div v-else-if="key != 'Project_Parent_ID' && key != 'Modify_By' && key != 'id'">
                                <q-input style="width: 500px;" v-model="taskRawData[key]" filled dense
                                    :readonly="opType == 'view'" :label-slot="isRequiredField(key)">
                                    <template v-if="isRequiredField(key)" v-slot:label>
                                        {{ key }}
                                        <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必填</em>
                                    </template>
                                </q-input>
                            </div>



                        </div>
                    </template>
                </div>

            </q-card-section>

            <q-card-section class="row items-center q-pb-none  q-gutter-xs" style="margin-bottom: 10px;">
                <q-space />
                <q-btn class="btn-fixed-width " color="primary" label="提交" @click="updateProjectData"
                    v-if="opType != 'view'" />
                <q-btn class="btn-fixed-width " color="red" label="删除" @click="deleteProjectData"
                    v-if="opType != 'view'" />
            </q-card-section>
        </q-card>
        <!-- </q-dialog> -->

    </div>

</template>
<script>
export default {
    props: {
        tasksData: {
            type: Object,
            default: {
                ID: null,
                Project_Name: '',
                Project_Material_URL: '',
                Mechanical_Engineer: '',
                Electrical_Engineer: '',
                Process_Engineer: '',
                Packaging_Engineer: '',
                Project_Start_Date: '',
                Project_Feature: '',
                Project_Proposal_URL: '',
                Project_Experiment_Report_URL: '',
                Project_Experiment_Cost: '',
                Project_Evaluation_Program_URL: '',
                Project_Capex_Forecast: 0,
                Project_Capex_YTD_Actual: 0,
                Project_Capex_Run_Rate: 0,
                Project_Next_Phase: '',
                Project_Next_Phase_Date: '',
                Project_Next_Phase_End_Date: '',
                Project_Progress: 0,
                Project_Status: '',
                Project_Parent_ID: null,
                Modify_By: ''
            }
        },
        
    },
    name: 'project-detail',
    components: {
    },
    data() {
        return {
            permission:'',
            projectData: false,
            projectDropdownData: {
                Project_Next_Phase: ['RSR', 'MSR', 'CINS'],
                Project_Status: ['方案评估中', '方案评估完成等待反馈中', '实验准备中', '商业化准备中']
            },
            // 必填字段列表
            requiredFields: [
                'Project_Name',
                'Project_Start_Date',
                'Project_Next_Phase_Date',
                'Project_Progress',
                'Project_Feature',
                'Project_Next_Phase',
                'Project_Next_Phase_End_Date',
                'Project_Status'
            ],
            // 字段中文名称映射
            fieldLabels: {
                'Project_Name': '项目名称',
                'Project_Start_Date': '项目开始日期',
                'Project_Next_Phase_Date': '项目下一阶段日期',
                'Project_Progress': '项目进度',
                'Project_Feature': '项目特点',
                'Project_Next_Phase': '项目下一阶段',
                'Project_Next_Phase_End_Date': '项目下一阶段结束日期',
                'Project_Status': '项目状态'
            },
            projectInputData: {
                id: null,
                Project_Name: '',
                Project_Material_URL: '',
                Mechanical_Engineer: '',
                Electrical_Engineer: '',
                Process_Engineer: '',
                Packaging_Engineer: '',
                Project_Start_Date: '',
                Project_Feature: '',
                Project_Proposal_URL: '',
                Project_Experiment_Report_URL: '',
                Project_Experiment_Cost: '',
                Project_Evaluation_Program_URL: '',
                Project_Capex_Forecast: '',
                Project_Capex_YTD_Actual: '',
                Project_Capex_Run_Rate: '',
                Project_Next_Phase: '',
                Project_Next_Phase_Date: '',
                Project_Next_Phase_End_Date: '',
                Project_Progress: 0,
                Project_Status: '',
                Project_Parent_ID: null,
                Modify_By: ''
            },
            showDialog: false,
            taskRawData: {},
            opType:'view'
        }
    },
    watch: {
        'taskRawData.Project_Capex_Forecast'(newValue1, oldValue1) {
            if (this.taskRawData.Project_Capex_Forecast > 0) {
                this.taskRawData.Project_Capex_Run_Rate = (this.taskRawData.Project_Capex_YTD_Actual / this.taskRawData.Project_Capex_Forecast * 100).toFixed(2)
            } else {
                this.taskRawData.Project_Capex_Run_Rate = 0
            }
            
        },
        'taskRawData.Project_Capex_YTD_Actual'(newValue1, oldValue1) {
            if (this.taskRawData.Project_Capex_Forecast > 0) {
                this.taskRawData.Project_Capex_Run_Rate = (this.taskRawData.Project_Capex_YTD_Actual / this.taskRawData.Project_Capex_Forecast * 100).toFixed(2)
            } else {
                this.taskRawData.Project_Capex_Run_Rate = 0
            }
        }
    },
    computed: {
    },

    mounted() {
        this.permission = localStorage.getItem('user_role')
        if (this.permission.includes('Tech_Management_管理员') || this.permission.includes('Tech_Management_编辑') || this.permission.includes('Admin')) {
            this.opType='edit'
        }

        // 确保数据正确初始化
        if (this.tasksData && Object.keys(this.tasksData).length > 0) {
            this.taskRawData = { ...this.tasksData }
        } else {
            // 如果没有传入数据，使用默认的空数据结构
            this.taskRawData = { ...this.projectInputData }
        }

        console.log('初始化数据:', JSON.stringify(this.taskRawData, null, 2))
        console.log('操作类型:', this.opType)
    },
    methods: {
        // 检查是否为必填字段
        isRequiredField(fieldName) {
            return this.requiredFields.includes(fieldName)
        },

        // 验证必填字段
        validateRequiredFields() {
            const _this = this
            const emptyFields = []

            for (let field of _this.requiredFields) {
                const value = _this.taskRawData[field]
                const fieldLabel = _this.fieldLabels[field] || field
                let isEmpty = false

                // 根据字段类型进行不同的验证
                switch (field) {
                    case 'Project_Progress':
                        // 项目进度必须大于0（0%表示未开始，不合理）
                        isEmpty = (value === null || value === undefined || value <= 0)
                        break
                    case 'Project_Start_Date':
                    case 'Project_Next_Phase_Date':
                    case 'Project_Next_Phase_End_Date':
                        // 日期字段不能为空
                        isEmpty = (value === null || value === undefined || value === '' ||
                                 (typeof value === 'string' && value.trim() === ''))
                        break
                    case 'Project_Next_Phase':
                    case 'Project_Status':
                        // 下拉选择字段不能为空
                        isEmpty = (value === null || value === undefined || value === '' ||
                                 (typeof value === 'string' && value.trim() === ''))
                        break
                    default:
                        // 其他文本字段不能为空
                        isEmpty = (value === null || value === undefined || value === '' ||
                                 (typeof value === 'string' && value.trim() === ''))
                        break
                }

                if (isEmpty) {
                    emptyFields.push(fieldLabel)
                }
            }

            if (emptyFields.length > 0) {
                _this.$q.notify({
                    type: 'negative',
                    position: 'center',
                    timeout: 5000,
                    message: `请填写以下必填字段：${emptyFields.join('、')}`,
                    actions: [
                        { label: '确定', color: 'white' }
                    ]
                })
                return false
            }

            return true
        },

        // 验证日期逻辑合理性
        validateDateLogic() {
            const _this = this
            const startDate = new Date(_this.taskRawData.Project_Start_Date)
            const nextPhaseDate = new Date(_this.taskRawData.Project_Next_Phase_Date)
            const nextPhaseEndDate = new Date(_this.taskRawData.Project_Next_Phase_End_Date)

            // 检查项目开始日期不能晚于下一阶段日期
            if (startDate > nextPhaseDate) {
                _this.$q.notify({
                    type: 'negative',
                    position: 'center',
                    timeout: 4000,
                    message: '项目开始日期不能晚于项目下一阶段日期！'
                })
                return false
            }

            // 检查下一阶段日期不能晚于下一阶段结束日期
            if (nextPhaseDate > nextPhaseEndDate) {
                _this.$q.notify({
                    type: 'negative',
                    position: 'center',
                    timeout: 4000,
                    message: '项目下一阶段日期不能晚于项目下一阶段结束日期！'
                })
                return false
            }

            return true
        },

        updateProjectData() {
            const _this = this

            // 验证必填字段
            if (!_this.validateRequiredFields()) {
                return
            }

            // 验证日期逻辑
            if (!_this.validateDateLogic()) {
                return
            }

            // 准备提交数据
            const submitData = {
                ..._this.taskRawData,
                Modify_By: localStorage.getItem('username')
            }

            console.log('提交的数据:', JSON.stringify(submitData, null, 2))

            _this.$http.post('tech_data/Tech_Project_Data_Insert', submitData).then(res => {
                console.log('服务器响应:', res)
                if (res.data == '更新成功' || res.data == '新增成功') {
                    _this.$q.notify({
                        type: 'positive',
                        position: 'center',
                        fontWeight: 'bold',
                        progress: true,
                        timeout: 2000,
                        message: '数据更新/添加成功！请关闭此窗口！点击查询按钮查看最新数据。'
                    })
                } else {
                    // 处理错误情况
                    _this.$q.notify({
                        type: 'negative',
                        position: 'center',
                        fontWeight: 'bold',
                        progress: true,
                        timeout: 3000,
                        message: '操作失败：' + res.data
                    })
                }
            }).catch(error => {
                console.error('请求失败:', error)
                _this.$q.notify({
                    type: 'negative',
                    position: 'center',
                    fontWeight: 'bold',
                    progress: true,
                    timeout: 3000,
                    message: '网络请求失败，请检查网络连接！'
                })
            })
        },

        deleteProjectData() {
            const _this = this
            const ID = { "ID": _this.taskRawData.id } 
            this.$q.dialog({
                title: '数据删除确认',
                message: '是否需要删除该条数据？',
                cancel: true,
                persistent: true
            }).onOk(() => {
                _this.$http.post('tech_data/Tech_Project_Data_delete', ID).then(res => {
                    console.log('res', res)
                    if (res.data == '删除成功') {
                        _this.$q.notify({
                            type: 'positive',
                            position: 'center',
                            fontWeight: 'bold',
                            progress: true,
                            timeout: 2000,
                            // caption :'',
                            message: '删除成功！请关闭此窗口！点击查询按钮查看最新数据。'
                        })
                    }
                })
            }).onOk(() => {
                // console.log('>>>> second OK catcher')
            }).onCancel(() => {
                // console.log('>>>> Cancel')
            }).onDismiss(() => {
                // console.log('I am triggered on both OK and Cancel')
            })
            
        },
        openUrl(url) {
            window.open(url)
        }
    },
} 
</script>
<style lang="sass" scoped>
.btn-fixed-width
  width: 200px
</style>
