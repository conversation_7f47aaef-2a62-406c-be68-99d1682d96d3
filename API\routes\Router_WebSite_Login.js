var express = require('express')
var router = express.Router()
var WebSite_Login = require('../sql/SQL_WebSite_Login')
var moment = require('moment')
const { resolve } = require('q')

router.get
(
    "/Test1", (req, res) => {                               //(req, res) => { ... } 中的 req 是请求对象，而 res 是响应对象。通过这两个对象，你可以处理客户端发来的请求，并返回相应的数据给客户端。
        console.log('req', req.query)
        var Line = "'" + req.query.Line + "'"
        // var Start = "'" + req.query.Start + "'"
        // var End = "'" + req.query.End + "'"
        // console.log('参数',Mill,Start,End)
        var returnQuery = new WebSite_Login()
        returnQuery.OPList(Line, function (result) {
            res.send(result)
            // res.send("result")

        })
    }
)

router.get
(
    "/Test2", (req, res) => {                               //(req, res) => { ... } 中的 req 是请求对象，而 res 是响应对象。通过这两个对象，你可以处理客户端发来的请求，并返回相应的数据给客户端。
        console.log('req', req.query)
        var Line = "'" + req.query.Line + "'"
        var Start = "'" + req.query.Start + "'"
        var End = "'" + req.query.End + "'"
        // console.log('参数',Mill,Start,End)
        var returnQuery = new WebSite_Login()
        returnQuery.MachineWaste(Line, Start, End, function (result) {
            res.send(result)
            // res.send("result")

        })
    }
)


module.exports = router