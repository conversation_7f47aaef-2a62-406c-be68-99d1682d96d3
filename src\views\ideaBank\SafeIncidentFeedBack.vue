<template>
    <base-content>

        <div class="q-pa-md">
            <q-dialog v-model="rejectDialog" persistent>
                <q-card style="min-width: 350px">
                    <q-card-section>
                        <div class="text-h6">退回理由</div>
                    </q-card-section>

                    <q-card-section class="q-pt-none">
                        <q-input dense v-model="inputData['assigned_Reject_Comment']" autofocus />
                    </q-card-section>

                    <q-card-actions align="right" class="text-primary">
                        <q-btn flat label="确定" @click="updateSafty('No')" />
                        <q-btn flat label="取消" v-close-popup />
                    </q-card-actions>
                </q-card>
            </q-dialog>



            <q-dialog v-model="firtDialog" transition-show="flip-down">
                <q-card style="width:1200px; max-width: 100vw;" v-if="approveIdea">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6 ">安全隐患方案反馈</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>

                    <q-card-section>
                        <div class="q-gutter-md row items-start" style="margin-bottom: 10px;">
                            <q-input type="textarea" filled v-model="inputData['solution']" dense stack-label label-slot
                                style="height: 100px;width: 300px;" v-if="approveIdea['流程状态'] == 'In progress'">
                                <template v-slot:label>行动方案
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-input>
                            <q-input type="textarea" filled v-model="inputData['temporary_Solution']" dense stack-label
                                label-slot style="height: 100px;;width: 300px;"
                                v-if="approveIdea['流程状态'] == 'In progress'">
                                <template v-slot:label>临时解决方案
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;" v-if="approveIdea['风险等级'] == '高'">必填</em>
                                </template>
                            </q-input>
                            <q-input v-model="inputData['assigned_Rectify_Date']" filled type="date" stack-label
                                label-slot style="width: 250px" v-if="approveIdea['流程状态'] == 'In progress'">
                                <template v-slot:prepend>
                                    <q-icon name="event" />
                                </template>
                                <template v-slot:label>预计整改完成时间
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-input>
                            <q-checkbox v-if="approveIdea['流程状态'] == 'Wait Rectify'"
                                v-model="inputData['rectify_Completed']" label="是否整改已完成" />
                            <q-uploader v-if="inputData['rectify_Completed']" batch multiple auto-upload dense
                                :url="webURL_upload + '?system=Safty'" style="max-width: 300px;height:150px"
                                @uploaded="handleUploadSuccess" label="完成整改照片上传" />

                        </div>
                        <q-btn style="width:150px ;margin-right: 10px;"
                            :label="approveIdea['流程状态'] == 'Wait Rectify' ? '整改完成' : '提交行动方案'" color="purple"
                            @click="updateSafty('Yes')" />
                        <q-btn style="width:150px" label="退回分配者" color="red" @click="updateSafty('No')" />


                    </q-card-section>

                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template v-for="(value, key) in approveIdea">
                                <div v-if="key == '隐患内容' || key == '建议解决方案' || key == '整改人方案' || key == '临时方案'"
                                    style="width: 100%;">
                                    <q-input type="textarea" filled v-model="approveIdea[key]" dense stack-label
                                        :label="key" readonly style="height: 100px;" />
                                </div>
                                <div v-else-if="key == '整改人'">
                                    <q-input v-if="value != null && value.hasOwnProperty('label')" filled
                                        v-model="approveIdea[key]['label']" dense stack-label :label="key" readonly
                                        style="width:300px" />
                                    <q-input v-else filled v-model="approveIdea[key]" dense stack-label :label="key"
                                        readonly style="width:300px" />
                                </div>
                                <div v-else-if="!key.includes('照片') && key !== '整改人'">
                                    <q-input filled v-model="approveIdea[key]" dense stack-label :label="key" readonly
                                        style="width:300px" />
                                </div>
                                <div v-else-if="key.includes('照片') && value != null">
                                    <q-btn class="text-bold btn-fixed-width" color="primary" style="width: 300px;" dense
                                        @click="downloadFile(value)">{{ key }}:{{
                                            value.split('/')[4] }}</q-btn>
                                </div>

                                <!-- {{ key }}:{{ value }} -->
                            </template>
                        </div>
                    </q-card-section>
                    <q-card-actions align="right">

                    </q-card-actions>
                </q-card>
            </q-dialog>


            <div v-if="approveIdeaList">
                <q-table :data="approveIdeaList" row-key="name" :pagination.sync="myPagination" :filter="filter1" dense
                    class="my-sticky-virtscroll-table" virtual-scroll :virtual-scroll-sticky-size-start="48"
                    :rows-per-page-options="[0]">
                    <template v-slot:top="props">
                        <div class="text-h6" style="font-weight: 600;">分配给我的安全隐患任务</div>
                        <q-space></q-space>
                        <div style="font-weight: 600;">如需查看所有反馈项目请点击-></div>
                        <q-toggle v-model="adpotFilter" color="green" :label="adpotFilter ? '未反馈项目' : '所有反馈项目'"
                            style="font-weight: 600;margin-right: 20px;" />
                        <q-input borderless dense debounce="300" v-model="filter1" placeholder="Search"
                            class="bg-indigo-1">
                            <template v-slot:append>
                                <q-icon name="search" />
                            </template>
                        </q-input>

                        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
                            @click="props.toggleFullscreen" class="q-ml-md" label="全屏" />
                    </template>

                    <template v-slot:body="props">
                        <q-tr :props="props" @click="toApprove(props)">
                            <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                {{ col.value }}
                            </q-td>
                        </q-tr>
                    </template>
                </q-table>
            </div>
        </div>




    </base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import uploadURL from '../../utils/uploadURL'
export default {
    components: {
        BaseContent
    },
    data() {
        return {
            webURL_upload: '',
            approveIdeaList: false,
            approveRawIdeaList: false,
            firtDialog: false,
            approveIdea: false,
            adpotFilter: true,
            filter1: '',
            inputData: {
                assigned_approval_status: '',
                assigned_Rectify_Confirm: '',
                solution: '',
                temporary_Solution: '',
                assigned_Rectify_Date: '',
                assigned_Reject_Comment: '',
                process_Status: '',
                riskLevel: '',
                ID: '',
                rectify_image_url: '',
                rectify_Completed: false
            },
            rejectDialog: false,
            myPagination: { rowsPerPage: 0 },
        }
    },
    mounted() {
        this.webURL_upload = uploadURL
        this.getMyApprovalIdea()

    },
    watch: {
        adpotFilter(newValue, oldValue) {
            this.filterMyApproval()
        },
    },

    methods: {
        async getMyApprovalIdea() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`ideabank/myApprovalSafty?employeeID=${localStorage.getItem('account')}&processType=saftyFeedback`)
            console.log('myApprovalSafty', res)
            _this.approveIdeaList = res
            _this.approveRawIdeaList = res
            _this.filterMyApproval()
        },
        toApprove(props) {
            console.log('toApprove', props)
            const _this = this
            _this.approveIdea = props.row
            _this.inputData = {
                assigned_approval_status: '',
                assigned_Rectify_Confirm: '',
                solution: props.row['整改人方案'] ? props.row['整改人方案'] : '',
                temporary_Solution: props.row['临时方案'] ? props.row['临时方案'] : '',
                assigned_Rectify_Date: props.row['预计完成时间'] ? props.row['预计完成时间'] : '',
                assigned_Reject_Comment: '',
                process_Status: props.row['流程状态'],
                riskLevel: props.row['风险等级'],
                ID: props.row['ID'],
                rectify_image_url: '',
                rectify_Completed: false
            }
            _this.firtDialog = true
        },
        filterMyApproval() {
            const _this = this
            _this.approveIdeaList = []
            for (let i = 0; i < _this.approveRawIdeaList.length; i++) {
                if (_this.adpotFilter) {
                    if (_this.approveRawIdeaList[i]['流程状态'] == 'In progress' || _this.approveRawIdeaList[i]['流程状态'] == 'Wait Rectify') {
                        _this.approveIdeaList.push(_this.approveRawIdeaList[i])
                    }
                } else {
                    _this.approveIdeaList = _this.approveRawIdeaList
                }
            }
        },
        async updateSafty(status) {
            const _this = this
            _this.rejectDialog = false
            console.log('approveSafty->assignedInput', _this.inputData)
            if (status == 'No' && _this.inputData['assigned_Reject_Comment'] == '') {
                _this.rejectDialog = true
                return
            }
            _this.inputData['assigned_approval_status'] = status
            if (_this.inputData['rectify_Completed']) {
                _this.inputData['assigned_Rectify_Confirm'] = status
                if (_this.inputData['rectify_image_url'] ==='') {
                    _this.$q.dialog({
                        title: '安全隐患无法提交',
                        message: '请上传图片'
                    })
                    return
                }
            }
            if (status == 'Yes' && !_this.inputData['rectify_Completed']) {
                if (_this.inputData['process_Status' == 'In progress']) {
                    if (_this.inputData['solution'] == '' || _this.inputData['assigned_Rectify_Date'] == '') {
                        _this.$q.dialog({
                            title: '安全隐患无法提交',
                            message: '由于没有填写行动方案或预计整改时间'
                        })
                        return
                    }
                }
                if (_this.inputData['process_Status'] == 'Wait Rectify') {
                    
                    _this.$q.dialog({
                        title: '安全隐患无法提交',
                        message: '请选择是否完成整改'
                    })
                    
                    return
                }
            }





            console.log('_this.assignedInput', _this.inputData)
            _this.$http.post('ideabank/updateSafty_Assigned', { 'rowData': _this.approveIdea, 'data': _this.inputData, 'approver': { "account": localStorage.getItem('account'), "username": localStorage.getItem('username') }, 'processType': 'feedback' }).then(function (response) {
                console.log('response', response)
                if (response.data === "已发送") {
                    _this.$q.notify({
                        type: 'positive',
                        message: `隐患反馈成功！`,
                        position: 'top'
                    })
                    _this.getMyApprovalIdea()
                    _this.firtDialog = false
                }
            })

        },
        downloadFile(url) {
            console.log(url)
            window.open(url, '_blank');
        },
        handleUploadSuccess(response) {
            var _this = this
            var responseFileName = response['files'][0]['name']
            var responseURL = response['files'][0]['xhr']['responseText']
            _this.inputData['rectify_image_url'] = responseURL
            // _this.selectedFiles.push({
            //     name: responseFileName,
            //     url: responseURL
            // });
        },
    }
}
</script>

<style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */
  height: 500px
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0
</style>