var express = require('express')
var router = express.Router()
var sqlexec_Hold = require('../sql/sqlHoldGoods')
var moment = require('moment')
const web_url = require('../config/webURL');
const { resolve } = require('q')
const request = require('request')
const EHS_List = require('../config/EHSAccount');


router.get("/getEmployee", (req, res) => {
    console.log(req.query)
    var returnQuery = new sqlexec_Hold()
    returnQuery.getEmployee(function (result) {
        res.send(result)
    })
})
router.get("/getMaterial", (req, res) => {
    console.log(req.query)
    var sku = "'" + req.query.SKU + "'"
    var returnQuery = new sqlexec_Hold()
    returnQuery.getMaterial(sku, function (result) {
        res.send(result)
    })
})

router.post("/insertHoldData", (req, res) => {
    const { department, employee_Name, Date, shift, SKU, SKU_desc, badCategory, holdQTY, holdCases, holdMin, holdRatio, rootCause, result, SampleQTY, holdStartTime, holdEndTime } = req.body.data
    const data = req.body.data
    var returnQuery = new sqlexec_Hold()
    returnQuery.insertHoldData(data, async function (result) {
        if (result == '已插入数据库') {
            console.log('SQL已插入数据')
            res.send(result)
        }
    })
})

router.get("/GetHoldedGoods", (req, res) => {
    console.log(req.query)
    var line = "'" + req.query.line + "'"
    var returnQuery = new sqlexec_Hold()
    returnQuery.getMaterial(sku, function (result) {
        res.send(result)
    })
})

router.get("/getHoldGoodsRecords", (req, res) => {
    var returnQuery = new sqlexec_Hold()
    returnQuery.getHoldGoodsRecords(function (result) {
        res.send(result)
    })
})

router.post("/updateHoldData", (req, res) => {
    console.log('req.body',req.body)
    const data = req.body
    if (!data || !data.id) {
        return res.status(400).send({status: 'error', message: '无效的请求数据'})
    }
    
    req.setTimeout(30000, () => {
        res.status(504).send({status: 'error', message: '请求超时'})
    })

    var returnQuery = new sqlexec_Hold()
    returnQuery.updateHoldData(data, function(result) {
        if (result.success) {
            res.send({
                status: 'success',
                message: result.message,
                rowsAffected: result.rowsAffected
            })
        } else {
            res.status(500).send({
                status: 'error',
                message: result.message
            })
        }
    })
})

router.post("/deleteHoldData", (req, res) => {
    console.log('接收到的参数', req.body);
    const id = req.body.id
    if (!id) {
        return res.status(400).send({status: 'error', message: '缺少ID参数'})
    }
    console.log('正在删除ID为', id, '的记录')
    var returnQuery = new sqlexec_Hold()
    returnQuery.deleteHoldData(id, function(result) {
        if (result === '删除成功') {
            res.send({status: 'success', message: result})
        } else {
            res.status(500).send({status: 'error', message: result})
        }
    })
})

// 添加测试参数化查询的路由
router.get("/testParamQuery", (req, res) => {
    var returnQuery = new sqlexec_Hold()
    returnQuery.testParamQuery(function(result) {
        if (result) {
            res.send({status: 'success', message: '参数化查询测试成功'})
        } else {
            res.status(500).send({status: 'error', message: '参数化查询测试失败'})
        }
    })
})

module.exports = router
