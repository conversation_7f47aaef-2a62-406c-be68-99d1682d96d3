<template>
	<base-content :class="{ 'fullscreen-mode': isFullscreen }" :style="fullscreenStyle">
		<div row v-if="!isFullscreen" class="header">
			<div class="header-left">
				<div class="title">生产订单列表</div>
			</div>
			<div class="header-right">
				<q-btn round dense flat icon="fullscreen" @click="toggleFullscreen" />
			</div>
		</div>
		<div class="row" style="margin-left: 20px;">
			<div class="q-pa-md" v-for="item in data" style="margin-left: -25px;margin-top: -25px;">
				<div class="container">
					<div :class="getRowClass(item)" style="max-width: 2000px;font-weight: 600;max-height: 200px;">
						<div class="col-1" style="width: 130px;font-size: 40px;margin-left: 5px;">
							{{ item['line'] }}</div>
						<div class="col-1" style="width: 190px;height: 20px;line-height: 40px;font-size: 25px">
							当前SKU
						</div>
						<div class="col-1" style="width: 160px;height: 20px;line-height: 40px;font-size: 25px">计划量
						</div>
						<div class="col-1" style="width: 120px;height: 20px;line-height: 40px;font-size: 25px">剩余量
						</div>
						<div class="col-1" style="width: 150px;height: 20px;line-height: 40px;font-size: 25px">
							预计完成时间</div>
					</div>
					<div class=" row items-center" style="font-weight: 600;line-height: 40px;font-size: 30px">
						<div class="col-1" :style="getPOTypeStyle(item)" style="width: 110px;">
							{{ item['POType'] }}
						</div>
						<div class="col-1" style="width: 210px; font-size: 38px;color: red;">{{ item['SKU'] }}</div>
						<div class="col-1" style="width: 170px;font-size: 35px">{{ item['Plan_QTY'] }}</div>
						<div class="col-1" style="width: 120px;font-size: 35px">{{ item['Remain_QTY'] }}</div>
						<div class="col-1" style="width: 170px;font-size: 30px">
							{{ item['POType'] === 'RSR' ? '实验中' : item['Est_Time'] }}</div>
					</div>
				</div>
				<div style="max-width: 2000px;margin-top: -5px;">
					<q-table dense :data="item['tableData']" :columns="column" row-key="name" :separator="separator"
						hide-pagination style="height: 125px;overflow-y: auto;">
					</q-table>
				</div>
			</div>
		</div>
		<div class="row" style="display: flex; justify-content: space-between; margin-left: 40px; font-size: 20px;">
			刷新时间：{{ date }}
			<q-btn color="primary" label="手动刷新" @click="getdate()" style=" margin-left: -1200px;" />
			<div style="margin-right: 40px;color: brown;">
				每10分钟自动刷新一次
			</div>
		</div>
	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import Quagga from 'quagga'

export default {

	components: {
		BaseContent,
		Quagga
	},

	data() {
		return {
			isFullscreen: false,
			fullscreenStyle: {
				height: '100vh',
				width: '100vw',
				position: 'fixed',
				top: 0,
				left: 0,
				zIndex: 9999,
				backgroundColor: 'white'
			},
			refreshInterval: null,
			separator: 'vertical',
			date: "",
			data: [],
			timer: '',
			timer1: '',
			column: [
				{ name: 'Status', required: true, label: '订单状态', align: 'center', field: row => row.Status, format: val => `${val}`, sortable: true },
				{ name: 'SKU', required: true, label: '后续SKU', align: 'center', field: row => row.SKU, format: val => `${val}`, sortable: true },
				{ name: 'Plan_QTY', required: true, label: '计划量', align: 'center', field: row => row.Plan_QTY, format: val => `${val}`, sortable: true },
				{ name: 'Plan_Start', required: true, label: '计划开始时间', align: 'center', field: row => row.Plan_Start, format: val => `${val}`, sortable: true }],
		}
	},

	mounted() {
		this.isFullscreen = this.$route.query.fullscreen === 'true'

		if (this.isFullscreen) {
			this.$q.fullscreen.request()
		}

		document.addEventListener('fullscreenchange', this.handleFullscreenChange)

		console.log("mounted", this.$route.query)
		this.getdate()
		this.timer = setInterval(() => {
			this.getdate()
		}, 1000 * 60 * 10);

		// this.timer1 = setInterval(() => {
		// 	this.date = this.formatDate()
		// }, 1000);
	},

	beforeDestroy() {
		document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
		clearTimeout(this.timer);
		clearTimeout(this.timer1);
	},

	methods: {
		toggleFullscreen() {
			if (!this.isFullscreen) {
				this.$q.fullscreen.request()
			} else {
				this.$q.fullscreen.exit()
			}
		},
		handleFullscreenChange() {
			this.isFullscreen = document.fullscreenElement !== null
		},

		startRefreshTimer() {
			this.refreshInterval = setInterval(() => {
				this.getdate();
			}, 60 * 60 * 1000);
		},
		getPOTypeStyle(item) {
			return item['POType'] === 'RSR' ? { color: 'red' } : {};
		},
		getdate() {
			const currentDate = new Date()
			const year = currentDate.getFullYear();
			const month = currentDate.getMonth() + 1;
			const day = currentDate.getDate();
			const hours = currentDate.getHours();
			const minutes = currentDate.getMinutes();
			const seconds = currentDate.getSeconds();
			this.date = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			this.getPO()
		},
		async getPO() {
			var _this = this
			const data = []
			let LineList = []
			let ActivePO = []
			let NextPO = []
			const {
				data: res
			} = await _this.$http.get('material/lineList')
			console.log('Line', res)
			LineList = res
			const {
				data: res_A
			} = await _this.$http.get('mes/ActivePO')
			console.log('ActivePO', res_A)
			ActivePO = res_A
			const {
				data: res_N
			} = await _this.$http.get('mes/NextPO')
			console.log('NextPO', res_N)
			NextPO = res_N
			const APOMap = new Map(ActivePO.map(item => [item.line, item]));
			const NPOMap = new Map();
			NextPO.forEach(item => {
				if (!NPOMap.has(item.line)) {
					NPOMap.set(item.line, []);
				}
				NPOMap.get(item.line).push({ Status: item.Status, SKU: item.SKU, Plan_QTY: item.Plan_QTY, Plan_Start: item.Plan_Start });
			});
			const result = LineList.map(line => {
				const APOItem = APOMap.get(line);
				const tableData = NPOMap.get(line) || [];

				return {
					line,
					POType: APOItem?.Order_Type === 'Prod' ? '' : (APOItem?.Order_Type || ''),
					SKU: APOItem?.SKU || '',
					Plan_QTY: APOItem?.Plan_QTY || 0,
					Remain_QTY: APOItem?.Remain_QTY || 0,
					Est_Time: APOItem?.Est_Time || '',
					tableData
				};
			});
			console.log('data', result);
			this.data = result
		},
		getRowClass(item) {
			const now = new Date();
			if (!item['Est_Time']) {
				return 'bg-primary text-white row items-center';
			}
			const [datePart, timePart] = item['Est_Time'].split(' ');
			const [month, day] = datePart.split('-').map(Number);
			const [hours, minutes] = timePart.split(':').map(Number);
			const estTime = new Date(now.getFullYear(), month - 1, day, hours, minutes);
			const diffInHours = (estTime - now) / (1000 * 60 * 60);
			console.log('estTime', estTime, 'now', now, 'diffInHours', diffInHours)
			const A_SKU = item['SKU']
			const N_SKU = item['tableData'] && item['tableData'].length > 0 ? item['tableData'][0]['SKU'] : null;
			const A_PO_type = item['POType']
			console.log('A_SKU', A_SKU, 'N_SKU', N_SKU)
			const className = (diffInHours < 1 && A_SKU !== N_SKU && A_PO_type !== 'RSR') ? 'bg-warning text-black row items-center' : 'bg-primary text-white row items-center';
			console.log('Returning class:', className);
			return className;
		},
	},
}

</script>

<style scoped>
/* 使用深度选择器 */
::v-deep .q-table__top thead th,
::v-deep .q-table__header th {
	font-size: 20px !important;
	font-weight: 700;
}

::v-deep .q-table__container td,
::v-deep .q-table__body td {
	font-size: 32px !important;
	font-weight: 800;
	padding: 2px 8px;
}

::v-deep .q-table__body tr {
	height: 30px;
}

.fullscreen-mode {
	::v-deep .base-content-header {
		display: none;
	}

	::v-deep .q-header {
		display: none;
	}

	::v-deep .q-footer {
		display: none;
	}

	::v-deep .q-drawer {
		display: none;
	}

	/* 新增以下规则 */
	::v-deep .navigation-buttons {
		display: none;
	}

	::v-deep .platform-title {
		display: none;
	}

	::v-deep .fullscreen-exit-button {
		display: none;
	}

	::v-deep .user-info {
		display: none;
	}
}
</style>

<style lang="css" scoped>
.fontStyle {
	color: brown;
	font-size: 25px;
	font-weight: 600;
	background-color: bisque;
	text-align: center;
	width: 500px;
	height: 200px;
	margin-top: 20px;
	margin-left: 20px;
}

.header {
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20px;
	background-color: #fff;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.title {
	font-size: 20px;
	font-weight: 600;
	color: #333;
	margin-right: auto;
}

.header-right {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-left: auto;
}

.my-content {
	border: 1px solid #070707;
	box-sizing: border-box;
}
