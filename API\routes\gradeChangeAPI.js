var express = require('express')
var router = express.Router()
var sqlexec_grade = require('../sql/sqlGradeChange')
var moment = require('moment')
const { resolve } = require('q')

router.get("/lineList", (req, res) => {
    // var y = "'" + req.query.y + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.lineList(function (result) {
        var returnLine=[]
        if (result.length>0){
            for (var i=0;i<result.length;i++){
                returnLine.push(result[i].line)
            }
            res.send(returnLine)

        }else{
            res.send("获取失败")
        }
    })
})

router.get("/productNameList", (req, res) => {
    var line = "'" + req.query.line + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.productNameList(line,function (result) {
        var returnProductName=[]
        if (result.length>0){
            for (var i=0;i<result.length;i++){
                returnProductName.push(result[i].product_Name)
            }
            res.send(returnProductName)

        }else{
            res.send("获取失败")
        }
    })
})

router.get("/taskContent", (req, res) => {
    var line = "'" + req.query.line + "'"
    var product1 = "'" + req.query.product1 + "'"
    var product2 = "'" + req.query.product2 + "'"
    var returnQuery = new sqlexec_grade()
    returnQuery.taskContent(line,product1,product2,function (result) {
        res.send(result)
    })
})

router.get("/gradeChange_ProductName", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.gradeChange_ProductName(req.query.Line,function (result) {
        let newArray = result.map(item => item.productName)
        res.send(newArray)
    })
})

router.get("/gradeChange_List", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.gradeChange_List(req.query.Line,req.query.sku,function (result) {
        res.send(result)
    })
})




module.exports = router