<!-- 写HTML -->
<template>

    <base-content>
        <div class="q-pa-md">
            <div class="text-center">
                <h1 class="text-h4">南京工厂培训管理平台</h1>
            </div>

            <!-- //增加一个选择框 -->
            <div class="fit row wrap justify-center items-start q-gutter-sm ">
                <q-select filled v-model="Training_Category" :options="Training_Category_List" label="请选择培训类别"
                    style="width: 200px;" />
                <q-select filled v-model="Business_Category" :options="Business_Category_List" label="请选择业务类别"
                    style="width: 200px;" />
                <q-select filled v-model="Position" :options="Position_List" label="请选择岗位" style="width: 200px;" />
            </div>


            <div v-if="TrainingData">
                <q-table :columns="columns" title="在线培训列表" :data="TrainingData" row-key="name" style="height: 800px"
                    virtual-scroll :pagination.sync="pagination" :rows-per-page-options="[0]">
                    <template v-slot:body="props">
                        <q-tr :props="props">
                            <q-td key="Sort_ID" :props="props">
                                {{ props.row.Sort_ID }}
                            </q-td>

                            <q-td key="Training_Category" :props="props">
                                {{ props.row.Training_Category }}
                            </q-td>

                            <q-td key="Business_Category" :props="props">
                                {{ props.row.Business_Category }}
                            </q-td>

                            <q-td key="Position" :props="props">
                                {{ props.row.Position }}
                            </q-td>

                            <q-td key="Knowledge_Category" :props="props">
                                {{ props.row.Knowledge_Category }}
                            </q-td>

                            <q-td key="Training_Name" :props="props">
                                {{ props.row.Training_Name }}
                            </q-td>
                            <q-td key="Training_Timer" :props="props">
                                {{ props.row.Training_Timer }}
                            </q-td>
                            <q-td key="Trainer" :props="props">
                                {{ props.row.Trainer }}
                            </q-td>
                            <q-td key="Training_Material" :props="props">
                                <li v-for="item in props.row.Training_Material">
                                    <a :href="item.url" target="_blank">{{ item.name }}</a>
                                </li>
                            </q-td>
                            <q-td key="Attachment" :props="props">
                                <q-btn 
                                    v-if="!props.row.attachmentUrl"
                                    label="上传附件" 
                                    @click="openUploadDialog(props.row)"
                                    color="primary"
                                    dense
                                />
                                <q-btn
                                    v-else
                                    label="查看附件"
                                    @click="openUploadDialog(props.row)"
                                    color="secondary"
                                    dense
                                />
                            </q-td>
                        </q-tr>
                    </template>
                </q-table>
            </div>

        </div>
    </base-content>
</template>
<script>
import BaseContent from '../../components/BaseContent/BaseContent'

export default {
    name: 'Home',
    components: {
        BaseContent,
    },
    //页面首次进入的默认
    mounted() {
        this.Get_Category()
    },
    watch: {
        Training_Category: function (newVal, oldVal) {
            this.getTrainingData(newVal, this.Business_Category, this.Position)
        },
        Business_Category: function (newVal, oldVal) {
            this.getTrainingData(this.Training_Category, newVal, this.Position)
        },
        Position: function (newVal, oldVal) {
            console.log(newVal, oldVal)
            this.getTrainingData(this.Training_Category, this.Business_Category, newVal)
        }
    },
    // 以下放变量
    data() {
        return {
            Training_Category: '',
            Business_Category: '',
            Position: '',
            Training_Category_List: ['入职培训'],
            Business_Category_List: ['Diaper'],
            Position_List: ['OP'],
            TrainingData: false,
            columns: [
                { name: 'Sort_ID', align: 'left', label: '序号', field: 'Sort_ID', sortable: true },
                { name: 'Training_Category', align: 'left', label: '培训类别', field: 'Training_Category', sortable: true },
                { name: 'Business_Category', align: 'left', label: '业务类别', field: 'Business_Category' },
                { name: 'Position', align: 'left', label: '岗位', field: 'Position' },
                { name: 'Knowledge_Category', align: 'left', label: '知识类别', field: 'Knowledge_Category' },
                { name: 'Training_Name', align: 'left', label: '课程名称', field: 'Training_Name', sortable: true },
                { name: 'Training_Timer', align: 'left', label: '培训时长', field: 'Training_Timer', sortable: true },
                { name: 'Trainer', align: 'left', label: '课件归属人', field: 'Trainer', sortable: true },
                { name: 'Training_Material', align: 'left', label: '课程链接', field: 'Training_Material', sortable: true },
                { name: 'Attachment', align: 'left', label: '附件上传', field: 'Attachment', sortable: false }
            ],
            TrainingMaterialData: false,
            myPagination: { rowsPerPage: 0 },
            uploadDialog: false,
            currentRow: null,
            attachmentUrl: '',
        }
    },
    //以下搞方法
    methods: {
        async getTrainingData(Training_Category, Business_Category, Position) {
            if (Training_Category == '' || Business_Category == '' || Position == '') {
                return
            } else {
                var _this = this
                const {
                    data: res
                } = await _this.$http.get(`training/TrainingList?Training_Category=${Training_Category}&Business_Category=${Business_Category}&Position=${Position}`)
                console.log('TrainingData', res)
                _this.TrainingData = res
            }
        },

        async Get_Category() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`training/TrainingCategory`)
            console.log('TrainingCategory', res)
            _this.Training_Category_List = res['Training_Category']
            _this.Business_Category_List = res['Business_Category']
            _this.Position_List = res['Position']
        },

        handleFileUpload(files, row) {
            console.log('Uploading files for row:', row);
            console.log('Files:', files);
            // 这里可以添加文件上传逻辑
        },

        handleFileRemove(files, row) {
            console.log('Removing files for row:', row);
            console.log('Files:', files);
            // 这里可以添加文件删除逻辑
        },

        openUploadDialog(row) {
            this.currentRow = row;
            this.uploadDialog = true;
        },

        factoryFn(file) {
            // 实现文件上传逻辑
            return {
                url: '/api/upload', // 替换为实际的上传接口
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.$store.state.token}`
                },
                formFields: [
                    { name: 'trainingId', value: this.currentRow.id }
                ]
            };
        },

        handleUploadSuccess(info) {
            const response = JSON.parse(info.xhr.response);
            this.currentRow.attachmentUrl = response.url;
            this.uploadDialog = false;
        },

        rePickImage() {
            this.attachmentUrl = '';
        }
    }
}
</script>
