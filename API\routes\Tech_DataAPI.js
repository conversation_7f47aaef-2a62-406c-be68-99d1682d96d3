var express = require('express')
var router = express.Router()
var Tech_Project_SQL = require('../sql/sqlTech_Data')
var moment = require('moment')
const { resolve } = require('q')

router.get("/Tech_Project_Data", (req, res) => {
    const { startDate, endDate } = req.query
    const Tech_Project = new Tech_Project_SQL()
    Tech_Project.Tech_Project_Data(startDate, endDate, function (result) {
        // res.send(result)
        //console.log('result', result)
        let projectArray = []
        if (result.length > 0) {
            for (let i = 0; i < result.length; i++) {
                let projectData = {
                    id: result[i]['ID'],
                    text: result[i]['Project_Name'],
                    Project_Name: result[i]['Project_Name'],
                    Project_Material_URL: result[i]['Project_Material_URL'],
                    Mechanical_Engineer: result[i]['Mechanical_Engineer'],
                    Electrical_Engineer: result[i]['Electrical_Engineer'],
                    Process_Engineer: result[i]['Process_Engineer'],
                    Packaging_Engineer: result[i]['Packaging_Engineer'],
                    SupportTeam: result[i]['Mechanical_Engineer'] +" "+ result[i]['Electrical_Engineer'] + " "+ result[i]['Process_Engineer'] +" "+ result[i]['Packaging_Engineer'],
                    start_date: result[i]['Project_Next_Phase_Date'],
                    Project_Next_Phase_End_Date: result[i]['Project_Next_Phase_End_Date'],
                    Project_Feature: result[i]['Project_Feature'],
                    Project_Proposal_URL: result[i]['Project_Proposal_URL'],
                    Project_Experiment_Report_URL: result[i]['Project_Experiment_Report_URL'],
                    Project_Experiment_Cost: result[i]['Project_Experiment_Cost'],
                    Project_Evaluation_Program_URL: result[i]['Project_Evaluation_Program_URL'],
                    Project_Capex_Forecast: result[i]['Project_Capex_Forecast'],
                    Project_Capex_YTD_Actual: result[i]['Project_Capex_YTD_Actual'],
                    Project_Capex_Run_Rate: result[i]['Project_Capex_Run_Rate'],
                    Project_Next_Phase: result[i]['Project_Next_Phase'],
                    Project_Next_Phase_Date: result[i]['Project_Next_Phase_Date'],
                    progress: result[i]['Project_Progress'],
                    status: result[i]['Project_Status'],
                    Project_Parent_ID: result[i]['Project_Parent_ID'],
                    Modify_By: result[i]['Modify_By'],
                    Modify_On: result[i]['Modify_On'],
                    duration: result[i]['duration'],
                    open: true,
                    toolTipsTxt: result[i]['Project_Name'],
                    Project_Start_Date: result[i]['Project_Start_Date'],
                    Project_Progress: result[i]['Project_Progress'],
                    Project_Status: result[i]['Project_Status']
                }
                //console.log('projectData', projectData)
                projectArray.push(projectData)
            }
        }
        res.send(projectArray)
    })
}
)

router.post("/Tech_Project_Data_Insert", (req, res) => {
    const Tech_Project = new Tech_Project_SQL()
    const data = req.body
    console.log('接收到的数据:', JSON.stringify(data, null, 2))

    // 数据验证
    if (!data.Project_Name || data.Project_Name.trim() === '') {
        console.log('数据验证失败: 项目名称为空')
        return res.send('项目名称不能为空')
    }

    if (data.id == null || data.id === '') {
        console.log('执行插入操作')
        Tech_Project.insertTech_Project_Data_Insert(data, function (result) {
            console.log('插入操作结果:', result)
            res.send(result)
        })
    } else {
        console.log('执行更新操作, ID:', data.id)
        Tech_Project.updateTech_Project_Data_Insert(data, data.id, function (result) {
            console.log('更新操作结果:', result)
            res.send(result)
        })
    }
})
router.post("/Tech_Project_Data_delete", (req, res) => {
    const Tech_Project = new Tech_Project_SQL()
    console.log('req.body', req.body.ID)
    const ID = req.body.ID
    Tech_Project.deleteTech_Project_Data(ID, function (result) {
        res.send(result)
    })
})

module.exports = router