<template>
  <div>
    <!-- 详情弹窗 -->
    <div v-if="dialogVisible" class="detail-modal">
      <div class="modal-content">
        <h3 class="modal-title">
          {{ currentRecord.Line }} | {{ formatDate(currentRecord.Date) }} |
          {{ currentRecord.Shift }} | {{ currentRecord.SKU_Desc }} |
          {{ currentRecord.rootCause }}
        </h3>
        <form class="detail-form">
          <div class="form-row">
            <div class="form-group">
              <label>机台</label>
              <input type="text" v-model="currentRecord.Line" disabled />
            </div>
            <div class="form-group">
              <label>提交人</label>
              <input type="text" v-model="currentRecord.Submitter" disabled />
            </div>
            <div class="form-group">
              <label>日期</label>
              <input type="text" v-model="currentRecord.Date" disabled />
            </div>
            <div class="form-group">
              <label>班次</label>
              <input type="text" v-model="currentRecord.Shift" disabled />
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>SKU编码</label>
              <input type="text" v-model="currentRecord.SKU_Code" disabled />
            </div>
            <div class="form-group">
              <label>SKU描述</label>
              <input type="text" v-model="currentRecord.SKU_Desc" disabled />
            </div>
            <div class="form-group">
              <label>不良类别</label>
              <input type="text" v-model="currentRecord.badCategory" disabled />
            </div>
            <div class="form-group">
              <label>不良率</label>
              <input type="text" v-model="currentRecord.holdRatio" disabled />
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>抽样数量</label>
              <input type="text" v-model="currentRecord.SampleQTY" disabled />
            </div>
            <div class="form-group">
              <label>不良数量</label>
              <input type="text" v-model="currentRecord.holdQTY" disabled />
            </div>
            <div class="form-group">
              <label>不良箱数</label>
              <input type="text" v-model="currentRecord.holdCases" disabled />
            </div>
            <div class="form-group">
              <label>不良分钟数</label>
              <input type="text" v-model="currentRecord.holdMin" disabled />
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>开始时间</label>
              <input type="datetime-local" v-model="currentRecord.holdStartTime" disabled />
            </div>
            <div class="form-group">
              <label>结束时间</label>
              <input type="datetime-local" v-model="currentRecord.holdEndTime" disabled />
            </div>
            <div class="form-group">
              <label>根本原因</label>
              <input type="text" v-model="currentRecord.rootCause" :disabled="!isEditMode" />
            </div>
            <div class="form-group">
              <label>处理结果</label>
              <input type="text" v-model="currentRecord.Result" disabled />
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>半成品消耗</label>
              <input type="text" v-model="currentRecord.resultSemiConsume" :disabled="!isEditMode" />
            </div>
            <div class="form-group">
              <label>袋装消耗</label>
              <input type="text" v-model="currentRecord.resultBagsConsume" :disabled="!isEditMode" />
            </div>
            <div class="form-group">
              <label>箱装消耗</label>
              <input type="text" v-model="currentRecord.resultCasesConsume" :disabled="!isEditMode" />
            </div>
          </div>
          <div class="action-buttons">
            <button type="button" class="edit-btn" @click="toggleEditMode">
              {{ isEditMode ? '保存' : '编辑' }}
            </button>
            <button type="button" class="delete-btn" @click="confirmDelete">
              删除
            </button>
            <button type="button" class="close-btn" @click="dialogVisible = false">
              关闭
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-area">
      <div class="date-range">
        <input type="date" v-model="startDate" class="filter-input" placeholder="开始日期" />
        <span>至</span>
        <input type="date" v-model="endDate" class="filter-input" placeholder="结束日期" />
      </div>
      <select v-model="filterLine" class="filter-input">
        <option value="">选择机台</option>
        <option v-for="line in lineOptions" :key="line" :value="line">
          {{ line }}
        </option>
      </select>
    </div>

    <!-- 表格区域 -->
    <table class="data-table">
      <thead>
        <tr>
          <th>机台</th>
          <th>提交人</th>
          <th>日期</th>
          <th>班次</th>
          <th>SKU编码</th>
          <th>SKU描述</th>
          <th>不良类别</th>
          <th>不良率</th>
          <th>抽样数量</th>
          <th>不良数量</th>
          <th>不良箱数</th>
          <th>不良分钟数</th>
          <th>开始时间</th>
          <th>结束时间</th>
          <th>根本原因</th>
          <th>处理结果</th>
          <th>半成品消耗</th>
          <th>袋装消耗</th>
          <th>箱装消耗</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(record, index) in filteredRecords" :key="record.id"
          :class="index % 2 === 0 ? 'even-row' : 'odd-row'" @click="showDetail(record)">
          <td>{{ record.Line }}</td>
          <td>{{ record.Submitter }}</td>
          <td>{{ formatDate(record.Date) }}</td>
          <td>{{ record.Shift }}</td>
          <td>{{ record.SKU_Code }}</td>
          <td>{{ record.SKU_Desc }}</td>
          <td>{{ record.badCategory }}</td>
          <td>{{ record.holdRatio }}</td>
          <td>{{ record.SampleQTY }}</td>
          <td>{{ record.holdQTY }}</td>
          <td>{{ record.holdCases }}</td>
          <td>{{ record.holdMin }}</td>
          <td>{{ formatDateTime(record.holdStartTime) }}</td>
          <td>{{ formatDateTime(record.holdEndTime) }}</td>
          <td>{{ record.rootCause }}</td>
          <td>{{ record.Result }}</td>
          <td>{{ record.resultSemiConsume }}</td>
          <td>{{ record.resultBagsConsume }}</td>
          <td>{{ record.resultCasesConsume }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  props: {
    // opType: {
    //     type: String,
    //     default: '新增'
    // },
    // linkid: {
    //     type: String,
    //     default: false
    // }
  },
  mounted() {
    this.queryHoldGoodsRecords();
  },
  watch: {},

  data() {
    return {
      holdGoodsRecords: [],
      startDate: null,
      endDate: null,
      filterLine: null,
      lineOptions: [],
      dialogVisible: false,
      currentRecord: {},
      isEditMode: false,
      originalRecord: {}
    };
  },

  computed: {
    filteredRecords() {
      return this.holdGoodsRecords.filter(record => {
        const recordDate = new Date(record.Date);
        const matchDate =
          (!this.startDate || recordDate >= new Date(this.startDate)) &&
          (!this.endDate || recordDate <= new Date(this.endDate));
        const matchLine = this.filterLine
          ? record.Line === this.filterLine
          : true;
        return matchDate && matchLine;
      });
    }
  },

  methods: {
    async queryHoldGoodsRecords() {
      var _this = this;
      const { data: res } = await _this.$http.get(`holdGoods/getHoldGoodsRecords`);
      console.log('queryHoldGoodsRecords', res);
      this.holdGoodsRecords = res;
      // 从数据中提取所有Line值并去重
      this.lineOptions = [...new Set(res.map(item => item.Line).filter(Boolean))];
    },

    formatDate(dateStr) {
      if (!dateStr || dateStr === '1900-01-01T00:00:00.000Z') return '';
      const date = new Date(dateStr);
      return date.toLocaleDateString();
    },

    formatDateTime(dateTimeStr) {
      if (!dateTimeStr || dateTimeStr === '1900-01-01T00:00:00.000Z') return '';
      const date = new Date(dateTimeStr);
      return date.toLocaleString();
    },

    showDetail(record) {
      // 确保record包含id属性
      if (!record.id) {
        console.error('Record missing id:', record);
        return;
      }
      this.currentRecord = { ...record };
      this.originalRecord = { ...record };
      this.isEditMode = false;
      this.dialogVisible = true;
    },

    async toggleEditMode() {
      if (this.isEditMode) {
        // 保存逻辑
        await this.saveChanges();
        this.isEditMode = false;
      } else {
        this.isEditMode = true;
      }
    },

    async saveChanges() {
      try {
        await this.$http.post('holdGoods/updateHoldData', this.currentRecord);
        await this.queryHoldGoodsRecords(); // 刷新数据
        this.originalRecord = { ...this.currentRecord };
        this.$q.notify({
          type: 'positive',
          message: '当前待判定修改成功！',
          position: 'top'
        });
        return true;
      } catch (error) {
        console.error('保存失败:', error);
        this.$q.notify({
          type: 'negative',
          message: error.response?.data?.message || '保存失败',
          position: 'top'
        });
        return false;
      }
    },

    confirmDelete() {
      this.$q.dialog({
        title: '确认删除',
        message: '确定要删除这条记录吗？此操作不可恢复！',
        cancel: true,
        persistent: true
      }).onOk(() => this.deleteRecord())
        .onCancel(() => {})
        .onDismiss(() => {});
    },

    async deleteRecord() {
      try {
        const response = await this.$http.post('/holdGoods/deleteHoldData', { id: this.currentRecord.id });
        if (response.data.status === 'success') {
          this.$q.notify({
            type: 'positive',
            message: '记录删除成功',
            position: 'top'
          });
          this.dialogVisible = false;
          this.queryHoldGoodsRecords(); // 刷新数据
        } else {
          throw new Error(response.data.message || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        this.$q.notify({
          type: 'negative',
          message: error.response?.data?.message || error.message || '删除失败',
          position: 'top'
        });
      }
    }
  }
};
</script>

<style scoped>
.filter-area {
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.date-range {
  display: inline-block;
  margin-right: 10px;
}

.date-range span {
  margin: 0 5px;
  color: #666;
}

.filter-input {
  margin-right: 10px;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.data-table th,
.data-table td {
  padding: 12px;
  border: 1px solid #ebeef5;
  text-align: center;
}

.data-table th {
  background-color: #f5f7fa;
  color: #909399;
  font-weight: 600;
}

.even-row {
  background-color: #f5f7fa;
}

.odd-row {
  background-color: #ffffff;
}

.black_border {
  border: 1px solid black;
}

/* 详情弹窗样式 */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 30px;
  border-radius: 12px;
  width: 80%;
  max-width: 1000px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.detail-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-weight: 500;
  color: #444;
  font-size: 14px;
}

.form-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #f8f8f8;
  font-size: 14px;
  transition: all 0.2s;
}

.form-group input:not(:disabled) {
  background: white;
  border-color: #409eff;
}

.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.edit-btn {
  padding: 10px 24px;
  background: #e6a23c;
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 24px;
}

.edit-btn:hover {
  background: #ebb563;
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

.edit-btn:active {
  background: #cf9236;
}

.delete-btn {
  padding: 10px 24px;
  background: #f56c6c;
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 24px;
}

.delete-btn:hover {
  background: #f78989;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.delete-btn:active {
  background: #dd6161;
}

.close-btn {
  padding: 10px 24px;
  background: #67c23a;
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 24px;
}

.close-btn:hover {
  background: #85ce61;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

.close-btn:active {
  background: #5daf34;
}
</style>
