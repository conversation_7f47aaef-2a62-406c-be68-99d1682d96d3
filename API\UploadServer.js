﻿const
  express = require('express'),
  app = express(),
  formidable = require('formidable'),
  path = require('path'),
  fs = require('fs'),
  throttle = require('express-throttle-bandwidth')
sd = require("silly-datetime");

const
  port = process.env.PORT || 3001,
  folder = 'C:/fileUpload/',
  folderPic = 'C:/fileUpload/'
// folder = path.join(__dirname, 'files')
if (!fs.existsSync(folder)) {
  fs.mkdirSync(folder)
}
if (!fs.existsSync(folderPic)) {
  fs.mkdirSync(folderPic)
}

app.set('port', port)
// app.use(throttle(1024 * 128)) // 节流带宽

app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*')
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept')
  next()
})

app.post('/upload', async (req, res) => {
  // console.log('req', req)
  var system=req.query.system
  // folder='C:/fileUpload/'+system
  var uploadFolder='D:/uploadFile/'+system+'/'
  console.log( 'C:/uploadFile/'+system+'/')
  const form = new formidable.IncomingForm()
  form.uploadDir = uploadFolder
  form.parse(req, (_, fields, files) => {
    var name=Object.keys(files)[0]
    console.log('files',files[name])
    var oldpath=files[name].filepath
    var originalName = ''
    console.log('name',name)
    if (name == 'null') {
      originalName = files[name].originalFilename
      originalName=decodeURI(originalName)
      console.log('originalname', files[name].originalFilename)
    } else {
      originalName = Object.keys(files)[0]

    }
    var newpath=uploadFolder+ generateRandomString(10)+"_"+ originalName
    console.log(oldpath)
    console.log(originalName)

    newpath=newpath.replace(/\s+/g,"")
    console.log(newpath)
    fs.rename(oldpath,newpath,function (err) {
        if(err){
            throw  Error("改名失败");
        }
    });
    var newName = newpath.replace(uploadFolder, 'http://172.21.65.192:3003/'+system+'/')
    console.log('newName ',newName)
    res.send(newName)
  })








  // folder = 'C:/fileUpload/LSW'
  // console.log('req', req.body)
  // const form = new formidable.IncomingForm()
  // form.uploadDir = folder
  // form.parse(req, (_, fields, files) => {
  //   var name = Object.keys(files)[0]
  //   console.log('name', name)

  //   console.log('files', files[name])
  //   var oldpath = files[name].filepath
  //   var originalName = ''
  //   if (name == 'null') {
  //     originalName = files[name].originalFilename
  //     console.log('originalname', files[name].originalFilename)
  //   } else {
  //     originalName = Object.keys(files)[0]
  //   }
  //   var newpath = folder + generateRandomString(10) + "_" + originalName
  //   console.log(oldpath)
  //   console.log(newpath)
  //   fs.rename(oldpath, newpath, function (err) {
  //     if (err) {
  //       throw Error("改名失败");
  //     }
  //   });
  //   var newName = newpath.replace(folder, 'http://localhost:3003/')
  //   console.log(newName)
  //   //resolve(newName)
  //   res.send(newName)
    //})

  // })
})



function generateRandomString(length) {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}



app.listen(port, () => {
  console.log('\nUpload server running on http://localhost:' + port)
})