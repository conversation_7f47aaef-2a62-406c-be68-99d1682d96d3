let Service = require('node-windows').Service;

let svc = new Service({
    name: 'webServer_new',
    description: '网页API',
    script: require('path').join(__dirname, 'server.js'),  // 使用完整路径
    wait: '1',
    grow: '0.25',
    maxRestarts: '40',
    nodeOptions: [],
    // 域账号配置
    user: "KCUS\\CNNBAP13",
    password: "Abcd1234567890",
    // 确保服务有正确的工作目录
    workingDirectory: __dirname
});

// 添加错误处理
svc.on('error', function(err) {
    console.error('Service error:', err);
});

svc.on('install', function() {
    console.log('Installation complete.');
    console.log('Starting service...');
    svc.start();
});

svc.on('start', function() {
    console.log('Service started successfully.');
});

svc.on('uninstall', function() {
    console.log('Uninstall complete');
    console.log('The service exists:', svc.exists);
});

svc.on('alreadyinstalled', function() {
    console.log('This service is already installed.');
    // 如果服务已安装，先卸载再重新安装
    svc.uninstall();
});

svc.on('invalidinstallation', function() {
    console.log('Invalid installation detected.');
});

// 确保服务存在时先卸载
if (svc.exists) {
    console.log('Service exists, uninstalling...');
    svc.uninstall();
} else {
    console.log('Installing service...');
    svc.install();
}
