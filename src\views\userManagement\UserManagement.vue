<template>
  <div class="user-management q-pa-md">
    <h1 class="text-h5 q-mb-lg">用户管理系统</h1>
    
    <q-tabs
      v-model="activeTab"
      dense
      class="text-primary"
      active-color="primary"
      indicator-color="primary"
      align="left"
      narrow-indicator
    >
      <q-tab name="register" label="用户注册" icon="person_add" />
      <q-tab name="info" label="用户信息管理" icon="manage_accounts" />
    </q-tabs>
    
    <q-separator />
    
    <q-tab-panels v-model="activeTab" animated>
      <q-tab-panel name="register">
        <user-registration />
      </q-tab-panel>
      
      <q-tab-panel name="info">
        <user-info-management />
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script>
import UserRegistration from './UserRegistration.vue'
import UserInfoManagement from './UserInfoManagement.vue'

export default {
  name: 'UserManagement',
  components: {
    UserRegistration,
    UserInfoManagement
  },
  data() {
    return {
      activeTab: 'register'
    }
  },
  mounted() {
    // 检查用户权限
    const userRole = localStorage.getItem('user_role') || ''
    if (!userRole.includes('Admin')) {
      this.$q.notify({
        color: 'negative',
        message: '您没有权限访问此页面',
        icon: 'warning'
      })
      this.$router.push('/')
    }
  }
}
</script>

<style scoped>
.user-management {
  max-width: 1200px;
  margin: 0 auto;
}
</style> 