﻿const _Base = require('../config/dbbase')

function CIProjectSQL() {
    _Base.call(this);
}

CIProjectSQL.prototype = new _Base()


// 获取设备单元
CIProjectSQL.prototype.getMachineUnit = function (callback) {
    const sql = `
        SELECT distinct Level2
  FROM [dbo].[Loss_Tree]
  where level1  in('电气','机械') order by Level2 asc
    `
    console.log('执行SQL查询:\n', sql)
    this._query(sql, function (err, result) {
        if (err) return callback(err)
        // console.log('查询结果:\n', result)
        callback(result.recordset)
    })
}


// 获取A3记录
CIProjectSQL.prototype.getA3RecordById = function(id, callback) {
    const sql = `
        SELECT m.*,
            (SELECT * FROM [dbo].[A3_Metrics] WHERE A3_ID = m.ID FOR JSON PATH) AS Metrics,
            (SELECT * FROM [dbo].[A3_WorkPrinciples] WHERE A3_ID = m.ID FOR JSON PATH) AS WorkPrinciples,
            (SELECT * FROM [dbo].[A3_RootCauseAnalysis] WHERE A3_ID = m.ID FOR JSON PATH) AS RootCauseAnalysis,
            (SELECT * FROM [dbo].[A3_ImprovementPlans] WHERE A3_ID = m.ID FOR JSON PATH) AS ImprovementPlans,
            (SELECT * FROM [dbo].[A3_PerformanceSummary] WHERE A3_ID = m.ID FOR JSON PATH) AS PerformanceSummary
        FROM [dbo].[A3_Management] m
        WHERE m.ID = ${id}
    `
    console.log('执行SQL查询:\n', sql)
    this._query(sql, function(err, result) {
        if (err) return callback(err)
        // console.log('查询结果:\n', result)
        callback(result.recordset[0])
    })
}

// 获取A3记录
CIProjectSQL.prototype.getA3RecordByPersonal = function (account, callback) {
    const sql = `
        select * from A3_Management where CreateBy = '${account}' order by ID ASC
    `
    console.log('执行SQL查询:\n', sql)
    this._query(sql, function (err, result) {
        if (err) return callback(err)
        // console.log('查询结果:\n', result)
        callback(result.recordset)
    })
}

// 获取A3记录
CIProjectSQL.prototype.getA3RecordByAll = function (callback) {
    const sql = `
        select * from A3_Management  order by ID ASC
    `
    console.log('执行SQL查询:\n', sql)
    this._query(sql, function (err, result) {
        if (err) return callback(err)
        // console.log('查询结果:\n', result)
        callback(result.recordset)
    })
}

// 创建A3记录
CIProjectSQL.prototype.createA3Record = function(data, callback) {
    const sql = `
        BEGIN TRANSACTION;

        INSERT INTO [dbo].[A3_Management] (
            [Factory], [Department], [Machine], [Unit], [Category],
            [TeamLeader], [TeamMembers], [ProblemDescription], [WhyImportant],
            [When], [HowOften], [Where], [PhotoDescription], [ProblemPhotoUrl],
            [CreatedAt], [UpdatedAt], [CreateBy], [ReportType], [Attachments]
        ) VALUES (
            '${data.problemDefinition.factory}',
            '${data.problemDefinition.department}',
            '${data.problemDefinition.machine}',
            '${data.problemDefinition.unit}',
            '${data.problemDefinition.category}',
            '${data.problemDefinition.teamLeader}',
            '${JSON.stringify(data.problemDefinition.teamMembers)}',
            '${data.problemDefinition.problemDescription}',
            '${data.problemDefinition.whyImportant}',
            '${data.problemDefinition.when}',
            '${data.problemDefinition.howOften}',
            '${data.problemDefinition.where}',
            '${data.problemDefinition.photoDescription}',
            '${data.problemDefinition.url}',
            getdate(),
            getdate(),
            '${data.problemDefinition.CreateBy}',
            '${data.reportType || '绿带问题解决A3报告'}',
            '${JSON.stringify(data.problemDefinition.attachments || [])}'
        );

        DECLARE @newId INT = SCOPE_IDENTITY();

        ${data.Metrics.map(metric => `
        INSERT INTO [dbo].[A3_Metrics] (
            [A3_ID], [MetricName], [Baseline], [Goal], [Actual], [Units]
            ) VALUES (
                @newId,
                '${metric.MetricName}',
                '${metric.Baseline}',
                '${metric.Goal}',
                '${metric.Actual || ''}',
                '${metric.Units}'
            );
        `).join('')}

        ${data.WorkPrinciples.map(principle => `
        INSERT INTO [dbo].[A3_WorkPrinciples] (
            [A3_ID], [PrincipleName], [MediaFiles], [MediaDescription], [SettingsAndDescriptions]
            ) VALUES (
                @newId,
                '${principle.name}',
                '${JSON.stringify(principle.mediaFiles)}',
                '${principle.mediaDescription}',
                '${JSON.stringify(principle.settingsAndDescriptions)}'
            );
        `).join('')}

        INSERT INTO [dbo].[A3_RootCauseAnalysis] (
            [A3_ID], [HumanIssues], [MachineIssues], [MaterialIssues],
            [MethodIssues], [EnvironmentIssues], [TopIssues], [whyAnalysis]
        ) VALUES (
            @newId,
            '${JSON.stringify(data.RootCauseAnalysis.human)}',
            '${JSON.stringify(data.RootCauseAnalysis.machine)}',
            '${JSON.stringify(data.RootCauseAnalysis.material)}',
            '${JSON.stringify(data.RootCauseAnalysis.method)}',
            '${JSON.stringify(data.RootCauseAnalysis.environment)}',
            '${JSON.stringify(data.RootCauseAnalysis.topIssues)}',
            '${JSON.stringify(data.RootCauseAnalysis.whyAnalysis)}'
        );

        ${(data.ImprovementPlans || []).map(plan => `
        INSERT INTO [dbo].[A3_ImprovementPlans] (
            [A3_ID], [Description], [ActionPlan], [Effort], [Benefit],
            [TotalScore], [Responsible], [DueDate], [BeforeImage], [AfterImage], [ImprovementResult]
            ) VALUES (
                @newId,
                '${plan.description || ''}',
                '${plan.actionPlan || ''}',
                ${plan.effort || 0},
                ${plan.benefit || 0},
                '${plan.totalScore || 'C'}',
                '${plan.responsible || ''}',
                ${plan.dueDate ? `'${plan.dueDate}'` : 'NULL'},
                '${plan.beforeImage || ''}',
                '${plan.afterImage || ''}',
                '${plan.improvementResult || ''}'
            );
        `).join('')}

        INSERT INTO [dbo].[A3_PerformanceSummary] (
            [A3_ID], [Description], [Images]
        ) VALUES (
            @newId,
            '${data.PerformanceSummary.description || ''}',
            '${JSON.stringify(data.PerformanceSummary.images || [])}'
        );

        COMMIT;

        SELECT @newId AS newId;
    `

    console.log('执行SQL插入:\n', sql)
    this._query(sql, function(err, result) {
        if (err) return callback(err)
        callback(null, result.recordset[0].newId)
    })
}

// 更新A3记录
CIProjectSQL.prototype.updateA3Record = function(data, callback) {
    const sql = `
        BEGIN TRANSACTION;

        UPDATE [dbo].[A3_Management] SET
            [Factory] = '${data.problemDefinition.factory}',
            [Department] = '${data.problemDefinition.department}',
            [Machine] = '${data.problemDefinition.machine}',
            [Unit] = '${data.problemDefinition.unit}',
            [Category] = '${data.problemDefinition.category}',
            [TeamLeader] = '${data.problemDefinition.teamLeader}',
            [TeamMembers] = '${JSON.stringify(data.problemDefinition.teamMembers)}',
            [ProblemDescription] = '${data.problemDefinition.problemDescription}',
            [WhyImportant] = '${data.problemDefinition.whyImportant}',
            [When] = '${data.problemDefinition.when}',
            [HowOften] = '${data.problemDefinition.howOften}',
            [Where] = '${data.problemDefinition.where}',
            [PhotoDescription] = '${data.problemDefinition.photoDescription}',
            [ProblemPhotoUrl] = '${data.problemDefinition.url}',
            [UpdatedAt] = getdate()
        WHERE [ID] = ${data.ID};

        DELETE FROM [dbo].[A3_Metrics] WHERE [A3_ID] = ${data.ID};
        ${data.Metrics.map(metric => `
            INSERT INTO [dbo].[A3_Metrics] (
                [A3_ID], [MetricName], [Baseline], [Goal], [Units]
            ) VALUES (
                ${data.ID},
                '${metric.MetricName}',
                '${metric.Baseline}',
                '${metric.Goal}',
                '${metric.Units}'
            );
        `).join('')}

        DELETE FROM [dbo].[A3_WorkPrinciples] WHERE [A3_ID] = ${data.ID};
        ${data.WorkPrinciples.map(principle => `
            INSERT INTO [dbo].[A3_WorkPrinciples] (
                [A3_ID], [PrincipleName], [MediaFiles], [MediaDescription], [SettingsAndDescriptions]
            ) VALUES (
                ${data.ID},
                '${principle.name}',
                '${JSON.stringify(principle.mediaFiles)}',
                '${principle.mediaDescription}',
                '${JSON.stringify(principle.settingsAndDescriptions)}'
            );
        `).join('')}

        UPDATE [dbo].[A3_RootCauseAnalysis] SET
            [HumanIssues] = '${JSON.stringify(data.RootCauseAnalysis.human)}',
            [MachineIssues] = '${JSON.stringify(data.RootCauseAnalysis.machine)}',
            [MaterialIssues] = '${JSON.stringify(data.RootCauseAnalysis.material)}',
            [MethodIssues] = '${JSON.stringify(data.RootCauseAnalysis.method)}',
            [EnvironmentIssues] = '${JSON.stringify(data.RootCauseAnalysis.environment)}',
            [TopIssues] = '${JSON.stringify(data.RootCauseAnalysis.topIssues)}',
            [whyAnalysis] = '${JSON.stringify(data.RootCauseAnalysis.whyAnalysis)}'
        WHERE [A3_ID] = ${data.ID};

        COMMIT;
    `

    console.log('执行SQL更新:\n', sql)
    this._query(sql, function(err, result) {
        if (err) return callback(err)
        callback(null)
    })
}

// 删除A3记录
CIProjectSQL.prototype.deleteA3Record = function(id, callback) {
    const sql = `
        BEGIN TRANSACTION;

        DELETE FROM [dbo].[A3_Metrics] WHERE [A3_ID] = ${id};
        DELETE FROM [dbo].[A3_WorkPrinciples] WHERE [A3_ID] = ${id};
        DELETE FROM [dbo].[A3_RootCauseAnalysis] WHERE [A3_ID] = ${id};
        DELETE FROM [dbo].[A3_ImprovementPlans] WHERE [A3_ID] = ${id};
        DELETE FROM [dbo].[A3_PerformanceSummary] WHERE [A3_ID] = ${id};
        DELETE FROM [dbo].[A3_Management] WHERE [ID] = ${id};

        COMMIT;
    `
    console.log('执行SQL删除:\n', sql)
    this._query(sql, function(err, result) {
        if (err) return callback(err)
        callback('已删除')
    })
}

module.exports = CIProjectSQL
