<!-- 写HTML -->
<template>
    <base-content>
        <q-dialog v-model="trainer_VerifyDialog" persistent transition-show="flip-down" transition-hide="flip-up">
            <q-card>
                <q-bar class="bg-teal text-white text-center">
                    <q-space />
                </q-bar>
                <q-card-section style="margin-left: 10px;">
                    <div class="fit row justify-start ">
                        <div class="fit row justify-start  text-h5 text-bold">
                            <div style="color:dodgerblue">
                                Kimberly-Clark
                            </div>
                            <div>老师权限验证</div>
                        </div>
                    </div>
                </q-card-section>

                <q-card-section>
                    <div class="fit row justify-start ">
                        <div class="fit row justify-start  text-h5 text-bold">
                            <div class="row wrap justify-start">
                                <div style="font-size: 15px;line-height: 45px;font-weight: 600;">员工号：</div>
                                <q-input style="width: 200px;" v-model="trainer_Verify['account']" filled label="员工号"
                                    stack-label dense>
                                </q-input>
                            </div>
                            <div class="row wrap justify-start">
                                <div style="font-size: 15px;line-height: 45px;font-weight: 600;">员工号：</div>
                                <q-input style="width: 200px;" v-model="trainer_Verify['psw']" filled type="password"
                                    label="密码" stack-label dense>
                                </q-input>
                            </div>
                        </div>
                    </div>
                </q-card-section>
                <q-card-actions align="right">
                    <!-- <q-btn flat color="primary" label="开始验证" @click="initData" /> -->
                    <!-- <q-btn flat color="primary" label="取消" /> -->
                </q-card-actions>
            </q-card>
        </q-dialog>






        <div class="q-pa-md">
            <q-card>
                <q-tabs v-model="tab" dense class="text-grey" active-color="primary" indicator-color="primary"
                    align="justify" narrow-indicator>
                    <q-tab name="scoreQueryTab" label="考试分数查询" />
                    <q-tab name="ExcelUploadTab" label="基础课程上传" />
                    <q-tab name="OJTUploadTab" label="岗位任务上传" />
                </q-tabs>

                <q-separator />
                <q-tab-panels v-model="tab" animated>
                    <q-tab-panel name="scoreQueryTab">
                        <div class="text-h6"></div>
                        <div class="row wrap justify-start">
                            <div style="font-size: 15px;line-height: 45px;font-weight: 600;">员工号:</div>
                            <q-input style="width: 200px;" v-model="Employee_ID" filled stack-label label-slot dense>
                                <template v-slot:label>员工号
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-input>
                            <q-btn style="margin-left: 10px;width: 100px;height: 40px;" color="primary" label="查询"
                                @click="query_by_student()" />
                        </div>
                        <div v-if="TrainingQueryTable">
                            <q-table :data="TrainingQueryTable" row-key="name" :pagination="initialPagination">
                            </q-table>
                        </div>
                    </q-tab-panel>


                    <q-tab-panel name="ExcelUploadTab">
                        <div class="text-h6">理论分数上传</div>
                        <div v-if="permission.includes('在线培训_管理员') || permission.includes('Admin') ">
                            <input type="file" @change="handleFileUpload" />
                            <button @click="uploadData">上传数据</button>
                            <div v-if="tableData.length > 0 && tab == 'ExcelUploadTab'">
                                <q-table :data="tableData" :columns="columns" row-key="name"
                                    :pagination="initialPagination">
                                    <template v-slot:body="props">
                                        <q-tr :props="props"
                                            :style="props.row.Verifty === '未通过' ? 'background-color:#ad0707;color:white;' : ''">
                                            <q-td key="ID" :props="props">
                                                {{ props.row.ID }}
                                            </q-td>
                                            <q-td key="Verifty" :props="props">
                                                {{ props.row.Verifty }}
                                            </q-td>
                                            <q-td key="考试名称" :props="props">
                                                {{ props.row["考试名称"] }}
                                            </q-td>
                                            <q-td key="工号" :props="props">
                                                {{ props.row['工号'] }}
                                            </q-td>
                                            <q-td key="姓名" :props="props">
                                                {{ props.row['姓名'] }}
                                            </q-td>
                                            <q-td key="是否通过" :props="props">
                                                {{ props.row['是否通过'] }}
                                            </q-td>
                                            <q-td key="最高分" :props="props">
                                                {{ props.row['最高分'] }}
                                            </q-td>
                                            <q-td key="第一次开考时间" :props="props">
                                                {{ props.row['第一次开考时间'] }}
                                            </q-td>
                                        </q-tr>
                                    </template>
                                </q-table>
                            </div>

                        </div>
                        <div v-else>
                            <div class="text-h6 text-bold">非常抱歉您没有此功能的权限。请联系管理员</div>
                        </div>


                    </q-tab-panel>

                    <q-tab-panel name="OJTUploadTab">
                        <div class="text-h6">实操分数上传</div>
                        <div
                            v-if="permission.includes('在线培训_管理员') || permission.includes('Admin') || permission.includes('在线培训_老师')">
                            <div class="fit row justify-start  text-h5 text-bold">
                                <div class="row wrap justify-start">
                                    <div style="font-size: 15px;line-height: 45px;font-weight: 600;">培训类别：</div>
                                    <q-select outlined v-model="Training_Category" :options="Training_Category_List"
                                        label-slot clearable style="width: 200px;" dense>
                                        <template v-slot:label>培训类别
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>
                                </div>

                                <div class="row wrap justify-start">
                                    <div style="font-size: 15px;line-height: 45px;font-weight: 600;">业务类别：</div>
                                    <q-select outlined v-model="Business_Category" :options="Business_Category_List"
                                        label-slot clearable style="width: 200px;" dense>
                                        <template v-slot:label>业务类别
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>
                                </div>

                                <div class="row wrap justify-start">
                                    <div style="font-size: 15px;line-height: 45px;font-weight: 600;">岗位：</div>
                                    <q-select outlined v-model="Position" :options="Position_List" label-slot clearable
                                        style="width: 200px;" dense>
                                        <template v-slot:label>岗位
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>
                                </div>

                                <div class="row wrap justify-start">
                                    <div style="font-size: 15px;line-height: 45px;font-weight: 600;">学员姓名:</div>
                                    <q-input style="width: 200px;" v-model="student_Data" filled stack-label label-slot
                                        dense>
                                        <template v-slot:label>学员姓名
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-input>
                                </div>


                                <q-btn style="margin-left: 10px;width: 100px;height: 40px;" color="primary" label="查询"
                                    @click="getTrainingData(Training_Category, Business_Category, Position)" />
                            </div>

                            <div v-if="Employee_Info_Data">
                                <div class="text-h6 text-bold" style="margin-top: 5px;"> 学员信息：</div>
                                <div class="row wrap justify-start" style="margin-bottom: 10px;"
                                    v-if="Employee_Info_Data">
                                    <div>
                                        <div class="text-h7 text-bold" style="margin-right: 30px;">员工号</div>
                                        <div>{{ Employee_Info_Data[0]['Employee_ID'] }}</div>
                                    </div>
                                    <div>
                                        <div class="text-h7 text-bold" style="margin-right: 30px;">姓名</div>
                                        <div>{{ Employee_Info_Data[0]['Employee_Name'] }}</div>
                                    </div>
                                    <div>
                                        <div class="text-h7 text-bold" style="margin-right: 30px;">职位</div>
                                        <div>{{ Employee_Info_Data[0]['Position'] }}</div>
                                    </div>
                                </div>

                                <div class="text-h6 text-bold" style="margin-top: 5px;">课程信息：</div>

                                <div class="row wrap justify-start">
                                    <div style="font-size: 15px;line-height: 45px;font-weight: 600;">选择课程：</div>
                                    <q-select outlined v-model="Training_Key_Data" :options="Training_Key_List"
                                        label-slot clearable style="width: 300px;" dense>
                                        <template v-slot:label>选择课程
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>
                                    <div class="row wrap justify-start">
                                        <div style="font-size: 15px;line-height: 45px;font-weight: 600;">考试日期：</div>
                                        <q-input style="width: 200px;" v-model="Training_Time" filled type="date"
                                            label="考试日期" stack-label dense>
                                            <template v-slot:label>考试日期
                                                <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                    style="font-size: 12px;">必填</em>
                                            </template>
                                        </q-input>
                                    </div>
                                    <div style="font-size: 15px;line-height: 45px;font-weight: 600;">是否合格：</div>
                                    <q-select outlined v-model="Training_Result_Data" :options="['正考通过', '未通过']"
                                        label-slot clearable style="width: 170px;" dense>
                                        <template v-slot:label>是否合格
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>
                                    <div style="font-size: 15px;line-height: 45px;font-weight: 600;">考试分数：</div>
                                    <q-input style="width: 200px;" v-model="Training_Score_Data" filled stack-label
                                        label-slot dense>
                                        <template v-slot:label>考试分数
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-input>
                                    <q-btn style="margin-left: 10px;width: 100px;height: 40px;" color="primary"
                                        label="上传结果" @click="uploadData_by_student()" />
                                </div>
                            </div>

                        </div>
                        <div v-else>
                            <div class="text-h6 text-bold">非常抱歉您没有此功能的权限。请联系管理员</div>
                        </div>








                    </q-tab-panel>

                </q-tab-panels>
            </q-card>
        </div>
    </base-content>
</template>
<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import * as XLSX from 'xlsx';

export default {
    name: 'Home',
    components: {
        BaseContent,
    },
    //页面首次进入的默认
    mounted() {
        this.permission = localStorage.getItem('user_role')
        
    },
    watch: {
        tab: function (newVal, oldVal) {
            if (this.tab == 'ExcelUploadTab') {
                this.TrainingList_By_ID() 
            }
            if (this.tab == 'OJTUploadTab' || this.tab == 'ExcelUploadTab') {
                this.Get_Category()
            }
        }
        // Training_Category: function (newVal, oldVal) {
        //     this.getTrainingData(newVal, this.Business_Category, this.Position)
        // },

    },
    // 以下放变量
    data() {
        return {
            permission:'',
            tab: 'scoreQueryTab',
            ojt_tab: 'by_student',
            Training_Category: '',
            Business_Category: '',
            Position: '',
            Training_Category_List: [],
            Business_Category_List: [],
            Position_List: [],
            tableData: [],
            initialPagination: { rowsPerPage: 0 },
            columns: [
                { name: 'ID', label: '课程ID', field: 'ID', align: 'center' },
                { name: 'Verifty', label: '上传验证', field: 'Verifty', align: 'center', },
                { name: "考试名称", label: '考试名称', field: "考试名称", align: 'center' },
                { name: '工号', label: '工号', field: '工号', align: 'center' },
                { name: '姓名', label: '姓名', field: '姓名', align: 'center' },
                { name: '是否通过', label: '是否通过', field: '是否通过', align: 'center' },
                { name: '最高分', label: '分数', field: '最高分', align: 'center' },
                { name: '第一次开考时间', label: '考试时间', field: '第一次开考时间', align: 'center' },
            ],

            Training_Key_List: [],
            Training_Key_Data: '',
            Training_Result_Data: '',
            Training_Score_Data: '',
            Training_Time: '',
            student_Data: '',
            Employee_ID:'',
            Employee_Info_Data: false,
            trainer_Verify: {
                account: '',
                psw: '',
            },
            trainer_VerifyDialog: false,
            TrainingQueryTable:false
        }
    },
    //以下搞方法
    methods: {
        async getTrainingData(Training_Category, Business_Category, Position) {
            console.log('Training_Category:', Training_Category)
            console.log('Business_Category:', Business_Category)
            console.log('Position:', Position)
            this.Employee_Info()
            if (Training_Category == '' || Business_Category == '' || Position == '') {
                console.log('请选择培训类别、业务类别、岗位')
                return
            } else {

                var _this = this
                const {
                    data: res
                } = await _this.$http.get(`training/TrainingList?Training_Category=${Training_Category}&Business_Category=${Business_Category}&Position=${Position}`)
                console.log('TrainingData', res)
                _this.TrainingData = res
                _this.Training_Key_List = []
                if (res.length > 0) {
                    for (let i = 0; i < res.length; i++) {
                        _this.Training_Key_List.push({
                            label: res[i]['Training_Key'],
                            value: res[i]['Training_ID'],
                        })
                    }
                }
            }

        },
        async TrainingList_By_ID() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`training/TrainingList_By_ID`)
            console.log('TrainingList_By_ID_Data', res)
            _this.TrainingList_By_ID_Data = res
        },
        clearTable() {
            this.tableData = []
        },
        getSelectedString() {
            return this.course_List.length === 0 ? '' : `${this.course_List.length} record${this.course_List.length > 1 ? 's' : ''} selected of ${this.TrainingData.length}`
        },
        handleFileUpload(event) {
            const file = event.target.files[0];
            const reader = new FileReader();
            reader.onload = (e) => {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                // 假设我们只读取第一个工作表
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];

                // 将工作表转换为 JSON 格式
                let jsonData = XLSX.utils.sheet_to_json(worksheet);
                //this.tableData = jsonData;
                // 修改键名
                jsonData = jsonData.map(item => {
                    if (item["﻿考试名称"]) {
                        item["考试名称"] = item["﻿考试名称"];
                        delete item["﻿考试名称"];
                    }
                    return item;
                });

                // 如果需要，您还可以设置表头
                // this.tableHeaders = Object.keys(jsonData[0]);
                console.log('jsonData,', jsonData)
                this.formatData(jsonData)
            };

            reader.readAsArrayBuffer(file);

        },
        formatData(data) {

            console.log('dataType:', typeof data)
            console.log('data:', data)
            const baseData = this.TrainingList_By_ID_Data
            for (let i = 0; i < data.length; i++) {
                const Training_Name = data[i]["考试名称"]
                // console.log('data_Training_Name:', Training_Name)
                data[i]['ID'] = ''
                data[i]['Verifty'] = '未通过'
                for (let j = 0; j < baseData.length; j++) {
                    // console.log('baseData_Training_Name:', baseData[j]['Training_Name'])
                    if (baseData[j]['Training_Key'] == Training_Name) {
                        data[i]['ID'] = baseData[j]['Training_ID']
                        data[i]['Verifty'] = '通过'
                        data[i]['培训者'] = ''
                        break
                    }
                }
            }

            // 将处理后的数据赋值给 tableData
            console.log(data)
            this.tableData = data;
        },
        uploadData() {
            const _this = this
            const data = _this.tableData
            for (let i = 0; i < data.length; i++) {
                const ID = data[i]['ID']
                const Verifty = data[i]['Verifty']
                if (ID != '' && Verifty == '通过') {
                    _this.$http.post('training/SP_API_Training_Score_Update', data[i]).then(function (response) {
                        console.log(response)
                        if (response.data == 'Success') {
                            data[i]['Verifty'] = '已上传'
                        }
                    })
                }
            }
        },
        uploadData_by_student() {
            const _this = this
            const data = {
                "ID": _this.Training_Key_Data['value'],
                "考试名称": _this.Training_Key_Data['label'],
                "工号": _this.Employee_Info_Data[0]['Employee_ID'],
                "姓名": _this.Employee_Info_Data[0]['Employee_Name'],
                "是否通过": _this.Training_Result_Data,
                "培训者": localStorage.getItem('username'),
                "最高分": _this.Training_Score_Data,
                "第一次开考时间": _this.Training_Time
            }
            _this.$http.post('training/SP_API_Training_Score_Update', data).then(function (response) {
                console.log(response)
                if (response.data == 'Success') {
                    // data[i]['Verifty'] = '已上传'
                    _this.$q.notify({
                        message: '上传成功',
                        color: 'positive',
                        textColor: 'white',
                        icon: 'done',
                        position: 'top',
                        timeout: 2000
                    })
                    _this.Training_Key_Data = ''
                    _this.Training_Result_Data = ''
                    _this.Training_Score_Data = ''
                    _this.Training_Time = ''
                }
            })
        },
        async Get_Category() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`training/TrainingCategory`)
            console.log('TrainingCategory', res)
            _this.Training_Category_List = res['Training_Category']
            _this.Business_Category_List = res['Business_Category']
            _this.Position_List = res['Position']

        },
        async Employee_Info() {
            var _this = this
            _this.Employee_Info_Data = false
            const {
                data: res
            } = await _this.$http.get(`training/Employee_Info?Employee_Name=${_this.student_Data}`)
            console.log('Employee_Info', res)
            _this.Employee_Info_Data = res
        },

        async query_by_student() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`training/SP_API_Training_Query_Personal?Employee_ID=${_this.Employee_ID}`)
            console.log('query_by_student', res)
            _this.TrainingQueryTable=res
        }
    }
}
</script>
