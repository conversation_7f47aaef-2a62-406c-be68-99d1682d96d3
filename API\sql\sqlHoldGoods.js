const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);
}

sqlexec.prototype = new _Base()

sqlexec.prototype.getLine = function (callBack) {
    sqlexec.prototype._query("select distinct line from dbo.Employee_List where line like 'N%'", function (err, result) {
        if (err) {
        }
        return callBack(result.recordset)
    })
}

sqlexec.prototype.getEmployee = function (callBack) {
    sqlexec.prototype._query(`select line,Employee_ID,Employee_PYName,Position,Business_Title,Manager_ID from dbo.Employee_List  order by Employee_PYName asc`, function (err, result) {
        if (err) {
        }
        return callBack(result.recordset)
    })
}

sqlexec.prototype.getMaterial = function (sku,callBack) {
    sqlexec.prototype._query(" select top 1 [SKU_Desc],[Cuts_Per_Case],[Target_Speed] FROM [dbo].[Product_Base] where SKU_Code=" + sku + " order by SKU_Code asc", function (err, result) {
        if (err) {
        }
        return callBack(result.recordset)
    })
}

sqlexec.prototype.insertHoldData = function (data, callBack) {
    console.log(`insert into Hold_Goods_Record(Line,Submitter,Date,Shift,SKU_Code,SKU_Desc,badCategory,holdRatio,SampleQTY,holdQTY,holdCases,holdMin,rootCause,Result,holdStartTime,holdEndTime) 
        values('${data['Line']}','${data['employee_Name']}','${data['Date']}','${data['shift']}','${data['SKU']}','${data['SKU_desc']}','${data['badCategory']}', 
        '${data['holdRatio']}','${data['SampleQTY']}','${data['holdQTY']}','${data['holdCases']}','${data['holdMin']}','${data['rootCause']}','${data['result']}',
        '${data['holdStartTime'] || '1900-01-01'}','${data['holdEndTime'] || '1900-01-01'}')`)

    sqlexec.prototype._query(`insert into Hold_Goods_Record(Line,Submitter,Date,Shift,SKU_Code,SKU_Desc,badCategory,holdRatio,SampleQTY,holdQTY,holdCases,holdMin,rootCause,Result,holdStartTime,holdEndTime) 
        values('${data['Line']}','${(data['employee_Name'] && data['employee_Name'].label) || ''}','${data['Date']}','${data['shift']}','${data['SKU']}','${data['SKU_desc']}','${data['badCategory']}', 
        '${data['holdRatio']}','${data['SampleQTY']}','${data['holdQTY']}','${data['holdCases']}','${data['holdMin']}','${data['rootCause']}','${data['result']}',
        '${data['holdStartTime'] || '1900-01-01'}','${data['holdEndTime'] || '1900-01-01'}')`, function (err, result) {
        if (err) {
            console.log(err)
        }
        console.log('result', result)
        return callBack('已插入数据库')
    })
}

sqlexec.prototype.getHoldGoodsRecords = function (callBack) {
    sqlexec.prototype._query("SELECT id,Line,Submitter,Date,Shift,SKU_Code,SKU_Desc,badCategory,holdRatio,SampleQTY,holdQTY,holdCases,holdMin,rootCause,Result,holdStartTime,holdEndTime,resultSemiConsume,resultBagsConsume,resultCasesConsume FROM [dbo].[Hold_Goods_Record]", function (err, result) {
        if (err) {
            console.log(err)
        }
        return callBack(result.recordset)
    })
}

sqlexec.prototype.updateHoldData = function (data, callBack) {
    const sqlStr = `
        UPDATE [dbo].[Hold_Goods_Record]
        SET 
            Line = '${data.Line || ''}',
            Submitter = '${data.Submitter || ''}', 
            Date = '${data.Date || '1900-01-01'}',
            Shift = '${data.Shift || ''}',
            SKU_Code = '${data.SKU_Code || 0}',
            SKU_Desc = '${data.SKU_Desc || ''}',
            badCategory = '${data.badCategory || ''}',
            holdRatio = '${data.holdRatio || 0}',
            SampleQTY = '${data.SampleQTY || 0}',
            holdQTY = '${data.holdQTY || 0}',
            holdCases = '${data.holdCases || 0}',
            holdMin = '${data.holdMin || 0}',
            rootCause = '${data.rootCause || ''}',
            Result = '${data.Result || ''}',
            resultSemiConsume = '${data.resultSemiConsume || ''}', 
            resultBagsConsume = '${data.resultBagsConsume || ''}', 
            resultCasesConsume = '${data.resultCasesConsume || ''}',
            holdStartTime = '${data.holdStartTime || '1900-01-01'}',
            holdEndTime = '${data.holdEndTime || '1900-01-01'}'
        WHERE id = '${data.id}'
    `;
    
    console.log('Executing update query:');
    console.log(sqlStr);

    sqlexec.prototype._query(sqlStr, function(err, result) {
        if (err) {
            console.error('数据库更新错误:', err.message);
            return callBack({
                success: false,
                message: '数据库错误: ' + err.message
            });
        }
        
        if (result.rowsAffected[0] === 0) {
            console.warn('更新警告: 未找到匹配的记录');
            return callBack({
                success: false,
                message: '记录不存在: 未找到匹配的记录'
            });
        }
        
        console.log('更新成功: 已更新' + result.rowsAffected[0] + '条记录');
        return callBack({
            success: true,
            message: '更新成功',
            rowsAffected: result.rowsAffected[0]
        });
    });
}

sqlexec.prototype.deleteHoldData = function(id, callBack) {
    console.log(`delete from Hold_Goods_Record where id=${id}`);
    sqlexec.prototype._query(`delete from Hold_Goods_Record where id=${id}`, function(err, result) {
        if (err) {
            console.error('数据库删除错误:', err);
            return callBack('删除失败', err);
        }
        if (result.rowsAffected[0] === 0) {
            return callBack('记录不存在');
        }
        return callBack('删除成功');
    });
}

sqlexec.prototype.testParamQuery = function(callBack) {
    const query = `
        SELECT TOP 1 * 
        FROM [dbo].[Hold_Goods_Record]
        WHERE id = @id
    `;
    
    const params = [
        { name: 'id', value: 1 }
    ];

    sqlexec.prototype._query(query, params, function(err, result) {
        if (err) {
            console.error('参数化查询测试失败:', err);
            return callBack(false);
        }
        return callBack(true);
    });
}

module.exports = sqlexec;
