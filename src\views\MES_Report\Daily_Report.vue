<template>
	<base-content>
		<div dense style="font-size: 20px;color: black;text-align: center;margin-top: 5px;">日报</div>
		<div class="q-pa-md">
			<!--  选择框及按钮 -->
			<div class="q-gutter-md row">
				<q-select outlined dense v-model="Line" :options="LineList" style="width: 140px" label-slot clearable>
					<template v-slot:label>产线
						<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
					</template>
				</q-select>
				<q-input v-model="Start" outlined type="date" dense label-slot style="width: 200px">
					<template v-slot:label>开始日期
						<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 10px;">必填</em>
					</template>
				</q-input>
				<q-input v-model="End" outlined type="date" dense label-slot style="width: 200px">
					<template v-slot:label>结束日期
						<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 10px;">必填</em>
					</template>
				</q-input>
				<q-select outlined dense v-model="Shift" :options="ShiftList" style="width: 140px" label-slot clearable>
					<template v-slot:label>班次</template>
				</q-select>
				<q-select outlined dense v-model="Crew" :options="CrewList" style="width: 140px" label-slot clearable>
					<template v-slot:label>班组</template>
				</q-select>
				<q-btn dense :loading="loading1" color="primary" label="查询" @click="getDailyKPI(1)"
					style="width: 70px;height: 40px;" />
			</div>
			<div dense style="font-size: 13px;color: brown;margin-left: 500px;">
				点击查询按钮获取原始数据后才可以进行班次，班组的筛选
			</div>
		</div>
		<!-- 日报详情列表 -->
		<div class="q-pa-md">
			<q-table class="my-sticky-header-table" dense title="日报详情" :data="DailyKPIList" :columns="column"
				row-key="name" style="margin-top: -30px;" meta charset="UTF-8" :pagination="{ rowsPerPage: 15 }" flat
				bordered>
				<template v-slot:top-right>
					<q-btn color="light-green" icon-right="archive" label="导出csv" no-caps @click="exportTable" />
				</template>
				<template v-slot:body="props">
					<q-tr :props="props">
						<q-td key="Date" :props="props">{{ props.row.Date }}</q-td>
						<q-td key="Shift" :props="props">{{ props.row.Shift }}</q-td>
						<q-td key="Crew" :props="props">{{ props.row.Crew }}</q-td>
						<q-td key="Total_Cuts" :props="props">{{ props.row.Total_Cuts }}</q-td>
						<q-td key="Good_Cuts" :props="props">{{ props.row.Good_Cuts }}</q-td>
						<q-td key="Waste" :props="props">{{ props.row.Waste }}</q-td>
						<q-td key="MWPec" :props="props">{{ props.row.MWPec }}</q-td>
						<q-td key="MW" :props="props">{{ props.row.MW }}</q-td>
						<q-td key="PakW" :props="props">{{ props.row.PakW }}</q-td>
						<q-td key="CLPec" :props="props">{{ props.row.CLPec }}</q-td>
						<q-td key="Alarm" :props="props">
							<div class="text-pre-wrap blue-underline">{{ props.row.Alarm }}</div>
							<q-popup-edit v-slot="scope" v-model="props.row.Var_Desc" :use-popup-proxy="true">
								<q-card class="my-card">
									<q-card class="popup-card" flat>
										<q-card-section>
											<!-- 弹窗卡片内容 -->
											<q-list dense bordered separator>
												<q-item v-for="(desc, index) in scope.value" :key="index">
													<q-item-section>{{ desc }}</q-item-section>
												</q-item>
											</q-list>
										</q-card-section>
									</q-card>
								</q-card>
							</q-popup-edit>
						</q-td>
						<q-td key="Level3" :props="props">{{ props.row.Level3 }}</q-td>
					</q-tr>
				</template>
			</q-table>
		</div>

	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import Quagga from 'quagga'
import { exportFile } from 'quasar'
import Papa from 'papaparse';



export default {
	components: {
		BaseContent, Quagga
	},
	mounted() {
		console.log("mounted", this.$route.query)
		this.User_ID = localStorage.getItem('account')
		this.Line = localStorage.getItem('Related_Line')
		this.getLine()
	},

	data() {
		const currentDate = new Date();
		const options = { timeZone: 'Asia/Shanghai' }; // 设置时区为UTC+8
		const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
		const formattedDate = firstDayOfMonth.toLocaleDateString('en-CA', options);
		const today = new Date();
		today.setDate(today.getDate());
		const yesterday = new Date(today);
		yesterday.setDate(today.getDate() - 1);
		return {
			loading1: false,
			comment: '',
			Line: "",
			LineList: [],
			Start: formattedDate,
			End: yesterday.toISOString().substr(0, 10),
			Shift: "",
			ShiftList: ['白班', '夜班'],
			Crew: "",
			CrewList: [],
			VarDate: [],
			DailyKPIListFull: [],
			DailyKPIList: [],
			column: [
				{ name: 'Date', required: true, label: '日期', align: 'left', field: row => row.Date, format: val => `${val}`, sortable: true },
				{ name: 'Shift', required: true, label: '班次', align: 'left', field: row => row.Shift, format: val => `${val}`, sortable: true },
				{ name: 'Crew', required: true, label: '班组', align: 'left', field: row => row.Crew, format: val => `${val}`, sortable: true },
				{ name: 'Total_Cuts', required: true, label: '总切', align: 'left', field: row => row.Total_Cuts, format: val => `${val}`, sortable: true },
				{ name: 'Good_Cuts', required: true, label: '入库片数', align: 'left', field: row => row.Good_Cuts, format: val => `${val}`, sortable: true },
				{ name: 'Waste', required: true, label: '废品(总切-入库)', align: 'left', field: row => row.Waste, format: val => `${val}`, sortable: true },
				{ name: 'MWPec', required: true, label: '机器排废%', align: 'left', field: row => row.MWPec, sortable: true, format: (val, row) => `${val}%` },
				{ name: 'MW', required: true, label: '机器排废片数', align: 'left', field: row => row.MW, format: val => `${val}`, sortable: true },
				{ name: 'PakW', required: true, label: '包装废品(录入)', align: 'left', field: row => row.PakW, format: val => `${val}`, sortable: true },
				{ name: 'CLPec', required: true, label: 'CL超范%', align: 'left', field: row => row.CLPec, sortable: true, format: (val, row) => `${val}%` },
				{ name: 'Alarm', required: true, label: 'CL超范项', align: 'left', field: row => row.Alarm, format: val => `${val}`, sortable: true },
				{ name: 'Level3', required: true, label: '排废Top3', align: 'left', field: row => row.Level3, format: val => `${val}`, sortable: true },
			]
		}
	},

	watch: {
		Shift: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.Filter()
		},
		Crew: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.Filter()
		},

	},
	methods: {
		async getLine() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`material/lineList`)
			console.log('NJSLineList', res)
			_this.LineList = res
			this.LineList = _this.LineList
		},


		async getDailyKPI(number) {
			this.DailyKPIList = []
			// we set loading state
			this[`loading${number}`] = true;
			try {
				var _this = this
				var Line = this.Line
				var Start = this.Start
				var End = this.End
				let KPIList = []

				console.log('DailyKPI参数', Line, Start, End)
				const { data: res1
				} = await _this.$http.get(`material/DailyKPI?Line=${Line}&Start=${Start}&End=${End}`)
				console.log('DailyKPI', res1)
				KPIList = res1
				let WasteTop3List = []
				const {
					data: res2
				} = await _this.$http.get(`material/WasteTop3?Line=${Line}&Start=${Start}&End=${End}`)
				console.log('WasteTop3', res2)
				WasteTop3List = res2
				let CLList_1 = []
				const {
					data: res3
				} = await _this.$http.get(`mes/Centerline?Line=${Line}&Start=${Start}&End=${End}`)
				console.log('CLList原始', res3)
				CLList_1 = res3
				// 创建一个空对象，用于以Date和Shift作为键存储结果
				const result = {};
				// 遍历数组的每个对象
				for (const obj of CLList_1) {
					const { Date_1, Shift, Total_Count, Alarm, Var_Desc } = obj;
					// 组合Date和Shift作为键
					const key = `${Date_1}-${Shift}`;
					// 如果键不存在，初始化Total_Count和Alarm为0，并初始化一个空数组用于存储Alarm为1的Var_Desc
					if (!result[key]) {
						result[key] = {
							Date: Date_1,
							Shift: Shift,
							Total_Count: 0,
							Alarm: 0,
							Percent: 0,
							Var_Desc: []
						};
					}
					// 累加Total_Count和Alarm
					result[key].Total_Count += Total_Count;
					result[key].Alarm += Alarm;
					result[key].Percent = ((result[key].Alarm / result[key].Total_Count) * 100).toFixed(2);
					// 如果Alarm为1，将Var_Desc添加到数组中
					if (Alarm === 1) {
						result[key].Var_Desc.push(Var_Desc);
					}
				}
				// 将Alarm为0的Var_Desc设置为空字符串
				for (const key in result) {
					if (result[key].Alarm === 0) {
						result[key].Var_Desc = [];
					}
				}
				console.log('处理后的CL', result);
				const CLList_2 = result
				const CLList = Object.keys(CLList_2).map((key, index) => {
					const newItem = CLList_2[key];
					newItem.key = index.toString(); // 添加一个新的key属性，值为索引值的字符串形式
					return newItem;
				});
				console.log('CL新数组', CLList)
				const mergedArray = WasteTop3List.reduce((acc, wasteItem) => {
					const matchingKPI = KPIList.find(kpiItem =>
						kpiItem.Date === wasteItem.Date && kpiItem.Shift === wasteItem.Shift
					);
					const mergedItem = acc.find(item =>
						item.Date === wasteItem.Date && item.Shift === wasteItem.Shift
					);
					if (mergedItem) {
						mergedItem.Level3.push(wasteItem.Level3);
					} else {
						const matchingCL = CLList.find(clItem =>
							clItem.Date === wasteItem.Date && clItem.Shift === wasteItem.Shift
						);
						acc.push({
							Date: wasteItem.Date,
							Shift: wasteItem.Shift,
							Crew: matchingKPI ? matchingKPI.Crew : null,
							Total_Cuts: matchingKPI ? matchingKPI.Total_Cuts : null,
							Good_Cuts: matchingKPI ? matchingKPI.Good_Cuts : null,
							Waste: matchingKPI ? matchingKPI.Waste : null,
							MWPec: matchingKPI ? matchingKPI.MWPec : null,
							MW: matchingKPI ? matchingKPI.MW : null,
							PakW: matchingKPI ? matchingKPI.PakW : null,
							CLPec: matchingCL ? matchingCL.Percent : null,
							Alarm: matchingCL ? matchingCL.Alarm : null,
							Level3: [wasteItem.Level3],
							Var_Desc: matchingCL && matchingCL.Var_Desc && matchingCL.Var_Desc.length > 0 ? matchingCL.Var_Desc : []
						});
					} return acc;
				}, []);
				console.log('合并后的数据', mergedArray);
				this.DailyKPIList = mergedArray;
				this.DailyKPIListFull = mergedArray;
				// 使用 Set 去除重复项获得Crew筛选list
				const uniqueCrew = new Set(this.DailyKPIList.map(item => item.Crew));
				this.CrewList = Array.from(uniqueCrew);
				console.log('CrewList ', this.CrewList)
			} catch (error) {
				console.error(error)
			} finally {
				// reset loading state
				this[`loading${number}`] = false;
			}
		},
		async exportTable() {
			const csvData = Papa.unparse(this.DailyKPIList);
			const blob = new Blob(["\uFEFF" + csvData], { type: 'text/csv;charset=utf-8' });
			const url = URL.createObjectURL(blob);
			const link = document.createElement('a');
			link.style.display = 'none';
			link.href = url;
			link.download = '日报详情.csv';
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
			URL.revokeObjectURL(url);
		},
		async Filter() {
			try {
				var data = this.DailyKPIListFull
				var Shift = this.Shift
				var Crew = this.Crew
				this.DailyKPIList = await Filterdata(data, Shift, Crew)
			} catch (error) {
				console.error(error)
			}
		},
	}
}
function Filterdata(data, Shift, Crew) {
	return new Promise((resolve, reject) => {
		if (data && Array.isArray(data)) {
			let filteredData = data;
			// 根据Shift过滤
			console.log('Shift-F', Shift)
			if (Shift) {
				filteredData = filteredData.filter(item => item.Shift === Shift);
			}
			console.log('filter-Shift', filteredData)
			// 根据Crew过滤
			console.log('Crew-F', Crew)
			if (Crew) {
				filteredData = filteredData.filter(item => item.Crew === Crew);
			}
			console.log('filter-Crew', filteredData)
			// 按Level3汇总显示MW_QTY和MW_Count
			resolve(filteredData)
		} else {
			resolve([]); // 如果data不存在或不是数组，返回空数组
		}
	})
}


</script>

<style lang="css" scoped>
.fontStyle {
	color: brown;
	font-size: 20px;
	font-weight: 600;
	background-color: rgb(254, 220, 0);
	text-align: center;
	width: 500px;
	height: 200px;
	margin-top: 20px;
	margin-left: 20px;
}

.card_label {
	color: brown;
	font-weight: 500;
	font-size: 13px
}

.body {
	color: black;
	font-weight: 400;
	font-size: 13px
}

.body2 {
	color: rgb(16, 6, 159);
	font-weight: 400;
	font-size: 13px;
	margin-left: 20px
}
</style>

<style scoped>
.blue-underline {
	color: blue;
	text-decoration: underline;
}
</style>