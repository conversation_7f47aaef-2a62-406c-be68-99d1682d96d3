<template>
	<div>
		<div style="width:100%;height:300px;" :id="echarts" class="echarts" ref="echarts"></div>
	</div>
</template>

<script>
	// 引入echarts
	import echarts from 'echarts'
	export default {
		name: 'EchartsComponents',
		props: {
			// 接收父组件传递过来的信息

			chartData: {
				categories: {
					type: Array,
					default: () => []
				},
				series: {
					type: Array,
					default: () => []
				},
				name: {
					type: String
				},

			}

		},
		data() {
			return {}
		},
		methods: {
			drawChart() {
				var unit = this.chartData.series[0].name == '罗盘评分' ? '' : '%'

				// console.log(this.chartData)
				const vm = this
				// 基于准备好的dom，初始化echarts实例
				var myChart = echarts.init(document.getElementById(this.echarts))
				// 绘制图表
				myChart.setOption({
					title: {
						text: this.chartData.name
					},
					grid: {
						left: '0%',
						right: '4%',
						bottom: '0%',
						containLabel: true
					},
					tooltip: {
						trigger: 'axis',
						hideDelay: 100,
						axisPointer: {
							animation: true,
							type: 'shadow'
						},
						formatter: {}
					},
					legend: {
						data: [this.chartData.series[0].name]
					},
					xAxis: {
						data: this.chartData.categories,
						axisTick: {
							//坐标轴刻度相关设置。
							show: false
						}
					},
					yAxis: {
						show: false,
						type: 'value',
						splitNumber: 4,
						splitLine: {
							show: false
						},
						axisLine: {
							show: false
						},
						axisTick: {
							//坐标轴刻度相关设置。
							show: false
						},
						axisLabel: {
							textStyle: {
								fontSize: 13
							},
							formatter: function(value) {
								return value.toFixed(0) + unit;
							}
						}
					},
					dataZoom: [{
							type: 'inside',
							startValue: this.chartData.categories[Math.round(this.chartData.categories.length /
								2, 0)],
					
							endValue: this.chartData.categories[this.chartData.categories.length - 1]
						},
						{
							startValue: this.chartData.categories[Math.round(this.chartData.categories.length /
								2, 0)],
							end: this.chartData.categories[this.chartData.categories.length - 1]
						}
					],
					// series: this.chartData.series
					series: {

						name: this.chartData.series[0].name,
						type: this.chartData.series[0].type,
						data: this.chartData.series[0].data,
            markPoint: {
                label: {
                    normal: {
                        formatter: function (param) {
                            return param != null ? param.value : '';
                        }
                    }
                },
                data: this.chartData.makePointData
            },
						itemStyle: {
							color: '#00aaff'
						},
						stack: 'a',
						areaStyle: {
							color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
								offset: 0,
								color: '#00aaff'
							}, {
								offset: 1,
								color: '#ffe'
							}])
						},

					}
				})
			}
		},
		computed: {
			echarts() {
				return 'echarts' + Math.random() * 100000
			}
		},
		mounted: function() {
			const vm = this
			vm.$nextTick(() => {
				vm.drawChart()
			})
		},
		created: () => {}
	}
</script>

<style scoped>
</style>
