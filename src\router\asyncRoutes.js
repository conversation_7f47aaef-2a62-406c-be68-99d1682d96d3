﻿import Layout from '../components/Layout/Layout'

/**
 * 需要授权访问的路由
 */
const asyncRoutesChildren = [
	{
		path: '/',
		name: 'Home',
		meta: {
			//roles: ['admin', 'edit', 'approve'],
			title: '主页',
			icon: 'home',
			keepAlive: true
		},
		component: () => import('@/views/home/<USER>')
	},
	{
		path: '/awardPage',
		name: 'awardPage',
		meta: {
			roles: ['积分系统_读取', '积分系统_编辑', '积分系统_审批', 'Admin'],
			title: '积分系统',
			icon: 'fas fa-hand-holding-usd',
			keepAlive: true
		},
		component: Layout,
		children: [
			{
				path: 'awardApplication',
				meta: {
					roles: ['积分系统_编辑', '积分系统_审批', 'Admin'],
					title: '积分每日申请',
					icon: 'fas fa-hand-holding-usd',
					keepAlive: true
				},
				component: () => import('@/views/awardPage/awardApplication.vue')
			},
			{
				path: 'award',
				name: '积分月度申请',
				meta: {
					roles: ['积分系统_审批', 'Admin'],
					title: '积分月度申请',
					icon: 'fas fa-hand-holding-usd',
					keepAlive: true
				},
				component: () => import('@/views/awardPage/awardPage.vue')
			},
			{
				path: 'ApprovalPage',
				name: '积分审批',
				meta: {
					roles: ['积分系统_审批', 'Admin'],
					title: '积分审批',
					icon: 'local_library',
					keepAlive: true
				},
				component: () => import('@/views/ApprovalPage/ApprovalPage')
			},
			{
				path: 'awardQuery',
				name: '积分审批',
				meta: {
					roles: ['积分系统_审批', '积分系统_编辑', '积分系统_读取', 'Admin'],
					title: '个人积分查询',
					icon: 'local_library',
					keepAlive: true
				},
				component: () => import('@/views/awardPage/awardQuery')
			}
		]
	},
	{
		path: '/ideaBank',
		name: 'ideaBank',
		meta: {
			roles: ['点子银行_编辑', '点子银行_审批', 'Admin'],
			title: '点子银行',
			icon: 'fas fa-lightbulb',
			keepAlive: true
		},
		component: Layout,
		children: [
			{
				path: 'ideaBankApplicate',
				name: '点子录入/审批',
				meta: {
					roles: ['点子银行_编辑', '点子银行_审批', 'Admin'],
					title: '点子录入/审批',
					icon: 'fas fa-lightbulb',
					keepAlive: true
				},
				component: () => import('@/views/ideaBank/ideaBank.vue')
			},
			{
				path: 'ideaBankFeedBack',
				name: '点子反馈',
				meta: {
					roles: ['点子银行_编辑', '点子银行_审批', 'Admin'],
					title: '点子反馈',
					icon: 'far fa-share-square',
					keepAlive: true
				},
				component: () => import('@/views/ideaBank/ideaBankFeedBack.vue')
			},
			{
				path: 'ideaBankQuery',
				name: '点子查询',
				meta: {
					roles: ['点子银行_编辑', '点子银行_审批', 'Admin'],
					title: '点子查询',
					icon: 'far fa-share-square',
					keepAlive: true
				},
				component: () => import('@/views/ideaBank/ideaBankQuery.vue')
			},
		]
	},
	// {
	// 	path: '/BP',
	// 	name: 'BP',
	// 	meta: {
	// 		roles: ['BP_编辑', 'BP_审批', 'Admin'],
	// 		title: 'BP',
	// 		icon: 'fab fa-bootstrap',
	// 		keepAlive: true,

	// 	},
	// 	component: () => import('@/views/ideaBank/BP.vue')
	// },
	{
		path: '/BP',
		name: 'BP',
		meta: {
			roles: ['BP_编辑', 'BP_审批', 'Admin'],
			title: 'BP',
			icon: 'fab fa-bootstrap',
			keepAlive: true
		},
		component: Layout,
		children: [
			{
				path: 'BP',
				name: 'BP录入/审批',
				meta: {
					roles: ['BP_编辑', 'BP_审批', 'Admin'],
					title: 'BP录入/审批',
					icon: 'fab fa-bootstrap',
					keepAlive: true
				},
				component: () => import('@/views/ideaBank/BP.vue')
			},
			{
				path: 'BPQuery',
				name: 'BP查询',
				meta: {
					roles: ['BP_编辑', 'BP_审批', 'Admin'],
					title: 'BP查询',
					icon: 'fab fa-bootstrap',
					keepAlive: true
				},
				component: () => import('@/views/ideaBank/BPQuery.vue')
			},
		]
	},




	{
		path: '/SafeIncident',
		name: '安全隐患',
		meta: {
			roles: ['安全隐患_编辑', '安全隐患_审批', 'Admin'],
			title: '安全隐患',
			icon: 'fas fa-shield-alt',
			keepAlive: true
		},
		component: Layout,
		children: [
			{
				path: 'SafeIncidentApplicate',
				name: '安全隐患录入/审批',
				meta: {
					roles: ['安全隐患_编辑', '安全隐患_审批', 'Admin'],
					title: '安全隐患录入/审批',
					icon: 'fas fa-shield-alt',
					keepAlive: true
				},
				component: () => import('@/views/ideaBank/SafeIncident.vue')
			},
			{
				path: 'SafeIncidentFeedBack',
				name: '安全隐患反馈',
				meta: {
					roles: ['安全隐患_编辑', '安全隐患_审批', 'Admin'],
					title: '安全隐患反馈',
					icon: 'far fa-share-square',
					keepAlive: true
				},
				component: () => import('@/views/ideaBank/SafeIncidentFeedBack.vue')
			},
		]
	},



	{
		path: '/GradeChange',
		name: '换型管理',
		meta: {
			roles: ['换型管理_查看', '换型数据维护_编辑', 'Admin'],
			title: '换型管理',
			icon: 'description',
			keepAlive: true
		},
		component: Layout,
		children: [
			{
				path: 'GradeChangeTask',
				//name: 'GradeChange',
				meta: {
					roles: ['Admin', '换型管理_查看'],
					title: '换型任务',
					icon: 'description',
				},
				component: () => import('@/views/GradeChange/GradeChange.vue')
			},
			{
				path: 'GradeChangeTaskList',
				//name: 'GradeChangeTaskList',
				meta: {
					roles: ['Admin', '换型管理_查看'],
					title: '换型任务清单',
					isHidden: true
				},
				component: () => import('@/views/GradeChange/GradeChangeTaskList.vue')
			},
			{
				path: 'GradeChangeBase',
				//name: 'GradeChangeBase',
				meta: {
					roles: ['Admin', '换型数据维护_编辑'],
					title: '换型基础数据维护',
					icon: 'build_circle',
				},
				component: () => import('@/views/GradeChange/GradeChangeBase.vue')
			},
		]
	},
	{
		path: '/material',
		name: 'material_list',
		meta: {
			roles: ['Admin', '换型管理_查看'],
			title: '物料清单-1',
			icon: 'phone',
			isHidden: true,


		},
		component: () => import('@/views/material/material')
	},
	{
		path: '/CI3S',
		name: 'CI3S',
		meta: {
			roles: ['Admin','3S系统_审批','3S系统_编辑'],
			title: 'AM系统',
			icon: 'phone',
			isHidden: false,
		},
		component: () => import('@/views/CI_3S/CI_3S')
	},
	// {
	// 	path: '/LSW',
	// 	name: 'LSW',
	// 	meta: {
	// 		roles: ['Admin', 'LSW_编辑'],
	// 		title: 'LSW',
	// 		icon: 'local_library',

	// 	},
	// 	component: () => import('@/views/lsw/lsw')
	// },
	{
		path: '/LSW',
		name: 'LSW',
		meta: {
			roles: ['Admin', 'LSW_编辑'],
			title: 'LSW',
			icon: 'local_library',
			keepAlive: true
		},
		component: Layout,
		children: [
			{
				path: 'LSW',
				meta: {
					roles: ['Admin', 'LSW_编辑'],
					title: 'LSW',
					icon: 'local_library',
					keepAlive: true
				},
				component: Layout,
				component: () => import('@/views/lsw/lsw.vue')
			},
			{
				path: 'lswFeedBack',
				meta: {
					roles: ['Admin', 'LSW_编辑'],
					title: 'LSW反馈',
					icon: 'local_library',
					keepAlive: true
				},
				// component: Layout,
				component: () => import('@/views/lsw/lswFeedBack.vue')
			},
			{
				path: 'lswQuery',
				meta: {
					roles: ['Admin', 'LSW_编辑'],
					title: 'LSW查询',
					icon: 'local_library',
					keepAlive: true
				},
				// component: Layout,
				component: () => import('@/views/lsw/lswQuery.vue')
			},
		]

	},
	{
		path: '/RM_Return',
		name: 'RM_Return-1',
		meta: {
			roles: ['Admin', '退料标签打印_编辑'],
			title: '退料标签打印',
			icon: 'printer',
			isHidden: false,


		},
		component: () => import('@/views/RMReturn/RMReturn')
	},

	{
		path: '/dataBaisc',
		name: 'dataBaisc',
		meta: {
			roles: ['Admin','在线培训_管理员'],
			title: '数据维护',
			icon: 'build_circle',
			keepAlive: true
		},
		component: Layout,
		children: [
			{
				path: 'Employee_List',
				meta: {
					roles: ['Admin','在线培训_管理员'],
					title: '人员维护',
					icon: 'group',
					keepAlive: true
				},
				component: Layout,
				component: () => import('@/views/dataBasic/employee.vue')
			},
			{
				path: 'Access_List',
				meta: {
					roles: ['Admin','在线培训_管理员'],
					title: '权限维护',
					icon: 'fas fa-lock',
					keepAlive: true
				},
				component: Layout,
				component: () => import('@/views/dataBasic/accessList.vue')
			},
			{
				path: 'FinanceYearMonth',
				meta: {
					roles: ['Admin'],
					title: '财务月',
					icon: 'filter_1',
					keepAlive: true
				},
				component: Layout,
				component: () => import('@/views/dataBasic/FinanceYearMonth.vue')
			},
			// {
			// 	path: 'UserReg',
			// 	meta: {
			// 		roles: ['Admin'],
			// 		title: '用户注册',
			// 		icon: 'filter_1',
			// 		keepAlive: true
			// 	},
			// 	component: Layout,
			// 	component: () => import('@/views/userManagement/UserRegistration.vue')
			// },
			// {
			// 	path: 'UserAccess',
			// 	meta: {
			// 		roles: ['Admin'],
			// 		title: '权限界面',
			// 		icon: 'filter_1',
			// 		keepAlive: true
			// 	},
			// 	component: Layout,
			// 	component: () => import('@/views/userManagement/UserAccessManagement.vue')
			// },
			{
				path: 'UserManage',
				meta: {
					roles: ['Admin'],
					title: '用户管理',
					icon: 'filter_1',
					keepAlive: true
				},
				component: Layout,
				component: () => import('@/views/userManagement/UserManagement.vue')
			},
			// {
			// 	path: 'UserInfoManage',
			// 	meta: {
			// 		roles: ['Admin'],
			// 		title: '用户信息',
			// 		icon: 'filter_1',
			// 		keepAlive: true
			// 	},
			// 	component: Layout,
			// 	component: () => import('@/views/userManagement/UserInfoManagement.vue')
			// },
		]
	},
	{
		path: '/MES_Report',
		name: 'MES_Report',
		meta: {
			roles: ['MES_Report_查看', 'Admin'],
			title: '分析报告',
			icon: 'equalizer',
			keepAlive: true
		},
		component: Layout,
		children: [
			{
				path: 'Shift_Change',
				meta: {
					roles: ['MES_Report_查看', 'Admin'],
					title: 'MES交接班报告',
					icon: 'event',
					keepAlive: true
				},
				component: Layout,
				component: () => import('@/views/MES_Report/Shift_Change.vue')
			},
			{
				path: 'MES_Daily',
				meta: {
					roles: ['MES_Report_查看', 'Admin'],
					title: '日报',
					icon: 'event',
					keepAlive: true,
					isHidden: false
				},
				component: Layout,
				component: () => import('@/views/MES_Report/Daily_Report.vue')
			},
			{
				path: 'MES_MW',
				meta: {
					roles: ['MES_Report_查看', 'Admin'],
					title: '机器废品分析',
					icon: 'delete_sweep',
					keepAlive: true
				},
				component: Layout,
				component: () => import('@/views/MES_Report/Machine_Waste.vue')
			},
			{
				path: 'MES_CL',
				meta: {
					roles: ['MES_Report_查看', 'Admin'],
					title: 'C/L报告',
					icon: 'engineering',
					keepAlive: true,
					isHidden: false
				},
				component: Layout,
				component: () => import('@/views/MES_Report/Centerline.vue')
			},
			{
				path: 'NanjingSouthPerformance',
				meta: {
					roles: ['MES_Report_查看', 'Admin'],
					title: '南京南厂绩效汇总',
					icon: 'assessment',
					keepAlive: true,
					isHidden: false
				},
				component: () => import('@/views/PerformanceSummary/NanjingSouthPerformance.vue')
			}
		]
	},

	//-------------------------------------------------------------------Training
	// {
	// 	path: '/webTraining',
	// 	name: 'training',
	// 	meta: {
	// 		roles: ['Admin','training_查看','training_编辑'],
	// 		title: '培训页面',
	// 		icon: 'directions',
	// 	},
	// 	component: () => import('@/views/trainingWeb/trainingWeb1.vue')
	// },

	// {
	// 	path: '/webTraining',
	// 	name: 'webTraining',
	// 	meta: {
	// 		roles: ['Admin', 'training_查看', 'training_编辑'],
	// 		title: '培训页面',
	// 		icon: 'directions',
	// 		keepAlive: true
	// 	},
	// 	component: Layout,
	// 	children: [
	// 		{
	// 			path: 'webTraining1',
	// 			meta: {
	// 				roles: ['Admin', 'training_查看', 'training_编辑'],
	// 				title: '培训页面1',
	// 				icon: 'fas fa-hand-holding-usd',
	// 				keepAlive: true
	// 			},
	// 			component: () => import('@/views/trainingWeb/trainingWeb1.vue')
	// 		},
	// 		{
	// 			path: 'webTraining2',
	// 			meta: {
	// 				roles: ['Admin', 'training_查看', 'training_编辑'],
	// 				title: '培训页面2',
	// 				icon: 'fas fa-hand-holding-usd',
	// 				keepAlive: true
	// 			},
	// 			component: () => import('@/views/trainingWeb/trainingWeb2.vue')
	// 		},
	// 	]
	// },
	{
		path: '/WH_PO_List',
		name: 'WH_PO_List',
		meta: {
			roles: ['仓库订单_查看', 'Admin'],
			title: '仓库-订单列表',
			icon: 'equalizer',
			keepAlive: true
		},
		component: Layout,
		children: [
			{
				path: '48h_POList',
				meta: {
					roles: ['仓库订单_查看', 'Admin'],
					title: '48小时订单列表',
					icon: 'event',
					keepAlive: true
				},
				component: Layout,
				component: () => import('@/views/WH_PO_List/WH_PO_List.vue')
			},
			{
				path: 'GC_Material',
				meta: {
					roles: ['仓库订单_查看', 'Admin'],
					title: '换型材料差异',
					icon: 'event',
					keepAlive: true,
					isHidden: false
				},
				component: Layout,
				component: () => import('@/views/WH_PO_List/GC_Material.vue')
			}
		]
	},
	{
		path: '/AssetMeeting',
		name: '资产会议',
		meta: {
			roles: ['Admin','资产会议'],
			title: '资产会议跟踪',
			icon: 'phone',
			isHidden: false,
		},
		component: () => import('@/views/AssetMeetingTrack/assetmeeting')
	},
	 {
		path: '/hkCheck',
		name: 'CI3S',
		meta: {
			roles: ['Admin','hkCheck_查看','hkCheck_编辑'],
			title: '清洁检查',
			icon: 'phone',
			isHidden: false,
		},
		component: () => import('@/views/hkCheck/hkCheck')
	},

	//-------------------------------------------------------------------Training

	// {
	// 	path: '/Training',
	// 	name: 'Training',
	// 	meta: {
	// 		roles: ['Admin', '在线培训_查看', '在线培训_编辑'],
	// 		title: '在线培训',
	// 		icon: 'directions',
	// 		keepAlive: true
	// 	},
	// 	component: Layout,
	// 	children: [
	// 		{
	// 			path: 'webTraining',
	// 			meta: {
	// 				roles: ['Admin', '在线培训_查看', '在线培训_编辑'],
	// 				title: '在线培训',
	// 				icon: 'fas fa-hand-holding-usd',
	// 				keepAlive: true
	// 			},
	// 			component: () => import('@/views/Training/Training.vue')
	// 		},

	// 	]
	// },
	{
		path: '/Training',
		name: 'Training',
		meta: {
			roles: ['Admin', '在线培训_学员', '在线培训_老师','在线培训_管理员'],
			title: '在线培训',
			icon: 'directions',
			keepAlive: true
		},
		component: Layout,
		children: [
			{
				path: 'Training_List',
				meta: {
					roles: ['Admin', '在线培训_学员', '在线培训_老师','在线培训_管理员'],
					title: '在线培训',
					icon: 'fas fa-hand-holding-usd',
					keepAlive: true
				},
				component: () => import('@/views/Training/Training.vue')
			},
			{
				path: 'Training_Score',
				meta: {
					roles: ['Admin', '在线培训_学员', '在线培训_老师', '在线培训_管理员'],
					title: '培训分数管理',
					icon: 'fas fa-hand-holding-usd',
					keepAlive: true
				},
				component: () => import('@/views/Training/Training_Score.vue')
			},
			{
				path: 'Training_Feedback',
				meta: {
					roles: ['Admin', '在线培训_老师', '在线培训_管理员'],
					title: '学员评价',
					icon: 'fas fa-hand-holding-usd',
					keepAlive: true
				},
				component: () => import('@/views/Training/Training_Feedback.vue')
			},
		]
	},
	{
		path: '/HoldGoodsEntry',
		name: 'HoldGoods',
		meta: {
			roles: ['Admin', '成品待判定_查看', '成品待判定_编辑'],
			title: '成品待判定',
			icon: 'directions',
			keepAlive: true
		},
		component: Layout,
		children: [
			{
				path: 'HoldGoodsEntry',
				meta: {
					roles: ['Admin', '成品待判定_查看', '成品待判定_编辑'],
					title: '成品待判定',
					icon: 'fas fa-hand-holding-usd',
					keepAlive: true
				},
				component: () => import('@/views/HoldGoodsEntry/HoldGoodsEntry.vue')
			},

		]
	},
// ------------------------------------CI Project
{
	path: '/CI_Management',
	name: 'CI_Management',
	meta: {
		roles: ['Admin', 'CI_Management编辑','CI_Management查看'],
		title: 'CI工具管理',
		icon: 'printer',
		isHidden: false,


	},

	component: () => import('@/views/CI_Management/CI_Management.vue')
},
{
	path: '/Tech_Management',
	name: 'Tech_Management',
	meta: {
		roles: ['Admin', 'Tech_Management编辑','Tech_Management查看'],
		title: '技术开发',
		icon: 'printer',
		isHidden: false,


	},
	component: () => import('@/views/Tech_Management/Tech_Management.vue')
},
// {
// 	path: '/Tech_Management',
// 	name: 'Tech_Management',
// 	meta: {
// 		roles: ['Admin', 'Tech_Management编辑','Tech_Management查看'],
// 		title: '技术开发',
// 		icon: 'printer',
// 		isHidden: false,


// 	},
// 	component: () => import('@/views/Tech_Management/Tech_Management.vue')
// },

	// ----------------------End
	{
		path: '*', // 此处需置于最底部
		redirect: '/NoFound404',
		meta: {
			//roles: ['admin', 'test'],
			isHidden: true
		}
	}
]

const asyncRoutes = [{
	path: '/',
	name: 'index',
	redirect: '/',
	component: () => import('@/views/Index'),
	children: asyncRoutesChildren
}]

export default asyncRoutes

export {
	asyncRoutesChildren
}
