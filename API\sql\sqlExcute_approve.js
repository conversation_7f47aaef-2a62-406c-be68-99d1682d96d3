const _Base = require('../config/dbbase')

function sqlexec() {
  _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.checkAccount = function (account, password, callBack) {
  sqlexec.prototype.
    _query(` select  a.*,b.[Position],c.Access_Group from WebSite_login_List as a
    left join Employee_List as b on a.Employee_ID=b.Employee_ID
    left join Access_List as c on a.Employee_ID=c.Employee_ID
    where a.Employee_ID=${account} and Psw=${password}`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.modifyPSW = function (account, password, callBack) {
  sqlexec.prototype.
    _query('update dbo.WebSite_login_List set Psw=' + password + ' where Employee_ID=' + account, function (err, result) {
      if (err) {
      }
      return callBack('修改成功')
    })
}

sqlexec.prototype.getMonthlyAward = function (yearmonth,line,department, callBack) {
  //"exec 月度积分汇总_个人 "+awardID
  sqlexec.prototype.
    _query(`select * from Award_Application_Crew_Monthly where Fin_YearMonth='${yearmonth}' and Line='${line}' and department='${department}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.getMonthlyAwardSummary = function (awardID, callBack) {
  //"exec 月度summary "+ awardID
  // console.log("exec 月度summary_test "+ awardID+","+system)
  sqlexec.prototype.
    _query("exec 月度summary " + awardID, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.api_insertAwardSummary = function (name, awardID, callBack) {
  //"exec api_insertAwardSummary "+ name +","+ awardID
  console.log()
  sqlexec.prototype.
    _query("exec api_insertAwardSummary " + name + "," + awardID, function (err, result) {
      if (err) {

      }
      return callBack('添加成功')
    })
}

sqlexec.prototype.getApplyAwardSummary = function (system,callBack) {
  //"select * from 月度积分汇总 order by 年月 desc, 工厂 desc, 机台 asc"
  //  console.log("select * from 月度积分汇总_test where 系统="+ system +" order by 年月 desc, 工厂 desc, 机台 asc")
  sqlexec.prototype.
    _query("select * from [dbo].[Award_Application_Summary_Monthly] order by Fin_YearMonth desc,Line asc", function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}
sqlexec.prototype.getApproverName = function (level, department, line, callBack) {
  //"select 审批人姓名,审批人邮箱   from 审批流程 where 审批系统='"+ system +"' and 层级ID='"+ id +"' and 机台='"+ line +"' "
  console.log(`select Approver_Name,[Approver_ID]
  from [Award_Application_Summary_Monthly_ApprovalFlow_Base]
  where Department='${department}' and Approval_Level='${level}' and Line='${line}' `)
  sqlexec.prototype.
    _query(`select Approver_Name,[Approver_ID]
          from [Award_Application_Summary_Monthly_ApprovalFlow_Base]
          where Department='${department}' and Approval_Level='${level}' and Line='${line}' `, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.delAwardApply = function (yearMonth,Line,Department, callBack) {
  //"exec 删除积分汇总 "+awardID
  sqlexec.prototype.
    _query(`exec  dbo.SP_API_Award_Del_Application_Monthly '${yearMonth}','${Line}','${Department}'`, function (err, result) {
      if (err) {
      }
      return callBack("删除成功")
    })
}

sqlexec.prototype.insertApprovalRecord = function (department, level,awardID,employee_id,employee_name , callBack) {
  //"insert into 审批历史流程 values ('"+ system +"',"+ applyId +",'"+ mail +"','"+ name +"','审批中',null,getdate(),"+ level +")"
  // console.log("insert into 审批历史流程_test values ('"+ system +"',"+ applyId +",'"+ mail +"','"+ name +"','审批中',null,getdate(),"+ level +")")
  sqlexec.prototype.
    _query(`insert into [Award_Application_Summary_Monthly_ApprovalFlow]
    values ('${department}','${level}','${awardID}','${employee_id}','${employee_name}','审批中',null,null)`, function (err, result) {
      if (err) {
      }
      return callBack("添加成功")
    })
}


sqlexec.prototype.getApproveStatus = function (awardid,department, callBack) {
  console.log(`select *
  FROM [NJDatacenter].[dbo].[Award_Application_Summary_Monthly_ApprovalFlow]
where Bill_Code='${awardid}' AND Department='${department}'`)
  sqlexec.prototype.
    _query(`select *
    FROM [NJDatacenter].[dbo].[Award_Application_Summary_Monthly_ApprovalFlow]
  where Bill_Code='${awardid}' AND Department='${department}'
  `, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}


sqlexec.prototype.updateApproveStatus = function (id, approveStatus, callBack) {
  //"update [审批历史流程] set 审批状态="+ approveStatus +"  from 审批历史流程 where id="+ id +""
  console.log(`update [Award_Application_Summary_Monthly_ApprovalFlow] set Approval_Status='${approveStatus}',Approval_Date=getdate()  where id=${id}`)
  sqlexec.prototype.
    _query(`update [Award_Application_Summary_Monthly_ApprovalFlow] set Approval_Status='${approveStatus}',Approval_Date=getdate()  where id=${id}`, function (err, result) {
      if (err) {
      }
      return callBack("更新成功")
    })
}




sqlexec.prototype.getMyApproveList = function (userName, callBack) {
  console.log(`select *  FROM dbo.[Award_Application_Summary_Monthly_ApprovalFlow]
  where Approver_ID='${userName}' order by id desc`)
  sqlexec.prototype.
    _query(`select *  FROM dbo.[Award_Application_Summary_Monthly_ApprovalFlow]
      where Approver_ID='${userName}' order by id desc`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}



sqlexec.prototype.getLevelStatus = function (department, awardID, callBack) {
  //"exec [审批流程控制] '"+ system +"'," +applyID+",'"+ line +"'"
  console.log(`exec SP_API_Award_Monthly_Flow_Check '${department}','${awardID}'`)
  sqlexec.prototype.

    _query(`exec SP_API_Award_Monthly_Flow_Check '${department}','${awardID}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.updateAwardStatus = function (department,yearMonth,line, status, levelID, callBack) {
  //"update 月度积分汇总 set 审批状态="+ status +" , 审批进度ID='"+ levelID +"' where AwardID="+ AwardID +""
  sqlexec.prototype.
    _query(`update [dbo].[Award_Application_Summary_Monthly]
          set Approval_Status='${status}' , Approval_Level='${levelID}'
          where Fin_YearMonth='${yearMonth}' and Department='${department}' and Line='${line}' `, function (err, result) {
      if (err) {
      }
      return callBack("updated")
    })
}


sqlexec.prototype.DashBoardPlaying = function (callBack) {
  sqlexec.prototype.
    _query('select * from DashBoardPlaying order by squence asc', function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}


sqlexec.prototype.getCalendar = function (startDate, endDate, callBack) {
  sqlexec.prototype.
    _query("select * from 日历表 where  日期 between " + startDate + " and " + endDate + "  order by 日期 asc", function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.SP_API_Award_Performance_List = function (startDate, endDate, Line, fun, callBack) {
  //console.log('SP_API_Award_Performance_Daily_List',`exec SP_API_Award_Performance_Daily_List ${startDate},${endDate},${Line},${fun}`)
  sqlexec.prototype.
    _query(`exec SP_API_Award_Performance_Daily_List ${startDate},${endDate},${Line},${fun}`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.SP_API_Award_Performance_Monthly_List = function (startDate, endDate, fun, callBack) {
  //console.log('SP_API_Award_Performance_Daily_List',`exec SP_API_Award_Performance_Daily_List ${startDate},${endDate},${Line},${fun}`)
  sqlexec.prototype.
    _query(`exec SP_API_Award_Performance_Monthly_List ${startDate},${endDate},${fun}`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.SP_API_Award_Performance_by_People = function (ShiftLinkID, fun, callBack) {
  // console.log(`select b.Employee_PYName,result,Comment,a.Modified_By,a.ID from [Award_Performance_Crew_Daily] as a left join Employee_List as b on a.Employee_ID=b.Employee_ID
  // where shiftlinkid='${ShiftLinkID}' and Performance_Type='${fun}'`)
  sqlexec.prototype.
    _query(`select c.result ${fun}, c.Comment 备注,a.Employee_ID 员工ID,b.Employee_PYName 姓名 ,format(a.Date,'yyyy-MM-dd') 日期,a.Line 机台,a.shift 班次,c.Modified_By 修改人,c.ID from Mes_Crew_Data as a
    left join dbo.Employee_List as b on a.Employee_ID=b.Employee_ID
    left join dbo.[Award_Performance_Crew_Daily] as c on CONCAT_WS('-',a.Date,a.Line,a.Shift)=c.shiftlinkid and a.Employee_ID=c.Employee_ID and c.Performance_Type='${fun}'
     where CONCAT_WS('-',a.Date,a.Line,a.Shift)='${ShiftLinkID}' and a.Position not like 'Electrician%' `, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.getLine = function (Mill, callBack) {
  //console.log('SP_API_Award_Performance_List',`exec SP_API_Award_Performance_List ${startDate},${endDate},${Line},${fun}`)
  console.log(`select Line from Line_Desc where Mill='${Mill}'`)
  sqlexec.prototype.
    _query(`select Line from Line_Desc`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.API_Execute_Award_Performance_Daily = function (dataArray, callBack) {
  //console.log('SP_API_Award_Performance_List',`exec SP_API_Award_Performance_List ${startDate},${endDate},${Line},${fun}`)
  //console.log('sqlData',dataArray)
  var sqlStr = `exec [SP_API_Update_Single_Award_Performance_Daily] '${dataArray['ShiftLinkID']}','${dataArray['Date']}','${dataArray['Line']}','${dataArray['Shift']}','${dataArray['Performance_Type']}',
  '${dataArray['Result']}',${dataArray['comment'] == null ? 'null' : "'" + dataArray['comment'] + "'"},'${dataArray['Modified_By']}',${dataArray['id'] == null ? '0' : dataArray['id']},${dataArray['Employee_ID'] == null ? "''" : "'" + dataArray['Employee_ID'] + "'"}`
  console.log(sqlStr)
  sqlexec.prototype.
    _query(sqlStr, function (err, result) {
      if (err) {
      }
      return callBack('更新成功')
    })
}

sqlexec.prototype.SP_API_Update_Award_Performance = function (dataArray, callBack) {
  sqlexec.prototype.
    _query(`exec SP_API_Update_Multiple_Award_Performance_Daily
                '${dataArray['startDate']}',
                '${dataArray['endDate']}',
                '${dataArray['Line']}',
                '${dataArray['Performance_Type']}',
                '${dataArray['modifyName']}'`, function (err, result) {
      if (err) {
      }
      return callBack('更新成功')
    })
}

sqlexec.prototype.SP_API_Update_Award_Performance_Monthly = function (dataArray, callBack) {
  console.log(`exec SP_API_Update_Award_Performance_Monthly
  '${dataArray['finYearMonthly']}',
  '${dataArray['Line']}',
  '${dataArray['Performance_Type']}',
  '${dataArray['Result']}',
  '${dataArray['comment']}',
  '${dataArray['modifyName']}',
  '${dataArray['type']}'
  `)
  sqlexec.prototype.
    _query(`exec SP_API_Update_Award_Performance_Monthly
                '${dataArray['finYearMonthly']}',
                '${dataArray['Line']}',
                '${dataArray['Performance_Type']}',
                '${dataArray['Result']}',
                '${dataArray['comment']}',
                '${dataArray['modifyName']}',
                '${dataArray['type']}'
                `, function (err, result) {
      if (err) {
      }
      return callBack('更新成功')
    })
}

sqlexec.prototype.Award_Production_List = function (startDate, endDate, Line, callBack) {
  sqlexec.prototype.
    _query(`exec SP_API_Award_Application_Production_List  '${startDate}','${endDate}','${Line}' `, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}
sqlexec.prototype.Award_Application_Summary_Daily = function (startDate, endDate, Line, callBack) {
  sqlexec.prototype.
    _query(`select format(a.Date,'yyyy-MM-dd') Date,a.Line,a.Shift,a.Award_Type,sum(b.totalPoint)totalPoint,a.Approval_Status,max(a.Award_Param)Award_Param from dbo.Award_Application_Daily as a
    left join (select Award_LinkID,sum(Point)totalPoint  from Award_Application_Crew_Daily group by  Award_LinkID) as b on a.Award_LinkID=b.Award_LinkID
    where Line='${Line}' and Date between '${startDate}' AND '${endDate}'
    group by format(a.Date,'yyyy-MM-dd'),a.Line,a.Shift,a.Award_Type,a.Approval_Status
    order by Date desc,Shift desc`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}
sqlexec.prototype.Award_Application_Single_Daily = function (Date, Line, Shift, callBack) {
  sqlexec.prototype.
    _query(`exec SP_API_Award_Application_Single_Daily '${Date}','${Line}','${Shift}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.Award_Application_Single_AwardParam_Daily = function (Date, Line, Shift, callBack) {
  sqlexec.prototype.
    _query(`select Award_LinkID,Award_Type,Award_Param from award_application_Daily where CONCAT_WS('-',Date,Line,Shift) ='${Date}-${Line}-${Shift}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.Award_Application_Crew_Single_Daily = function (Date, Line, Shift, callBack) {
  sqlexec.prototype.
    _query(`select a.Position ,a.Employee_ID ,b.Employee_Name  from Mes_Crew_Data as a
    left join Employee_List as b on a.Employee_ID=b.Employee_ID
    where Date='${Date}' and a.Line='${Line}' and Shift='${Shift}' and b.Employee_ID is not null
    and a.Position not like 'Electrician%' order by b.Position desc`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}
sqlexec.prototype.Award_Application_CrewAward_Single_Daily = function (Date, Line, Shift, callBack) {
  sqlexec.prototype.
    _query(`select Employee_ID,Award_Type,sum(Point) Point from Award_Application_Crew_Daily where substring(Award_LinkID,1,18) ='${Date}-${Line}-${Shift}' group by  Employee_ID,Award_Type`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.Award_Update_Application_Approved = function (startDate, endDate, Line,modifyName, callBack) {
  sqlexec.prototype.
    _query(`update Award_Application_Daily
    set Approval_Status='Approved',Approval_Date=GETDATE(),Approver_Name='${modifyName}'
    from(
    select a.Award_LinkID,a.Date,a.Line,a.Shift,a.ID,b.安全,b.质量,b.生产 from Award_Application_Daily as a
    left join (
    select ShiftLinkID,
    max(case when Performance_Type='安全' then Result else null end) 安全,
    max(case when Performance_Type='质量' then Result else null end) 质量,
    max(case when Performance_Type='生产' then Result else null end) 生产
    from [dbo].[Award_Performance_Daily]
    where Line='${Line}' and Date between '${startDate}' AND '${endDate}'
    group by ShiftLinkID)b on CONCAT_WS('-',Date,Line,Shift)=b.ShiftLinkID
     where a.Line='${Line}' and a.Date between '${startDate}' AND '${endDate}'
     and b.生产='Yes' and b.安全='Yes' and b.质量='Yes' and a.Approval_Status is null)t
     where t.ID=Award_Application_Daily.ID`, function (err, result) {
      if (err) {
      }
      return callBack('更新成功')
    })
}
sqlexec.prototype.Award_Update_Application_CancelApproved_byShift = function (ShiftLinkid, callBack) {
   sqlexec.prototype.
    _query(`update Award_Application_Daily
    set Approval_Status=null,Approval_Date=null,Approver_Name=null
    where concat_ws('-',Date,Line,Shift)='${ShiftLinkid}'`, function (err, result) {
      if (err) {
      }
      return callBack('更新成功')
    })
}

sqlexec.prototype.SP_API_Award_Update_Application_Daily = function (dataArray, callBack) {
  sqlexec.prototype.
    _query(`exec dbo.SP_API_Award_Update_Application_Daily
    @award_Linkid='${dataArray['award_Linkid']}',
    @award_Type='${dataArray['award_Type']}',
    @award_Param='${dataArray['award_Param']}',
    @modifyName='${dataArray['modifyName']}',
    @award_base='${dataArray['award_base']}',
    @type='${dataArray['type']}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}



sqlexec.prototype.Award_Base = function (yearMonth, Line ,department,award_Type , callBack) {
  //console.log(`select '${award_Type}' Award_Type , Award,Position,Point from Award_Base where Award like '%${award_Type}%' and Category=(select Category from Line_Desc where Line='${Line}') ${department=='生产'?"and Position not like('%技师%')":"and Position like('%技师%') and Position<>'工艺技师'"} `)
  sqlexec.prototype.
    _query(`exec [SP_API_Award_base_Monthly_Single] '${yearMonth}','${Line}','${department}','${award_Type}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.Award_Base_dropdown = function (award_Type, Mill, callBack) {
  console.log(`select subAward_Type,DropDown_Value from Award_List_DropDown where Award_Type='${award_Type}' and Mill='${Mill}'`)
  sqlexec.prototype.
    _query(`select subAward_Type,DropDown_Value from Award_List_DropDown where Award_Type='${award_Type}' and Mill='${Mill}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}


sqlexec.prototype.Award_Base_QCO_Target_Manage = function (Line, Date, callBack) {
  //console.log(`select subAward_Type,DropDown_Value from Award_List_DropDown where Award_Type='${award_Type}' and Mill='${Mill}'`)
  sqlexec.prototype.
    _query(`  select distinct AwardType,Type,Time_Target,Waste_Target from [QCO_Target_Manage]
          where '${Date}' between effective_Date and expire_Date and Line='${Line}'
          order by AwardType asc,Type asc`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.Award_Clear_singleCrew_Award = function (award_shiftLinkid, Employee_ID, modifyName, callBack) {
  sqlexec.prototype.
    _query(`update Award_Application_Crew_Daily set Point=0 ,Modified_By='${modifyName}',Modified_On=getdate() where SUBSTRING(Award_LinkID,1,18)+'-'+Award_Type ='${award_shiftLinkid}' and Employee_ID='${Employee_ID}'`, function (err, result) {
      if (err) {
      }
      return callBack('更新成功')
    })
}

sqlexec.prototype.Award_List = function (Award_Cycle, Position,applyFunction, callBack) {
  sqlexec.prototype.
    _query(`select * from dbo.Award_List where Award_Cycle='${Award_Cycle}'  and Position='${Position}' ${applyFunction=='Daily'?"and Award_Type not in('点子','BP')":""} `, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}
sqlexec.prototype.Award_List_All = function (callBack) {
  sqlexec.prototype.
    _query(`select  Award_Type,Award_Cycle from [dbo].Award_List order by Award_Cycle desc`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}



sqlexec.prototype.getMonthlyCrewList = function (yearMonth,Line,department ,callBack) {
  sqlexec.prototype.
    _query(`exec dbo.SP_API_Award_Monthly_Crew_List '${yearMonth}','${Line}','${department}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.getMonthlyAwardList = function (yearMonth,Line,department, callBack) {

  sqlexec.prototype.
    _query(`exec dbo.SP_API_Award_base_Monthly '${yearMonth}','${Line}','${department}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.getMonthlyApplicationAward= function (Line,yearMonth,department, callBack) {
  sqlexec.prototype.
    _query(`select  * from  dbo.Award_Application_Crew_Monthly where Line='${Line}' and Fin_YearMonth='${yearMonth}' and department='${department}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.SP_API_Award_Update_Application_Monthly = function (yearMonth,line,award_type,Crew_account,Crew_name,Crew_Position,award_base,modifyName,operate,comment,department, callBack) {
  // 添加调试信息
  console.log('SP_API_Award_Update_Application_Monthly function called with:')
  console.log('yearMonth:', yearMonth)
  console.log('line:', line)
  console.log('award_type:', award_type)
  console.log('Crew_account:', Crew_account)
  console.log('Crew_name:', Crew_name)
  console.log('Crew_Position:', Crew_Position)
  console.log('award_base:', award_base)
  console.log('modifyName:', modifyName)
  console.log('operate:', operate)
  console.log('comment:', comment)
  console.log('department:', department)

  console.log(`exec SP_API_Award_Update_Application_Monthly
  '${yearMonth}',
  '${line}',
  '${award_type}',
  '${Crew_account}',
  '${Crew_name}',
  '${Crew_Position}',
  '${award_base}',
  '${modifyName}',
  '${comment}',
  '${operate}',
  '${department}'`)

  const sqlQuery = `exec SP_API_Award_Update_Application_Monthly
    '${yearMonth}',
    '${line}',
    '${award_type}',
    '${Crew_account}',
    '${Crew_name}',
    '${Crew_Position}',
    '${award_base}',
    '${modifyName}',
    '${comment}',
    '${operate}',
    '${department}'`

  console.log('Executing SQL query:', sqlQuery)

  sqlexec.prototype.
    _query(sqlQuery, function (err, result) {
      if (err) {
        console.error('SQL error:', err)
        return callBack('更新失败')
      }
      console.log('SQL result:', result)
      return callBack('更新成功')
    })
}

sqlexec.prototype.delete_Application_Monthly = function (yearMonth,line,award_type,Crew_account,operate, callBack) {
  sqlexec.prototype.
    _query(`delete from dbo.Award_Application_Crew_Monthly
        where Fin_YearMonth='${yearMonth}' and Line='${line}' and Employee_ID='${Crew_account}'
        ${operate=='清空积分'?` and award_Type='${award_type}'`:` `}
        `, function (err, result) {
      if (err) {
      }
      return callBack('更新成功')
    })
}

sqlexec.prototype.other_crew_list = function (line,Business_Title, callBack) {
  console.log(`select Employee_ID,Employee_PYName,Position from dbo.Employee_List where Line='${line}' and Business_Title like '%${Business_Title}%' order by Line asc,Employee_PYName asc`)
  sqlexec.prototype.
    _query(`select Employee_ID,Employee_PYName,Position from dbo.Employee_List where Line='${line}' and Business_Title like '%${Business_Title}%' order by Position asc,Employee_PYName asc`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.SP_API_Award_Count = function (line,yearMonth,awardType,department, callBack) {
  console.log()
  sqlexec.prototype.
    _query(`exec dbo.SP_API_Award_Count '${awardType}','${yearMonth}','${line}','${department}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}
sqlexec.prototype.SP_API_Award_Count_byDetail = function (line,yearMonth,department,awardType, callBack) {
  console.log()
  sqlexec.prototype.
    _query(`exec dbo.SP_API_Award_Count_byDetail '${yearMonth}','${line}','${department}','${awardType}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}
sqlexec.prototype.SP_API_Award_SummaryAward_ByCrew = function (line,yearMonth,employee_id,Employee_PYName,position,modifyName,department, callBack) {
  console.log(`exec dbo.SP_API_Award_SummaryAward_ByCrew '${yearMonth}','${line}','${employee_id}','${Employee_PYName}','${position}','${modifyName}','${department}'`)
  sqlexec.prototype.
    _query(`exec dbo.SP_API_Award_SummaryAward_ByCrew '${yearMonth}','${line}','${employee_id}','${Employee_PYName}','${position}','${modifyName}','${department}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}
sqlexec.prototype.Award_Application_Summary_Monthly = function (line,yearMonth,department,awardType,awardPoints,modifyName, callBack) {
  sqlexec.prototype.
    _query(`	insert into Award_Application_Summary_Monthly values(CONCAT_WS('-','${yearMonth}','${line}','${department}','${awardType}'),'${yearMonth}','${department}','${line}','${awardType}','${awardPoints}','${modifyName}',GETDATE(),1,'progress')`, function (err, result) {
      if (err) {
      }
      return callBack('更新成功')
    })
}

sqlexec.prototype.Award_Application_Summary_Monthly_verify = function (line,yearMonth,department, callBack) {
  sqlexec.prototype.
    _query(`select top 1 Approval_Status from Award_Application_Summary_Monthly where Fin_YearMonth='${yearMonth}' and Line='${line}' and Department='${department}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.Award_Performance_Summary_Monthly = function (line,yearMonth, callBack) {
  sqlexec.prototype.
    _query(`select Performance_Type,Result,try_convert(decimal(5,2), dbo.Cal_开机率('${yearMonth}','${line}')*100) 开机率 from Award_Performance_Monthly where Fin_YearMonth='${yearMonth}' and Line='${line}'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.Award_Application_Summary_byApproverMail = function (line,yearMonth,department, callBack) {
  // console.log(`select Fin_YearMonth,Department,Line,Award_Type,Points,Create_By,Create_Date
  // from dbo.Award_Application_Summary_Monthly
  // where Fin_YearMonth='${yearMonth}' and Department='${department}' and Line='${line}' and Points>0`)
  sqlexec.prototype.
    _query(`select Fin_YearMonth,Department,Line,Award_Type,Points,Create_By,Create_Date
    from dbo.Award_Application_Summary_Monthly
    where Fin_YearMonth='${yearMonth}' and Department='${department}' and Line='${line}' and Points>0`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.SP_API_Award_Count_Summary = function (department,yearMonth,line, callBack) {
  // console.log(`exec SP_API_Award_Count_Summary '${department}','${yearMonth}','${line}'`)
  sqlexec.prototype.
    _query(`exec SP_API_Award_Count_Summary '${department}','${yearMonth}','${line}'`, function (err, result) {
      if (err) {
      }
      // console.log('result',result)
      return callBack(result.recordset)
    })
}

sqlexec.prototype.awardQuery_byPersonal = function (IDCode, yearMonth, callBack) {
  sqlexec.prototype.
    _query(`SELECT Fin_YearMonth,a.Employee_Name,Award_Type,sum(Point)Points
  FROM [Award_Application_Crew_Monthly] as a
  left join Employee_List as b on a.Employee_ID=b.Employee_ID
  where b.ID_Code='${IDCode}' and Fin_YearMonth=${yearMonth}
  GROUP BY Fin_YearMonth,a.Employee_Name,Award_Type`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}






module.exports = sqlexec;