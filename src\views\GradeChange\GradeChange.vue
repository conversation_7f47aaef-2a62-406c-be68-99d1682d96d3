<template>
	<base-content>

		<div class="q-pa-md">
			<div class="q-gutter-md">
				<q-select filled v-model="line" :options="optionsData.line" label="机台" stack-label />
				<q-select filled v-model="group" :options="optionsData.group" label="班组" stack-label />
				<q-select filled v-model="product1" :options="optionsData.product" label="换型前产品" stack-label />
				<q-select filled v-model="product2" :options="optionsData.product" label="换型后产品" stack-label />
				<q-btn color="primary" label="获取换型任务清单" style="width: 200px; font-weight: 600;font-size: 15px;"
					@click="gotoTaskList" />
				<q-btn color="secondary" label="查询已完成任务记录" style="width: 200px; font-weight: 600;font-size: 15px;" />
			</div>
			<!-- 			
			<DIV>
				    <q-select outlined v-model="model" :options="options" label="Outlined" />
			</DIV>
			<div>
				选择的项目是:{{model}}
			</div> -->
		</div>




	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import qs from 'qs'
export default {
	components: {
		BaseContent
	},
	data() {
		return {
			options: [
				'A', 'B', 'C', 'D', 'E'
			],
			model: '',
			optionsData: { line: [], product: [], group: ['A', 'B', 'C', 'D', 'E', 'F'] },
			line: '',
			group: '',
			product1: '',
			product2: ''
		}
	},
	mounted() {
		this.getLineList()

	},
	watch: {
		line: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.getProductNameList(newVal)
		}
	},

	methods: {
		gotoTaskList() {
			var _this = this
			if (_this.line !== "" && _this.group !== "" && _this.product1 !== "" && _this.product2 !== "") {
				if (_this.product1 == _this.product2) {
					_this.alert1()
				} else {
					var data = 'line=' + _this.line + '&group=' + _this.group + '&product1=' + _this.product1 + '&product2=' + _this.product2
					_this.$router.push('/GradeChange/GradeChangeTaskList?' + data)
				}
			} else {
				_this.alert()
			}
		},
		async getLineList() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('gradeChange/lineList')
			_this.optionsData.line = res
		},
		async getProductNameList(line) {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('gradeChange/productNameList?line=' + _this.line)
			_this.optionsData.product = res
			console.log ('Productnamelist',res)
		},



		alert() { this.$q.dialog({ title: '错误！', message: '请填写完整所需要的信息！' }) },
		alert1() { this.$q.dialog({ title: '获取失败', message: '请确保换型前产品和换型后产品的不一致' }) },

	}
}
</script>

<style scoped></style>
