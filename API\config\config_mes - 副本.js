﻿const config = {
  server: 'CNNBAS70', // You can use 'localhost\instance' to connect to named instance
  database: 'UMDB',
  options: {
    trustedConnection: true, // 使用Windows身份验证
    encrypt: false,  // 禁用加密
    trustServerCertificate: true,  // 信任自签名证书
    enableArithAbort: true
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000
  },
  // options: {
  //   encrypt: false // Use this if you're on Windows Azure
  // },
  debug: false
}
//执行sql,返回数据.

module.exports=config
