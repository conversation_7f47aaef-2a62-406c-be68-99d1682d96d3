const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);

}

sqlexec.prototype = new _Base()




// sqlexec.prototype.insert_quality_inspection = function (Crew, line, Technician, JZJJY, HJTJL, LUYJL, LUGF, Pass, LYG, HCCP, DPD, Teams, PF, Comments, callBack) {
//     console.log(`insert into Quality_Shift_Inspect (Date,Crew,Line,检验员,金重检校验,黑接头记录,留样与记录一致性,留样规范,PASS记录,
//         留样柜准确性,缓存产品是否落地,待判定处理,Teams记录,排废验证记录,其它)
//         values( GETDATE(), '${Crew}','${line}' , '${Technician}','${JZJJY}','${HJTJL}','${LUYJL}',
//         '${LUGF}','${Pass}','${LYG}','${HCCP}','${DPD}','${Teams}',
//         '${PF}','${Comments}') `)
//     sqlexec.prototype.
//         _query(`insert into Quality_Shift_Inspect (Date,Crew,Line,检验员,金重检校验,黑接头记录,留样与记录一致性,留样规范,PASS记录,
//             留样柜准确性,缓存产品是否落地,待判定处理,Teams记录,排废验证记录,其它)
//             values( GETDATE(), '${Crew}','${line}' , '${Technician}','${JZJJY}','${HJTJL}','${LUYJL}',
//             '${LUGF}','${Pass}','${LYG}','${HCCP}','${DPD}','${Teams}',
//             '${PF}','${Comments}') `, function (err, result) {
//             if (err) {
//             }
//             return callBack('insert successfully')
//         })
// }

// sqlexec.prototype.query_shift_change = function (line, startdate, enddate, callBack) {
//     console.log(`select id, Line, format(Date,'yyyy-MM-dd')Date, Submitter, Issue_Category, Display_Days,Issue_Desc, ModifyDate
//     from Shift_Change_Issue_Log where line='${line}' and  date>= '${startdate}' and date<= '${enddate}' `)
//     sqlexec.prototype.
//         _query(`select id, Line, format(Date,'yyyy-MM-dd')Date, Submitter, Issue_Category,Display_Days, Issue_Desc, ModifyDate
//         from Shift_Change_Issue_Log where line='${line}' and  date>= '${startdate}' and date<= '${enddate}' `, function (err, result) {
//             if (err) {
//             }
//             return callBack(result.recordset)
//         })
// }
// sqlexec.prototype.update_shift_change = function (Date, Category, Desc, Display_Days, id, callBack) {
//     console.log(`update Shift_Change_Issue_Log set Date='${Date}',Issue_Category= '${Category}',Issue_Desc='${Desc}',Display_Days='${Display_Days}' where id=${id}`)
//     sqlexec.prototype.
//         _query(`update Shift_Change_Issue_Log set Date='${Date}',Issue_Category= '${Category}',Issue_Desc='${Desc}' ,Display_Days='${Display_Days}',ModifyDate=getdate() where id=${id}`, function (err, result) {
//             if (err) {
//             }

//             return callBack('更新成功')
//         })
// }
// sqlexec.prototype.delete_shift_change = function (id, callBack) {
//     console.log(`delete from Shift_Change_Issue_Log where id=${id}`)
//     sqlexec.prototype.
//         _query(`delete from Shift_Change_Issue_Log where id=${id}`, function (err, result) {
//             if (err) {
//             }
//             return callBack('删除成功')
//         })
// }


// sqlexec.prototype.insert_shfit_change = function (line, Submitter, Issue_Category, Issue_Desc, Display_Days, callBack) {
//     console.log(`insert into Shift_Change_Issue_Log( Line, Date, Submitter, Issue_Category, Issue_Desc, ModifyDate,Display_Days
//         ) values('${line}',getdate(),'${Submitter}','${Issue_Category}','${Issue_Desc}',getdate(),'${Display_Days}'`)
//     sqlexec.prototype.
//         _query(`insert into Shift_Change_Issue_Log( Line, Date, Submitter, Issue_Category, Issue_Desc, ModifyDate,Display_Days
//             ) values('${line}',getdate(),'${Submitter}','${Issue_Category}','${Issue_Desc}',getdate(),'${Display_Days}')`, function (err, result) {
//             if (err) {
//             }
//             return callBack('添加成功')
//         })
// }


sqlexec.prototype.insert_asset_meeting = function (line, Submitter, Submitter_Dept, Problem_category, Deterioration_Type, Problem_Desc, Problem_Rootcuase, Problem_Solution, Problem_Prevention, Owner_Dept, Owner, Priority,
    Completed_Date, Status, callBack) {
    console.log(`insert into Asset_Meeting_Tracking( Date, Line, Submitter, Submitter_Dept, Problem_category, Deterioration_Type, Problem_Desc, Problem_Rootcuase, Problem_Solution, Problem_Prevention, Owner_Dept, Owner, Priority,
        Completed_Date, Status
        ) values(getdate(),'${line}','${Submitter}','${Submitter_Dept}','${Problem_category}','${Deterioration_Type}','${Problem_Desc}','${Problem_Rootcuase}','${Problem_Solution}','${Problem_Prevention}','${Owner_Dept}','${Owner}','${Priority}','${Completed_Date}','${Status}')`)
    sqlexec.prototype.
        _query(`insert into Asset_Meeting_Tracking( Date, Line, Submitter, Submitter_Dept, Problem_category, Deterioration_Type, Problem_Desc, Problem_Rootcuase, Problem_Solution, Problem_Prevention, Owner_Dept, Owner, Priority,
            Completed_Date, Status
            ) values(getdate(),'${line}','${Submitter}','${Submitter_Dept}','${Problem_category}','${Deterioration_Type}','${Problem_Desc}','${Problem_Rootcuase}','${Problem_Solution}','${Problem_Prevention}','${Owner_Dept}','${Owner}','${Priority}','${Completed_Date}','${Status}')`, function (err, result) {
            if (err) {
            }
            return callBack('添加成功')
        })
}

sqlexec.prototype.query_asset_meeting = function (line, startdate, enddate, Status, callBack) {
    console.log(`select * from Asset_Meeting_Tracking where line='${line}' and  date>= '${startdate}' and date<= '${enddate}'and Status= '${Status}' `)
    sqlexec.prototype.
        _query(`select  format(Date,'yyyy-MM-dd')Date, Line, Submitter, Submitter_Dept, Problem_category, Deterioration_Type, Problem_Desc, Problem_Rootcuase, Problem_Solution, Problem_Prevention, Owner_Dept, Owner, Priority,
        format(Completed_Date,'yyyy-MM-dd')Completed_Date, Status, id
from Asset_Meeting_Tracking where line='${line}' and  date>= '${startdate}' and date<= '${enddate}'and Status= '${Status}' `, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.update_asset_meeting = function (Problem_Desc, Problem_Rootcuase, Problem_Solution, Problem_Prevention, Owner_Dept, Owner, Priority, Completed_Date, Status, id, Deterioration_Type, callBack) {
    console.log(`update Asset_Meeting_Tracking set Problem_Desc='${Problem_Desc}',Problem_Rootcuase= '${Problem_Rootcuase}',Problem_Solution='${Problem_Solution}',Problem_Prevention='${Problem_Prevention}' ,Owner_Dept='${Owner_Dept}' ,Owner='${Owner}',Priority='${Priority}', Completed_Date='${Completed_Date}',Status='${Status}',Deterioration_Type='${Deterioration_Type}' where id=${id}`)
    sqlexec.prototype.
        _query(`update Asset_Meeting_Tracking set Problem_Desc='${Problem_Desc}',Problem_Rootcuase= '${Problem_Rootcuase}',Problem_Solution='${Problem_Solution}',Problem_Prevention='${Problem_Prevention}' ,Owner_Dept='${Owner_Dept}' ,Owner='${Owner}',Priority='${Priority}', Completed_Date='${Completed_Date}',Status='${Status}',Deterioration_Type='${Deterioration_Type}' where id=${id}`, function (err, result) {
            if (err) {
            }

            return callBack('更新成功')
        })
}

sqlexec.prototype.delete_asset_meeting = function (id, callBack) {
    console.log(`delete from Asset_Meeting_Tracking where id=${id}`)
    sqlexec.prototype.
        _query(`delete from Asset_Meeting_Tracking where id=${id}`, function (err, result) {
            if (err) {
            }
            return callBack('删除成功')
        })
}

sqlexec.prototype.email_asset_meeting = function (startdate, enddate, line, callBack) {
    console.log(`exec [SP_Asset_Meeting_AssetSummary_Email] '${startdate}','${enddate}','${line}'`)
    sqlexec.prototype.
        _query(`exec [SP_Asset_Meeting_AssetSummary_Email] '${startdate}','${enddate}','${line}'`, function (err, result) {
            if (err) {
            }

            return callBack(result.recordset)
        })
}

sqlexec.prototype.getDepartment = function (callBack) {
    sqlexec.prototype.
        _query("select distinct position from dbo.Employee_List  where position in ('资产主管','电气工程师','机械工程师','工艺工程师','质量工程师','原材料质量工程师','工艺技师','EHS工程师','电气技师','维修技师','胶技师','维修技师+胶技师','模具维修技师','设施主管','数字化制造主管') ", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.getEmployee = function (callBack) {
    sqlexec.prototype.
        _query(`select Position,Employee_ID,Employee_Name from dbo.Employee_List  order by Employee_Name asc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}


sqlexec.prototype.insert_Mes_Crew_Data = function (CQDate, Line, Shift, Crew, Position, Full_Name, Employee_ID, callBack) {
    console.log(`insert into Mes_Crew_Data(Date, Line, Shift, Crew, Position, Full_Name, Employee_ID
        ) values('${CQDate}','${Line}','${Shift}','${Crew}','${Position}','${Full_Name}','${Employee_ID}')`)
    sqlexec.prototype.
        _query(`insert into Mes_Crew_Data(Date, Line, Shift, Crew, Position, Full_Name, Employee_ID
            ) values('${CQDate}','${Line}','${Shift}','${Crew}','${Position}','${Full_Name}','${Employee_ID}')`, function (err, result) {
            if (err) {
            }
            return callBack('添加成功')
        })
}

sqlexec.prototype.getEmployeename = function (callBack) {
    sqlexec.prototype.
        _query(`SELECT line,Employee_name,Employee_id FROM dbo.Employee_List where    Line IN ('TD01', 'TD02', 'TD03', 'TD31', 'TD32', 'TD33', 'TD34')`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

// sqlexec.prototype.query_Mes_Crew_Data = function (Line, date, Shift, callBack) {
//     console.log(`SELECT    format(date,'yyyy-MM-dd')date, Line, Shift, Crew, Position, Full_Name, Employee_ID FROM     dbo.Mes_Crew_Data
//     where Line='${Line}' and  date= '${date}' and Shift= '${Shift}' `)
//     sqlexec.prototype.
//         _query(`SELECT    format(date,'yyyy-MM-dd')date, Line, Shift, Crew, Position, Full_Name, Employee_ID FROM     dbo.Mes_Crew_Data
//         where Line='${Line}' and  date= '${date}' and Shift= '${Shift}' `, function (err, result) {
//             if (err) {
//             }
//             return callBack(result.recordset)
//         })
// }

// sqlexec.prototype.update_Mes_Crew_Data = function (date, Line, Shift, Position, Full_Name, Employee_ID, Crew, callBack) {
//     console.log(`update Mes_Crew_Data set Full_Name='${Full_Name}',Employee_ID= '${Employee_ID}'where date='${date}' and Line='${Line}' and Shift='${Shift}' and Position='${Position}'and Crew='${Crew}'`)
//     sqlexec.prototype.
//         _query(`update Mes_Crew_Data set Full_Name='${Full_Name}',Employee_ID= '${Employee_ID}'where date='${date}' and Line='${Line}' and Shift='${Shift}' and Position='${Position}' and Crew='${Crew}'`, function (err, result) {
//             if (err) {
//             }

//             return callBack('更新成功')
//         })
// }

// sqlexec.prototype.insert_EHS_Shift_Turnover = function ( shift, Asset, Shift_Leader, CQQK, JSZT, PPE, SafetyIssue_Previousshift, EHSHighlight_Currentshift, EHS_Status, Communication,
//     ProcetiveCover_Status, CS_Status, Chemicals_Status, TravellingCrane_Status, PMDL_Status, FLBY_Status, Ladder_Status, Comments, callBack) {
//     console.log(`insert into EHS_Shift_Turnover( DATE, shift, Asset, Shift_Leader, CQQK, JSZT, PPE, SafetyIssue_Previousshift, EHSHighlight_Currentshift, EHS_Status, Communication,
//         ProcetiveCover_Status, CS_Status, Chemicals_Status, TravellingCrane_Status, PMDL_Status, FLBY_Status, Ladder_Status, Comments
//         ) values(getdate(),'${shift}','${Asset}','${Shift_Leader}','${CQQK}','${JSZT}','${PPE}','${SafetyIssue_Previousshift}','${EHSHighlight_Currentshift}',
//         '${EHS_Status}','${Communication}','${ProcetiveCover_Status}','${CS_Status}','${Chemicals_Status}','${TravellingCrane_Status}','${PMDL_Status}','${FLBY_Status}',
//         '${Ladder_Status}','${Comments}')`)
//     sqlexec.prototype.
//         _query(`insert into EHS_Shift_Turnover( DATE, shift, Asset, Shift_Leader, CQQK, JSZT, PPE, SafetyIssue_Previousshift, EHSHighlight_Currentshift, EHS_Status, Communication,
//             ProcetiveCover_Status, CS_Status, Chemicals_Status, TravellingCrane_Status, PMDL_Status, FLBY_Status, Ladder_Status, Comments
//             ) values(getdate(),'${shift}','${Asset}','${Shift_Leader}','${CQQK}','${JSZT}','${PPE}','${SafetyIssue_Previousshift}','${EHSHighlight_Currentshift}',
//             '${EHS_Status}','${Communication}','${ProcetiveCover_Status}','${CS_Status}','${Chemicals_Status}','${TravellingCrane_Status}','${PMDL_Status}','${FLBY_Status}',
//             '${Ladder_Status}','${Comments}')`, function (err, result) {
//             if (err) {
//             }
//             return callBack('添加成功')
//         })
// }
// sqlexec.prototype.query_EHS_Shift_Turnover = function (Asset, startdate, enddate,  callBack) {
//     console.log(`select format(DATE,'yyyy-MM-dd')DATE, shift, Asset, Shift_Leader, CQQK, JSZT, PPE, SafetyIssue_Previousshift, EHSHighlight_Currentshift, EHS_Status, Communication,
//     ProcetiveCover_Status, CS_Status, Chemicals_Status, TravellingCrane_Status, PMDL_Status, FLBY_Status, Ladder_Status, Comments, ID
// FROM  EHS_Shift_TurnoverFROM  EHS_Shift_Turnover where Asset='${Asset}' and  date>= '${startdate}' and date<= '${enddate}' `)
//     sqlexec.prototype.
//         _query(`select format(DATE,'yyyy-MM-dd')DATE, shift, Asset, Shift_Leader, CQQK, JSZT, PPE, SafetyIssue_Previousshift, EHSHighlight_Currentshift, EHS_Status, Communication,
//         ProcetiveCover_Status, CS_Status, Chemicals_Status, TravellingCrane_Status, PMDL_Status, FLBY_Status, Ladder_Status, Comments, ID
// FROM  EHS_Shift_Turnover where Asset='${Asset}' and  date>= '${startdate}' and date<= '${enddate}' `, function (err, result) {
//             if (err) {
//             }
//             return callBack(result.recordset)
//         })
// }

module.exports = sqlexec;

