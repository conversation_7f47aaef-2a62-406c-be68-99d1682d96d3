var express = require('express')
var router = express.Router()
var sqlexec_grade = require('../sql/sqlIdeabank')
var moment = require('moment')
const web_url = require('../config/webURL');
const { resolve } = require('q')
const request = require('request')
const EHS_List = require('../config/EHSAccount');

router.get("/getDepartment", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.getDepartment(function (result) {
        let returnDepartment = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                returnDepartment.push(result[i].line)
            }
            res.send(returnDepartment)
        } else {
            res.send("获取失败")
        }
    })
})
router.get("/getIdeaType", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.getIdeaType(function (result) {
        let returnIdea_Type = []
        let returnIdea_subType = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                if (result[i]['Type'] == '点子类型') {
                    returnIdea_Type.push(result[i].Idea_Type)
                }
                if (result[i]['Type'] == '点子子类型') {
                    returnIdea_subType.push(result[i].Idea_Type)
                }
            }
            res.send({ 'Idea_Type': returnIdea_Type, 'Idea_subType': returnIdea_subType })
        } else {
            res.send("获取失败")
        }
    })
})

router.get("/getSaftyType", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.getSaftyType(function (result) {
        let returnSafty_Type = []
        let returnSafty_DailyType = []
        if (result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                if (result[i]['Type'] == '隐患类别') {
                    returnSafty_Type.push(result[i].Incident_Type)
                }
                if (result[i]['Type'] == '日常检查类型') {
                    returnSafty_DailyType.push(result[i].Incident_Type)
                }
            }
            res.send({ 'Safty_Type': returnSafty_Type, 'Safty_DailyType': returnSafty_DailyType })
        } else {
            res.send("获取失败")
        }
    })
})
router.get("/getDepartment_Owner_List", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.getDepartment_Owner_List(function (result) {
        res.send(result)
    })
})

router.get("/getBPType", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.getBPType(function (result) {
        let newArray = result.map(item => item.Best_Practice_Type)
        res.send(newArray)
    })
})


router.get("/getEmployee", (req, res) => {
    console.log(req.query)
    var returnQuery = new sqlexec_grade()
    returnQuery.getEmployee(function (result) {
        res.send(result)
    })
})

router.get("/queryIdea_ByDepartment", (req, res) => {
    console.log(req.query)
    var returnQuery = new sqlexec_grade()
    returnQuery.queryIdea_ByDepartment(req.query.department, function (result) {
        res.send(result)
    })
})
router.get("/queryIdea", (req, res) => {
    console.log(req.query)
    var returnQuery = new sqlexec_grade()
    returnQuery.queryIdea(function (result) {
        res.send(result)
    })
})
router.get("/queryBP", (req, res) => {
    console.log(req.query)
    var returnQuery = new sqlexec_grade()
    returnQuery.queryBP(function (result) {
        res.send(result)
    })
})
router.get("/queryBP_ByDepartment", (req, res) => {
    console.log(req.query)
    var returnQuery = new sqlexec_grade()
    returnQuery.queryBP_ByDepartment(req.query.department, function (result) {
        res.send(result)
    })
})
router.get("/querySafty", (req, res) => {
    console.log(req.query)
    var returnQuery = new sqlexec_grade()
    returnQuery.querySafty(req.query.startDate,req.query.endDate, function (result) {
        res.send(result)
    })
})

router.get("/myApprovalIdea", (req, res) => {
    console.log(req.query)
    var returnQuery = new sqlexec_grade()
    returnQuery.myApprovalIdea(req.query.employeeID, req.query.processType, function (result) {
        res.send(result)
    })
})

router.get("/myApprovalIdea_BP", (req, res) => {
    console.log(req.query)
    var returnQuery = new sqlexec_grade()
    returnQuery.myApprovalIdea_BP(req.query.employeeID, function (result) {
        res.send(result)
    })
})
router.get("/myApprovalSafty", (req, res) => {
    console.log(req.query)
    var returnQuery = new sqlexec_grade()
    returnQuery.myApprovalSafty(req.query.employeeID, req.query.processType, function (result) {
        res.send(result)
    })
})

router.post("/insertIdea", (req, res) => {
    console.log(req.body)
    const data = req.body.data
    const Manager_ID = req.body.Manager_ID
    var returnQuery = new sqlexec_grade()
    returnQuery.insertIdea(data, async function (result) {
        if (result == '已添加') {
            const htmlBody = `<h2> <span style=colorred>${data.department}</span>的<span style=color:red>${data.employee_Name}</span>提交的点子</h2>
            <h4>提交时间：${moment().format('YYYY-MM-DD HH:mm:ss')}</h4>
            <h4>点子类型：${data.ideaType}</h4>
            <h4>问题描述：${data.ideaProblem}</h4>
            <h4>建议方案：${data.ideaSolution}</h4>
            <h4>是否自主完成：${data.selfComplete}</h4>
            <h4>建议分配人：${data.assignedBy.label}</h4>
            <a href="${web_url.webSite + '/#/ideaBank/ideaBankApplicate?mail=true&function=myApproval&department=' + data.department}" style="text-decoration:underline;font-weight: 600">点击此处登录网页进行审批</a>`
            const repsonse = await sendMailtoApprover(Manager_ID + '@kcc.com', data.employee_Name + '提交了一个点子,请查看', htmlBody, '点子银行通知系统')
            res.send(repsonse)
        }
    })
})
router.post("/insertBP", (req, res) => {
    console.log(req.body)
    const data = req.body.data
    const Manager_ID = req.body.Manager_ID
    var returnQuery = new sqlexec_grade()
    returnQuery.insertBP(data, Manager_ID, async function (result) {
        if (result == '已添加') {
            const htmlBody = `<h2> <span style=colorred>${data.department}</span>的<span style=color:red>${data.employee_Name}</span>提交的BP</h2>
            <h4>提交时间：${moment().format('YYYY-MM-DD HH:mm:ss')}</h4>
            <h4>BP类型：${data.BP_Type}</h4>
            <h4>问题描述：${data.BP_Problem}</h4>
            <h4>建议方案：${data.BP_Solution}</h4>
            <h4>改善生产线：${data.Improve_Line}</h4>
            <h4>BP文件：<a href="${data.BP_URL}" style="text-decoration:underline;">${data.BP_URL}</a></h4>
            <h4>是否变更设备：${data.BPEquipment_Upgrading}</h4>
            <h4>变更设备图纸文件：<a href="${data.BPdrawing_URL}" style="text-decoration:underline;">${data.BPdrawing_URL}</a></h4>
            <a href="${web_url.webSite + '/#/BP?mail=true&function=myApproval&department=' + data.department}" style="text-decoration:underline;font-weight: 600">点击此处登录网页进行审批</a>`
            const repsonse = await sendMailtoApprover(Manager_ID + '@kcc.com', data.employee_Name + '提交了一个BP,请查看', htmlBody, '点子银行通知系统')
            res.send(repsonse)
        }
    })
})
router.post("/insertSafty", (req, res) => {
    console.log(req.body)
    const data = req.body.data
    const Manager_ID = req.body.Manager_ID
    const Entry_By = req.body.Entry_By
    var returnQuery = new sqlexec_grade()
    returnQuery.insertSafty(data, Manager_ID, Entry_By, async function (result) {
        if (result == '已添加') {
            const htmlBody = `<h2> <span style=colorred>${data.department}</span><span style=color:red>${data.employee_Name}</span>提交的安全隐患，请您审核</h2>
            <h4>发现时间：${moment().format('YYYY-MM-DD HH:mm:ss')}</h4>
            <h4>日常检查类型：${data.Safty_DailyType}</h4>
            <h4>是否重复发生：${data.Safty_Repeat}</h4>
            <h4>发生区域部门：${data.Safty_Department}</h4>
            <h4>发生区域：${data.Safty_Area}</h4>
            <h4>隐患类别：${data.Safty_Type}</h4>
            <h4>隐患描述：${data.Safty_Problem}</h4>
            <h4>上传的图片：<a href="${data.Safty_URL}" style="text-decoration:underline;">${data.Safty_URL}</a></h4>
            <img src="${data.Safty_URL}" alt="图片描述">
            <a href="${web_url.webSite + '/#/SafeIncident/SafeIncidentApplicate?mail=true&function=myApproval&department=' + data.department}" style="text-decoration:underline;font-weight: 600">点击此处登录网页进行审批</a>`
            const repsonse = await sendMailtoApprover(Manager_ID + '@kcc.com', data.employee_Name + '提交的安全隐患，请您审核', htmlBody, '安全隐患通知')
            res.send(repsonse)
        }
    })
})




router.post("/updateIdea", (req, res) => {
    console.log(req.body)
    const data = req.body.data
    const approver = req.body.approver
    const processType = req.body.processType
    const assignedMail = data.Assigned_By.hasOwnProperty('value') ? data.Assigned_By.value : data.Assigned_By
    console.log('assignedMail', assignedMail)
    var returnQuery = new sqlexec_grade()
    returnQuery.updateIdea(data, processType,async function (result) {
        console.log('result',result)
        if (result == '已更新' && (data['流程状态'] == 'open'||data['流程状态'] == 'In progress' ) && processType == 'approval') {
            const htmlBody = `<h2> <span style=colorred>${data['部门']}</span>的<span style=color:red>${data['提交人']}</span>提交的点子，由<span style=color:red>${approver}</span>确认后分配给您</h2>
            <h4>提交时间：${data['提交日期']}</h4>
            <h4>点子类型：${data['点子类型']}</h4>
            <h4>点子子类型：${data['点子子类型']}</h4>
            <h4>问题描述：${data['发现的问题']}</h4>
            <h4>建议方案：${data['建议解决方案']}</h4>
            <h4>点子价值：${data['点子价值']}</h4>
            <h4>分配人：${approver}</h4>
            <h4>整改确认：${data['整改确认']}</h4>
            <a href="${web_url.webSite + '/#/ideaBank/ideaBankFeedBack?mail=true&function=myApproval&department=' + data['部门']}" style="text-decoration:underline;font-weight: 600">点击并在系统中提供相关的行动方案</a>`
            console.log('mail',data.Assigned_By.hasOwnProperty('value')?data.Assigned_By.value:data.Assigned_By+'@kcc.com')
            const repsonse = await sendMailtoApprover(assignedMail + '@kcc.com', approver + '分配了一个任务，请查看', htmlBody, '点子银行通知系统')
            res.send(repsonse)
        } else if (result == '已更新' && processType == 'feedback') {
            const htmlBody = `<h2> <span style=colorred>${data['部门']}</span>的<span style=color:red>${data['提交人']}</span>提交的点子由已被<span style=color:red>${approver}</span>${data['流程状态']=='open'?'退回':''}处理。</h2>
            <h4>提交时间：${data['提交日期']}</h4>
            <h4>点子类型：${data['点子类型']}</h4>
            <h4>点子子类型：${data['点子子类型']}</h4>
            <h4>问题描述：${data['发现的问题']}</h4>
            <h4>建议方案：${data['建议解决方案']}</h4>
            <h4>点子价值：${data['点子价值']}</h4>
            <h4>整改人行动方案：${data['整改人提交方案']}</h4>
            <h4>预计整改完成时间：${data['预计整改完成时间']}</h4>
            <h4>整改人：${approver}</h4>
            <h4>整改人拒绝理由：${data['整改人拒绝理由']}</h4>
            <a href="${web_url.webSite+'/#/ideaBank/ideaBankApplicate?mail=true&function=myApproval&department='+data['部门']}" style="text-decoration:underline;font-weight: 600">点击此处登录网页再次确认</a>`
            //console.log('mail',data.Assigned_By.hasOwnProperty('value')?data.Assigned_By.value:data.Assigned_By+'@kcc.com')
            const repsonse = await sendMailtoApprover(assignedMail + '@kcc.com', approver + '反馈了一个点子银行任务，请查看', htmlBody, '点子银行通知系统')
            res.send(repsonse)
        } else {
            res.send(result)
        }
    })
})

router.post("/updateBP", (req, res) => {
    console.log(req.body)
    const data = req.body.data
    const approver = req.body.approver
    var returnQuery = new sqlexec_grade()
    returnQuery.updateBP(data, async function (result) {
        res.send(result)
    })
})

router.post("/updateSafty", (req, res) => {
    console.log(req.body)
    const data = req.body.data
    const rowData = req.body.rowData
    const approver = req.body.approver.username
    const approver_id = req.body.approver.account
    const processType = req.body.processType
    const assignedMail = data.assigned_name.hasOwnProperty('value') ? data.assigned_name.value : data.assigned_name
    console.log('assignedMail', assignedMail)
    var returnQuery = new sqlexec_grade()
    returnQuery.updateSafty(data, approver_id, processType, async function (result) {
        let EHS_Manager = EHS_List.mailto
        if (result == '已更新' && processType == 'approval') {
            let htmlBody = ''
            if (data['process_Status'] == 'open' || data['process_Status'] == 'In progress') {
                htmlBody = `<h2> <span style=colorred>${data['riskLevel'] == '高' ? '高风险邮件通知:' : ''}${rowData['部门']}</span><span style=color:red>${rowData['提交人']}</span>提交的安全隐患，由区域负责人(${approver})确认后分配给您</h2>
                <h4>发现时间：${rowData['提交日期']}</h4>
                <h4>日常检查类型：${rowData['日常检查类型']}</h4>
                <h4>是否重复发生：${rowData['是否重复发生']}</h4>
                <h4>发生区域部门：${rowData['隐患区域部门']}</h4>
                <h4>发生区域：${rowData['隐患区域']}</h4>
                <h4>隐患类别：${rowData['隐患类型']}</h4>
                <h4>隐患描述：${rowData['隐患内容']}</h4>
                <h4>风险等级：${data['riskLevel']}</h4>
                <h4>上传的图片：<a href="${rowData['隐患照片']}" style="text-decoration:underline;">${rowData['隐患照片']}</a></h4>
                <h4>区域负责人：${approver}</h4>
                <h4>整改人：${data.assigned_name.label}</h4>
                <a href="${web_url.webSite + '/#/SafeIncident/SafeIncidentFeedBack?mail=true&function=myApproval&department=' + rowData['部门']}" style="text-decoration:underline;font-weight: 600">点击此处登录网页进行反馈</a>`
                const repsonse = await sendMailtoApprover(data['riskLevel'] == '高' ? assignedMail + '@kcc.com;' + EHS_Manager : assignedMail + '@kcc.com;', `${data['riskLevel'] == '高' ? '高风险邮件通知:' : ''}区域负责人-${approver}分配了一个安全隐患任务`, htmlBody, '安全隐患通知')
                res.send(repsonse)
            } else if ((data['process_Status'] == 'Final Check'|| data['process_Status'] == 'EHS Check' ) && data['owner_approval_status'] == 'No') {
                console.log('退回邮件开始')
                htmlBody = `<h2> <span style=color:red>${data['riskLevel'] == '高' ? '高风险邮件通知:' : ''}${rowData['部门']}</span><span style=color:red>${rowData['提交人']}</span>提交的安全隐患，区域负责人(${approver})退回给您</h2>
                <h4>发现时间：${rowData['提交日期']}</h4>
                <h4>日常检查类型：${rowData['日常检查类型']}</h4>
                <h4>是否重复发生：${rowData['是否重复发生']}</h4>
                <h4>发生区域部门：${rowData['隐患区域部门']}</h4>
                <h4>发生区域：${rowData['隐患区域']}</h4>
                <h4>隐患类别：${rowData['隐患类型']}</h4>
                <h4>隐患描述：${rowData['隐患内容']}</h4>
                <h4>风险等级：${data['riskLevel']}</h4>
                <h4>上传的图片：<a href="${rowData['隐患照片']}" style="text-decoration:underline;">${rowData['隐患照片']}</a></h4>
                <h4>区域负责人：${approver}</h4>
                <h4>区域退回理由：${data['rejectedComment']}</h4>
                <a href="${web_url.webSite + '/#/SafeIncident/SafeIncidentApplicate?mail=true&function=myApproval&department=' + rowData['部门']}" style="text-decoration:underline;font-weight: 600">点击此处登录网页进行反馈</a>`
                console.log('html', htmlBody)
                const repsonse = await sendMailtoApprover(data['riskLevel'] == '高' ? rowData['Assigned_By'] + '@kcc.com;' + EHS_Manager : rowData['Assigned_By'] + '@kcc.com;', '区域负责人-' + approver + '退回了安全隐患任务', htmlBody, '安全隐患通知')
                res.send(repsonse)
            } else if (data['process_Status'] == 'Final Check' && data['riskLevel'] == '高' && data['owner_approval_status'] == 'Yes') {
                const htmlBody = `<h2> <span style=color:red>高风险邮件通知EHS确认:${rowData['部门']}</span>的<span >${rowData['提交人']}</span>提交的隐患由已被<span style=color:red>${rowData['整改人']}</span>处理。</h2>
                <h4>发现时间：${rowData['提交日期']}</h4>
                <h4>日常检查类型：${rowData['日常检查类型']}</h4>
                <h4>是否重复发生：${rowData['是否重复发生']}</h4>
                <h4>发生区域部门：${rowData['隐患区域部门']}</h4>
                <h4>发生区域：${rowData['隐患区域']}</h4>
                <h4>隐患类别：${rowData['隐患类型']}</h4>
                <h4>隐患描述：${rowData['隐患内容']}</h4>
                <h4>风险等级：${rowData['风险等级']}</h4>
                <h4>上传的图片：<a href="${rowData['隐患照片']}" style="text-decoration:underline;">${rowData['隐患照片']}</a></h4>
                <h4>行动方案：${rowData['整改人方案']}</h4>
                <h4>临时方案：${rowData['临时方案']}</h4>
                <h4>预计完成时间：${rowData['预计完成时间']}</h4>
                <h4>整改是否完成：${rowData['整改人完成整改确认']}</h4>
                <h4>整改照片：<a href="${rowData['整改照片']}" style="text-decoration:underline;">${rowData['整改照片']}</a></h4>
                <a href="${web_url.webSite + '/#/SafeIncident/SafeIncidentApplicate?mail=true&function=myApproval&department=' + rowData['部门']}" style="text-decoration:underline;font-weight: 600">点击此处登录网页进行审批</a>`
                //console.log('mail',data.Assigned_By.hasOwnProperty('value')?data.Assigned_By.value:data.Assigned_By+'@kcc.com')
                const repsonse = await sendMailtoApprover(EHS_Manager, `高风险邮件通知EHS确认:整改人-${rowData['整改人']}已完成整改`, htmlBody, '安全隐患通知')
                res.send(repsonse)
            } else {
                res.send('已发送')
            }
        }
    })
})

router.post("/updateSafty_Assigned", (req, res) => {
    console.log(req.body)
    const data = req.body.data
    const rowData = req.body.rowData
    const assinged = req.body.approver.username
    const assinged_id = req.body.approver.account
    let owner_name = rowData.Owner_Name
    console.log('owner_name', owner_name)

    const processType = req.body.processType
    var returnQuery = new sqlexec_grade()
    returnQuery.updateSafty(data, assinged_id, processType, async function (result) {
        let EHS_Manager = EHS_List.mailto
        console.log('to', rowData['风险等级'] == '高' ? owner_name + '@kcc.com;' + EHS_Manager : owner_name + '@kcc.com;')
        if (result == '已更新' && processType == 'feedback') {
            const htmlBody = `<h2> <span style=color:red>${rowData['风险等级'] == '高' ? '高风险邮件通知:' : ''}${rowData['部门']}</span>的<span style=color:red>${rowData['提交人']}</span>提交的隐患由已被<span style=color:red>${assinged}</span>${data['assigned_Reject_Comment']!=''?'退回':''}处理。</h2>
            <h4>发现时间：${rowData['提交日期']}</h4>
            <h4>日常检查类型：${rowData['日常检查类型']}</h4>
            <h4>是否重复发生：${rowData['是否重复发生']}</h4>
            <h4>发生区域部门：${rowData['隐患区域部门']}</h4>
            <h4>发生区域：${rowData['隐患区域']}</h4>
            <h4>隐患类别：${rowData['隐患类型']}</h4>
            <h4>隐患描述：${rowData['隐患内容']}</h4>
            <h4>风险等级：${rowData['风险等级']}</h4>
            <h4>上传的图片：<a href="${rowData['隐患照片']}" style="text-decoration:underline;">${rowData['隐患照片']}</a></h4>
            <h4>行动方案：${data['solution']}</h4>
            <h4>临时方案：${data['temporary_Solution']}</h4>
            <h4>预计完成时间：${data['assigned_Rectify_Date']}</h4>
            <h4>整改是否完成：${data['rectify_Completed'] ? '已完成' : ''}</h4>
            <h4>整改人拒绝理由：${data['assigned_Reject_Comment']}</h4>
            <h4>整改照片：<a href="${data['rectify_image_url']}" style="text-decoration:underline;">${data['rectify_image_url']}</a></h4>
            <a href="${web_url.webSite + '/#/SafeIncident/SafeIncidentApplicate?mail=true&function=myApproval&department=' + rowData['部门']}" style="text-decoration:underline;font-weight: 600">点击此处登录网页进行反馈</a>`
            //console.log('mail',data.Assigned_By.hasOwnProperty('value')?data.Assigned_By.value:data.Assigned_By+'@kcc.com')
            const repsonse = await sendMailtoApprover(rowData['风险等级'] == '高' ? owner_name + '@kcc.com;' + EHS_Manager : owner_name + '@kcc.com;', `${rowData['风险等级'] == '高' ? '高风险邮件通知:' : ''}整改人-${assinged}已${data['rectify_Completed'] ? '完成整改' : '反馈整改意见'}`, htmlBody, '安全隐患通知')
            res.send(repsonse)
        } else if (result == '已更新' && data['owner_approval_status'] == 'No') {
            res.send('已发送')
        }
    })
})






function sendMailtoApprover(to, subject, htmlbody, fromName) {
    return new Promise((resolve, reject) => {
        // 定义 API 接口地址
        const apiUrl = web_url.mailAPI
        const data = {
            'to': to,
            'subject': subject,
            'htmlbody': htmlbody,
            'fromName': fromName
        }
        console.log('data', data)
        request({
            method: "POST",
            url: apiUrl,
            form: data
        }, (err, response, data) => {
            // if(err){resolve(err)}
            // console.log('response',response.statusCode)
            if (response.statusCode == 200) {
                resolve('已发送')
            } else {
                resolve('发送失败')
            }

        })
    })
}



module.exports = router