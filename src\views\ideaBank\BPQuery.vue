<template>
    <base-content>

        <div class="q-pa-md">
            <q-dialog v-model="showDetailDialog" transition-show="flip-down">
                <q-card style="width:1000px; max-width: 100vw;" v-if="showDetailData">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6 ">BP查询</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>

                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template v-for="(value, key) in showDetailData">
                                <div v-if="key == '发现的问题' || key == '建议解决方案' || key == '拒绝理由'" style="width: 100%;">
                                    <q-input type="textarea" filled v-model="showDetailData[key]" dense stack-label
                                        :label="key" readonly style="height: 100px;" />
                                </div>
                                <div v-else>
                                    <q-input filled v-model="showDetailData[key]" dense stack-label :label="key"
                                        readonly style="width:300px" />
                                </div>

                                <!-- {{ key }}:{{ value }} -->
                            </template>
                        </div>
                        <!-- <div class="q-gutter-md row items-start"></div> -->
                        <div class="q-gutter-md" style="margin-top: 10px;">
                        </div>
                        <div class="q-gutter-md row items-start" style="margin-top: 10px;">

                        </div>
                        <!-- <q-input outlined v-model="approveIdea.row['Owner_Reject_Cause']" label-slot clearable
                            type="textarea" style="height: 100px;margin-top: 10px;">
                            <template v-slot:label>拒绝理由</template>
                        </q-input> -->
                    </q-card-section>
                </q-card>
            </q-dialog>
            <!-- <q-dialog v-model="firtDialog" transition-show="flip-down">
                <q-card style="width:1000px; max-width: 100vw;" v-if="showDetailData">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6 ">点子查询</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>
                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template v-for="(value, key) in showDetailData">
                                <div v-if="key == '发现的问题' || key == '建议解决方案' || key == '整改人提交方案' || key == '拒绝理由'"
                                    style="width: 100%;">
                                    <q-input type="textarea" filled v-model="showDetailData[key]" dense stack-label
                                        :label="key" readonly style="height: 100px;" />
                                </div>
                                <div v-else>
                                    <q-input filled v-model="showDetailData[key]" dense stack-label :label="key"
                                        readonly style="width:300px" />
                                </div>
                            </template>
                        </div>
                    </q-card-section>
                </q-card>
            </q-dialog> -->
            <!-- <q-dialog v-model="firtDialog" transition-show="flip-down">
                <q-card style="width:1000px; max-width: 100vw;" v-if="showDetailData">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6 ">点子查询</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>
                    <q-card-section>
                        <div class="q-gutter-md row items-start">

                        </div>
                    </q-card-section>
                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template v-for="(value, key) in showDetailData">
                                <div v-if="key == 'Problem_Desc' || key == 'Problem_Root_Case' || key == 'Problem_Solution' || key == '拒绝理由'"
                                    style="width: 100%;">
                                    <q-input type="textarea" filled v-model="showDetailData[key]" dense stack-label
                                        :label="key" readonly style="height: 100px;" />
                                </div>
                                <div v-else-if="key == 'Problem_File_URL_Array'" style="width: 100%;">
                                    <div>Problem_File_URL_Array</div>
                                    <template>
                                        <a :href="JSON.parse(showDetailData[key])[showDetailData['Problem_File_Name_Array']]"
                                            target="_blank">{{
                                                JSON.parse(showDetailData[key])[showDetailData['Problem_File_Name_Array']]
                                            }}</a>
                                    </template>
                                </div>
                                <div v-else>
                                    <q-input filled v-model="showDetailData[key]" dense stack-label :label="key"
                                        readonly style="width:300px" />
                                </div>
                            </template>
                        </div>
                    </q-card-section>
                </q-card>
            </q-dialog> -->

            <div v-if="approveIdeaList">
                <q-table :data="approveIdeaList" row-key="name" :pagination.sync="myPagination" :filter="filter1" dense
                    class="my-sticky-virtscroll-table" virtual-scroll :virtual-scroll-sticky-size-start="48"
                    :rows-per-page-options="[0]" :columns="columns_myproblem_list">
                    <template v-slot:top="props">
                        <div class="text-h6" style="font-weight: 600;">点子清单</div>
                        <q-space></q-space>

                        <q-input borderless dense debounce="300" v-model="filter1" placeholder="Search"
                            class="bg-indigo-1">
                            <template v-slot:append>
                                <q-icon name="search" />
                            </template>
                        </q-input>

                        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
                            @click="props.toggleFullscreen" class="q-ml-md" label="全屏" />
                    </template>

                    <template v-slot:body="props">
                        <q-tr :props="props" @click="toApprove(props)">
                            <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                {{ col.value }}
                            </q-td>
                        </q-tr>
                    </template>
                </q-table>
            </div>
        </div>




    </base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
export default {
    components: {
        BaseContent
    },
    data() {
        return {
            approveIdeaList: false,
            approveRawIdeaList: false,
            firtDialog: false,
            approveIdea: false,
            adpotFilter: true,
            filter1: '',
            myPagination: { rowsPerPage: 0 },
            showDetailData: false,
            approvalData: {
                Adopt: null,
                id: null,
                rootCase: null,
                problemSolution: null,
                completeDate: null,
                rejectedReason: null,
                process_status: null
            },
            // myIdeaColumn: [
            //     { name: '提交日期', align: 'left', label: '提交日期', field: '提交日期', sortable: true, format: val => `${val.substring(0, 19).replace('T', ' ')}` },
            //     { name: '提交人', align: 'left', label: '提交人', field: '提交人', sortable: true },
            //     { name: '是否采纳', align: 'left', label: '是否采纳', field: '是否采纳', sortable: true },
            //     { name: '点子价值', align: 'left', label: '点子价值', field: '点子价值', sortable: true },
            //     { name: '发现的问题', align: 'left', label: '发现的问题', field: '发现的问题', sortable: true, style: 'width: 100px' },
            //     { name: '建议解决方案', align: 'left', label: '解决方案', field: '建议解决方案', sortable: true }
            // ],
            columns_myproblem_list: [

				
                {
					name: '提交日期',
					align: 'left',
					label: '提交日期',
					field: '提交日期',
					sortable: true
				}, 
                {
					name: '部门',
					align: 'left',
					label: '部门',
					field: '部门',
					sortable: true
				}, 
                {
					name: '提交人',
					align: 'left',
					label: '提交人',
					field: '提交人',
					sortable: true
				}, 
                {
					name: '是否采纳',
					align: 'left',
					label: '是否采纳',
					field: '是否采纳',
					sortable: true
				}, 
                {
					name: 'BP价值',
					align: 'left',
					label: 'BP价值',
					field: 'BP价值',

					sortable: true
				}, {
					name: '发现的问题',
					align: 'left',
					label: '发现的问题',
					field: '发现的问题',
					sortable: true
				}, {
					name: '建议解决方案',
					align: 'left',
					label: '建议解决方案',
					field: '建议解决方案',
					sortable: true
				}
			],


        }
    },
    mounted() {
        this.getMyApprovalIdea()

    },
    watch: {

    },

    methods: {
        async getMyApprovalIdea() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`ideabank/queryBP`)
            console.log('queryBP', res)
            _this.approveIdeaList = res


        },
        toApprove(props) {
            console.log('toApprove', props)
            const _this = this
            _this.showDetailData = props.row

            _this.firtDialog = true
            //_this.getMyApprovalIdea()
        },

    }


}
</script>

<style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */
  height: 700px
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0
</style>