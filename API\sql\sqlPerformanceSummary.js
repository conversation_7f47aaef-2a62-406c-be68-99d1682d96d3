const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);
}

sqlexec.prototype = new _Base()

// 执行南京南厂绩效汇总查询
sqlexec.prototype.getPerformanceSummaryData = function (startDate, endDate, callBack) {
    sqlexec.prototype.
        _query(`declare @startdate date, @enddate date 
                set @startdate = ${startDate}
                set @enddate = ${endDate}
                select * from production_Summary_Function_Table(@startdate, @enddate) 
                where datediff(hour, Time_Stamp, getdate()) >= 24 
                order by date desc`, function (err, result) {
            if (err) {
                console.error("SQL Error:", err);
            }
            return callBack(result.recordset)
        })
}

// 获取所有生产线列表
sqlexec.prototype.getProductionLines = function (callBack) {
    sqlexec.prototype.
        _query(`SELECT DISTINCT Line FROM production_Summary_Function_Table(DATEFROMPARTS(YEAR(GETDATE()) - 2, 12, 28), '2099-12-31')
                ORDER BY Line`, function (err, result) {
            if (err) {
                console.error("SQL Error:", err);
            }
            return callBack(result.recordset)
        })
}

module.exports = sqlexec;
