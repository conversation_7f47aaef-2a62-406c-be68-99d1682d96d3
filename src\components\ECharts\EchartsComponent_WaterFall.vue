<template>
	<div>
		<div style="width:100%;height:300px;" :id="echarts" class="echarts" ref="echarts"></div>
	</div>
</template>

<script>
// 引入echarts
import echarts from 'echarts'
export default {
	name: 'EchartsComponent_WaterFall',
	props: {
		// 接收父组件传递过来的信息

		chartData: {
			categories: {
				type: Array,
				default: () => []
			},
			series: {
				type: Array,
				default: () => []
			},
			name: {
				type: String
			},

		}

	},
	data() {
		return {}
	},
	methods: {
		drawChart() {
			var unit = this.chartData.series[0].name == 'Percent' ? '%' : ''
			console.log('瀑布图收到的数据', this.chartData)
			console.log('this.chartData.series[0].value', this.chartData.series[0].data)
			const vm = this
			// 基于准备好的dom，初始化echarts实例
			var myChart = echarts.init(document.getElementById(this.echarts))
			// 绘制图表
			myChart.setOption({
				//图表标题
				// title: {
				// 	text: 'Waterfall'
				// },
				//鼠标悬停显示
				tooltip: {
					trigger: 'axis',
					//悬停框沿坐标轴显示
					axisPointer: {
						type: 'shadow'
					},
					//自定义悬停框提示内容
					formatter: function (datas) {
						// console.log('datas', datas)
						var res = datas[0].name + '\n',
							val;
						res = datas[0].axisValue + '：' + datas[1].value + unit;
						return res;
					}
				},
				//设置图表网格位置和大小
				grid: {
					left: '3%',
					right: '4%',
					top: '0.5%', // 控制图表在容器中的垂直位置
					bottom: '3%',
					containLabel: true
				},
				xAxis: {
					//配置X轴的类型为类目型
					type: 'category',
					//不显示分隔线
					splitLine: { show: false },
					//X轴的类目数据
					data: this.chartData.categories,
				},
				yAxis: {
					//Y轴的类型为数值型
					type: 'value',
					min: 95.5, // 设置Y轴坐标的最小值
					max: 100.5, // 设置Y轴坐标的最大值
					splitNumber: 4,
					splitLine: {
						show: false
					},
					axisLine: {
						show: false
					},
					axisTick: {
						//坐标轴刻度相关设置。
						show: false
					},
					axisLabel: {
						show: false, // 设置不显示Y轴坐标值
						textStyle: {
							fontSize: 13
						},
						formatter: function (value) {
							return value.toFixed(4) + unit;
						}
					}
				},

				series: [
					{
						//占位的柱状图
						name: 'Placeholder',
						type: 'bar',
						stack: 'Total',
						itemStyle: {
							borderColor: 'transparent',
							color: 'transparent'
						},
						emphasis: {
							itemStyle: {
								borderColor: 'transparent',
								color: 'transparent'
							}
						},
						data: this.chartData.series[1].data
					},
					{
						//实际数据的柱状图
						name: 'Waste%',
						type: 'bar',
						stack: 'Total',
						label: {
							show: true,
							position: 'top',
							textStyle: {
								color: 'black' // 设置标签字体颜色为黑色
							},
							formatter: (params) => params.value + '%'
						},
						itemStyle: {
							color: 'rgb(0,170,255)' // 设置数据柱状图为蓝色
						},
						data: this.chartData.series[0].data
					}
				]
			})
		}
	},
	computed: {
		echarts() {
			return 'echarts' + Math.random() * 100000
		}
	},
	mounted: function () {
		const vm = this
		vm.$nextTick(() => {
			vm.drawChart()
		})
	},
	created: () => { }
}
</script>

<style scoped></style>
