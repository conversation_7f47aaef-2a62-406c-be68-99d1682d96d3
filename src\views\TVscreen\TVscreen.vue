<template>
	<div>
<!-- 		<div id="app">
			<iframe style="border:none" :width="searchTableWidth" :height="searchTableHeight"
				v-bind:src="reportUrl"></iframe>
		</div> -->
<!-- 		<div>
		        <iframe src="http://cnnbas22/PIVision/#/Displays/559/HomePage" id="mobsf" scrolling="no" frameborder="0" style="position:absolute;top:64px;left: 240px;right:0px;bottom:100px;"></iframe>
		    </div>
		 -->
		 <iframe title="China Production Dashboard - Performance " width="1140" height="541.25" src="http://www.baidu.com" frameborder="0" allowFullScreen="true"></iframe>
	</div>
</template>

<script>
	import VueWebview from 'vue-webview'
	export default {
		name: 'TVscreen',
		components: {
			VueWebview
		},
		mounted() {
			window.onresize = () => {
				this.widthHeight(); // 自适应高宽度
			};
			this.$nextTick(function() {
				this.widthHeight();
			});

		},
		  created() {
		    // 从路由里动态获取 url地址   具体地址看libs下util.js里的 backendMenuToRoute  方法 
		    this.reportUrl = 'http://cnnbas22/PIVision/#/Displays/559/HomePage'
		  },
		  watch: {
		    '$route': function () {
		      // 监听路由变化
		      this.reportUrl =  'http://cnnbas22/PIVision/#/Displays/559/HomePage'
		    }
		  },

		data() {
			return {
				reportUrl: 'http://************',
				searchTableHeight: 0,
				searchTableWidth: 0
			}
		},

		methods: {
			widthHeight() {
				this.searchTableHeight = window.innerHeight - 180;
				this.searchTableWidth = window.innerWidth - 230
			},

		}
	}
</script>

<style scoped>

</style>
