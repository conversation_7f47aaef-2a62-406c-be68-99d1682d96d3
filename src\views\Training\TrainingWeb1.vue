<!-- 写HTML -->
<template>

	<base-content>
    <div class="q-pa-md q-gutter-sm">
            <div class="fit row wrap justify-start items-start q-gutter-sm  ">
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />
                <q-input filled v-model="text" label="Standard" style="width: 200px;" />

            </div>
 
            <div class="fit row wrap justify-start items-start q-gutter-sm ">
                <q-input filled v-model="text" label="Standard" style="width: 100%;" />
            </div>
            <!-- //增加一个选择框 -->
            <div class="fit row wrap justify-start items-start q-gutter-sm ">
                <q-select filled v-model="Line" :options="LineList" label="请选择机台" style="width: 200px;" />
            </div>


            <div class="fit row wrap justify-start items-start q-gutter-sm ">
                <q-select filled v-model="model" :options="options1" label="请选择SKU" style="width: 300px;" />
            </div>




        </div>

    <!-- <q-input filled v-model="text" label="Filled" />

    <q-input outlined v-model="text" label="Outlined" />

    <q-input standout v-model="text" label="Standout" /> -->


	</base-content>
</template>
<script>
import BaseContent from '../../components/BaseContent/BaseContent'

export default {
	name: 'Home',
	components: {
		BaseContent,
	},
	//页面首次进入的默认
	mounted() {
		this.DefaultValue(),
		this.getAPI()
	},
	// 以下放变量
	data()
    

    {
		return {
			//设置自定义变量
			testValue: 'TEST123',
			testValue1: '',
			testValue2: '',
			apiData:'',
            Line:'',
            LineList: [
        'ND05', 'ND06', 'ND07', 'ND08', 'ND09','ND34'
      ],
      SKUList:[]


		}
	},
	//以下搞方法
	methods: {
		// 上面的button的单击事件
		ChangeTestValue() {
			this.testValue = 'Test999'
		},

		DefaultValue() {
			this.testValue = 'Test444'
		},
		async getAPI() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`suibian/Wave1`)
            console.log('line', res)
            _this.apiData=res
        },
	}
}
</script>



<!-- 

//终端里调试启动网页服务器代码如下: npm run serve(修改后无需要重复启动,保存会重启)

//jason格式 key,value

data={
'a':1,
'b':2
} 

data1=[{},{},{},{}]


-->

