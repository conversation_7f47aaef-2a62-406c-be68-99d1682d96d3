@echo off
echo =============================================
echo    安装/重启 Web API 服务 (使用域账号运行)
echo =============================================
echo.

REM 检查是否以管理员权限运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 已获取管理员权限，继续执行...
) else (
    echo 错误: 请以管理员身份运行此脚本！
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo 步骤1: 停止现有服务...
net stop webServer_new /y >nul 2>&1
timeout /t 3 /nobreak > nul

echo 步骤2: 删除现有服务...
sc delete webServer_new >nul 2>&1
timeout /t 3 /nobreak > nul

echo 步骤3: 清理残留文件...
del /f /q "%PROGRAMDATA%\Microsoft\Windows\Services\Manifest\webServer_new*" >nul 2>&1
rmdir /s /q "daemon" >nul 2>&1
timeout /t 2 /nobreak > nul

echo 步骤4: 安装并启动新服务...
node nw.js
timeout /t 5 /nobreak > nul

echo.
echo 检查服务状态...
sc qc webServer_new | findstr "SERVICE_START_NAME"
sc query webServer_new | findstr "STATE"
echo.

echo 操作完成！
if %ERRORLEVEL% NEQ 0 (
    echo 警告：服务可能未正常启动，请检查以下内容：
    echo 1. 确认域账号 KCUS\CNNBAP13 密码正确
    echo 2. 确认域账号具有"作为服务登录"权限
    echo 3. 检查事件查看器中的系统日志
    echo 4. 检查 daemon 目录下的日志文件
) else (
    echo 服务已成功启动！
)
echo.
echo 按任意键退出...
pause > nul
