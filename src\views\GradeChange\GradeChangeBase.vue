<template>
	<base-content>

		<div class="q-pa-md q-gutter-md row items-start">
			<q-select dense outlined v-model="line" :options="lineList" label-slot clearable style="width:200px;">
				<template v-slot:label>机台 <em class="q-px-sm bg-deep-orange text-white rounded-borders"
						style="font-size: 12px;">必选</em></template>
			</q-select>
			<q-select dense outlined v-model="sku" :options="skuList" label-slot clearable style="width:200px;">
				<template v-slot:label>SKU <em class="q-px-sm bg-deep-orange text-white rounded-borders"
						style="font-size: 12px;">必选</em></template>
			</q-select>

			<q-btn color="primary" label="新增SKU" />
		</div>

		<div v-if="taskData">
			<q-table :data="taskData" title="QDataTable with QPopupEdit" :rows-per-page-options="[]" wrap-cells
				:selected.sync="selected" selection="multiple" row-key="ID" :filter="filter">
				<template v-slot:top-right>
					<q-input borderless dense debounce="300" v-model="filter" placeholder="Search">
						<template v-slot:append>
							<q-icon name="search" />
						</template>
					</q-input>
				</template>


				<template v-slot:header="props">
					<q-tr :props="props">
						<q-th class="bg-primary text-white">
							选择
						</q-th>
						<q-th v-for="col in props.cols" :key="col.name" :props="props">
							{{ col.name }}
						</q-th>
					</q-tr>
				</template>



				<template v-slot:body="props">
					<q-tr :props="props">
						<q-checkbox :value="props.selected"
							@input="(val, evt) => { Object.getOwnPropertyDescriptor(props, 'selected').set(val, evt) }" />
						<q-td v-for="col in props.cols" :key="col.name" :props="props" @click="gotoDetail(props)">
							{{ col.value }}
						</q-td>
					</q-tr>
				</template>
			</q-table>
		</div>




	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
export default {
	components: {
		BaseContent
	},
	data() {
		return {
			filter:'',
			selected: [],
			lineList: [],
			skuList: [],
			line: '',
			sku: '',
			taskItem: false,
			taskData: false,
			// columns:[
			// 	{ name: 'desc', style: 'min-width: 160px; width: 160px', align: 'left', label: 'Dessert', field: 'name' }
			// ]
		}
	},
	mounted() {
		this.lineList = localStorage.getItem('lineArray').split(',')
	},
	watch: {
		line(newValue, oldValue) {
			console.log('newValue->', newValue)
			console.log('oldValue->', oldValue)
			this.gradeChange_ProductName()
		},
		sku(newValue, oldValue) {
			this.gradeChange_List()
		},
		selected(newValue, oldValue) {
			console.log('oldValue->', oldValue)
			console.log('newValue->', newValue)

		}
	},

	methods: {
		gotoDetail(col) {
			console.log('col', col)
		},
		async gradeChange_ProductName() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`gradeChange/gradeChange_ProductName?Line=${_this.line}`)
			console.log('gradeChange_ProductName', res)
			_this.skuList = res
		},
		async gradeChange_List() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`gradeChange/gradeChange_List?Line=${_this.line}&sku=${_this.sku}`)
			console.log('gradeChange_List', res)
			_this.taskData = res
		},
	}
}
</script>

<style scoped></style>
