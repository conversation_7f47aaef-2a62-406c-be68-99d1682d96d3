const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.QA = function (Mill,Start,End,callBack) {
    sqlexec.prototype.
        _query(`select date 日期,Shift 班次,Line 机台,Crew 班组,'' 班组人员,SKU_Code SKU,Machine_Status 设备状态 
         from Production_Summary_Function_Table(${Start},${End}) where Mill=${Mill}`, function (err, result) {
      
            if (err) {
            }
            return callBack(result.recordset)
        })
}




module.exports = sqlexec;