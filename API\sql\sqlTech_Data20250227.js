const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.Tech_Project_Data = function (startDate,endDate, callBack) {
    sqlexec.prototype.
        _query(`select [ID]
      ,[Project_Name]
      ,[Project_Material_URL]
      ,format([Project_Start_Date],'yyyy-MM-dd') Project_Start_Date
      ,format([Project_End_Date],'yyyy-MM-dd') Project_End_Date
      ,[Project_Feature]
      ,[Project_Proposal_URL]
      ,[Project_Experiment_Report_URL]
      ,[Project_Experiment_Cost]
      ,[Project_Evaluation_Program_URL]
      ,[Project_Capex_Cost]
      ,[Project_Next_Phase]
      ,format([Project_Next_Phase_Date],'yyyy-MM-dd') Project_Next_Phase_Date
      ,[Project_Progress]
      ,[Project_Status]
      ,[Project_Parent_ID]
      ,[Modify_By]
      ,format([Modify_On],'yyyy-MM-dd HH:mm:ss')Modify_On
	  ,datediff(d,project_start_date,Project_End_Date) duration
       from Tech_Project_Data where Project_Start_Date between  '${startDate}' and  '${endDate}' or Project_End_Date between  '${startDate}' and  '${endDate}' order by Project_Start_Date `, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.updateTech_Project_Data_Insert = function (data, ID, callBack) {
    // console.log('data',data)
    console.log(`update Tech_Project_Data
            set Project_Name = '${data.Project_Name}',
            Project_Material_URL = '${data.Project_Material_URL}',
            Project_Start_Date = '${data.Project_Start_Date}',
            Project_End_Date = '${data.Project_End_Date}',
            Project_Feature = '${data.Project_Feature}',
            Project_Proposal_URL = '${data.Project_Proposal_URL}',
            Project_Experiment_Report_URL = '${data.Project_Experiment_Report_URL}',
            Project_Experiment_Cost = '${data.Project_Experiment_Cost}',
            Project_Evaluation_Program_URL = '${data.Project_Evaluation_Program_URL}',
            Project_Capex_Cost = '${data.Project_Capex_Cost}',
            Project_Next_Phase = '${data.Project_Next_Phase}',
            Project_Next_Phase_Date = '${data.Project_Next_Phase_Date}',
            Project_Progress = '${data.Project_Progress}',
            Project_Status = '${data.Project_Status}',
            Project_Parent_ID = ${data.Project_Parent_ID},
            Modify_By = '${data.Modify_By}',
            Modify_On = GETDATE()
            where ID = ${ID}`)
    sqlexec.prototype.
        _query(`update Tech_Project_Data
            set Project_Name = '${data.Project_Name}',
            Project_Material_URL = '${data.Project_Material_URL}',
            Project_Start_Date = '${data.Project_Start_Date}',
            Project_End_Date = '${data.Project_End_Date}',
            Project_Feature = '${data.Project_Feature}',
            Project_Proposal_URL = '${data.Project_Proposal_URL}',
            Project_Experiment_Report_URL = '${data.Project_Experiment_Report_URL}',
            Project_Experiment_Cost = '${data.Project_Experiment_Cost}',
            Project_Evaluation_Program_URL = '${data.Project_Evaluation_Program_URL}',
            Project_Capex_Cost = '${data.Project_Capex_Cost}',
            Project_Next_Phase = '${data.Project_Next_Phase}',
            Project_Next_Phase_Date = '${data.Project_Next_Phase_Date}',
            Project_Progress = '${data.Project_Progress}',
            Project_Status = '${data.Project_Status}',
            Project_Parent_ID = ${data.Project_Parent_ID},
            Modify_By = '${data.Modify_By}',
            Modify_On = GETDATE()
            where ID = ${ID}`, function (err, result) {
            if (err) {
                return callBack(err)
            }
            return callBack('更新成功')
        })
}
sqlexec.prototype.insertTech_Project_Data_Insert = function (data, callBack) {
    console.log(`insert into Tech_Project_Data (Project_Name,Project_Material_URL,Project_Start_Date,Project_End_Date,Project_Feature,Project_Proposal_URL,Project_Experiment_Report_URL,Project_Experiment_Cost,Project_Evaluation_Program_URL,Project_Capex_Cost,Project_Next_Phase,Project_Next_Phase_Date,Project_Progress,Project_Status,Project_Parent_ID,Modify_By,Modify_On) values ('${data.Project_Name}','${data.Project_Material_URL}','${data.Project_Start_Date}','${data.Project_End_Date}','${data.Project_Feature}','${data.Project_Proposal_URL}','${data.Project_Experiment_Report_URL}','${data.Project_Experiment_Cost}','${data.Project_Evaluation_Program_URL}','${data.Project_Capex_Cost}','${data.Project_Next_Phase}', '${data.Project_Next_Phase_Date}','${data.Project_Progress}','${data.Project_Status}',${data.Project_Parent_ID},'${data.Modify_By}',GETDATE())`)
    sqlexec.prototype.
        _query(`insert into Tech_Project_Data (Project_Name,Project_Material_URL,Project_Start_Date,Project_End_Date,Project_Feature,Project_Proposal_URL,Project_Experiment_Report_URL,Project_Experiment_Cost,Project_Evaluation_Program_URL,Project_Capex_Cost,Project_Next_Phase,Project_Next_Phase_Date,Project_Progress,Project_Status,Project_Parent_ID,Modify_By,Modify_On) values ('${data.Project_Name}','${data.Project_Material_URL}','${data.Project_Start_Date}','${data.Project_End_Date}','${data.Project_Feature}','${data.Project_Proposal_URL}','${data.Project_Experiment_Report_URL}','${data.Project_Experiment_Cost}','${data.Project_Evaluation_Program_URL}','${data.Project_Capex_Cost}','${data.Project_Next_Phase}', '${data.Project_Next_Phase_Date}','${data.Project_Progress}','${data.Project_Status}',${data.Project_Parent_ID},'${data.Modify_By}',GETDATE())`, function (err, result) {
            if (err) {
            }
            return callBack('新增成功')
        })
}




module.exports = sqlexec;