<template>
  <div class="user-info-management">
    <h2 class="text-h6 q-mb-md">用户信息管理</h2>
    
    <!-- 用户列表 -->
    <q-table
      title="用户列表"
      :data="users"
      :columns="columns"
      row-key="employee_id"
      :loading="loading"
      :filter="filter"
      :pagination.sync="pagination"
    >
      <template v-slot:top-right>
        <q-input dense debounce="300" v-model="filter" placeholder="搜索">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="employee_id" :props="props">
            {{ props.row.employee_id }}
          </q-td>
          <q-td key="employee_name" :props="props">
            {{ props.row.employee_name }}
          </q-td>
          <q-td key="department" :props="props">
            {{ props.row.department }}
          </q-td>
          <q-td key="title" :props="props">
            {{ props.row.title }}
          </q-td>
          <q-td key="actions" :props="props">
            <q-btn flat round dense color="primary" icon="edit" @click="openEditDialog(props.row)" />
            <q-btn flat round dense color="negative" icon="key" @click="resetUserPassword(props.row)" />
            <q-btn flat round dense color="secondary" icon="security" @click="openAccessDialog(props.row)" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
    
    <!-- 编辑用户对话框 -->
    <q-dialog v-model="editDialog" persistent>
      <q-card style="min-width: 400px">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">编辑用户信息</div>
        </q-card-section>

        <q-card-section class="q-pt-md">
          <q-form @submit="updateUserInfo" class="q-gutter-md">
            <q-input v-model="editingUser.employee_id" label="员工ID" readonly filled />
            <q-input v-model="editingUser.employee_name" label="员工姓名" readonly filled />
            <q-input 
              v-model="editingUser.department" 
              label="部门 *" 
              filled
              :rules="[val => !!val || '部门不能为空']"
            />
            <q-input 
              v-model="editingUser.title" 
              label="职位 *" 
              filled
              :rules="[val => !!val || '职位不能为空']"
            />
            
            <div class="row justify-end q-mt-md">
              <q-btn label="取消" color="grey-7" flat v-close-popup />
              <q-btn label="保存" type="submit" color="primary" :loading="submitting" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>
    
    <!-- 密码重置成功对话框 -->
    <q-dialog v-model="resetPasswordDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section class="bg-positive text-white">
          <div class="text-h6">密码重置成功</div>
        </q-card-section>
        
        <q-card-section class="q-pt-md">
          <p>用户 <strong>{{ resetPasswordResult.employee_id }}</strong> 的密码已重置！</p>
          <p>新密码: <q-badge color="primary">{{ resetPasswordResult.new_password }}</q-badge></p>
          <p class="text-negative">请记下此密码，页面关闭后将不再显示！</p>
        </q-card-section>
        
        <q-card-actions align="right">
          <q-btn flat label="关闭" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
    
    <!-- 权限管理对话框 -->
    <q-dialog v-model="accessDialog" persistent maximized>
      <q-card>
        <q-card-section class="bg-secondary text-white">
          <div class="text-h6">用户权限管理</div>
          <div class="text-subtitle2">{{ currentUser.employee_name }} ({{ currentUser.employee_id }})</div>
        </q-card-section>
        
        <q-card-section class="q-pa-lg">
          <div class="row q-col-gutter-md">
            <!-- 当前权限列表 -->
            <div class="col-xs-12 col-md-5">
              <q-card>
                <q-card-section class="bg-primary text-white">
                  <div class="text-h6">当前权限</div>
                </q-card-section>
                
                <q-card-section>
                  <div v-if="loadingAccess" class="text-center q-pa-md">
                    <q-spinner color="primary" size="3em" />
                    <div class="q-mt-md">加载中...</div>
                  </div>
                  
                  <div v-else-if="userAccess.length === 0" class="text-center q-pa-md">
                    <q-icon name="info" color="grey" size="3em" />
                    <div class="q-mt-md text-grey">该用户暂无权限</div>
                  </div>
                  
                  <q-list bordered separator v-else>
                    <q-item v-for="access in userAccess" :key="access.access_link_id">
                      <q-item-section>
                        <q-item-label>{{ access.app_name }}</q-item-label>
                        <q-item-label caption>角色: {{ access.access_group }}</q-item-label>
                      </q-item-section>
                      
                      <q-item-section side>
                        <q-btn flat round dense color="negative" icon="delete" @click="removeAccess(access)" />
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-card-section>
              </q-card>
            </div>
            
            <!-- 批量添加权限 -->
            <div class="col-xs-12 col-md-7">
              <q-card>
                <q-card-section class="bg-primary text-white">
                  <div class="text-h6">批量添加权限</div>
                </q-card-section>
                
                <q-card-section>
                  <q-form @submit="addBatchAccess" class="q-gutter-md">
                    <!-- 权限选择区块，按应用名分组 -->
                    <div v-for="(appInfo, appName) in appGroupsMap" :key="appName" class="q-mb-md">
                      <q-expansion-item
                        :label="appName"
                        header-class="bg-grey-3"
                        expand-separator
                        group="accessApps"
                      >
                        <q-card>
                          <q-card-section>
                            <div class="row items-center q-mb-sm">
                              <div class="text-subtitle2">选择一个权限角色：</div>
                            </div>
                            <div class="q-gutter-sm">
                              <q-radio
                                v-for="group in appInfo.groups"
                                :key="`${appName}-${group}`"
                                v-model="appPermissionMap[appName]"
                                :val="group"
                                :label="group"
                              />
                            </div>
                          </q-card-section>
                        </q-card>
                      </q-expansion-item>
                    </div>
                    
                    <div class="row justify-end q-mt-md">
                      <q-btn label="取消" color="grey-7" flat v-close-popup />
                      <q-btn 
                        label="添加权限" 
                        type="submit" 
                        color="primary" 
                        :loading="submittingAccess"
                        :disable="Object.keys(appPermissionMap).length === 0"
                      />
                    </div>
                  </q-form>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script>
export default {
  name: 'UserInfoManagement',
  data() {
    return {
      loading: false,
      submitting: false,
      filter: '',
      users: [],
      columns: [
        { name: 'employee_id', required: true, label: '员工ID', align: 'left', field: 'employee_id', sortable: true },
        { name: 'employee_name', required: true, label: '员工姓名', align: 'left', field: 'employee_name', sortable: true },
        { name: 'department', required: true, label: '部门', align: 'left', field: 'department', sortable: true },
        { name: 'title', required: true, label: '职位', align: 'left', field: 'title', sortable: true },
        { name: 'actions', required: true, label: '操作', align: 'center', field: 'actions' }
      ],
      pagination: {
        rowsPerPage: 10
      },
      editDialog: false,
      editingUser: {
        employee_id: '',
        employee_name: '',
        department: '',
        title: ''
      },
      resetPasswordDialog: false,
      resetPasswordResult: {
        employee_id: '',
        new_password: ''
      },
      // 权限管理相关
      accessDialog: false,
      loadingAccess: false,
      submittingAccess: false,
      currentUser: {
        employee_id: '',
        employee_name: ''
      },
      userAccess: [],
      accessGroups: [],
      appsByGroup: {},
      appGroupsMap: {}, // 应用-权限组映射
      appPermissionMap: {}, // 每个应用选中的权限
      selectAllInApp: {} // 应用全选状态
    }
  },
  mounted() {
    this.fetchUsers()
  },
  methods: {
    async fetchUsers() {
      this.loading = true
      try {
        const response = await this.$http.get('/userManagement_api/users')
        console.log('用户列表响应:', response.data)
        if (response.data.status === 200) {
          if (Array.isArray(response.data.data)) {
            this.users = response.data.data.map(user => ({
              employee_id: user.Employee_ID || user.employee_id || '',
              employee_name: user.Employee_Name || user.employee_name || '',
              department: user.Department || user.department || '',
              title: user.Title || user.title || ''
            }))
          } else {
            console.error('返回的数据不是数组:', response.data.data)
            this.$q.notify({
              color: 'negative',
              message: '获取用户列表数据格式错误',
              icon: 'error'
            })
            this.users = []
          }
        } else {
          this.$q.notify({
            color: 'negative',
            message: response.data.message || '获取用户列表失败',
            icon: 'error'
          })
          this.users = []
        }
      } catch (error) {
        console.error('获取用户列表错误：', error)
        this.$q.notify({
          color: 'negative',
          message: '网络请求错误，请稍后重试',
          icon: 'error'
        })
        this.users = []
      } finally {
        this.loading = false
      }
    },
    
    openEditDialog(user) {
      this.editingUser = { ...user }
      this.editDialog = true
    },
    
    async updateUserInfo() {
      if (!this.editingUser.department || !this.editingUser.title) {
        this.$q.notify({
          color: 'negative',
          message: '请填写所有必填字段',
          icon: 'warning'
        })
        return
      }
      
      this.submitting = true
      
      try {
        const response = await this.$http.post('/userManagement_api/update-info', {
          employee_id: this.editingUser.employee_id,
          department: this.editingUser.department,
          title: this.editingUser.title
        })
        
        if (response.data.status === 200) {
          // 更新成功，关闭对话框并刷新用户列表
          this.editDialog = false
          this.$q.notify({
            color: 'positive',
            message: '用户信息更新成功',
            icon: 'check'
          })
          this.fetchUsers()
        } else {
          this.$q.notify({
            color: 'negative',
            message: response.data.message || '用户信息更新失败',
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('更新用户信息错误：', error)
        this.$q.notify({
          color: 'negative',
          message: '网络请求错误，请稍后重试',
          icon: 'error'
        })
      } finally {
        this.submitting = false
      }
    },
    
    async resetUserPassword(user) {
      this.$q.dialog({
        title: '确认重置密码',
        message: `确定要重置用户 ${user.employee_name} (${user.employee_id}) 的密码吗？`,
        cancel: true,
        persistent: true
      }).onOk(async () => {
        try {
          const response = await this.$http.post('/userManagement_api/reset-password', {
            employee_id: user.employee_id
          })
          
          if (response.data.status === 200) {
            // 重置成功，显示新密码
            this.resetPasswordResult = response.data.data
            this.resetPasswordDialog = true
          } else {
            this.$q.notify({
              color: 'negative',
              message: response.data.message || '密码重置失败',
              icon: 'error'
            })
          }
        } catch (error) {
          console.error('重置密码错误：', error)
          this.$q.notify({
            color: 'negative',
            message: '网络请求错误，请稍后重试',
            icon: 'error'
          })
        }
      })
    },
    
    // 权限管理相关方法
    async openAccessDialog(user) {
      this.currentUser = { ...user }
      this.appPermissionMap = {}
      this.accessDialog = true
      
      // 加载用户当前权限
      await this.fetchUserAccess()
      
      // 加载所有权限组和应用
      await this.fetchAccessGroups()
    },
    
    async fetchUserAccess() {
      if (!this.currentUser.employee_id) return
      
      this.loadingAccess = true
      this.userAccess = []
      
      try {
        const response = await this.$http.get(`/userManagement_api/user-access?employee_id=${this.currentUser.employee_id}`)
        console.log('用户权限响应:', response.data)
        
        // 检查响应格式
        if (response.data && response.data.status === 200) {
          let accessData = response.data.data || []
          
          console.log('原始权限数据:', JSON.stringify(accessData))
          
          if (Array.isArray(accessData)) {
            // 处理数组格式的数据
            this.userAccess = accessData.map(access => {
              return {
                access_link_id: access.Access_LinkID || access.access_link_id || '',
                employee_id: access.Employee_ID || access.employee_id || '',
                employee_name: access.Employee_Name || access.employee_name || '',
                access_group: access.Access_Group || access.access_group || '未定义',
                app_name: access.App_Name || access.app_name || '未定义'
              }
            })
          } else if (accessData && typeof accessData === 'object') {
            // 处理对象格式的数据（可能是单个用户权限）
            console.log('权限数据是对象格式，尝试转换为数组')
            const accessItem = {
              access_link_id: accessData.Access_LinkID || accessData.access_link_id || '',
              employee_id: accessData.Employee_ID || accessData.employee_id || '',
              employee_name: accessData.Employee_Name || accessData.employee_name || '',
              access_group: accessData.Access_Group || accessData.access_group || '未定义',
              app_name: accessData.App_Name || accessData.app_name || '未定义'
            }
            this.userAccess = [accessItem]
          } else {
            console.error('返回的权限数据格式无法识别:', accessData)
            this.$q.notify({
              color: 'warning',
              message: '获取用户权限数据格式错误',
              icon: 'warning'
            })
            this.userAccess = []
          }
          
          console.log('处理后的用户权限数据:', this.userAccess)
        } else {
          this.$q.notify({
            color: 'negative',
            message: response.data?.message || '获取用户权限失败',
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('获取用户权限错误：', error)
        this.$q.notify({
          color: 'negative',
          message: '获取用户权限失败',
          icon: 'error'
        })
      } finally {
        this.loadingAccess = false
      }
    },
    
    async fetchAccessGroups() {
      try {
        const response = await this.$http.get('/userManagement_api/access-groups')
        console.log('获取权限组响应:', response.data)
        
        if (response.data && response.data.status === 200) {
          let groupsData = response.data.data || []
          
          console.log('原始权限组数据:', JSON.stringify(groupsData))
          
          if (Array.isArray(groupsData)) {
            // 处理数组格式的数据，按应用分类
            const processedGroups = groupsData.map(group => ({
              group_name: group.Group_Name || group.group_name || '未定义',
              app_name: group.App_Name || group.app_name || '未定义'
            })).filter(group => group.group_name !== '未定义' && group.app_name !== '未定义')
            
            // 按应用名称分组
            this.appGroupsMap = {}
            processedGroups.forEach(item => {
              if (!this.appGroupsMap[item.app_name]) {
                this.appGroupsMap[item.app_name] = {
                  groups: []
                }
                // 初始化应用选择状态
                this.selectAllInApp[item.app_name] = false
              }
              if (!this.appGroupsMap[item.app_name].groups.includes(item.group_name)) {
                this.appGroupsMap[item.app_name].groups.push(item.group_name)
              }
            })
            
            console.log('按应用分类的权限组:', this.appGroupsMap)
            
            // 为了兼容性，保留原有的数据结构
            const uniqueGroupNames = [...new Set(processedGroups.map(g => g.group_name))]
            this.appsByGroup = {}
            uniqueGroupNames.forEach(groupName => {
              this.appsByGroup[groupName] = processedGroups
                .filter(g => g.group_name === groupName)
                .map(g => ({ app_name: g.app_name }))
            })
            this.accessGroups = uniqueGroupNames.map(name => ({ group_name: name }))
          } else {
            console.error('返回的权限组数据格式无法识别:', groupsData)
            this.$q.notify({
              color: 'negative',
              message: '获取权限组数据格式错误',
              icon: 'error'
            })
            this.appGroupsMap = {}
            this.accessGroups = []
            this.appsByGroup = {}
          }
        } else {
          this.$q.notify({
            color: 'negative',
            message: response.data?.message || '获取权限组列表失败',
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('获取权限组列表错误：', error)
        this.$q.notify({
          color: 'negative',
          message: '获取权限组列表失败',
          icon: 'error'
        })
      }
    },
    
    async addBatchAccess() {
      // 准备要添加的权限数据
      const selectedPermissions = Object.entries(this.appPermissionMap)
        .filter(([_, value]) => value) // 过滤掉没有选择的应用
        .map(([appName, groupName]) => ({
          app: appName,
          group: groupName
        }))
      
      if (selectedPermissions.length === 0) {
        this.$q.notify({
          color: 'warning',
          message: '请至少为一个应用选择权限',
          icon: 'warning'
        })
        return
      }
      
      this.submittingAccess = true
      
      try {
        // 准备批量添加的权限数据
        const accessRequests = selectedPermissions.map(item => ({
          employee_id: this.currentUser.employee_id,
          employee_name: this.currentUser.employee_name,
          access_group: item.group,
          app_name: item.app
        }))
        
        console.log('批量添加权限请求数据:', accessRequests)
        
        // 逐个添加权限
        let successCount = 0
        let existCount = 0
        let failCount = 0
        
        for (const request of accessRequests) {
          try {
            const response = await this.$http.post('/userManagement_api/set-access', request)
            
            if (response.data.status === 200) {
              if (response.data.message === '权限已存在') {
                existCount++
              } else {
                successCount++
              }
            } else {
              failCount++
              console.error('添加权限失败:', response.data.message)
            }
          } catch (error) {
            failCount++
            console.error('添加权限请求错误:', error)
          }
        }
        
        // 显示结果通知
        this.$q.notify({
          color: successCount > 0 ? 'positive' : (existCount > 0 ? 'warning' : 'negative'),
          message: `添加结果: ${successCount} 个成功, ${existCount} 个已存在, ${failCount} 个失败`,
          icon: successCount > 0 ? 'check' : 'warning',
          timeout: 3000
        })
        
        // 刷新用户权限
        if (successCount > 0) {
          this.fetchUserAccess()
          this.appPermissionMap = {} // 清空选择
        }
      } catch (error) {
        console.error('批量添加权限错误:', error)
        this.$q.notify({
          color: 'negative',
          message: '批量添加权限失败',
          icon: 'error'
        })
      } finally {
        this.submittingAccess = false
      }
    },
    
    async removeAccess(access) {
      this.$q.dialog({
        title: '确认移除权限',
        message: `确定要移除用户 ${access.employee_name} 的 ${access.app_name} (${access.access_group}) 权限吗？`,
        cancel: true,
        persistent: true
      }).onOk(async () => {
        try {
          console.log('移除权限请求数据:', {
            access_link_id: access.access_link_id
          })
          
          const response = await this.$http.post('/userManagement_api/remove-access', {
            access_link_id: access.access_link_id
          })
          
          if (response.data.status === 200) {
            this.$q.notify({
              color: 'positive',
              message: '权限移除成功',
              icon: 'check'
            })
            
            // 重新获取用户权限
            this.fetchUserAccess()
          } else {
            this.$q.notify({
              color: 'negative',
              message: response.data.message || '移除权限失败',
              icon: 'error'
            })
          }
        } catch (error) {
          console.error('移除权限错误：', error)
          this.$q.notify({
            color: 'negative',
            message: '网络请求错误，请稍后重试',
            icon: 'error'
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.user-info-management {
  max-width: 1000px;
  margin: 0 auto;
}
</style> 