const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.TrainingList = function (Training_Category, Business_Category, Position, callBack) {
    
    sqlexec.prototype.
        _query(`select * from Training_List where Training_Active='Y' and Training_Category ='${Training_Category}'  and Business_Category ='${Business_Category}' and Position ='${Position}' order by Sort_ID asc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.TrainingMaterialList = function (callBack) {

    sqlexec.prototype.
        _query(`select * from Training_Material order by Training_ID asc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.getTraining_Category = function (callBack) {

    sqlexec.prototype.
        _query(`select distinct Training_Category from Training_List order by Training_Category asc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.getBusiness_Category = function (callBack) {

    sqlexec.prototype.
        _query(`select distinct Business_Category from Training_List order by Business_Category asc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.getPosition = function (callBack) {

    sqlexec.prototype.
        _query(`select distinct Position from Training_List order by Position asc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}




sqlexec.prototype.TrainingList_By_ID = function (callBack) {

    sqlexec.prototype.
        _query(`select Training_Key,Training_ID from Training_List`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.SP_API_Training_Score_Update = function (data,callBack) {
    console.log(`exec SP_API_Training_Score_Update '${data.Training_ID}', '${data.Traing_Name}', '${data.Employee_ID}', '${data.Employee_Name}', '${data.Training_Result}','${data.Trainer}','${data.Score}','${data.Training_Time}'`)
    sqlexec.prototype.
        _query(`exec SP_API_Training_Score_Update '${data.Training_ID}', '${data.Traing_Name}', '${data.Employee_ID}', '${
        data.Employee_Name}', '${data.Training_Result}','${data.Trainer}','${data.Score}','${data.Training_Time}'`, function (err, result) {
            if (err) {
            }
            return callBack('Success')
        })
}
sqlexec.prototype.Employee_Info = function (Employee_Name,callBack) {
    sqlexec.prototype.
        _query(`select Employee_ID,Employee_Name,Line,Position from Employee_List where Employee_Name='${Employee_Name}'`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.SP_API_Training_Query_Personal = function (Employee_ID, callBack) {
    sqlexec.prototype.
        _query(`exec SP_API_Training_Query_Personal '${Employee_ID}'`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.Training_Student_Feedback = function (data, callBack) {
    sqlexec.prototype.
        _query(`insert into Training_Student_Feedback (Employee_ID,Employee_Name,Advantage,Disadvantage,Create_By) values ('${data.Employee_ID}','${data.Employee_Name}','${data.Advantage}','${data.Disadvantage}','${data.Create_By}')`, function (err, result) {
            if (err) {
            }
            return callBack('Success')
        })
}







module.exports = sqlexec;