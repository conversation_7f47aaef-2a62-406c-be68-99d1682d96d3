<template>
    <base-content>

        <div class="q-pa-md">
            <q-dialog v-model="firtDialog" transition-show="flip-down">
                <q-card style="width:1000px; max-width: 100vw;" v-if="showDetailData">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6 ">LSW详情</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>
                    <q-card-section>
                        <div class="q-gutter-md row items-start">

                        </div>
                    </q-card-section>
                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template v-for="(value, key) in showDetailData">
                                <div v-if="key == 'Problem_Desc' || key == 'Problem_Root_Case' || key == 'Problem_Solution' || key == '拒绝理由'"
                                    style="width: 100%;">
                                    <q-input type="textarea" filled v-model="showDetailData[key]" dense stack-label
                                        :label="key" readonly style="height: 100px;" />
                                </div>
                                <div v-else-if="key == 'Problem_File_URL_Array'" style="width: 100%;">
                                    <div>Problem_File_URL_Array</div>
                                    <template>
                                        <a :href="JSON.parse(showDetailData[key])[showDetailData['Problem_File_Name_Array']]"
                                            target="_blank">{{
                                                JSON.parse(showDetailData[key])[showDetailData['Problem_File_Name_Array']]
                                            }}</a>
                                    </template>
                                </div>
                                <div v-else>
                                    <q-input filled v-model="showDetailData[key]" dense stack-label :label="key"
                                        readonly style="width:300px" />
                                </div>
                            </template>
                        </div>
                    </q-card-section>
                </q-card>
            </q-dialog>

            <div v-if="approveIdeaList">
                <q-table :data="approveIdeaList" row-key="name" :pagination.sync="myPagination" :filter="filter1" dense
                    class="my-sticky-virtscroll-table" virtual-scroll :virtual-scroll-sticky-size-start="48"
                    :rows-per-page-options="[0]" :columns="columns_myproblem_list">
                    <template v-slot:top="props">
                        <div class="text-h6" style="font-weight: 600;">LSW任务清单</div>
                        <q-space></q-space>

                        <q-input borderless dense debounce="300" v-model="filter1" placeholder="Search"
                            class="bg-indigo-1">
                            <template v-slot:append>
                                <q-icon name="search" />
                            </template>
                        </q-input>

                        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
                            @click="props.toggleFullscreen" class="q-ml-md" label="全屏" />
                    </template>

                    <template v-slot:body="props">
                        <q-tr :props="props" @click="toApprove(props)">
                            <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                {{ col.value }}
                            </q-td>
                        </q-tr>
                    </template>
                </q-table>
            </div>
        </div>




    </base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
export default {
    components: {
        BaseContent
    },
    data() {
        return {
            approveIdeaList: false,
            approveRawIdeaList: false,
            firtDialog: false,
            approveIdea: false,
            adpotFilter: true,
            filter1: '',
            myPagination: { rowsPerPage: 0 },
            showDetailData: false,
            approvalData: {
                Adopt: null,
                id: null,
                rootCase: null,
                problemSolution: null,
                completeDate: null,
                rejectedReason: null,
                process_status: null
            },
            columns_myproblem_list: [

				
                {
					name: '完成状态',
					align: 'left',
					label: '完成状态',
					field: 'Process_Status',
					sortable: true
				}, 
                {
					name: '日期',
					align: 'left',
					label: '日期',
					field: 'Date',
					sortable: true
				}, 
                {
					name: '发现人',
					align: 'left',
					label: '发现人',
					field: 'Employee_Name',
					sortable: true
				}, 
                {
					name: '区域',
					align: 'left',
					label: '区域',
					field: 'Problem_Area',

					sortable: true
				}, {
					name: '问题分类',
					align: 'left',
					label: '问题分类',
					field: 'Problem_Category',
					sortable: true
				}, {
					name: '问题描述',
					align: 'left',
					label: '问题描述',
					field: 'Problem_Desc',
					sortable: true
				}, {
					name: '根本原因',
					align: 'left',
					label: '根本原因',
					field: 'Problem_Root_Case',
					sortable: true
				}, {
					name: '改善方案',
					align: 'left',
					label: '改善方案',
					field: 'Problem_Solution',
					sortable: true
				}, {
					name: '负责人职位',
					align: 'left',
					label: '负责人职位',
					field: 'Assigned_Department',
					sortable: true
				}, {
					name: '负责人',
					align: 'left',
					label: '负责人',
					field: 'Assigned_By',
					sortable: true
				}, {
					name: '优先级',
					align: 'left',
					label: '优先级',
					field: 'Priority',
					sortable: true
				}, {
					name: '完成日期',
					align: 'left',
					label: '完成日期',
					field: 'Action_Complete_Date',
					sortable: true
				}, {
					name: 'ID',
					align: 'left',
					label: 'ID',
					field: 'id',
					sortable: true
				}
			],


        }
    },
    mounted() {
        this.getMyApprovalIdea()

    },
    watch: {

    },

    methods: {
        async getMyApprovalIdea() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`lsw/LSW_List`)
            console.log('getLSW_Assign_List', res)
            _this.approveIdeaList = res


        },
        toApprove(props) {
            console.log('toApprove', props)
            const _this = this
            _this.showDetailData = props.row

            _this.firtDialog = true
            //_this.getMyApprovalIdea()
        },

    }


}
</script>

<style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */
  height: 700px
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0
</style>