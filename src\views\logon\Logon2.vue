<template>
	<div class="flex justify-center items-center" style="height: 100vh">

		<!-- 密码修改弹窗 -->
		<q-dialog v-model="modifyPsw">
			<q-card style="width: 300px">
				<q-card-section class="bg-teal text-white">
					<div class="text-h6">密码修改</div>
				</q-card-section>

				<q-card-section class="q-gutter-md">
					<q-input filled v-model="modifyPassword.username" label="用户名（员工号首字母大写）"
						:rules="[val => val && val.length > 0 || '请输入员工号']" />
					<q-input filled v-model="modifyPassword.oldPassword" label="原密码"
						:rules="[val => val && val.length > 0 || '请输入原始密码']" />
					<q-input filled v-model="modifyPassword.newPassword" label="新密码"
						:rules="[val => val && val.length > 0 || '请输入新密码']" />
					<q-input filled v-model="modifyPassword.repeatPassword" label="重复新密码"
						:rules="[val => val && val.length > 0 || '请重复输入新密码']" />
				</q-card-section>

				<q-card-actions align="right" class="bg-white text-teal">
					<q-btn flat label="确认修改" @click="checkModifyPsw" />
					<q-btn flat label="关闭" v-close-popup />
				</q-card-actions>
			</q-card>
		</q-dialog>





		<!-- 主界面 -->
		<div class="row base-card-shadow" style="width: 60vw;min-width: 300px">

			<div class="col flex justify-center items-center">
				<q-card square style="min-width: 400px;height: 100%; width: 70%;" class="no-shadow">
					<q-card-section align="center">
						<h4 class="text">南京南厂数字化平台</h4>
						<!-- 用户名 -->
						<q-input class="logon-input" clearable standout="bg-cyan text-white" bottom-slots
							v-model="username" label="账号">
							<template v-slot:prepend>
								<q-icon name="account_circle" />
							</template>
						</q-input>
						<!-- 密码 -->
						<q-input class="logon-input" standout="bg-cyan text-white" bottom-slots v-model="password"
							label="密码" :type="isPwd ? 'password' : 'text'" hint="">
							<template v-slot:prepend>
								<q-icon name="vpn_key" />
							</template>
							<!-- <template v-slot:append>
								<q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer"
									@click="isPwd = !isPwd" />
							</template> -->

						</q-input>
						<div class="float-right">
							<q-checkbox keep-color v-model="rememberPsw" label="记住密码" color="cyan" />
						</div>


						<!-- 登录按钮 -->
						<q-btn :loading="loading" class="logon-btn bg-logon-card-input" text-color="white" unelevated
							label="" style="font-size: large;" @click="logon">登 录 系 统
						</q-btn>
						<div class="row" style="margin-bottom: 20px;">
							<q-btn flat label="忘记密码" @click="alert" />
							<q-btn outline label="修改密码" @click="modifyPsw = !modifyPsw" />
							<q-space></q-space>
							<q-btn style="width: 200px;" @click="switchNJN()" color="secondary" label="切换到南京北厂" />
						</div>
						<p class="text-grey" align="left"></p>
					</q-card-section>
				</q-card>
			</div>
		</div>
	</div>
</template>

<script>
import { json } from 'body-parser';
import CryptoJS from 'crypto-js';
const encrypt = (data, key) => {
	const encryptedData = CryptoJS.AES.encrypt(JSON.stringify(data), key).toString();
	return encryptedData;
};
// 解密
const decrypt = (encryptedData, key) => {
	// console.log('encryptedData',encryptedData)
	// const decryptedData = CryptoJS.AES.decrypt(encryptedData, key).toString(CryptoJS.enc.Utf8);
	// console.log('decryptedData',decryptedData)
	// return JSON.parse(decryptedData);
	const cipherParams = CryptoJS.lib.CipherParams.create({ ciphertext: CryptoJS.enc.Hex.parse(encryptedData) });
	console.log('cipherParams', cipherParams)
	const decryptedData = CryptoJS.AES.decrypt(cipherParams, key).toString(CryptoJS.enc.Utf8);
	console.log('decryptedData', decryptedData)
	return JSON.parse(decryptedData);
};
export default {
	name: 'Logon',
	data() {
		return {
			rememberPsw: false,
			isPwd: true,
			username: '',
			password: '',
			loading: false,
			modifyPsw: false,
			modifyPassword: {
				username: null,
				oldPassword: null,
				newPassword: null,
				repeatPassword: null
			}
		}
	},
	created() {

	},


	mounted(e) {
		var _this = this
		const rememberC = localStorage.getItem('rememberC')
		if (rememberC) {
			const decrypted = CryptoJS.AES.decrypt(rememberC, 'secretKey').toString(CryptoJS.enc.Utf8)
			const [username, password] = decrypted.split('|')
			console.log(username, password)
			_this.username = username
			_this.password = password
			_this.rememberPsw = true
		}
		console.log('e', e)

	},

	methods: {
		switchNJN() {
			window.location.href = 'http://*************';
		},
		alert() {
			this.$q.dialog({
				title: '忘记密码',
				message: '如您忘记密码，请联系工厂DM Leader'
			}).onOk(() => {
				// console.log('OK')
			}).onCancel(() => {
				// console.log('Cancel')
			}).onDismiss(() => {
				// console.log('I am triggered on both OK and Cancel')
			})
		},
		async checkModifyPsw() {
			var _this = this
			_this.modifyPassword.username = _this.modifyPassword.username.toUpperCase()
			if (_this.modifyPassword.newPassword != _this.modifyPassword.repeatPassword) {
				_this.$q.notify({
					icon: 'warning',
					message: '新密码和重复新密码不匹配，请重新输入',
					color: 'red',
					position: 'top',
					timeout: 1500
				})
			} else {
				const {
					data: res
				} = await _this.$http.post('approve/modifyPsw', _this.modifyPassword)
				console.log('res', res)

				_this.$q.notify({
					icon: res.msg == '修改密码成功' ? 'insert_emoticon' : 'warning',
					message: res.msg == '修改密码成功' ? '修改密码成功。请关闭此页' : '修改密码失败！',
					color: res.msg == '修改密码成功' ? 'green' : 'red',
					position: 'top',
					timeout: 1500
				})
			}
		},
		logon() {
			this.loading = !this.loading
			this.getUser()
		},
		handleFinish(e) {
			this.isLottieF = e

		},
		getUser() {
			var _this = this
			const query = {
				// url: 'http://*************:3000/approve/login',
				url: 'http://localhost:3000/approve/login',
				type: 'FORM',
				data: {
					account: _this.username,
					password: _this.password
				},

			}
			_this.$fetchData(query).then(res => {
				console.log(res.data)
				if (res.data.msg === '登陆成功') {
					const lt = setTimeout(() => {
						sessionStorage.setItem('access_token', '**********')
						localStorage.setItem('username', res.data.username.Employee_Name)
						localStorage.setItem('mill', res.data.username.Mill)
						localStorage.setItem('account', res.data.username.Employee_ID)
						localStorage.setItem('user_role', res.data.Access_Group)
						localStorage.setItem('Position', res.data.username.Position)
						localStorage.setItem('Related_Line', res.data.username.Related_Line)
						if (_this.rememberPsw) {
							_this.saveCredentials()
						}
						_this.$router.push('/')
						_this.$q.notify({
							icon: 'insert_emoticon',
							message: 'hi，' + res.data.username.Employee_Name + ' 欢迎回来',
							color: 'green',
							position: 'top',
							timeout: 1500
						})
						clearTimeout(lt)
						_this.loading = !_this.loading
					}, Math.random() * 3000)
				} else {
					_this.loading = !_this.loading
					_this.$q.notify({
						icon: 'announcement',
						message: '账号错误',
						color: 'red',
						position: 'top',
						timeout: 1500
					})
				}
			}).catch(error => {
				console.log(error)
			})
		},

		saveCredentials() {
			var _this = this
			const encryptedPassword = CryptoJS.AES.encrypt(`${_this.username}|${_this.password}`, 'secretKey').toString();
			localStorage.setItem('rememberC', encryptedPassword);
		},
	}
}
</script>

<style scoped>
.logon-btn {
	font-size: large;
	margin-top: 0px;
	margin-bottom: 20px;
	width: 100%;
}

.bg-logon-card-input {
	background: linear-gradient(to right, #36d1dc 1%, #5b86e5 99%);
	transition: all 0.3s ease-in-out;
	background-size: 200% auto;
}

.bg-logon-card-input:hover {
	background-position: right center;
	box-shadow: 0 12px 20px -11px #5b86e5;
}
</style>
