<template>
    <div>
        <q-dialog v-model="confirm" persistent>
            <q-card>
                <q-card-section class="row items-center">
                    <q-avatar icon="report_problem" color="primary" text-color="white" />
                    <span class="q-ml-sm">您是否取消{{ selectedApplication }}的申请。</span>
                </q-card-section>

                <q-card-actions align="right">
                    <q-btn flat label="取消" color="primary" v-close-popup />
                    <q-btn flat label="确定" color="primary" @click="cancelAward" />
                </q-card-actions>
            </q-card>
        </q-dialog>

        <q-dialog v-model="crewCancelPrompt">
            <q-card>
                <q-card-section class="row items-center">
                    <q-avatar icon="report_problem" color="primary" text-color="white" />
                    <span v-if="selectedCrew" class="q-ml-sm">您是否取消{{ selectedCrew.row.Employee_Name }}的积分。</span>
                </q-card-section>
                <q-card-section>
                    <div class="q-gutter-sm">
                        <template v-for="value in award_type">
                            <q-checkbox v-model="clearAward" :val="value" :label="value" color="teal" />
                        </template>
                    </div>
                </q-card-section>
                <q-card-actions align="right">
                    <q-btn flat label="取消" color="primary" v-close-popup />
                    <q-btn flat label="确定" color="primary" @click="applyClearCrewAward" />
                </q-card-actions>
            </q-card>
        </q-dialog>

        <q-dialog v-model="secondDialog" transition-show="flip-down">
            <q-card style="width: 800px; max-width: 100vw;">
                <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                    <div class="text-h6 ">{{ selectedApplication }}积分申请</div>
                    <q-space />
                    <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                </q-card-section>
                <q-card-section class="q-pa-md q-gutter-sm" horizontal>
                    <!-- <q-card> -->
                    <q-card-section>
                        <template v-for="award in awardList.column">
                            <div v-if="award.Award_Type === selectedApplication">
                                <template v-for="(value, key) in JSON.parse(award.Award_Param)">
                                    <div style="margin-bottom: 15px; " class=" q-gutter-sm row">
                                        <div class="text-bold"
                                            :style="value == 'imageUpload' ? 'height:150px;line-height: 150px;width:100px' : 'height:50px;line-height: 50px;width:100px'">
                                            {{ key }}
                                        </div>
                                        <template
                                            v-if="value == 'imageUpload' && awardList.awardBase[selectedApplication][key]['value'] == ''">
                                            <q-uploader batch multiple auto-upload
                                                :url="selectedApplication == '0Delay' ? 'http://*************:3001/upload?system=Odelay' : 'http://*************:3001/upload?system=GradeChange'"
                                                style="max-width: 200px;height:150px" @uploaded="handleUploadSuccess" />
                                        </template>
                                        <template
                                            v-if="value == 'imageUpload' && awardList.awardBase[selectedApplication][key]['value'] != ''">
                                            <q-btn class="text-bold btn-fixed-width" color="primary"
                                                style="min-width: 200px; max-width: 300px;" icon="cloud_download"
                                                @click="downloadFile(awardList.awardBase[selectedApplication][key]['value'])">{{
                                                    awardList.awardBase[selectedApplication][key]['value'].split('/')[4]
                                                }}</q-btn>
                                        </template>

                                        <q-select v-if="value == 'dropDown'" color="teal" filled
                                            v-model="awardList.awardBase[selectedApplication][key]['value']"
                                            :options="awardList.awardBase[selectedApplication][key]['array']"
                                            style="min-width: 200px; max-width: 300px;height:50px" dense>
                                        </q-select>
                                        <q-input v-if="value == 'input'" filled
                                            style="min-width: 200px; max-width: 300px;height:50px"
                                            v-model="awardList.awardBase[selectedApplication][key]['value']" dense />
                                        <q-separator inset />
                                    </div>
                                </template>
                            </div>
                        </template>
                        <!-- <q-btn @click="applicatAward">确认</q-btn> -->
                    </q-card-section>

                    <q-separator vertical />

                    <q-card-section>
                        <div class="q-gutter-y-md column" style="width: 250px" v-if="selectedApplication === '换型'">
                            <q-input filled v-model="selected[0]['换型前']" dense stack-label label="换型前产品" readonly />
                            <q-input filled v-model="selected[0]['换型后']" dense stack-label label="换型前产品" readonly />
                            <q-input filled v-model="selected[0]['换型开始时间']" dense stack-label label="换型开始时间" readonly />
                            <q-input filled v-model="selected[0]['换型结束时间']" dense stack-label label="换型结束时间" readonly />
                            <q-input filled v-model="selected[0]['换型时间']"
                                :bg-color="selected[0]['换型时间'] <= QCO_Target_Data.time_base ? 'light-green-5' : 'red-5'"
                                dense stack-label label="实际换型时间" readonly>
                                <!-- <template v-slot:prepend>
									<q-icon :name="selected[0]['换型时间']<=QCO_Target_Data.time_base?'check_box':'disabled_by_default'" 
											:color="selected[0]['换型时间']<=QCO_Target_Data.time_base?'positive':'red'" />
								</template> -->
                            </q-input>
                            <q-input filled v-model="QCO_Target_Data.time_base" dense stack-label label="目标换型时间" readonly />
                            <q-input filled v-model="selected[0]['换型废品']"
                                :bg-color="selected[0]['换型废品'] <= QCO_Target_Data.waste_base ? 'light-green-5' : 'red-5'"
                                dense stack-label label="实际换型废品" readonly>
                                <!-- <template v-slot:prepend>
									<q-icon :name="selected[0]['换型废品']<=QCO_Target_Data.waste_base?'check_box':'disabled_by_default'" 
											:color="selected[0]['换型废品']<=QCO_Target_Data.waste_base?'positive':'red'" />
								</template> -->
                            </q-input>
                            <q-input filled v-model="QCO_Target_Data.waste_base" dense stack-label label="目标换型废品"
                                readonly />
                        </div>

                        <!-- {{ selected[0] }} -->
                    </q-card-section>


                </q-card-section>

                <q-card-actions class="bg-white text-teal">
                    <q-space />
                    <q-btn style="width:150px" color="primary" label="统一分配" @click="applicatAward('统一分配')" :disable="btnModifyEnable" />
                    <q-btn style="width:150px" color="secondary" label="按职位分配" @click="applicatAward('按职位分配')" :disable="btnModifyEnable" />
                    <q-btn style="width:150px" color="red" label="取消申请" @click="gotoCancel" :disable="btnModifyEnable" />
                </q-card-actions>
            </q-card>
        </q-dialog>

        <q-dialog v-model="firstDialog" transition-show="flip-down">
            <q-card style="width: 1200px; max-width: 100vw;">
                <transition appear enter-active-class="animated fadeIn" leave-active-class="animated fadeOut">
                    <div>
                        <q-card-section>
                            <q-card>
                                <!-- 生产数据列表 -->
                                <q-table :data="productRow_bySingle" :columns="productColumns_bySingle"
                                    :pagination.sync="myPagination" :title="selectedProductionRow" :selected.sync="selected"
                                    selection="single" row-key="Link" @selection="handleSelection">
                                    <template v-slot:top="props">
                                        <div class="text-h5">{{ selectedProductionRow + "|" +
                                            selectedProductByShift['row']['审批状态'] }}</div>
                                        <q-space />
                                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                                    </template>
                                    <template v-slot:header="props">
                                        <q-tr :props="props">
                                            <q-th class="bg-primary text-white">
                                                选择
                                            </q-th>

                                            <q-th v-for="col in props.cols" :key="col.name" :props="props">
                                                {{ col.name }}
                                            </q-th>
                                        </q-tr>
                                    </template>
                                    <template v-slot:body="props">
                                        <q-tr :props="props">
                                            <q-checkbox :value="props.selected"
                                                @input="(val, evt) => { Object.getOwnPropertyDescriptor(props, 'selected').set(val, evt) }" />
                                            <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                                <template v-if="typeof col.value === 'string'">
                                                    <q-icon v-if="col.value.split('|')[0] === '已申请'" name="done"
                                                        color="positive" size="30px" />
                                                    <template v-if="col.value.split('|')[0] != '已申请'">
                                                        {{ col.value }}
                                                    </template>
                                                </template>
                                                <template v-if="typeof col.value !== 'string'">
                                                    {{ col.value }}
                                                </template>


                                            </q-td>
                                        </q-tr>
                                    </template>

                                    <template v-slot:bottom>
                                        <template v-for="award in awardList.column">

                                            <q-btn
                                                :color="selected.length == 0 ? 'grey-4' :
                                                    (award.Award_Type.includes('换型')  && selected[0]['换型前'] == null) ||
                                                        (!award.Award_Type.includes('换型') && award_flag[award.Award_Type] > 0 && !Object.prototype.hasOwnProperty.call(selected[0], award.Award_Type)) ||
                                                        selectedProductByShift['row']['审批状态'] == 'Approved' ? 'grey-4' : 'primary'"
                                                :label="award.Award_Type + '申请'" class="q-ml-sm"
                                                :disable="selected.length == 0 ? true :
                                                    (award.Award_Type.includes('换型') && selected[0]['换型前'] == null) ||
                                                        (!award.Award_Type.includes('换型') && award_flag[award.Award_Type] > 0 && !Object.prototype.hasOwnProperty.call(selected[0], award.Award_Type)) ||
                                                        selectedProductByShift['row']['审批状态'] == 'Approved' ? true : false"
                                                @click="awardApplication_show(award.Award_Type)" />
                                        </template>
                                        <q-space />
                                        <q-btn v-if="selectedProductByShift['row']['审批状态'] === 'Approved'" color="purple"
                                            style="width: 150px" @click="Award_Cancel_approval()" :disable="btnEnable">取消审批</q-btn>

                                    </template>
                                </q-table>

                            </q-card>
                        </q-card-section>

                        <q-card-section>

                            <q-card>
                                <!-- 人员列表 -->
                                <q-table :data="crewRow_bySingle" :pagination.sync="myPagination"
                                    :columns="crewColumns_bySingle">
                                    <template v-slot:header="props">

                                        <q-tr :props="props">
                                            <q-th  auto-width class="bg-primary text-white">积分清空</q-th>
                                            <q-th v-for="col in props.cols" :key="col.name" :props="props">
                                                {{ col.name }}
                                            </q-th>
                                        </q-tr>
                                    </template>
                                    <template v-slot:body="props">

                                        <q-tr :props="props">
                                            <q-td auto-width>
                                                <q-icon name="delete" size="30px" color="blue-grey" v-if="selectedProductByShift['row']['审批状态'] !== 'Approved' || btnModifyEnable=='false'" @click="clearCrewAward(props)"/>
                                                <!-- <q-btn v-if="selectedProductByShift['row']['审批状态'] !== 'Approved'" color="purple" size="md" icon="delete" @click="clearCrewAward(props)" :disable="btnModifyEnable"/> -->
                                            </q-td>
                                            <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                                {{ col.value }}
                                            </q-td>
                                        </q-tr>
                                    </template>
                                    <template v-slot:bottom>
                                        <!-- <template v-for="award in awardList.column">
											<q-btn class="q-ml-sm" color='primary' label="保存提交" />
										</template> -->
                                        <!-- {{ selected }} -->
                                    </template>

                                </q-table>
                            </q-card>
                        </q-card-section>
                    </div>
                </transition>
                <q-inner-loading :showing="visible">
                    <q-spinner-gears size="50px" color="primary" />
                </q-inner-loading>
            </q-card>
        </q-dialog>

        <q-dialog v-model="summaryPrompt">
            <q-card>
                <q-card-section class="row items-center q-pb-none">
                    <div class="text-h6">每日积分汇总</div>
                    <q-space />
                    <q-btn icon="close" flat round dense v-close-popup />
                </q-card-section>

                <q-card-section>
                    <q-markup-table>
                        <thead>
                            <tr>
                                <th class="text-left">奖项</th>
                                <th class="text-left">已审批金额</th>
                                <th class="text-right">未审批金额</th>
                                <th class="text-right">已审批个数</th>
                                <th class="text-right">未审批个数</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template v-for="item in summaryAwardRow">
                                <tr>
                                    <td class="text-left">{{ item[0] }}</td>
                                    <td class="text-right">{{ item[1] }}</td>
                                    <td class="text-right">{{ item[2] }}</td>
                                    <td class="text-right">{{ item[3] }}</td>
                                    <td class="text-right">{{ item[4] }}</td>
                                </tr>
                            </template>

                        </tbody>
                    </q-markup-table>
                </q-card-section>
            </q-card>
        </q-dialog>

        <div class="q-pa-md">
            <q-card class="q-pa-md q-gutter-md row items-start">
                <!-- <div class="text-h6">安全审批</div> -->
                <q-input v-model="dateRange.startDate" filled type="date" label="开始日期" stack-label dense>
                    <template v-slot:prepend>
                        <q-icon name="event" />
                    </template>
                </q-input>

                <q-input v-model="dateRange.endDate" filled type="date" label="结束日期" stack-label dense>
                    <template v-slot:prepend>
                        <q-icon name="event" />
                    </template>
                </q-input>
                <q-select color="teal" filled v-model="Line" :options="LineArray" label="机台"
                    style="min-width: 150px; max-width: 300px" dense>
                    <template v-slot:prepend>
                        <q-icon name="event" />
                    </template>
                </q-select>

                <q-btn :loading="loading" color="primary" @click="getProduction_List()" style="width: 150px">
                    查询
                    <template v-slot:loading>
                        <q-spinner-hourglass class="on-left" />
                        数据获取中...
                    </template>
                </q-btn>
                <q-btn color="purple" style="width: 150px" @click="Award_multiple_approval()" :disable="btnEnable">批量审批</q-btn>
                <q-btn color="purple" style="width: 150px" @click="summaryAward()" >汇总数据</q-btn>

            </q-card>
        </div>

        <div class="">
            <q-table :data="productRow" :columns="productColumns" row-key="name" :pagination.sync="myPagination"
                class="my-sticky-virtscroll-table" virtual-scroll :virtual-scroll-sticky-size-start="48"
                :rows-per-page-options="[0]" dense>
                <template v-slot:header="props">
                    <q-tr :props="props">
                        <q-th v-for="col in props.cols" :key="col.name" :props="props">
                            {{ col.label }}
                        </q-th>
                    </q-tr>
                </template>

                <template v-slot:body="props">
                    <q-tr :props="props" :class="props['rowIndex'] % 2 === 0 ? 'bg-grey-2 ellipsis' : ''">
                        <q-td v-for="col in   props.cols  " :key="col.name" :props="props" @click="toCrewData(props)">
                            <template v-if="col.name == '三连班' && col.value != ''">
                                <template v-for="(value, key) in JSON.parse(col.value.split('|')[2])">
                                    <template v-if="key === '三连班ID'">
                                        <q-icon v-if="value['value'] == 1" name="looks_one" color="positive" size="20px" />
                                        <q-icon v-if="value['value'] == 2" name="looks_two" color="primary" size="20px" />
                                        <q-icon v-if="value['value'] == 3" name="looks_3" color="accent" size="20px" />
                                    </template>
                                </template>
                            </template>
                            <!-- {{ col.value }}
											{{ typeof(col.value) }} -->
                            <!-- <template v-if="col.value.split('|')">
												
											</template> -->

                            {{ col.value.split('|')[0] }}

                            <template>
                                <q-icon v-if="col.value.split('|')[1] === 'Approved'" name="done" color="positive"
                                    size="17px" />
                            </template>
                            <template>
                                <q-icon v-if="col.value.split('|')[1] === 'Reject'" name="clear" color="red" size="17px" />
                            </template>

                            <template v-if="col.name == '日期'">
                                <q-badge align='middle' outline color="secondary">
                                    <span v-if="props.row['安全'] != ''">
                                        安全:<q-icon :name="props.row['安全'] == 'Yes' ? 'done' : 'clear'"
                                            :color="props.row['安全'] == 'Yes' ? 'positive' : 'red'" />
                                    </span>
                                    <span v-if="props.row['质量'] != ''">
                                        |质量:<q-icon :name="props.row['质量'] == 'Yes' ? 'done' : 'clear'"
                                            :color="props.row['质量'] == 'Yes' ? 'positive' : 'red'" />
                                    </span>
                                    <span v-if="props.row['生产'] != ''">
                                        |生产: <q-icon :name="props.row['生产'] == 'Yes' ? 'done' : 'clear'"
                                            :color="props.row['生产'] == 'Yes' ? 'positive' : 'red'" />
                                    </span>
                                </q-badge>
                            </template>

                        </q-td>
                    </q-tr>
                </template>
            </q-table>
            <!-- <q-inner-loading :showing="visibleBody">
								<q-spinner-gears size="50px" color="primary" />
							</q-inner-loading> -->
        </div>


    </div>
</template>

<script>
export default {
    //props: ['AwardPerformanceType'],
    mounted() {
        const _this = this
        _this.$nextTick(() => {
            //console.log(localStorage.getItem('lineArray').split(','))
            _this.LineArray = localStorage.getItem('lineArray').split(',')
            _this.getAward_List()
            _this.AccessToBtn(localStorage.getItem('Position'))
        })

    },
    computed: {
        slbBaseID(paramArray) {
            console.log('paramArray', paramArray)
            return ''
        }

    },
    watch: {

        firstDialog(newValue, oldValue) {
            console.log(oldValue + '->' + newValue)
            if (oldValue) {
                this.getProduction_List()
                this.selectedApplication = ''
            }
        },
        selected(newValue, oldValue) {
            console.log(newValue)
            let old
        },
        'awardList.awardBase.换型.奖励类型.value'(newValue, oldValue) {
            var _this = this
            console.log('oldValue', oldValue)
            console.log('newValue', newValue)
            _this.awardList.awardBase.换型.换型子类型.value=''
            _this.awardList.awardBase.换型.换型标准.value=''
            let newBaseArray = [0]
            console.log('_this.award_base_byAwardType[i]', _this.award_base_byAwardType)
            for (var i = 0; i < _this.award_base_byAwardType.length; i++) {
                if (_this.award_base_byAwardType[i]['Award'] == newValue) {
                    if (!newBaseArray.includes(_this.award_base_byAwardType[i]['Point'])) {
                        newBaseArray.push(_this.award_base_byAwardType[i]['Point'])
                    }
                }
            }
            let newQCOTargetArray = []
            console.log('_this.QCO_Target_Manage', _this.QCO_Target_Manage)
            for (var j = 0; j < _this.QCO_Target_Manage.length; j++) {
                if (_this.QCO_Target_Manage[j]['AwardType'] == newValue) {
                    newQCOTargetArray.push(_this.QCO_Target_Manage[j]['Type'])
                }
            }

            console.log('newQCOTargetArray', newQCOTargetArray)
            console.log('newBaseArray', newBaseArray)
            _this.awardList.awardBase.换型.换型子类型.array = newQCOTargetArray
            _this.awardList.awardBase.换型.换型标准.array = newBaseArray
        },
        'awardList.awardBase.换型.换型子类型.value'(newValue, oldValue) {
            const _this = this
            console.log('换型子类型-oldValue', oldValue)
            console.log('换型子类型-newValue', newValue)
            _this.QCO_Target_Data = {
                time_base: '',
                waste_base: ''
            }
            for (var j = 0; j < _this.QCO_Target_Manage.length; j++) {
                if (_this.QCO_Target_Manage[j]['Type'] == newValue) {
                    _this.QCO_Target_Data.time_base = _this.QCO_Target_Manage[j]['Time_Target']
                    _this.QCO_Target_Data.waste_base = _this.QCO_Target_Manage[j]['Waste_Target']
                }
            }
            console.log('_this.QCO_Target_Data', _this.QCO_Target_Data)
        }
    },

    data() {
        return {
            summaryPrompt: false,
            crewCancelPrompt: false,
            selectedCrew: false,
            selectedProductByShift: false,
            clearAward: [],
            confirm: false,
            dateRange: {
                startDate: null,
                endDate: null,
            },
            LineArray: [],
            Line: '',
            tab: 'mails',
            loading: false,
            // visibleBody: false,
            awardList: false,
            awardbase_Array: {},
            award_type: [],
            award_flag: {},
            productRow: [],
            productColumns: [{ name: '日期', label: "日期", field: "Date", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
            { name: '机台', label: "机台", field: "Line", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
            { name: '班次', label: "班次", field: "Shift", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
            { name: '换型个数', label: "换型个数", field: "hx_Count", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
            { name: '班组人员', label: "班组人员", field: "OP", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
            { name: '审批状态', label: "审批状态", field: "审批状态", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
            { name: '审批人', label: "审批人", field: "审批人", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                // { name: '生产', label: "生产", field: "生产", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
            ],
            myPagination: { rowsPerPage: 0 },
            firstDialog: false,
            secondDialog: false,
            productRow_bySingle: [],
            productRow_awardParm: [],
            productColumns_bySingle: [
                { name: '产品号', label: "产品号", field: "产品号", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: '产品描述', label: "产品描述", field: "产品描述", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: 'OEE_T', label: "OEE_T", field: "OEE_T", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: 'Waste', label: "Waste", field: "Waste", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: 'ODelay', label: "ODelay", field: "ODelay", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: '换型前', label: "换型前", field: "换型前", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: '换型后', label: "换型后", field: "换型后", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: '换型时间', label: "换型时间", field: "换型时间", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: '换型废品', label: "换型废品", field: "换型废品", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: '换型开始时间', label: "换型开始时间", field: "换型开始时间", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: '换型结束时间', label: "换型结束时间", field: "换型结束时间", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: 'Link', label: "Link", field: "Link", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
            ],
            crewRow_bySingle: [],
            crewColumns_bySingle: [
                { name: '职位', label: "职位", field: "Position", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: '员工号', label: "员工号", field: "Employee_ID", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
                { name: '姓名', label: "姓名", field: "Employee_Name", align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
            ],
            selectedProductionRow: '',
            selected: [],
            selectedApplication: '',
            visible: false,
            award_base_byAwardType: {},
            QCO_Target_Manage: [],
            QCO_Target_Data: {
                time_base: '',
                waste_base: ''
            },
            summaryAwardRow: [],
            btnEnable: true,
            btnModifyEnable: true
        }
    },

    methods: {
        async AccessToBtn(awardType) {
            this.btnEnable = await checkAccess(awardType, 'approve')
            this.btnModifyEnable = await checkAccess(awardType, 'edit')
            if(this.btnEnable==false){
                this.btnModifyEnable = false
            }
        },
        clickAward(e) {
            console.log(e)
        },
        gotoCancel() {
            this.confirm = true
        },
        cancelAward() {
            const _this = this
            console.log('取消操作')
            console.log('取消的字段', _this.awardList.awardBase[_this.selectedApplication])
            _this.confirm = false
            _this.$nextTick(() => {
                _this.awardList.awardBase[_this.selectedApplication][_this.selectedApplication + '标准']['value'] = "0"
                _this.applicatAward('取消申请')
            })

        },
        Award_multiple_approval() {
            const _this = this
            const postParam = {
                'startDate': _this.dateRange.startDate,
                'endDate': _this.dateRange.endDate,
                'Line': _this.Line,
                'modifyName': localStorage.getItem('account')
            }
            _this.$http.post('approve/Award_Update_Application_Approved', postParam).then(function (response) {
                console.log('response', response)
                if (response.data === '更新成功') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `批量审批成功`,
                        position: 'top'
                    })
                    _this.getProduction_List()
                } else {
                    _this.$q.notify({
                        type: 'negative',
                        message: `批量审批失败`,
                        position: 'top'
                    })
                }
            })

        },
        Award_Cancel_approval() {
            const _this = this
            console.log('_this.selectedProductionRow', _this.selectedProductionRow)
            const postParam = {
                'ShiftLinkid': _this.selectedProductionRow
            }
            _this.$http.post('approve/Award_Update_Application_CancelApproved_byShift', postParam).then(function (response) {
                console.log('response', response)
                if (response.data === '更新成功') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `取消审批成功`,
                        position: 'top'
                    })
                    _this.selectedProductByShift['row']['审批状态'] = ""
                } else {
                    _this.$q.notify({
                        type: 'negative',
                        message: `取消审批失败`,
                        position: 'top'
                    })
                }
            })

        },
        downloadFile(url) {
            window.open(url, '_blank');
        },
        async getAward_List() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`approve/getAward_List?Mill=${localStorage.getItem('mill')}&Award_Cycle=daily&Position=操作工`)
            console.log('getAward_List', res)
            _this.awardList = res
            _this.awardbase_Array = res.awardBase
            for (const key in res.awardBase) {
                // console.log('key',key)
                _this.award_type.splice(0, 0, key)
                const singleColumn = { name: key, label: key, field: key, align: 'left', headerClasses: 'bg-primary text-white', sortable: true }
                _this.productColumns.splice(0, 0, singleColumn)
                _this.crewColumns_bySingle.splice(0, 0, singleColumn)
                _this.productColumns_bySingle.splice(0, 0, singleColumn)
            }
            console.log('_this.award_type', _this.award_type)
        },

        async getProduction_List() {
            var _this = this
            _this[`loading`] = true
            // _this.visibleBody = true
            const {
                data: res
            } = await _this.$http.get(`approve/getAward_Production_List?startDate=${_this.dateRange.startDate}&endDate=${_this.dateRange.endDate}&Line=${_this.Line}`)
            console.log('getAward_Production_List', res)
            const additionalKeys = _this.award_type
            const resultArray = res.map(obj => {
                const newObj = Object.assign({}, obj)
                additionalKeys.forEach(key => {
                    newObj[key] = ''
                })
                return newObj
            })
            console.log('resultArray', resultArray)
            const {
                data: res1
            } = await _this.$http.get(`approve/getAward_Application_Summary_Daily?startDate=${_this.dateRange.startDate}&endDate=${_this.dateRange.endDate}&Line=${_this.Line}`)
            console.log('Award_Application_Summary_Daily', res1)
            const result = resultArray.map(item1 => {
                // 在第二个数组中查找匹配条件
                const matchedItem = res1.find(item2 =>
                    item2.Date === item1.Date && item2.Line === item1.Line && item2.Shift === item1.Shift
                );
                // 如果有匹配项，则在第一个数组中赋值相应字段的值
                if (matchedItem) {
                    for (var i = 0; i < additionalKeys.length; i++) {
                        if (matchedItem.hasOwnProperty(additionalKeys[i])) {
                            item1[additionalKeys[i]] = matchedItem[additionalKeys[i]];
                        }
                    }
                }
                return item1;
            });
            console.log('result', result);
            _this.productRow = result
            _this[`loading`] = false
            // _this.visibleBody = false
        },
        async toCrewData(row) {
            let _this = this
            if (row.row['OP'] === "") {
                console.log('OP为空')
                _this.$q.dialog({
                    title: '无法申请',
                    message: '无法申请班组成员为空的积分'
                })
                return
            }


            _this.awardList.awardBase = {}
            _this.awardList.awardBase = _this.awardbase_Array
            _this.firstDialog = true
            _this.selected = []
            _this.visible = true
            console.log('row', row)
            _this.selectedProductByShift = row
            const param = {
                Date: row.row['Date'],
                Line: row.row['Line'],
                Shift: row.row['Shift']
            }
            _this.selectedProductionRow = row.row['Date'] + "-" + row.row['Line'] + "-" + row.row['Shift']
            const {
                data: res
            } = await _this.$http.get(`approve/getAward_Application_Production_Single_List?Date=${param.Date}&Line=${param.Line}&Shift=${param.Shift}`)
            console.log('getAward_Application_Production_Single_List', res)
            const additionalKeys = _this.award_type
            const resultArray = res.crew
            const resultArray_Production = res.product
            let award_flag = {}
            award_flag = {}
            for (var k = 0; k < additionalKeys.length; k++) {
                award_flag[additionalKeys[k]] = 0
                for(var s=0;s<resultArray.length;s++){
                    resultArray[s][additionalKeys[k]] = ''
                }
                // resultArray_Production[additionalKeys[k]] = ''
            }
            console.log('resultArray_Production', resultArray_Production)
            console.log('awardFlat', award_flag)
            console.log('resultArray', resultArray)
            const {
                data: res1
            } = await _this.$http.get(`approve/getAward_Application_CrewAward_Single_Daily?Date=${param.Date}&Line=${param.Line}&Shift=${param.Shift}`)
            console.log('getAward_Application_CrewAward_Single_Daily', res1)
            const result = resultArray.map(item1 => {
                // 在第二个数组中查找匹配条件
                const matchedItem = res1.find(item2 =>
                    item2.Employee_ID === item1.Employee_ID
                );
                // 如果有匹配项，则在第一个数组中赋值相应字段的值
                if (matchedItem) {
                    for (var i = 0; i < additionalKeys.length; i++) {
                        // console.log('award', additionalKeys[i])
                        if (matchedItem.hasOwnProperty(additionalKeys[i])) {
                            item1[additionalKeys[i]] = matchedItem[additionalKeys[i]];
                        }
                    }
                }
                return item1;
            });
            console.log('result', result)

            _this.crewRow_bySingle = result
            _this.productRow_awardParm = res.award
            console.log('_this.productRow_awardParm', _this.productRow_awardParm)
            for (var i = 0; i < resultArray_Production.length; i++) {
                if (res.award.length > 0) {
                    for (var j = 0; j < res.award.length; j++) {
                        console.log(res.award[j]['Award_LinkID'].substring(0, 27))
                        if (resultArray_Production[i]['Link'] == res.award[j]['Award_LinkID'].substring(0, 27)) {
                            resultArray_Production[i][res.award[j]['Award_Type']] = '已申请|' + res.award[j]['Award_Param']
                        }
                        if (i == 0) {
                            award_flag[res.award[j]['Award_Type']] = award_flag[res.award[j]['Award_Type']] + 1
                        }

                    }
                }

            }
            _this.award_flag = award_flag
            _this.productRow_bySingle = resultArray_Production
            console.log('awardFlat', award_flag)
            _this.visible = false
        },
        async awardApplication_show(award_Type) {
            const _this = this
            console.log('award_type', award_Type)
            console.log('_this.selected', _this.selected)
            _this.selectedApplication = award_Type
            _this.secondDialog = true
            const {
                data: res
            } = await _this.$http.get(`approve/getAward_Base?award_Type=${award_Type}&Line=${_this.Line}&department=生产`)
            console.log('getAward_Base', res)
            _this.award_base_byAwardType = res
            const uniquePoints = [...new Set(res.map(item => item.Point))];
            // if (award_Type === '三连班') {
            // 	_this.awardList.awardBase['三连班']['三连班ID']['array'] = [1, 2, 3]
            // }
            // _this.awardList.awardBase[res[0]['Award_Type']][award_Type + '标准']['value'] = ''
            _this.awardList.awardBase[res[0]['Award_Type']][award_Type + '标准']['array'] = [0, ...uniquePoints]
            console.log('_this.awardList.awardBase',_this.awardList.awardBase)
            const {
                data: res1
            } = await _this.$http.get(`approve/getAward_Base_dropdown?award_Type=${award_Type}&mill=${localStorage.getItem('mill')}`)
            console.log('getAward_Base_dropdown', res1)
            let keys = Object.keys(_this.awardList.awardBase[award_Type]);
            for (let j = 0; j < keys.length; j++) {
                let key = keys[j]
                if (key.indexOf("标准") === -1) {
                    //console.log('找到了！', key)
                    _this.awardList.awardBase[award_Type][key]['array'] = res1[key]
                    _this.awardList.awardBase[award_Type][key]['value'] = ''
                } else {
                    _this.awardList.awardBase[award_Type][key]['value'] = ''
                }
            }

            const award_Linkid = _this.selected[0]['Link'] + '-' + award_Type
            const awardParam = _this.productRow_awardParm
            console.log('awardParam', awardParam)
            console.log('_this.awardList', _this.awardList)
            if (awardParam.length > 0) {
                for (var i = 0; i < awardParam.length; i++) {
                    if (awardParam[i]['Award_LinkID'] === award_Linkid) {
                        // _this.awardList.awardBase[res[0]['Award_Type']][award_Type + '标准']['value'] = parmam[award_Type + '标准']['value']
                        if (awardParam[i]['Award_Type'] === award_Type) {
                            const parmam = JSON.parse(awardParam[i]['Award_Param'])
                            console.log('parmam', parmam)
                            _this.awardList.awardBase[award_Type] = parmam
                        }
                    }
                }
            }
            for (let j = 0; j < keys.length; j++) {
                let key = keys[j]
                if (key.indexOf("标准") === -1) {
                    _this.awardList.awardBase[award_Type][key]['array'] = res1[key]
                }
            }
            if (award_Type == '换型') {
                const queryDate = _this.selected[0]['Link'].substring(0, 10)
                const {
                    data: res_xhAward
                } = await _this.$http.get(`approve/getAward_Base_QCO_Target_Manage?Line=${_this.Line}&Date=${queryDate}`)
                console.log('getAward_Base_QCO_Target_Manage', res_xhAward)
                _this.QCO_Target_Manage = res_xhAward
            }



            console.log('_this.awardList_update', _this.awardList.awardBase)

        },

        async applicatAward(type) {
            const _this = this
            const award_Type = _this.selectedApplication
            if (award_Type === '换型') {
                if (_this.selected[0]['换型时间'] > _this.QCO_Target_Data.time_base || _this.selected[0]['换型废品'] > _this.QCO_Target_Data.waste_base) {
                    _this.$q.notify({
                        type: 'negative',
                        message: `换型目标未达标，无法申请`,
                        position: 'top'
                    })
                    return
                }
            }

            const contentCheck_status = await checkContent(_this.awardList.awardBase[award_Type], award_Type)
            if (contentCheck_status == 'No') {
                _this.$q.notify({
                    type: 'negative',
                    message: `申请错误，请填写完整相关信息`,
                    position: 'top'
                })
                return
            } else {
                _this.$q.notify({
                    type: 'positive',
                    message: `申请成功！`,
                    position: 'top'
                })
            }
            console.log('_this.selected', _this.selected)
            console.log('_this.awardList', _this.awardList)
            console.log('_this.crewRow_bySingle', _this.crewRow_bySingle)
            // if (type==)
            const awrd_Linkid = _this.selected[0]['Link'] + '-' + award_Type
            let keys = Object.keys(_this.awardList.awardBase[award_Type]);
            for (let j = 0; j < keys.length; j++) {
                let key = keys[j]
                if (key.indexOf("标准") === -1) {
                    //console.log('找到了！', key)
                    _this.awardList.awardBase[award_Type][key]['array'] = []
                }
            }


            const award_Param = JSON.stringify(_this.awardList.awardBase[award_Type])
            const paramData = {
                award_Linkid: awrd_Linkid,
                award_Type: award_Type,
                award_Param: award_Param,
                modifyName: localStorage.getItem('account'),
                award_base: _this.awardList.awardBase[award_Type][award_Type + '标准']['value'],
                type: type
            }
            console.log('paramData', paramData)
            console.log('paramData_str', JSON.stringify(paramData))
            const {
                data: res
            } = await _this.$http.get(`approve/SP_Update_Application_Daily?param=${JSON.stringify(paramData)}`)
            console.log('SP_Update_Application_Daily', res)

            let newCrewArray = _this.crewRow_bySingle
            console.log('newCrewArray', _this.crewRow_bySingle)
            const crew_award_application_list = res
            if (crew_award_application_list.length > 0) {
                for (var i = 0; i < newCrewArray.length; i++) {
                    for (var j = 0; j < crew_award_application_list.length; j++) {
                        if (newCrewArray[i]['Employee_ID'] == crew_award_application_list[j]['Employee_ID']) {
                            //console.log(crew_award_application_list[j]['Point'])
                            newCrewArray[i][crew_award_application_list[j]['Award_Type']] = crew_award_application_list[j]['Point']
                        }
                    }
                }
            }
            for (var j = 0; j < _this.productRow_bySingle.length; j++) {
                if (_this.selected[0]['Link'] == _this.productRow_bySingle[j]['Link']) {
                    _this.productRow_bySingle[j][award_Type] = '已申请|' + _this.awardList.awardBase[award_Type]
                    _this.award_flag[award_Type] = _this.award_flag[award_Type] + 1
                }
            }
            _this.crewRow_bySingle = newCrewArray
            console.log('newCrewArray1', newCrewArray)
            console.log('_this.awardList.awardBase', _this.awardList.awardBase)
            // for ()
            _this.secondDialog = false
        },
        clearCrewAward(row) {
            const _this = this
            console.log(row)
            _this.clearAward = []
            _this.selectedCrew = row
            _this.crewCancelPrompt = true
        },
        async applyClearCrewAward() {
            const _this = this
            const crew_Row = _this.selectedCrew
            const crew_Index = crew_Row.pageIndex
            console.log('crew_Index', crew_Index)
            if (_this.clearAward.length == 0) {
                _this.$q.notify({
                    type: 'negative',
                    message: `请选择需要清空的奖励`,
                    position: 'top'
                })
                return
            }
            const postParam = {
                shiftLinkid: _this.selectedProductionRow,
                crew_Employee_ID: crew_Row.row.Employee_ID,
                clearAward: _this.clearAward,
                modifyName: localStorage.getItem('account')
            }
            _this.$http.post('approve/Award_Clear_singleCrew_Award', postParam).then(function (response) {
                console.log('response', response)
                if (response.data === '更新成功') {
                    for (var i = 0; i < _this.clearAward.length; i++) {
                        _this.crewRow_bySingle[crew_Index][_this.clearAward[i]] = 0
                        //console.log('_this.crewRow_bySingle[crew_Index][_this.clearAward[i]]',_this.crewRow_bySingle[crew_Index][_this.clearAward[i]])
                    }
                    console.log('newCrewList', _this.crewRow_bySingle)
                    console.log('clearCrew:postParam', postParam)
                    _this.$q.notify({
                        type: 'positive',
                        message: `清空奖励成功`,
                        position: 'top'
                    })
                    _this.crewCancelPrompt = false
                } else {
                    _this.$q.notify({
                        type: 'negative',
                        message: `清空人员信息失败`,
                        position: 'top'
                    })
                }
            })




        },
        summaryAward() {
            const _this = this
            const Award_List = _this.award_type
            console.log('Award_List', Award_List)
            let summaryRow = []
            const Award_Applicat_List = _this.productRow
            const Award_Summary_Row = []

            console.log('Award_Applicat_List', Award_Applicat_List)

            for (var i = 0; i < Award_List.length; i++) {
                let Award_Summary_Single_Row = {}
                Award_Summary_Single_Row = [Award_List[i], 0, 0, 0, 0]

                for (var j = 0; j < Award_Applicat_List.length; j++) {
                    if (Award_Applicat_List[j][Award_List[i]] != "" && Award_Applicat_List[j]['审批状态'] == "Approved") {
                        Award_Summary_Single_Row[1] += parseInt(Award_Applicat_List[j][Award_List[i]].split('|')[0])
                        Award_Summary_Single_Row[3] += 1
                    }
                    if (Award_Applicat_List[j][Award_List[i]] != "" && Award_Applicat_List[j]['审批状态'] == "") {
                        console.log(Award_Applicat_List[j][Award_List[i]].split('|')[0])
                        Award_Summary_Single_Row[2] += parseInt(Award_Applicat_List[j][Award_List[i]].split('|')[0])
                        Award_Summary_Single_Row[4] += 1
                    }
                }
                Award_Summary_Row.push(Award_Summary_Single_Row)
            }
            console.log('Award_Summary_Row', Award_Summary_Row)
            _this.summaryAwardRow = Award_Summary_Row
            _this.summaryPrompt = true
        },

        handleUploadSuccess(response) {
            console.log('response', response)
            var _this = this
            var responseFileName = response['files'][0]['name']
            var responseURL = response['files'][0]['xhr']['responseText']
            _this.awardList.awardBase[_this.selectedApplication][_this.selectedApplication + '照片']['value'] = responseURL
            _this.prompt_uploadFile = false
        },

        handleSelection({ rows, added, evt, rowIndex }) {
            // ignore selection change from header of not from a direct click event
            if (rows.length !== 1 || evt === void 0) {
                return
            }

            const { oldSelectedRow } = this
            const [newSelectedRow] = rows
            const { ctrlKey, shiftKey } = evt

            if (shiftKey !== true) {
                this.oldSelectedRow = newSelectedRow
            }

            // wait for the default selection to be performed
            this.$nextTick(() => {
                if (shiftKey === true) {
                    const tableRows = this.$refs.table.filteredSortedRows
                    let firstIndex = tableRows.indexOf(oldSelectedRow)
                    let lastIndex = tableRows.indexOf(newSelectedRow)

                    if (firstIndex < 0) {
                        firstIndex = 0
                    }

                    if (firstIndex > lastIndex) {
                        [firstIndex, lastIndex] = [lastIndex, firstIndex]
                    }

                    const rangeRows = tableRows.slice(firstIndex, lastIndex + 1)

                    this.selected = added === true
                        ? this.selected.concat(rangeRows.filter(row => this.selected.includes(row) === false))
                        : this.selected.filter(row => rangeRows.includes(row) === false)
                }
                else if (ctrlKey !== true && added === true) {
                    console.log()
                    console.log('newSelectedRow', rows)
                    this.selected = [newSelectedRow]
                }
            })
        }
    }
}

function checkContent(data, award_Type) {
    return new Promise((resolve, reject) => {
        let keys = Object.keys(data);
        let status = 'Yes'
        for (let j = 0; j < keys.length; j++) {
            let key = keys[j]
            if (key !== '换型照片') {
                if (data[key]['value'] === "") {
                    status = 'No'
                }
            }
        }
        resolve(status)
    })
}
function checkAccess(award_type, btnType) {
    return new Promise((resolve, reject) => {
        const userRole = localStorage.getItem('user_role')
        const userPosition = localStorage.getItem('Position')
        let result = true
        console.log('userRole', userRole)
        console.log('userPosition', userPosition)
        if (btnType === 'approve') {
            if ((userRole.indexOf('积分系统_审批') !== -1 && userPosition.indexOf('资产主管')!==-1 )|| userRole.indexOf('Admin') !== -1) {
                console.log('有权限')
                result = false
            }
            else {
                console.log('没权限')
            }
        }
        if  (btnType === 'edit') {
            if ((userRole.indexOf('积分系统_编辑') !== -1) || userRole.indexOf('Admin') !== -1) {
                console.log('有权限')
                result = false
            }
            else {
                console.log('没权限')
            }
        }

        console.log(result)
        resolve(result)
    })
}

</script>

<style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */
  height: 700px
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0
</style>





















