let moment = require('moment')
var sqlexec = require('./sql/sqlDailyRun')
var schedule = require('node-schedule')


function product_D() {
    return new Promise((resolve, reject) => {
        var pro = new sqlexec()
        pro.productionData_D(function (result) {
            // console.log(result)
            resolve(result)
        })
    })
} 
function Delay_D() {
    return new Promise((resolve, reject) => {
        var pro = new sqlexec()
        pro.Delay_D(function (result) {
            // console.log(result)
            resolve(result)
        })
    })
}

function product_N() {

    return new Promise((resolve, reject) => {
        var pro = new sqlexec()
        pro.productionData_N(function (result) {
            //console.log(result)
            resolve(result)
        })
    })
}
function Delay_N() {
    return new Promise((resolve, reject) => {
        var pro = new sqlexec()
        pro.Delay_N(function (result) {
            // console.log(result)
            resolve(result)
        })
    })
}

function nonProduction() {

    return new Promise((resolve, reject) => {
        var pro = new sqlexec()
        pro.nonProduction(function (result) {
            //console.log(result)
            resolve(result)
        })
    })
}
function topLoss_weekly() {

    return new Promise((resolve, reject) => {
        var pro = new sqlexec()
        pro.topLoss_weekly(function (result) {
            //console.log(result)
            resolve(result)
        })
    })
}

function ideabank_daily_NJS() {

    return new Promise((resolve, reject) => {
        var pro = new sqlexec()
        pro.ideabank_daily_NJS(function (result) {
            //console.log(result)
            resolve(result)
        })
    })
}
function ideabank_daily_NJN() {

    return new Promise((resolve, reject) => {
        var pro = new sqlexec()
        pro.ideabank_daily_NJN(function (result) {
            //console.log(result)
            resolve(result)
        })
    })
}

function ideabank_weekly() {

    return new Promise((resolve, reject) => {
        var pro = new sqlexec()
        pro.ideabank_weekly(function (result) {
            //console.log(result)
            resolve(result)
        })
    })
}

function getMonthlyPeople() {

    return new Promise((resolve, reject) => {
        var pro = new sqlexec()
        pro.getMonthlyPeople(function (result) {
            //console.log(result)
            resolve(result)
        })
    })
}

function getMonthlyMaintenancePeople() {

    return new Promise((resolve, reject) => {
        var pro = new sqlexec()
        pro.getMonthlyMaintenancePeople(function (result) {
            //console.log(result)
            resolve(result)
        })
    })
}

function updateSemiProduction() {

    return new Promise((resolve, reject) => {
        var pro = new sqlexec()
        pro.updateSemiProduction(function (result) {
            //console.log(result)
            resolve(result)
        })
    })
}




// async function sendMail_D() {
//     let data = await product()
//     // console.log(data)



function scheduleMail() {

    schedule.scheduleJob('00 00 10 * * *', function () {
        product_N()
        Delay_N()
        nonProduction()
        getMonthlyMaintenancePeople()
        getMonthlyPeople()

    })
    schedule.scheduleJob('00 00 09 * * *', function () {
        ideabank_daily_NJS()
        ideabank_daily_NJN()
        

    })
    schedule.scheduleJob('00 30 21 * * *', function () {
        product_D()
        Delay_D()
        // console.log("N")
    })
    let rule = new schedule.RecurrenceRule();
    rule.dayOfWeek = 1;
    rule.hour = 11;
    rule.minute = 30;
    rule.second = 0;
    let job = schedule.scheduleJob(rule, () => {
        console.log(new Date());
        topLoss_weekly()
        ideabank_weekly()
    });

    let rule1 = new schedule.RecurrenceRule();
    rule1.minute =[0, 5, 10, 15, 40, 58];
    rule1.second = 0;
    // 启动任务
    let job = schedule.scheduleJob(rule, () => {
    console.log(new Date());
    updateSemiProduction()
  });

}


scheduleMail()






