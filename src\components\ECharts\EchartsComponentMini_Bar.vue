<template>
	<div>
		<div style="width:100%;height:300px;" :id="echarts" class="echarts" ref="echarts"></div>
	</div>
</template>

<script>
	// 引入echarts
	import echarts from 'echarts'
	export default {
		name: 'EchartsComponents',
		props: {
			// 接收父组件传递过来的信息

			chartData: {
				categories: {
					type: Array,
					default: () => []
				},
				series: {
					type: Array,
					default: () => []
				},
				name: {
					type: String
				},

			}

		},
		data() {
			return {}
		},
		methods: {
			drawChart() {
				var unit = '亿'

				// console.log(this.chartData)
				const vm = this
				// 基于准备好的dom，初始化echarts实例
				var myChart = echarts.init(document.getElementById(this.echarts))
				// 绘制图表
				myChart.setOption({
					title: {
						text: this.chartData.name
					},
					grid: {
						left: '3%',
						right: '4%',
						bottom: '5%',
						containLabel: true
					},
					tooltip: {
						trigger: 'axis',
						hideDelay: 100,
						axisPointer: {
							animation: true,
							type: 'shadow'
						},
						formatter: function(datas) {
							var res = datas[0].name + '\n',
								val;
							if (datas[0].value >= 100000000 || datas[0].value <= -100000000) {
								val = (datas[0].value / 100000000).toFixed(2) + '亿';
							} else {
								val = (datas[0].value / 10000).toFixed(0) + '万';
							}
							res += datas[0].seriesName + '：' + val + '\n';
							return res;
						}
					},

					legend: {
						data: [this.chartData.series[0].name]
					},
					xAxis: {
						data: this.chartData.categories,
						axisTick: {
							//坐标轴刻度相关设置。
							show: false
						},
						// axisLabel:{
						// 	//X坐标只显示头和尾
						// 	interval:this.chartData.categories.length-2
						// }
					},
					yAxis: {
						type: 'value',
						splitNumber: 4,
						splitLine: {
							show: false
						},
						axisLine: {
							show: false
						},
						axisTick: {
							//坐标轴刻度相关设置。
							show: false
						},
						axisLabel: {
							
							show: false,

							// show: false,
							// textStyle: {
							// 	fontSize: 13
							// },
							// formatter: function(value) {
							// 	return value.toFixed(0) + unit;
							// }
						}
					},
					// dataZoom: [{
					// 		type: 'inside',
					// 		startValue: this.chartData.categories[Math.round(this.chartData.categories.length /
					// 			2, 0)],

					// 		endValue: this.chartData.categories[this.chartData.categories.length - 1]
					// 	},
					// 	{
					// 		startValue: this.chartData.categories[Math.round(this.chartData.categories.length /
					// 			2, 0)],
					// 		end: this.chartData.categories[this.chartData.categories.length - 1]
					// 	}
					// ],
					// series: this.chartData.series
					series: {

						name: this.chartData.series[0].name,
						type: this.chartData.series[0].type,
						data: this.chartData.series[0].data,
						// markPoint: {
						// 	data: [{
						// 			type: 'max',
						// 			name: '最大值'
						// 		},
						// 		{
						// 			type: 'min',
						// 			name: '最小值'
						// 		}
						// 	]
						// },
						itemStyle: {
							color: function(params) {
								// console.log(params)
								if (params.value < 0) {
									return '#00aa00'
								} else {
									return "#aa0000"
								}
							},
							// this.chartData.series[0].name==='营业收入预测'||  this.chartData.series[0].name==='净利润预测'? :'#00aaff',

						},
					}
				})
			}
		},
		computed: {
			echarts() {
				return 'echarts' + Math.random() * 100000
			}
		},
		mounted: function() {
			const vm = this
			vm.$nextTick(() => {
				vm.drawChart()
			})
		},
		created: () => {}
	}
</script>

<style scoped>
</style>
