<template>
  <div id="app">
    <router-view></router-view>
  </div>
</template>

<script>
import asyncRoutes from './router/asyncRoutes'
export default {
  name: 'App',
  // created(){
	 //  window.localStorage.clear()
	 //  console.log('已清空')
	 //  this.$router.push('/logon')
  // },
	
  beforeunload() {
    // 清空 localStorage 中的缓存
    localStorage.clear();
  },
  mounted(){
    console.log('asyncRoutes',asyncRoutes)
    console.log('this.$route.query ',this.$route.query)
  }
}
</script>

<style>
  @import "./assets/css/main.css";
  @import './assets/scss/transition.scss';
  @import "./assets/css/cimo.css";
</style>
