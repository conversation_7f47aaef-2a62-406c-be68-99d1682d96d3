<template>
  <base-content>

    <q-dialog v-model="dialog" persistent full-width>
      <q-card style="min-width: 800px">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">详细信息</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <CIManagementComponent :mode="tab" :A3_ID="selectedA3_ID" />
        </q-card-section>
      </q-card>
    </q-dialog>

    <div>

      <q-card>
        <q-tabs v-model="tab" dense class="text-grey" active-color="primary" indicator-color="primary" align="justify"
          narrow-indicator>
          <q-tab name="input" label="录入" />
          <q-tab name="query" label="查询" />
          <q-tab name="myReport" label="我的报告" />
        </q-tabs>

        <q-separator />

        <q-tab-panels v-model="tab" animated>
          <q-tab-panel name="input">
            <CIManagementComponent mode="create" />
          </q-tab-panel>

          <q-tab-panel name="query">

            <q-table v-if="rows" title="所有报告" :data="rows" :columns="columns" row-key="A3_ID" @row-click="onRowClick" />
          </q-tab-panel>

          <q-tab-panel name="myReport">

            <q-table v-if="rows" title="我的报告" :data="rows" :columns="columns" row-key="A3_ID" @row-click="onRowClick" />
          </q-tab-panel>
        </q-tab-panels>
      </q-card>


    </div>
  </base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import CIManagementComponent from './CI_Management_Component.vue'

export default {
  name: 'CI_Management',
  components: {
    BaseContent,
    CIManagementComponent,
  },
  data() {
    return {
      tab: 'input',
      rows: false,
      columns: [
        { name: 'ID', label: 'A3_ID', field: 'ID', sortable: true },
        { name: 'Department', label: '部门', field: 'Department', sortable: true },
        { name: 'Machine', label: '设备', field: 'Machine', sortable: true },
        { name: 'Unit', label: '单元', field: 'Unit', sortable: true },
        { name: 'Category', label: '问题类别', field: 'Category', sortable: true },
        { name: 'TeamLeader', label: '团队负责人', field: 'TeamLeader', sortable: true },
        { name: 'ProblemDescription', label: '问题描述', field: 'ProblemDescription', sortable: true },
      ],
      dialog: false,
      selectedA3_ID: ''
    }
  },
  watch: {
    tab: function (val) {
      this.rows = false
      this.getA3Report(val);
    },
    dialog:function(val){
      if(!this.dialog && this.tab=='myReport'){
        this.getA3Report(this.tab);
      }
    }

  },

  mounted() {
    // 检查URL参数，处理直接访问的情况
    const urlParams = new URLSearchParams(window.location.search)
    const id = this.$route.query.id
    const mode = this.$route.query.mode
    const mail = this.$route.query.mail
    const tab = this.$route.query.tab

    if (id && mode && mail === 'true') {
      // 如果是通过邮件链接直接访问
      this.selectedA3_ID = id
      this.tab = mode
      this.dialog = true
    } else if (tab) {
      // 如果指定了tab参数
      if (['input', 'query', 'myReport'].includes(tab)) {
        this.tab = tab
      }
    }
  },

  methods: {
    getA3Report(tab) {
      const _this = this
      if (tab == 'input') {
        return
      }
      const url = tab == 'query' ? 'ciProject/getA3RecordByAll' :`ciProject/getA3RecordByPersonal?account=${localStorage.getItem('account')}`
      this.$http.get(url).then(function (response) {
        console.log('response', response)

        if (response.data.success) {
          _this.rows=response.data.data;
        } else {
          _this.$q.notify({
            type: 'negative',
            message: response.data.message || '获取A3报告失败',
            position: 'top'
          })
        }
      }).catch(function (error) {
        console.error('获取A3报告失败:', error)
        _this.$q.notify({
          type: 'negative',
          message: '获取A3报告失败，请稍后重试',
          position: 'top'
        })
      })
    },
    onRowClick(evt, row) {
      this.selectedA3_ID = row.ID;
      console.log('this.selectedA3_ID:', this.selectedA3_ID)
      // this.getA3Report_ByID(row.A3_ID);
      this.dialog = true;
    },

  }
}
</script>

<style scoped>
</style>
