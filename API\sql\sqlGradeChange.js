const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.lineList = function (callBack) {
    sqlexec.prototype.
        _query("select line from GradeChange_Task_Base group by line order by line asc", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.productNameList = function (line, callBack) {
    sqlexec.prototype.
        _query("select product_Name from GradeChange_Task_Base where line=" + line + " group by product_Name order by product_Name asc", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.taskContent = function (line, product1, product2, callBack) {
    console.log("exec SP_API_GradeChange_TaskList " + line + "," + product1 + "," + product2)
    sqlexec.prototype.
        _query("exec SP_API_GradeChange_TaskList " + line + "," + product1 + "," + product2, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.gradeChange_ProductName = function (line, callBack) {
    //console.log("exec SP_API_GradeChange_TaskList " + line + "," + product1 + "," + product2)
    sqlexec.prototype.
        _query(`select distinct product_Name from [GradeChange_Task_Base] where line='${line}'`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.gradeChange_List = function (line,sku, callBack) {
    //console.log("exec SP_API_GradeChange_TaskList " + line + "," + product1 + "," + product2)
    sqlexec.prototype.
        _query(`select * from [GradeChange_Task_Base] where line='${line}' and product_Name='${sku}'`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}






module.exports = sqlexec;