var express = require('express')
var router = express.Router()
var sqlexec = require('../sql/sqlData_base')
var moment = require('moment')
const { resolve } = require('q')

router.get("/getEmployee_List", (req, res) => {
    var returnQuery = new sqlexec()
    returnQuery.queryEmployee_list(function (result) {
        res.send(result)
    })
})
router.post("/updateEmployee", (req, res) => {
    console.log(req.body)
    const data = req.body.postData
    const opType = req.body.opType
    const id = data.id
    const returnQuery = new sqlexec()
    console.log(data['ID_Code'])
    const keys = Object.keys(data);
    let updateValue = ''
    keys.forEach(key => {
        // console.log('data[key]',data[key])
        if (opType == '更新') {
            if (data[key] !== null && key != 'id') {
                updateValue += key + "='" + data[key] + "',"
            }

        } else if (opType == '新增') {
            if (data[key] !== null && key != 'id') {
                updateValue += "'"  + data[key] + "',"
            }else if( key != 'id'){
                updateValue +='null,'
            }
        }
    });
    updateValue = updateValue.substring(0,updateValue.length-1)
    console.log('updateValue', updateValue)
    if (opType == '更新') {
        returnQuery.updateEmployee_list(updateValue,id,function (result) {
            res.send(result)
        })
    }
    if (opType == '新增'){
        returnQuery.insertEmployee_list(updateValue,function (result) {
            res.send(result)
        })
    }
    if (opType == '删除'){
        returnQuery.deleteEmployee_list(id,function (result) {
            res.send(result)
        })
    }

})
router.get("/accessTree", (req, res) => {
    var returnQuery = new sqlexec()
    returnQuery.accessTree(function (result) {
        const transformedData = transformData(result);
        res.send(transformedData)
    })
})
router.get("/accessByPersonal", (req, res) => {
    const employeeid=req.query.employeeid
    var returnQuery = new sqlexec()
    returnQuery.accessByPersonal(employeeid,function (result) {
        const expanded=[]
        const ticked=[]
        if(result.length>0){
            for(let i=0;i<result.length;i++){
                ticked.push(result[i]['Access_Group']) 
                if(!expanded.includes(result[i]['App_Name'])){
                    expanded.push(result[i]['App_Name'])
                }
            }
        }
        res.send({'expanded':expanded,'ticked':ticked})
    })
})
router.post("/insert_Multiple_Access", (req, res) => {
    const data = req.body
    console.log('data',data)
    const employeeList=data.employeeList
    const accessList=data.AccessList
    var returnQuery = new sqlexec()
    for(let i=0;i<employeeList.length;i++){
        const employeeid=employeeList[i]['Employee_ID']
        for(let j=0;j<accessList.length;j++){
            const access=accessList[j]
            returnQuery.InsertAccessByPersonal(employeeid,access,function (result1) {
                if (j==accessList.length-1 && i==employeeList.length-1 &&result1=='已添加' ){
                    res.send('更新成功')
                }
            })
        }
    }

})
router.get("/Web_Index_Router", (req, res) => {
    var returnQuery = new sqlexec()
    returnQuery.Web_Index_Router(function (result) {
        console.log(result)
        let resultData = {}
        if (result.length > 0) {
            const resultData = result.reduce((acc, cur) => {
                const { Category, Sub_Category,App_Name, App_Desc, Image_URL, Menu_Name, Link_Side, Link, Sort,icon } = cur;
                if (!acc[Category]) {
                    acc[Category] = {};
                }
                if (!acc[Category][Sub_Category]) {
                    acc[Category][Sub_Category] = {};
                }

                if (!acc[Category][Sub_Category][App_Name]) {
                    acc[Category][Sub_Category][App_Name] = {
                        desc: App_Desc,
                        Image_URL,
                        Menu_Name: [],
                        icon
                    };
                }

                acc[Category][Sub_Category][App_Name].Menu_Name.push({
                    name: Menu_Name,
                    Link_Side,
                    Link,
                    Sort
                });

                return acc;
            }, {});
            console.log('resultData', resultData);
            res.send(resultData)
        }

    })
})
// router.get("/Web_Index_Router", (req, res) => {
//     var returnQuery = new sqlexec()
//     returnQuery.Web_Index_Router(function (result) {
//         console.log(result)
//         let resultData = {}
//         if (result.length > 0) {
//             const resultData = result.reduce((acc, cur) => {
//                 const { Category, App_Name, App_Desc, Image_URL, Menu_Name, Link_Side, Link, Sort } = cur;
//                 if (!acc[Category]) {
//                     acc[Category] = {};
//                 }

//                 if (!acc[Category][App_Name]) {
//                     acc[Category][App_Name] = {
//                         desc: App_Desc,
//                         Image_URL,
//                         Menu_Name: []
//                     };
//                 }

//                 acc[Category][App_Name].Menu_Name.push({
//                     name: Menu_Name,
//                     Link_Side,
//                     Link,
//                     Sort
//                 });

//                 return acc;
//             }, {});
//             console.log(resultData);
//             res.send(resultData)
//         }
        
//     })
// })
router.post("/update_accessByPersonal", (req, res) => {
    const data = req.body
    console.log('data',data)
    const employeeid=data.employeeID
    const accessList=data.AccessList
    var returnQuery = new sqlexec()
    returnQuery.deleteAccessByPersonal(employeeid,function (result) {
        if(result=='已删除'){
            if(accessList.length>0){
                for(let i =0;i<accessList.length;i++){
                    const access=accessList[i]
                    returnQuery.InsertAccessByPersonal(employeeid,access,function (result1) {
                        if (i==accessList.length-1 && result1=='已添加' ){
                            res.send('更新成功')
                        }
                    })
                }

            }
            else{
                res.send('更新成功')
            }

        }
    })
})

router.get("/FinanceYearMonth", (req, res) => {
	var Year = req.query.Year
    console.log('Year',Year)
	var returnQuery = new sqlexec()
	returnQuery.YearMonthQuery(Year, function (result) {
		console.log(result)
		res.send(result)
	})
})


router.post("/insert_FinYearMonth", (req, res) => {
    const data = req.body
    console.log('data',req.body)  
    console.log('finYM:',req.body.Fin_YearMonth)

    var returnQuery = new sqlexec()
    returnQuery.InsertYearMonth(data,function (result) {
        if (result=='已添加' ){
            res.send('添加成功')
        }
    })


})
router.post("/Del_FinYearMonth", (req, res) => {
    const data = req.body
    console.log('data',req.body)  
    console.log('finYM:',req.body.Fin_YearMonth)

    var returnQuery = new sqlexec()
    returnQuery.DeLYearMonth(data,function (result) {
        if (result=='已删除' ){
            res.send('更新成功')
        }
    })


})

function transformData(data) {
    // 用于存储转换后的结果
    const result = [];
  
    // 遍历原始数据
    data.forEach(item => {
      // 获取 Group_Name 和 App_Name
      const groupName = item.App_Name;
      const appName = item.Group_Name;
  
      // 在结果中查找具有相同 Group_Name 的项
      let group = result.find(group => group.label === groupName);
  
      // 如果找到匹配的组，则将当前项添加为该组的子项
      if (group) {
        group.children.push({
          label: appName
        });
      } else {
        // 如果没有找到匹配的组，则创建一个新的组项
        result.push({
          label: groupName,
          children: [
            {
              label: appName
            }
          ]
        });
      }
    });
  
    return result;
  }
module.exports = router


