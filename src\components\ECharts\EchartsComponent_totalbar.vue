<template>
	<div>
		<div style="width:100%;height:300px;" :id="echarts" class="echarts" ref="echarts" v-if="isShow"></div>
	</div>
</template>

<script>
	// 引入echarts
	import echarts from 'echarts'
	export default {
		name: 'EchartsComponents',
		props: {
			// 接收父组件传递过来的信息

			chartData: {
				dimensions: {
					type: Array,
					default: () => []
				},
				source: {
					type: Array,
					default: () => []
				},
				series: {
					type: Array,
					default: () => []
				},
			}

		},
		data() {
			return {
				isShow: true
			}
		},
		methods: {


			getKey(data) {
				Dictionary.prototype.keys = () => {
					return Object.keys(data)
				}
			},


			drawChart() {
				// 基于准备好的dom，初始化echarts实例
				var myChart = echarts.init(document.getElementById(this.echarts))
				var _this = this
				// 绘制图表
				myChart.setOption({
					legend: {},
					tooltip: {
						trigger: 'axis',
						axisPointer: {
							// Use axis to trigger tooltip
							type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
						},
						formatter: function(params) {
							// console.log(params)
							var tip = ""
							var val = ""
							if (params != null && params.length > 0) {
								tip += params[0].name + '<br />'
								for (var i = 0; i < params.length; i++) {
									if (params[0].seriesType == 'line') {
										val = (params[i]['value'][params[i].seriesName] * 100).toFixed(2) +
										'%';
										if (val == 'NaN%') {
											val = '-'
										}
									} else {
										if (params[i]['value'][params[i].seriesName] >= 100000000 || params[i][
												'value'
											][params[i].seriesName] <= -100000000) {
											val = (params[i]['value'][params[i].seriesName] / 100000000)
												.toFixed(2) + '亿';
										} else {
											val = (params[i]['value'][params[i].seriesName] / 10000).toFixed(
												0) + '万';
										}
										if (val == 'NaN万') {
											val = '-'
										}
									}



									tip += params[i].marker + params[i].seriesName + '&nbsp;&nbsp;&nbsp;' +
										val + '<br />'
								}
							}
							return tip;
						},

					},
					dataset: {
						dimensions: this.chartData.dimensions,
						source: this.chartData.source
					},
					xAxis: {
						type: 'category',
						axisTick: {
							//坐标轴刻度相关设置。
							show: false
						}
					},
					yAxis: [{
							type: 'value',
							splitNumber: 4,
							splitLine: {
								show: false
							},
							axisLabel: {
								textStyle: {
									fontSize: 13
								},
								formatter: function(value) {
									console.log(value)
									if (value>0 && value<1) {
											return (value *100).toFixed(0) + '%';
									} else {
										if (value >= 100000000 || value <= -100000000) {
											return (value / 100000000).toFixed(0) + '亿';
										} else {
											return (value / 10000).toFixed(0) + '万';
										}
									}

								}
							},

							axisLine: {
								show: false
							},
							axisTick: {
								//坐标轴刻度相关设置。
								show: false
							}
						},
						{
							type: 'value',
							splitNumber: 4,
							splitLine: {
								show: false
							},
							axisLabel: {
								textStyle: {
									fontSize: 13
								},
								formatter: function(value) {
									return value.toFixed(0) + '%';
								}
							},
							axisLine: {
								show: false
							},
							axisTick: {
								//坐标轴刻度相关设置。
								show: false
							}
						},
					],
					// Declare several bar series, each will be mapped
					// to a column of dataset.source by default.
					series: this.chartData.series
				})

			},

		},
		computed: {
			echarts() {
				return 'echarts' + Math.random() * 100000
			}
		},

		watch: {

			// 'chartData.dimensions': function(val) {
			// 	console.log('val', val)
			// 	const vm = this
			// 	vm.$nextTick(() => {
			// 		vm.init()
			// 	})
			// },
			// isShow:function(newVal,oldVal){
			// 	console.log(newVal)
			// 	console.log(oldVal)
			// }
		},


		mounted: function() {
			const vm = this
			vm.$nextTick(() => {
				vm.drawChart()
			})





		},
		created: () => {}
	}
</script>

<style scoped>
</style>
