var express = require('express')
var router = express.Router()
var sqlexec_grade = require('../sql/QA_Raw_Data')
var moment = require('moment')
const { resolve } = require('q')

router.get("/QA1", (req, res) => {
    console.log('req',req.query)
    var Mill = "'" + req.query.Mill + "'"
    var Start = "'" + req.query.Start + "'"
    var End = "'" + req.query.End + "'"
    console.log('参数',Mill,Start,End)
    var returnQuery = new sqlexec_grade()
    returnQuery.QA(Mill,Start,End,function (result) {
        res.send(result)

    })
})




module.exports = router