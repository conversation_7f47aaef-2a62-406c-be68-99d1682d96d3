<template>
  <base-content>
    <div class="q-pa-md">
      <div class="q-gutter-y-md">
        <q-card class="shadow-5 rounded-borders">
          <q-tabs 
            v-model="tab" 
            inline-label 
            outside-arrows 
            mobile-arrows 
            align="justify"
            class="bg-primary text-white shadow-2"
          >
            <q-tab name="inputHoldGoods" icon="edit" label="待判定成品录入" />
            <q-tab name="queryHoldGoods" icon="search" label="待判定成品查询" />
            <q-tab name="myApproval" icon="check_circle" label="待判定成品审批" />
          </q-tabs>
          <q-separator />

          <q-tab-panels v-model="tab" animated>
            <q-tab-panel name="queryHoldGoods">
              <HoldGoodsQuery />
            </q-tab-panel>

            <q-tab-panel name="inputHoldGoods">
              <div class="q-pa-md">
                <div class="row q-col-gutter-md">
                  <!-- 第一行 -->
                  <div class="col-12 col-md-3">
                    <q-select 
                      outlined
                      v-model="inputData['department']"
                      :options="dropDownData['department']"
                      label="部门/生产线"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>
                  
                  <div class="col-12 col-md-3">
                    <q-select
                      outlined
                      v-model="inputData['employee_Name']"
                      :options="dropDownData['employee_Name']"
                      label="提交人"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>

                  <div class="col-12 col-md-3">
                    <q-input
                      outlined
                      v-model="inputData['Date']"
                      type="date"
                      label="日期"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>

                  <div class="col-12 col-md-3">
                    <q-select
                      outlined
                      v-model="inputData['shift']"
                      :options="dropDownData['shift']"
                      label="班次"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>

                  <!-- 第二行 -->
                  <div class="col-12 col-md-3">
                    <q-input
                      outlined
                      v-model="inputData['SKU']"
                      label="SKU号"
                      type="number"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>

                  <div class="col-12 col-md-3">
                    <q-input
                      outlined
                      v-model="inputData['SKU_desc']"
                      label="SKU描述"
                      readonly
                      class="q-mb-md"
                    />
                  </div>

                  <div class="col-12 col-md-3">
                    <q-select
                      outlined
                      v-model="inputData['badCategory']"
                      :options="dropDownData['badCategory']"
                      label="缺陷类型"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>

                  <div class="col-12 col-md-3">
                    <q-input
                      outlined
                      v-model="inputData['holdRatio']"
                      label="缺陷比例%"
                      type="number"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>

                  <!-- 第三行 -->
                  <div class="col-12 col-md-3">
                    <q-input
                      outlined
                      v-model="inputData['SampleQTY']"
                      label="取样量(片)"
                      type="number"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>

                  <div class="col-12 col-md-3">
                    <q-input
                      outlined
                      v-model="inputData['holdQTY']"
                      label="待判定片数"
                      type="number"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>

                  <div class="col-12 col-md-3">
                    <q-input
                      outlined
                      v-model="inputData['holdCases']"
                      label="箱数"
                      readonly
                      class="q-mb-md"
                    />
                  </div>

                  <div class="col-12 col-md-4">
                    <q-input
                      outlined
                      v-model="inputData['holdStartTime']"
                      type="datetime-local"
                      label="待判定开始时间"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>
                  <div class="col-12 col-md-4">
                    <q-input
                      outlined
                      v-model="inputData['holdEndTime']"
                      type="datetime-local"
                      label="待判定结束时间"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>
                  <div class="col-12 col-md-4">
                    <q-input
                      outlined
                      v-model="inputData['holdMin']"
                      label="时间(Min)"
                      readonly
                      class="q-mb-md"
                    />
                  </div>

                  <!-- 文本区域 -->
                  <div class="col-12">
                    <q-input
                      outlined
                      v-model="inputData['rootCause']"
                      label="待判定产品缺陷描述"
                      type="textarea"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>

                  <div class="col-12">
                    <q-select
                      outlined
                      v-model="inputData['result']"
                      :options="['返工', '报废','放行']"
                      label="处置方案"
                      :rules="[val => !!val || '此字段为必填项']"
                      class="q-mb-md"
                    />
                  </div>

                  <!-- 提交按钮 -->
                  <div class="col-12 text-right">
                    <q-btn
                      color="primary"
                      label="提交"
                      icon="send"
                      @click="submit"
                      class="q-px-xl"
                    />
                  </div>
                </div>
              </div>
            </q-tab-panel>
          </q-tab-panels>
        </q-card>
      </div>
    </div>
  </base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import HoldGoodsQuery from './HoldGoodsQuery.vue';

export default {
  components: {
    BaseContent,
    HoldGoodsQuery
  },
  data() {
    return {
      filter: '',
      filter1: '',
      tab: 'inputHoldGoods',
      myPagination: { rowsPerPage: 0 },
      employee_List: [],
      dropDownData: {
        department: [],
        employee_Name: [],
        shift: ['白班', '夜班'],
        badCategory: ['属性I类/变量', '属性II类/III类']
      },
      queryDropDownData: {
        department: []
      },
      inputData: {
        department: '',
        employee_Name: '',
        Date: '',
        shift: '',
        SKU: '',
        SKU_desc: '',
        badCategory: '',
        holdQTY: '',
        holdCases: '',
        holdMin: '',
        holdRatio: '',
        rootCause: '',
        result: '',
        SampleQTY: '',
      },
      adpotFilter: true,
      firtDialog: false,
      showDetailDialog: false,
      showDetailData: false,
      rejectDialog: false
    }
  },
  mounted() {
    console.log('this.$query', this.$route.query)
    this.getDepartment()
    this.getEmployee()

    if (this.$route.query.function) {
      console.log('有参数')
      this.tab = this.$route.query.function
      this.myIdeaSelectedData['department'] = this.$route.query.department
    }
  },
  watch: {
    'inputData.department'(newValue, oldValue) {
      this.dropDownData['employee_Name'] = []
      this.inputData['employee_Name'] = ''
      this.filterEmployee()
    },
    'inputData.SKU'(newValue, oldValue) {
      this.inputData['SKU_desc'] = ''
      this.filterSKUdesc(newValue)
    },
    'inputData.holdQTY'(newValue, oldValue) {
      this.inputData['holdCases'] = ''
      this.CalCases(newValue)
    },
    'inputData.holdStartTime'(newValue, oldValue) {
      this.calculateTimeDiff()
    },
    'inputData.holdEndTime'(newValue, oldValue) {
      this.calculateTimeDiff()
    },
    'queryInputData.department'(newValue, oldValue) {
      this.GetHoldedGoods(newValue)
    },
  },
  methods: {
    async getDepartment() {
      const _this = this
      try {
        const { data: res } = await _this.$http.get('ideabank/getDepartment')
        console.log('getDepartment', res)
        if (!res || !Array.isArray(res)) {
          console.error('Invalid department data:', res)
          _this.dropDownData['department'] = []
          return
        }
        _this.dropDownData['department'] = res
          .map(item => item?.toString?.())
          .filter(item => 
            item && (item.startsWith('ND') || item === '质量')
          )
      } catch (error) {
        console.error('Failed to get department:', error)
        _this.dropDownData['department'] = []
      }
    },

    async getEmployee() {
      const _this = this
      const { data: res } = await _this.$http.get('ideabank/getEmployee')
      console.log('getEmployee', res)
      _this.employee_List = res
    },
    async filterEmployee() {
      const _this = this
      const department = _this.inputData['department']
      if (department != null && department != '') {
        for (let i = 0; i < _this.employee_List.length; i++) {
          if (_this.employee_List[i]['line'] == department) {
            _this.dropDownData['employee_Name'].push({
              label: _this.employee_List[i].Employee_PYName,
              value: _this.employee_List[i].Employee_ID
            })
          }
        }
      }
    },
    async GetHoldedGoods(department) {
      const _this = this
      const { data: res } = await _this.$http.get(`holdGoods/GetHoldedGoods?department=${department}`)
      console.log('queryIdea_ByDepartment', res)
      _this.myIdeaData = res
      _this.myIdeaRawData = res
      _this.filterApplicant()
    },
    async filterSKUdesc(SKU) {
      console.log('SKU', SKU);
      try {
        const { data: res } = await this.$http.get(`holdGoods/getMaterial?SKU=${SKU}`);
        if (Array.isArray(res) && res.length > 0) {
          const { SKU_Desc, Cuts_Per_Case, Target_Speed } = res[0];
          this.inputData['SKU_desc'] = SKU_Desc;
          this.cutsPerCase = Cuts_Per_Case;
          this.tarSpeed = Target_Speed;
        } else {
          console.warn('No data found for the given SKU.');
          this.inputData['SKU_desc'] = '';
          this.cutsPerCase = null;
          this.tarSpeed = null;
        }
      } catch (error) {
        console.error('Error fetching material data:', error);
      }
    },
    async CalCases(QTY) {
      const _this = this
      console.log('res', _this.cutsPerCase)
      _this.inputData['holdCases'] = QTY / _this.cutsPerCase
      _this.inputData['holdCases'] = parseFloat(_this.inputData['holdCases'].toFixed(2));
    },
    
    calculateTimeDiff() {
      const _this = this
      if (_this.inputData['holdStartTime'] && _this.inputData['holdEndTime']) {
        const start = new Date(_this.inputData['holdStartTime'])
        const end = new Date(_this.inputData['holdEndTime'])
        const diffInMs = end - start
        const diffInMinutes = Math.floor(diffInMs / (1000 * 60))
        _this.inputData['holdMin'] = diffInMinutes
      } else {
        _this.inputData['holdMin'] = ''
      }
    },
    filterApplicant() {
      const _this = this
      console.log('_this.myIdeaSelectedData', _this.myIdeaSelectedData)
      const employee_name = _this.myIdeaSelectedData['employee_Name']
      console.log('employee_name', employee_name)
      _this.myIdeaData = []
      if (employee_name != null && employee_name != '') {
        for (let i = 0; i < _this.myIdeaRawData.length; i++) {
          if (_this.myIdeaRawData[i]['提交人'] == employee_name['label']) {
            _this.myIdeaData.push(_this.myIdeaRawData[i])
          }
        }
      } else {
        _this.myIdeaData = _this.myIdeaRawData
      }
    },
    submit() {
      const _this = this
      let hasEmptyField = false;
      for (let key in _this.inputData) {
        if (_this.inputData[key] === '') {
          _this.$q.notify({
            type: 'negative',
            message: `${key} 不能为空`,
            position: 'top'
          });
          hasEmptyField = true;
        }
      }
      if (hasEmptyField) {
        return;
      }
      const submitData = {
        Line: _this.inputData.department,
        employee_Name: _this.inputData.employee_Name,
        Date: _this.inputData.Date,
        shift: _this.inputData.shift,
        SKU: _this.inputData.SKU,
        SKU_desc: _this.inputData.SKU_desc,
        badCategory: _this.inputData.badCategory,
        holdRatio: _this.inputData.holdRatio,
        SampleQTY: _this.inputData.SampleQTY,
        holdQTY: _this.inputData.holdQTY,
        holdCases: _this.inputData.holdCases,
        holdMin: _this.inputData.holdMin,
        rootCause: _this.inputData.rootCause,
        result: _this.inputData.result,
        holdStartTime: _this.inputData.holdStartTime,
        holdEndTime: _this.inputData.holdEndTime
      }
      console.log('submitData', submitData)
      _this.$http.post('holdGoods/insertHoldData', { 'data': submitData }).then(function (response) {
        console.log('response', response)
        if (response.data === '已插入数据库') {
          console.log('接口已发送成功')
          _this.$q.notify({
            type: 'positive',
            message: `待判定提交成功！系统第一时间发送邮件给您的主管`,
            position: 'top'
          })
          for (let key in _this.inputData) {
            _this.inputData[key] = '';
          }
        }
      })
    },
  }
}
</script>

<style lang="scss">
.q-card {
  border-radius: 8px;
  overflow: hidden;
}

.q-tab-panel {
  padding: 24px;
}

.q-input, .q-select {
  background-color: #f5f5f5;
  border-radius: 4px;
}

.q-btn {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

.q-field__label {
  font-weight: 500;
}

.q-field--outlined .q-field__control {
  border-radius: 4px;
}

.q-field--focused .q-field__control {
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}
</style>
