<template>
	<base-content>
		<div dense style="font-size: 20px;color: black;text-align: center;margin-top: 5px;">交接班MES数据核对报告</div>
		<div class="q-pa-md">
			<!--  选择框及按钮 -->
			<div class="q-gutter-md row">
				<q-select outlined dense v-model="Line" :options="LineList" style="width: 150px;margin-top: -10px;"
					label-slot clearable>
					<template v-slot:label>产线
						<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
					</template>
				</q-select>
				<q-input v-model="Date_1" outlined type="date" dense label-slot style="width: 150px;margin-top: -10px;">
					<template v-slot:label>
						<div for="date-input" class="cursor-pointer" style="font-size: 13px;">
							日期
							<em class="q-px-sm bg-deep-orange text-white rounded-borders"
								style="font-size: 10px;">必填</em>
						</div>
					</template>
				</q-input>
				<q-select outlined dense v-model="Shift" :options="ShiftList" style="width: 150px;margin-top: -10px;"
					label-slot clearable>
					<template v-slot:label>班次
						<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
					</template>
				</q-select>
				<div class="q-pa-md q-gutter-sm">
					<q-btn :loading="loading1" color="primary" @click="getShiftChange_MES(1)" label="查询"
						style="width: 90px;height: 40px;margin-left:-10px;margin-top: -28px;" />
				</div>
				<!-- <q-btn dense color="primary" label="查询" @click="getShiftChange_MES" style="width: 70px;height: 40px;" /> -->
			</div>
		</div>
		<!--  交接班汇总信息 -->
		<div class="q-pa-md">
			<q-table title="MES-KPI数据详情" dense :data="MESdata1" :columns="column1" row-key="name" hide-pagination
				style="margin-top: -20px;" />
			<div dense style="font-size: 13px;color: brown;margin-left: 180px;margin-top: 25px;">说明：差异=总切-入库片数-废品总数
			</div>

			<q-table title="MES-废品详情" dense :data="MESdata2" :columns="column2" row-key="name" hide-pagination />
		</div>

	</base-content>
</template>
<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import Quagga from 'quagga'



export default {
	components: {
		BaseContent, Quagga,
	},
	mounted() {
		console.log("mounted", this.$route.query)
		this.User_ID = localStorage.getItem('account')
		this.Line = localStorage.getItem('Related_Line')
		this.getLine()
		this.getShift()
	},

	data() {
		const today = new Date();
		today.setDate(today.getDate());
		return {
			loading1: false,
			separator: 'vertical',
			Line: "",
			Date_1: today.toISOString().substr(0, 10),
			Shift: "",
			Total_Cuts: "",
			LineList: [],
			ShiftList: ["Day", "Night"],
			MESdata1: [],
			column1: [
				{ name: 'SKU', required: true, label: 'SKU', align: 'left', field: 'SKU', sortable: true },
				{ name: 'Target_Speed', required: true, label: '目标速度', align: 'left', field: 'Target_Speed', sortable: true },
				{ name: 'Total_Cuts', required: true, label: '总切', align: 'left', field: 'Total_Cuts', sortable: true },
				{ name: 'FG', required: true, label: '入库箱数', align: 'left', field: 'FG', sortable: true, style: 'color: Blue;' },
				{ name: 'FG_Cut', required: true, label: '入库片数', align: 'left', field: 'FG_Cut', sortable: true },
				{ name: 'SEMI_FG', required: true, label: '半成品箱数', align: 'left', field: 'SEMI_FG', sortable: true }
			],
			MESdata2: [],
			column2: [
				{ name: 'SKU', required: true, label: 'SKU', align: 'left', field: 'SKU', sortable: true },
				{ name: 'TotalWaste', required: true, label: '废品总数', align: 'left', field: 'TotalWaste', sortable: true },
				{ name: 'balance', required: true, label: '差异', align: 'left', field: 'balance', sortable: true, style: 'color: red;' },
				{ name: 'MWaste', required: true, label: '机械废品(自动)', align: 'left', field: 'MWaste', sortable: true },
				{ name: 'QAWaste_Auto', required: true, label: '质量废品(自动)', align: 'left', field: 'QAWaste_Auto', sortable: true },
				{ name: 'QAWaste_Mua', required: true, label: '质量废品(人工录入)', align: 'left', field: 'QAWaste_Mua', sortable: true },
				{ name: 'PacWaste', required: true, label: '包装废品(人工录入)', align: 'left', field: 'PacWaste', sortable: true },
				{ name: 'ReWorkWaste', required: true, label: '返工废品', align: 'left', field: 'ReWorkWaste', sortable: true },
			]
		}
	},

	methods: {
		getShift() {
			const currentTime = new Date().getHours(); // 获取当前时间的小时数
			if (currentTime < 20 || currentTime >= 8) {
				this.Shift = "Day"; // 当前时间大于等于12点时，设置Shift为"Day"
			} else {
				this.Shift = "Night"; // 当前时间小于12点时，设置Shift为"Night"
			}
			console.log('当前时间', currentTime)
		},
		async getLine() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`material/lineList`)
			console.log('NJSLineList', res)
			_this.LineList = res
			this.LineList = _this.LineList
		},
		// async getLine() {
        //     var _this = this
        //     const {
        //         data: res
        //     } = await _this.$http.get(`approve/getLine?mill=${localStorage.getItem('mill')}`)
        //     console.log('line', res)
        //     this.lineList=res
        // },

		async getShiftChange_MES(number) {
			// we set loading state
			this.MESdata2 = []
			this.MESdata1 = []
			this[`loading${number}`] = true;
			try {
				// simulate a delay
				await new Promise(resolve => setTimeout(resolve, 4000));

				var _this = this
				var Line = this.Line
				var Date = this.Date_1
				var Shift = this.Shift
				console.log('ShiftChangeKPI参数', Line, Date, Shift)
				// send request and get response
				const response1 = await _this.$http.get(`mes/ShiftChange_MESKPI?Line=${Line}&Date=${Date}&Shift=${Shift}`)
				console.log('SC_MES', response1.data)
				// _this.product=res.map(item=>item.SKU)
				_this.MESdata1 = response1.data
				this.MESdata1 = _this.MESdata1
				// send request and get response
				const response2 = await _this.$http.get(`mes/ShiftChange_MESWaste?Line=${Line}&Date=${Date}&Shift=${Shift}`)
				console.log('SC_MES', response2.data)
				// _this.product=res.map(item=>item.SKU)
				_this.MESdata2 = response2.data
				this.MESdata2 = _this.MESdata2
				// Calculate balance for each data object in MESdata2
				const i = 0
				for (let i = 0; i < this.MESdata2.length; i++) {
					const matchingItemInMESdata1 = this.MESdata1.find(item => item.SKU === this.MESdata2[i].SKU);

					if (matchingItemInMESdata1) {
						const balance = matchingItemInMESdata1.Total_Cuts - matchingItemInMESdata1.FG_Cut - this.MESdata2[i].TotalWaste;
						this.MESdata2[i].balance = balance;
					} else {
						console.warn(`No matching SKU found in MESdata1 for SKU: ${this.MESdata2[i].SKU}`);
					}
					console.log('MESdata2', this.MESdata2)
				}

				console.warn(`No matching SKU found in MESdata1 for SKU: ${this.MESdata2[i].SKU}`)
			} catch (error) {
				// handle error here
				console.error(error);
			} finally {
				// reset loading state
				this[`loading${number}`] = false;
			}
		}
	}
}

</script>


<style lang="css" scoped>
.text_type {
	color: yellow;
	font-size: 20px;
	font-weight: bold
}
</style>
