import Vue from 'vue'
import App from './App.vue'
import store from './store'
import './quasar'

import axios from 'axios'

// 前端根据角色处理路由
import router from './router/permission'

// 后端返回动态路由
// import router from './router/permissionWithDynamicRouter'

// 系统全局配置
import './config'

// 全局 axios 工具类 fetchData
import './axios/FetchData'



axios.defaults.baseURL = "http://localhost:3000/"

// axios.defaults.baseURL = "http://*************:3000/"


Vue.prototype.$http = axios

// 第三方组件.
import animated from 'animate.css'
import jsonView from 'vue-json-views'

// markdown
import './components/Markdown/Markdown'

// ECharts
import './components/ECharts/EChartsConfig'

Vue.config.productionTip = false
Vue.use(animated)
Vue.use(jsonView)

// 前往 public 文件夹的路径
Vue.prototype.$PUBLIC_PATH = process.env.BASE_URL


const vue = new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')


export default vue

