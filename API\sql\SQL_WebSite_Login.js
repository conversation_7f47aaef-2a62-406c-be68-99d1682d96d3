const _Base = require('../config/dbbase')


// sqlexec是一个函数
// 调用父类构造函数，并将当前对象作为参数传递给父类构造函数。这样做的目的是让子类（在这里是sqlexec）能够继承父类（_Base）的属性和方法。
// 在这种情况下，call不是系统的方法，而是JavaScript中内置的函数方法，用于在调用函数时设置函数体内this的值。通过将this作为参数传递给父类构造函数，子类可以继承父类的属性和方法，同时确保在子类实例化时正确设置作用域。
// 总的来说，_Base.call(this)的作用是在子类中调用父类的构造函数，并确保正确设置作用域。是一种实现对象间继承的常见方式
function sqlexec() {
    _Base.call(this);

}

// prototype是每个函数对象都具有的一个属性，它允许您向对象的原型链中添加属性和方法。当您创建一个函数时，该函数会自动获得一个名为prototype的属性，它指向一个原型对象，可以给该函数添加属性和方法。通过在函数的prototype属性上添加属性和方法，所有通过该构造函数创建的实例都可以共享这些属性和方法。

sqlexec.prototype = new _Base()


// function (callBack)作为一个函数参数，允许您在调用函数时动态传入一个函数，以实现在适当的时机执行相应的操作。

sqlexec.prototype.lineList = function (callBack) {
    sqlexec.prototype._query(
        "select distinct line from Product_Base where mill='南京南厂' and Line not in ('ND03','ND04') Order by Line",
        function (err, result) {
            if (err) {                
            }
            return callBack(result.recordset)  //else 省略,但是用法不便;result.recordset 是一个对象中的属性，用来存储数据库查询结果的记录集合。在Node.js中，特定的数据库操作返回的结果集可能会包含一个recordset属性，其中存储了查询结果的记录集
        }
    )

}

sqlexec.prototype.OPList = function (Line, callBack) {
    // console.log('参数', Line)
    var sqlStr = ''
    sqlStr = `select Employee_PYName from Employee_List where Line=${Line} Order by Employee_PYName`
    // console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.RM = function (RM_Code, callBack) {
    console.log('参数', RM_Code)
    var sqlStr = ''
    sqlStr = `select RM_Desc,UOM from MES_Raw_Material where RM_Code=${RM_Code}`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.PalletLine = function (callBack) {
    sqlexec.prototype.
        _query("select distinct line from NJN_Pallet_Test ", function (err, result) {
            // _query("select line from gradeChange_BaseData group by line order by line asc", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.PalletSumLine = function (callBack) {
    sqlexec.prototype.
        _query("select distinct line from NJN_Pallet_ID_Test ", function (err, result) {
            // _query("select line from gradeChange_BaseData group by line order by line asc", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.PalletSKU = function (Line, callBack) {
    sqlexec.prototype.
        _query("select SKU_Code SKU from NJN_Pallet_Test Where Line=" + Line + "order by SKU_Code", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.PalletSumSKU = function (Line, Date, Shift, callBack) {
    sqlexec.prototype.
        _query(`select distinct SKU_Code SKU from NJN_Pallet_ID_Test 
        Where Line=${Line} and Date=${Date} and Shift=${Shift} order by SKU_Code`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.PalletData = function (Line, SKU, callBack) {
    sqlexec.prototype.
        _query(`select Product_Code,SKU_Desc,Cut_Per_Bag,Bag_Per_Case,pallet,Case_Per_Pallet,Cut_Per_Case from
         NJN_Pallet_Test Where Line=${Line} and SKU_Code=${SKU} `, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.API_Execute_PalletID = function (pallet, callBack) {
    console.log('sqlData', pallet)
    var sqlStr = ''
    sqlStr = `exec SP_API_Execute_PalletID_NJN '${pallet['Line']}','${pallet['SKU_Code']}','${pallet['pallet_ID']}',
                '${pallet['pallet_QTY']}','${pallet['Entry_By']}'`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            var status = result.recordset[0]['状态']
            return callBack(status)
        })
}

sqlexec.prototype.PalletSum = function (Line, SKU, ShiftStart, callBack) {
    console.log('参数', Line, SKU, ShiftStart)
    var sqlStr = ''
    sqlStr = `select count(*) Pallet_Sum ,sum(Pallet_QTY) Case_Sum from NJN_Pallet_ID_Test Where Line=${Line} and SKU_Code=${SKU} and entry_on>=${ShiftStart}`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.PalletSumData = function (Line, SKU, Date, Shfit, callBack) {
    sqlexec.prototype.
        _query(`select Line 产线,convert(varchar(100),Date,23) 日期,Shift 班次,SKU_Code SAP号,Pallet_ID 托盘码,
        Pallet_QTY 箱数,Entry_By 扫码人,convert(varchar(100),entry_on,20) 扫码时间 
        from NJN_Pallet_ID_Test 
        Where Line=${Line} and SKU_Code=${SKU} and Date=${Date} and Shift=${Shfit}`, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.ScanPalletQTY = function (Line, SKU, Date1, Shift, callBack) {
    console.log('sql参数', Line, SKU, Date1, Shift)
    var sqlStr = ''
    sqlStr = `select COUNT(Pallet_ID) PalletCount, sum(pallet_QTY) CaseQTY 
    from NJN_Pallet_ID_Test 
    Where Line=${Line} and SKU_Code=${SKU} and Date=${Date1} and Shift=${Shift}`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}


sqlexec.prototype.API_Execute_RM_Return_Insert = function (RM_Ruturn, callBack) {
    console.log('RM_Ruturn', RM_Ruturn)
    var sqlStr = ''
    sqlStr = `exec MES_RM_Return_Insert '${RM_Ruturn['Return_Type']}','${RM_Ruturn['Pallet_Lable']}','${RM_Ruturn['Line']}',
                '${RM_Ruturn['RM_Code']}','${RM_Ruturn['RM_Desc']}','${RM_Ruturn['RM_Lot_No']}','${RM_Ruturn['RM_QTY']}',
                '${RM_Ruturn['Operator']}','${RM_Ruturn['Comment']}','${RM_Ruturn['QR_Code']}','${RM_Ruturn['Entry_By']}',
                '${RM_Ruturn['Raw_Lot']}'`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            var status = result.recordset[0]['状态']
            return callBack(status)
        })
}

sqlexec.prototype.DailyKPIData = function (Line, Start, End, callBack) {
    console.log('sql参数', Line, Start, End)
    var sqlStr = ''
    sqlStr = `select convert(varchar(20),Date,23) Date,Line,Shift,Crew,SKU_Code,Machine_Status,
    sum(Total_Cuts) Total_Cuts,sum(Good_Cuts) Good_Cuts
    from Production_Data
    where Line=${Line} and date between ${Start} and ${End} 
    group by Date,Line,Shift,Crew,SKU_Code,Machine_Status
    order by Date,Shift`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.TierSize = function (Line, callBack) {
    console.log('sql参数', Line)
    var sqlStr = ''
    sqlStr = `select Line,Tier,Size,Country,SKU_Code from product_base
    where Line=${Line} `
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.MachineWaste = function (Line, Start, End, callBack) {
    console.log('MW参数', Line, Start, End)
    var sqlStr = ''
    sqlStr = `select b.Tier,b.Size,b.Country,a.Level3,sum(a.Amount) MW_QTY,sum(a.Counts) MW_Count
    from Mes_Waste_Data as a
    left join Product_Base as b on a.SKU_Code=b.SKU_Code
    Where a.Process_Order like '11%'and a.Line=${Line} and a.Date>=${Start} and a.Date<=${End}
    group by b.Tier,b.Size,b.Country,a.Level3`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.TierSize = function (Line, callBack) {
    var sqlStr = ''
    sqlStr = `select distinct Tier,Size,Country,SKU_Code
    from Product_Base 
    Where Line=${Line}`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.OPForQA = function (Mill, Start, End, callBack) {
    var sqlStr = ''
    sqlStr = `select a.Mill 工厂,Fin_YearMonth 财务年月,a.Date 日期,a.Shift 班次,a.Line 机台,a.Crew 班组,
    b.OP 班组人员,a.SKU_Code SKU,c.Tier
    from production_Data as a
    left join (SELECT Date, a1.Line, Shift, STRING_AGG(a1.employee_id,',') ID, STRING_AGG(b1.employee_Name,',') OP
                from (SELECT Date, Line, Shift, employee_id
                        FROM Mes_Crew_Data where Date between ${Start} and ${End} ) as a1 
                left join Employee_List as b1 on a1.employee_id=b1.Employee_ID
                 Group by Date, a1.Line, Shift ) as b on a.Line=b.Line and convert(varchar(100),a.Date,23)=b.Date and a.Shift=b.Shift
    left join Product_Base as c on a.SKU_Code=c.SKU_Code
    where a.Mill=${Mill} and b.Date between ${Start} and ${End}`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.CLTierSize = function (Line, SKU, callBack) {
    console.log('CLTS参数', Line, SKU)
    var sqlStr = ''
    sqlStr = `select distinct Tier,Size,Country,SKU_Code
    from Product_Base 
    Where Line=${Line} and SKU_Code in (${SKU})`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}


module.exports = sqlexec;