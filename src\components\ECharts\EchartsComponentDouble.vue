<template>
  <div>
    <div style="width:100%;height:300px;" :id="echarts" class="echarts" ref="echarts"></div>
  </div>
</template>

<script>
  // 引入echarts
  import echarts from 'echarts'
  export default {
    name: 'EchartsComponents',
    props: {
      // 接收父组件传递过来的信息

      chartData: {
        categories: {
          type: Array,
          default: () => []
        },
        series: {
          type: Array,
          default: () => []
        },
        name: {
          type: String
        }
      }

    },
    data() {
      return {}
    },
    methods: {
      drawChart() {
        // console.log(this.chartData)
        const vm = this
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById(this.echarts))
        // 绘制图表
        myChart.setOption({
          title: {
            text: this.chartData.name
          },
          grid: {
            left: '2%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            hideDelay: 100,
            axisPointer: {
              animation: true,
              type: 'shadow'
            },
            // formatter: function(datas) {
            //   // var res = datas[0].name + '\n',
            //   //   val;
            //   // if (datas[0].value >= 100000000 || datas[0].value <= -100000000) {
            //   //   val = (datas[0].value / 100000000).toFixed(2) + '亿';
            //   // } else {
            //   //   val = (datas[0].value / 10000).toFixed(0) + '万';
            //   // }
            //   // res += datas[0].seriesName + '：' + val + '\n';
            //   // res += datas[1].seriesName + '：' + datas[1].value + '%';
            //   // return res;
            // }
          },
          legend: {
            data: [this.chartData.series[0].name,this.chartData.series[1].name]
          },
          xAxis: {
            data: this.chartData.categories,
            axisTick: {
            	//坐标轴刻度相关设置。
            	show: false
            }
          },
          yAxis: 
          [{
              type: 'value',
              splitNumber: 4,
              splitLine: {
                show: false
              },
              axisLabel: {
                textStyle: {
                  fontSize: 13
                },
                // formatter: function(value) {
                //   if (value >= 100000000 || value <= -100000000) {
                //     return (value / 100000000).toFixed(0) + '亿';
                //   } else {
                //     return (value / 10000).toFixed(0) + '万';
                //   }
                // }
              },

              axisLine: {
                show: false
              },
              axisTick: {
                //坐标轴刻度相关设置。
                show: false
              }
            },
          ],
					// dataZoom: [{
					// 		type: 'inside',
					// 		startValue: this.chartData.categories[Math.round(this.chartData.categories.length /
					// 			2, 0)],
					
					// 		endValue: this.chartData.categories[this.chartData.categories.length - 1]
					// 	},
					// 	{
					// 		startValue: this.chartData.categories[Math.round(this.chartData.categories.length /
					// 			2, 0)],
					// 		end: this.chartData.categories[this.chartData.categories.length - 1]
					// 	}
					// ],
          series: [{
            name: this.chartData.series[0].name,
            type: this.chartData.series[0].type,
            data: this.chartData.series[0].data,
            itemStyle: {
              // color:function(params) {
              //   // console.log(params)
              //   var colorList
              //   if (params.seriesName==='营业收入预测'||params.seriesName==='净利润预测'){
              //      colorList = ["#3398db", "#3398db", "#f7a35c", "#f7a35c", "#f7a35c"];
              //      return colorList[params.dataIndex]
              //   }else if(params.value<0){
              //     return '#aa0000'
              //   }
              //   else{
              //     return "#3398db"
              //   }
              //   },
               // this.chartData.series[0].name==='营业收入预测'||  this.chartData.series[0].name==='净利润预测'? :'#00aaff',

            },
            // stack: 'a',
            // areaStyle: {
            //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            //     offset: 0,
            //     color: '#00aaff'
            //   }, {
            //     offset: 1,
            //     color: '#ffe'
            //   }])
            // },

          },{
            name: this.chartData.series[1].name,
            type: this.chartData.series[1].type,
            data: this.chartData.series[1].data,
            // yAxisIndex: 1,
            // itemStyle: {
            //   color: '#5a5a5a'
            // },



          }]
        })
      }
    },
    computed: {
      echarts() {
        return 'echarts' + Math.random() * 100000
      }
    },
    mounted: function() {
      const vm = this
      vm.$nextTick(() => {
        vm.drawChart()
      })
    },
    created: () => {}
  }
</script>

<style scoped>
</style>
