@echo off
setlocal enabledelayedexpansion

REM ===== 基于截图的配置 ========================================
set "SERVICE_NAME=DWAPI"
set "NODE_PATH=D:\Program Files\nodejs\node.exe"
set "APP_SCRIPT=server.js"
set "WORKING_DIR=D:\api\newAPI"
set "APP_ARGS="

REM Details标签配置
set "DISPLAY_NAME=数字化平台"
set "DESCRIPTION=数字化平台"
set "STARTUP_TYPE=SERVICE_AUTO_START"  REM 对应Automatic启动类型

REM Log on标签配置
set "LOGON_ACCOUNT=KCUS\CNNBAP13"
set "LOGON_PASSWORD=Abcd1234567890"  REM 替换为实际密码

REM NSSM路径
set "NSSM_PATH=nssm.exe"

REM ===== 服务检测与清理 ========================================
echo 正在检查服务状态...
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorlevel% equ 0 (
    echo 发现已存在的服务: %SERVICE_NAME%
    
    REM 停止服务
    echo 正在停止服务...
    %NSSM_PATH% stop "%SERVICE_NAME%" confirm >nul
    timeout /t 3 /nobreak >nul
    
    REM 检查服务是否仍在运行
    sc query "%SERVICE_NAME%" | findstr /i "RUNNING" >nul
    if %errorlevel% equ 0 (
        echo 强制停止服务...
        net stop "%SERVICE_NAME%" /y >nul
        timeout /t 5 /nobreak >nul
    )
    
    REM 移除旧服务
    echo 正在移除旧服务...
    %NSSM_PATH% remove "%SERVICE_NAME%" confirm >nul
    if errorlevel 1 (
        echo 尝试使用SC删除...
        sc delete "%SERVICE_NAME%" >nul
    )
    
    REM 二次确认是否移除成功
    sc query "%SERVICE_NAME%" >nul 2>&1
    if %errorlevel% equ 0 (
        echo [错误] 无法移除现有服务
        exit /b 1
    )
    echo 旧服务已成功移除
)

REM ===== 验证NSSM可用性 =======================================
where %NSSM_PATH% >nul 2>&1 || (
    echo [错误] 未找到nssm.exe，请添加到PATH或指定完整路径
    exit /b 1
)

REM ===== 创建新服务 ===========================================
echo 正在创建新服务: %SERVICE_NAME%
%NSSM_PATH% install "%SERVICE_NAME%" "%NODE_PATH%" "%APP_SCRIPT%"
if errorlevel 1 (
    echo [错误] 服务创建失败
    exit /b 1
)

REM ===== 配置服务参数 =========================================
%NSSM_PATH% set "%SERVICE_NAME%" AppDirectory "%WORKING_DIR%"
%NSSM_PATH% set "%SERVICE_NAME%" DisplayName "%DISPLAY_NAME%"
%NSSM_PATH% set "%SERVICE_NAME%" Description "%DESCRIPTION%"
%NSSM_PATH% set "%SERVICE_NAME%" Start %STARTUP_TYPE%
%NSSM_PATH% set "%SERVICE_NAME%" ObjectName "%LOGON_ACCOUNT%" "%LOGON_PASSWORD%"

REM ===== 启动服务并验证 =======================================
echo 正在启动服务...
%NSSM_PATH% start "%SERVICE_NAME%"
timeout /t 3 /nobreak >nul

sc query "%SERVICE_NAME%" | findstr /i "RUNNING" >nul
if %errorlevel% equ 0 (
    echo ===== 服务部署成功 =====
    echo 服务名称: %SERVICE_NAME%
    echo 显示名称: %DISPLAY_NAME%
    echo 运行账户: %LOGON_ACCOUNT%
    echo 工作目录: %WORKING_DIR%
    echo 启动命令: "%NODE_PATH%" "%APP_SCRIPT%"
) else (
    echo [警告] 服务启动失败，请检查日志
    echo 尝试手动启动: nssm start "%SERVICE_NAME%"
    exit /b 1
)

endlocal
