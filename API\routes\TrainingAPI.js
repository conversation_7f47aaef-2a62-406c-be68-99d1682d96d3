var express = require('express')
var router = express.Router()
var sqlexec_Training = require('../sql/sqlTraining')
var moment = require('moment')
const { resolve } = require('q')


router.get("/TrainingList", (req, res) => {
    const Training_Category = req.query.Training_Category
    const Business_Category = req.query.Business_Category
    const Position = req.query.Position
    // console.log("Training_Category", Training_Category)
    // console.log("Business_Category", Business_Category)
    // console.log("Position", Position)
    var returnQuery = new sqlexec_Training()
    returnQuery.TrainingList(Training_Category, Business_Category, Position, function (result) {
        if (result.length > 0) {
            returnQuery.TrainingMaterialList(function (result_material) {
                console.log(result)
                let trainingListdata=result
                for (var i = 0; i < result.length; i++) {
                    trainingListdata[i]["Training_Material"] = []
                    for (var j = 0; j < result_material.length; j++) {
                        if (result[i].Training_ID == result_material[j].Training_ID) {
                            const singleData = { 'name': result_material[j].Training_Material_Name, 'url': result_material[j].Training_Material_URL }
                            trainingListdata[i].Training_Material.push(singleData)
                        }
                    }
                }
                //console.log(trainingListdata)
                res.send(trainingListdata)
            })
        }
    })
})

router.get("/TrainingCategory", (req, res) => {
    let returnCategory = {
        'Training_Category': [],
        'Business_Category': [],
        'Position': [],
    }
    var returnQuery = new sqlexec_Training()
    returnQuery.getTraining_Category(function (result) {
        returnCategory['Training_Category'] = result.map(item => item.Training_Category);
        returnQuery.getBusiness_Category(function (result1) {
            returnCategory['Business_Category'] = result1.map(item => item.Business_Category);
            returnQuery.getPosition(function (result2) {
                returnCategory['Position'] = result2.map(item => item.Position);
                res.send(returnCategory)
            })
        })
    })
})

router.get("/TrainingList_By_ID", (req, res) => {
    var returnQuery = new sqlexec_Training()
    returnQuery.TrainingList_By_ID(function (result) {
        res.send(result)
    })
})

router.get("/Employee_Info", (req, res) => {
    var returnQuery = new sqlexec_Training()
    returnQuery.Employee_Info(req.query.Employee_Name,function (result) {
        res.send(result)
    })
})

router.post("/SP_API_Training_Score_Update", (req, res) => {
    console.log(req.body)
    var returnQuery = new sqlexec_Training()
    const data = req.body
    const data_single = {
        "Training_ID": data['ID'],
        "Traing_Name": data['考试名称'],
        "Employee_ID": data['工号'],
        "Employee_Name": data['姓名'],
        "Training_Result": data['是否通过'],
        "Trainer": data['培训者'],
        "Score": data['最高分'],
        "Training_Time": data['第一次开考时间']
    }
    returnQuery.SP_API_Training_Score_Update(data_single, function (result) {
        res.send(result)
    })
})

router.get("/SP_API_Training_Query_Personal", (req, res) => {
    var returnQuery = new sqlexec_Training()
    returnQuery.SP_API_Training_Query_Personal(req.query.Employee_ID, function (result) {
        res.send(result)
    })
})


router.post("/Training_Student_Feedback", (req, res) => {
    console.log(req.body)
    var returnQuery = new sqlexec_Training()
    const data = req.body
    returnQuery.Training_Student_Feedback(data, function (result) {
        res.send(result)
    })
})



module.exports = router