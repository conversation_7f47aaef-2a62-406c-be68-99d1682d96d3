<template>
	<base-content>
		<div class="q-pa-md ">
			<!--  -->

			<q-dialog v-model="firtDialog" transition-show="flip-down">
				<q-card style="width: 800px; max-width: 100vw;">
					<q-card-section class=" bg-teal text-white fit row  justify-between  ">
                    <div class="text-h6 ">人员名单维护</div>
                    <q-space />
                    <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                </q-card-section>
					<q-card-section>
						<div style="width: 40%;" v-if="employeeData"
							class="full-width row  justify-start items-start content-start q-col-gutter-sm">
							<template v-for="(item, key)  in employeeData">
								<q-input filled v-model="employeeDataValue[key]" :label="key" :disable="key=='Modified_By'||key=='Modified_On'||key=='id'" />
							</template>
						</div>
					</q-card-section>
					<q-card-actions align="right">
						<q-btn style="width:150px" :label="opType=='更新'?'更新人员':'新增人员'" color="purple" @click="updateEmployee(opType)"/>
						<q-btn style="width:150px" label="删除人员" color="red"  v-if="opType=='更新'" @click="updateEmployee('删除')"/>
						<q-btn style="width:150px" label="取消" color="primary" v-close-popup />
					</q-card-actions>
				</q-card>
			</q-dialog>

			<div class="q-pa-md" v-if="rows">
				<q-table :data="rows" row-key="name" :pagination.sync="myPagination" class="my-sticky-virtscroll-table"
					virtual-scroll :virtual-scroll-sticky-size-start="48" :rows-per-page-options="[0]" :filter="filter">

					<template v-slot:top-right>
						<q-btn style="width: 200px;margin-right:20px" label="新增人员" color="primary" @click="toAddEmployee()"/>
						<q-input borderless dense debounce="300" v-model="filter" placeholder="Search">
							<template v-slot:append>
								<q-icon name="search" />
							</template>
						</q-input>
					</template>

					<template v-slot:body="props">
						<q-tr :props="props" @click="detail(props)">
							<q-td v-for="item in props.cols" :key="item.name" :props="props">
								{{ props.row[item.name] }}
							</q-td>
						</q-tr>
					</template>

				</q-table>
			</div>
		</div>
	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
// we generate lots of rows here

export default {
	name: 'employee',
	components: {
		BaseContent,
	},
	mounted() {
		this.getEmployee_List()
	},


	data() {
		return {
			filter: '',
			rows: false,
			myPagination: {
				rowsPerPage: 0
			},
			employeeData: false,
			employeeDataValue: {},
			firtDialog: false,
			opType:false,
		}
	},

	methods: {
		detail(e) {
			const _this=this
			_this.employeeDataValue=e.row
			_this.opType='更新'
			_this.firtDialog=true
		},
		toAddEmployee(){
			const _this=this
			_this.employeeDataValue=_this.employeeData
			_this.opType="新增"
			_this.firtDialog=true
		},
		async getEmployee_List() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('data_base/getEmployee_List')
			console.log(res)
			_this.rows = res
			const keys = Object.keys(res[0]);
			const emplyeeListData = {}
			keys.forEach(key => {
				emplyeeListData[key] = null;
			});
			_this.employeeData = emplyeeListData
			_this.employeeDataValue = emplyeeListData
			console.log(emplyeeListData)
		},
		async updateEmployee(opType){
			const _this = this
			console.log('opType',opType)
			_this.employeeDataValue['Modified_On']=new Date(Date.now() + 8 * 3600 * 1000).toISOString();
			_this.employeeDataValue['Modified_By']=localStorage.getItem('username')
            const postParam = {
                'postData': _this.employeeDataValue,
				'opType':opType
            }
			_this.$http.post('data_base/updateEmployee', postParam).then(function (response) {
                console.log('response', response)
                if (response.data === '更新成功') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `${opType}人员成功`,
                        position: 'top'
                    })
                    _this.getEmployee_List()
					_this.firtDialog=false
                }
            })
		}

	}
}
</script>
<style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */
  height: 700px

  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0
</style>
