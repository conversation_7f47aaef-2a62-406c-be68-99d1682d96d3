const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.Tech_Project_Data = function (startDate, endDate, callBack) {
    sqlexec.prototype.
        _query(`select [ID]
      ,[Project_Name]
      ,[Project_Material_URL]
      ,[Mechanical_Engineer]
      ,[Electrical_Engineer]
      ,[Process_Engineer]
      ,[Packaging_Engineer]
      ,format([Project_Start_Date],'yyyy-MM-dd') Project_Start_Date
      ,format([Project_Next_Phase_End_Date],'yyyy-MM-dd') Project_Next_Phase_End_Date
      ,[Project_Feature]
      ,[Project_Proposal_URL]
      ,[Project_Experiment_Report_URL]
      ,[Project_Experiment_Cost]
      ,[Project_Evaluation_Program_URL]
      ,[Project_Capex_Forecast]
      ,[Project_Capex_YTD_Actual]
      ,[Project_Capex_Run_Rate]
      ,[Project_Next_Phase]
      ,format([Project_Next_Phase_Date],'yyyy-MM-dd') Project_Next_Phase_Date
      ,[Project_Progress]
      ,[Project_Status]
      ,[Project_Parent_ID]
      ,[Modify_By]
      ,format([Modify_On],'yyyy-MM-dd HH:mm:ss')Modify_On
	  ,datediff(d,Project_Next_Phase_Date,Project_Next_Phase_End_Date) duration
       from Tech_Project_Data where Project_Start_Date between  '${startDate}' and  '2999-12-31' or Project_Next_Phase_End_Date between  '${startDate}' and  '2999-12-31' order by Project_Start_Date `, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.updateTech_Project_Data_Insert = function (data, ID, callBack) {
    // console.log('data',data)
    console.log(`update Tech_Project_Data
            set Project_Name = '${data.Project_Name}',
            Project_Material_URL = '${data.Project_Material_URL}',
            [Mechanical_Engineer]=${data.Mechanical_Engineer == null ? null : `'${data.Mechanical_Engineer}'`},
            [Electrical_Engineer]=${data.Electrical_Engineer == null ? null : `'${data.Electrical_Engineer}'`},
            [Process_Engineer]=${data.Process_Engineer == null ? null : `'${data.Process_Engineer}'`},
            [Packaging_Engineer]=${data.Packaging_Engineer == null ? null : `'${data.Packaging_Engineer}'`},
            Project_Start_Date = '${data.Project_Start_Date}',
            Project_Next_Phase_End_Date = '${data.Project_Next_Phase_End_Date}',
            Project_Feature = '${data.Project_Feature}',
            Project_Proposal_URL = '${data.Project_Proposal_URL}',
            Project_Experiment_Report_URL = '${data.Project_Experiment_Report_URL}',
            Project_Experiment_Cost = '${data.Project_Experiment_Cost}',
            Project_Evaluation_Program_URL = '${data.Project_Evaluation_Program_URL}',
            Project_Capex_Forecast = '${data.Project_Capex_Forecast}',
            Project_Capex_YTD_Actual= '${data.Project_Capex_YTD_Actual}',
            Project_Capex_Run_Rate = '${data.Project_Capex_Run_Rate}',
            Project_Next_Phase = '${data.Project_Next_Phase}',
            Project_Next_Phase_Date = '${data.Project_Next_Phase_Date}',
            Project_Progress = '${data.Project_Progress}',
            Project_Status = '${data.Project_Status}',
            Project_Parent_ID = ${data.Project_Parent_ID},
            Modify_By = '${data.Modify_By}',
            Modify_On = GETDATE()
            where ID = ${ID}`)
    sqlexec.prototype.
        _query(`update Tech_Project_Data
            set Project_Name = '${data.Project_Name}',
            Project_Material_URL = '${data.Project_Material_URL}',
            [Mechanical_Engineer]='${data.Mechanical_Engineer}',
            [Electrical_Engineer]='${data.Electrical_Engineer}',
            [Process_Engineer]='${data.Process_Engineer}',
            [Packaging_Engineer]='${data.Packaging_Engineer}',
            Project_Start_Date = '${data.Project_Start_Date}',
            Project_Next_Phase_End_Date = '${data.Project_Next_Phase_End_Date}',
            Project_Feature = '${data.Project_Feature}',
            Project_Proposal_URL = '${data.Project_Proposal_URL}',
            Project_Experiment_Report_URL = '${data.Project_Experiment_Report_URL}',
            Project_Experiment_Cost = '${data.Project_Experiment_Cost}',
            Project_Evaluation_Program_URL = '${data.Project_Evaluation_Program_URL}',
            Project_Capex_Forecast = '${data.Project_Capex_Forecast}',
            Project_Capex_YTD_Actual= '${data.Project_Capex_YTD_Actual}',
            Project_Capex_Run_Rate = '${data.Project_Capex_Run_Rate}',
            Project_Next_Phase = '${data.Project_Next_Phase}',
            Project_Next_Phase_Date = '${data.Project_Next_Phase_Date}',
            Project_Progress = '${data.Project_Progress}',
            Project_Status = '${data.Project_Status}',
            Project_Parent_ID = ${data.Project_Parent_ID},
            Modify_By = '${data.Modify_By}',
            Modify_On = GETDATE()
            where ID = ${ID}`, function (err, result) {
            if (err) {
            }
            return callBack('更新成功')
        })
}
sqlexec.prototype.insertTech_Project_Data_Insert = function (data, callBack) {
    // 数据验证和处理
    const processedData = {
        Project_Name: data.Project_Name || '',
        Project_Material_URL: data.Project_Material_URL || '',
        Mechanical_Engineer: data.Mechanical_Engineer || '',
        Electrical_Engineer: data.Electrical_Engineer || '',
        Process_Engineer: data.Process_Engineer || '',
        Packaging_Engineer: data.Packaging_Engineer || '',
        Project_Start_Date: data.Project_Start_Date || null,
        Project_Next_Phase_End_Date: data.Project_Next_Phase_End_Date || null,
        Project_Feature: data.Project_Feature || '',
        Project_Proposal_URL: data.Project_Proposal_URL || '',
        Project_Experiment_Report_URL: data.Project_Experiment_Report_URL || '',
        Project_Experiment_Cost: data.Project_Experiment_Cost || '',
        Project_Evaluation_Program_URL: data.Project_Evaluation_Program_URL || '',
        Project_Capex_Forecast: data.Project_Capex_Forecast || 0,
        Project_Capex_YTD_Actual: data.Project_Capex_YTD_Actual || 0,
        Project_Capex_Run_Rate: data.Project_Capex_Run_Rate || 0,
        Project_Next_Phase: data.Project_Next_Phase || '',
        Project_Next_Phase_Date: data.Project_Next_Phase_Date || null,
        Project_Progress: data.Project_Progress || 0,
        Project_Status: data.Project_Status || '',
        Project_Parent_ID: data.Project_Parent_ID || null,
        Modify_By: data.Modify_By || ''
    };

    const insertSQL = `insert into Tech_Project_Data (
        Project_Name,Project_Material_URL,Mechanical_Engineer,Electrical_Engineer,Process_Engineer,Packaging_Engineer,
        Project_Start_Date,Project_Next_Phase_End_Date,Project_Feature,Project_Proposal_URL,Project_Experiment_Report_URL,
        Project_Experiment_Cost,Project_Evaluation_Program_URL,Project_Capex_Forecast,Project_Capex_YTD_Actual,
        Project_Capex_Run_Rate,Project_Next_Phase,Project_Next_Phase_Date,Project_Progress,Project_Status,
        Project_Parent_ID,Modify_By,Modify_On
    ) values (
        '${processedData.Project_Name}','${processedData.Project_Material_URL}','${processedData.Mechanical_Engineer}',
        '${processedData.Electrical_Engineer}','${processedData.Process_Engineer}','${processedData.Packaging_Engineer}',
        ${processedData.Project_Start_Date ? `'${processedData.Project_Start_Date}'` : 'NULL'},
        ${processedData.Project_Next_Phase_End_Date ? `'${processedData.Project_Next_Phase_End_Date}'` : 'NULL'},
        '${processedData.Project_Feature}','${processedData.Project_Proposal_URL}','${processedData.Project_Experiment_Report_URL}',
        '${processedData.Project_Experiment_Cost}','${processedData.Project_Evaluation_Program_URL}',
        ${processedData.Project_Capex_Forecast},${processedData.Project_Capex_YTD_Actual},${processedData.Project_Capex_Run_Rate},
        '${processedData.Project_Next_Phase}',
        ${processedData.Project_Next_Phase_Date ? `'${processedData.Project_Next_Phase_Date}'` : 'NULL'},
        ${processedData.Project_Progress},'${processedData.Project_Status}',
        ${processedData.Project_Parent_ID || 'NULL'},'${processedData.Modify_By}',GETDATE()
    )`;

    console.log('执行插入SQL:', insertSQL);
    console.log('插入数据:', JSON.stringify(processedData, null, 2));

    sqlexec.prototype._query(insertSQL, function (err, result) {
        if (err) {
            console.error('插入数据时发生错误:', err);
            console.error('错误的SQL语句:', insertSQL);
            return callBack('插入失败: ' + err.message);
        }
        console.log('插入成功，结果:', result);
        return callBack('新增成功');
    });
}

sqlexec.prototype.deleteTech_Project_Data = function (ID, callBack) {
    console.log(`delete from Tech_Project_Data where ID = ${ID}`)
    sqlexec.prototype.
        _query(`delete from Tech_Project_Data where ID = ${ID}`, function (err, result) {
            if (err) {
            }
            return callBack('删除成功')
        })
}




module.exports = sqlexec;