<template>
    <q-splitter v-model="splitterModel">
        <q-dialog v-model="card" transition-show="flip-down">
            <q-card style="width: 1000px; max-width: 100vw;">
                <transition appear enter-active-class="animated fadeIn" leave-active-class="animated fadeOut">
                    <q-card-section>
                        <q-table :data="peopleRows" :columns="peopleColumns" row-key="name" :pagination.sync="myPagination">
                            <template v-slot:header="props">
                                <q-tr :props="props">
                                    <q-th auto-width class="bg-primary text-white">{{ innerTab }}操作</q-th>
                                    <q-th v-for="col in props.cols" :key="col.name" :props="props">
                                        {{ col.label }}
                                    </q-th>
                                </q-tr>
                            </template>

                            <template v-slot:body="props">
                                <q-tr :props="props" :class="props['rowIndex'] % 2 === 0 ? 'bg-grey-2 ellipsis' : ''">
                                    <q-td auto-width>
                                        <q-btn style="width:50px" size="sm"
                                            :color="props.row[innerTab] == 'No' ? 'red' : 'positive'"
                                            @click="rowClick(props, 'crew')" :disable="btnEnable">
                                            {{ props.row[innerTab] }}
                                        </q-btn>
                                    </q-td>
                                    <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                        {{ col.value }}
                                    </q-td>
                                </q-tr>
                            </template>
                            <template v-slot:bottom>
                                <q-space />
                                <q-btn v-close-popup color="primary" label="关闭" />
                            </template>
                        </q-table>
                    </q-card-section>
                </transition>
                <q-inner-loading :showing="visible">
                    <q-spinner-gears size="50px" color="primary" />
                </q-inner-loading>
            </q-card>
        </q-dialog>

        <q-dialog v-model="monthlyPrompt" transition-show="flip-down">
            <q-card style="width: 1000px; max-width: 100vw;">
                <transition appear enter-active-class="animated fadeIn" leave-active-class="animated fadeOut">
                    <q-card-section>
                        <q-table :data="rows_byMonthly" :columns="columns_byMonthly" row-key="name"
                            :pagination.sync="myPagination">
                            <template v-slot:header="props">
                                <q-tr :props="props">
                                    <q-th auto-width class="bg-primary text-white">{{ innerTab }}操作</q-th>
                                    <q-th v-for="col in props.cols" :key="col.name" :props="props">
                                        {{ col.label }}
                                    </q-th>
                                </q-tr>
                            </template>

                            <template v-slot:body="props">
                                <q-tr :props="props" :class="props['rowIndex'] % 2 === 0 ? 'bg-grey-2 ellipsis' : ''">
                                    <q-td auto-width>
                                        <q-btn style="width:50px" size="sm"
                                            :color="props.row[innerTab] == 'No' ? 'red' : 'positive'"
                                            @click="MonthlyRowClick(props, 'single')" :disable="btnEnable">
                                            {{ props.row[innerTab] }}
                                        </q-btn>
                                    </q-td>
                                    <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                        {{ col.value }}
                                    </q-td>
                                </q-tr>
                            </template>
                            <template v-slot:bottom="props">
                                <q-space />
                                <q-btn style="margin-right:20px" color="primary"
                                    @click="updatePerformance_Monthly(rows_byMonthly[0]['财务年月'], 'Multiple')" label="批量审批"
                                    :disable="btnEnable" />
                                <q-btn v-close-popup color="primary" label="关闭" />
                            </template>
                        </q-table>
                    </q-card-section>
                </transition>
                <q-inner-loading :showing="visible">
                    <q-spinner-gears size="50px" color="primary" />
                </q-inner-loading>
            </q-card>
        </q-dialog>

        <template v-slot:before>
            <q-tabs v-model="innerTab" vertical class="text-teal">
                <q-tab name="安全" icon="mail" label="安全" />
                <q-tab name="质量" icon="alarm" label="质量" />
                <q-tab name="生产" icon="movie" label="生产" />
            </q-tabs>
        </template>
        <template v-slot:after>
            <div class="q-pa-md q-gutter-md row items-start">
                <!-- <div class="text-h6">安全审批</div> -->
                <q-input v-model="dateRange.startDate" filled type="date" label="开始日期" stack-label dense>
                    <template v-slot:prepend>
                        <q-icon name="event" />
                    </template>
                </q-input>

                <q-input v-model="dateRange.endDate" filled type="date" label="结束日期" stack-label dense>
                    <template v-slot:prepend>
                        <q-icon name="event" />
                    </template>
                </q-input>
                <q-select color="teal" filled v-model="Line" :options="LineArray" label="机台"
                    style="min-width: 150px; max-width: 300px" dense>
                    <template v-slot:prepend>
                        <q-icon name="event" />
                    </template>
                </q-select>

                <q-btn push color="primary" class="btn-fixed-width" style="min-width:150px"
                    @click="getDailyPerformanceAPI(innerTab)" dense :loading="loading">查询
                    <template v-slot:loading>
                        <q-spinner-hourglass class="on-left" />
                        数据获取中...
                    </template>
                </q-btn>

                <q-btn push color="purple" label="一键批准" class="btn-fixed-width" style="min-width:150px"
                    @click="updaUpdate_multiple_items" dense :disable="btnEnable" />
                <q-btn push color="secondary" label="月度审批状态" class="btn-fixed-width" style="min-width:150px"
                    @click="getMonthlyProductData" dense />
            </div>

            <div>
                <q-table :data="rows" :columns="columns" row-key="name" :pagination.sync="myPagination"
                    :title="innerTab + '审批'">
                    <!-- <transition appear enter-active-class="animated fadeIn" leave-active-class="animated fadeOut"> -->
                    <template v-slot:top>
                        <div>{{ innerTab + '审批' }}</div>
                        <div style="color:darkgrey;font-size:15px;margin-left: 30px;">注意项:无法审批班组人员为空的项目</div>
                    </template>
                    <template v-slot:header="props">
                        <q-tr :props="props">
                            <q-th auto-width class="bg-primary text-white">{{ innerTab }}操作</q-th>
                            <template v-if="innerTab === '生产'">
                                <q-th auto-width class="bg-primary text-white">{{ innerTab }}绩效判断</q-th>
                            </template>

                            <q-th v-for="col in props.cols" :key="col.name" :props="props">
                                {{ col.label }}
                            </q-th>
                        </q-tr>
                    </template>

                    <template v-slot:body="props">
                        <q-tr :props="props" :class="props['rowIndex'] % 2 === 0 ? 'bg-grey-2 ellipsis' : ''">

                            <q-td auto-width>
                                <q-btn style="width:50px" size="sm"
                                    :color="props.row[innerTab] == 'No' ? 'red' : 'positive'"
                                    @click="rowClick(props, 'main')" :disable="btnEnable">
                                    {{ props.row[innerTab] }}
                                </q-btn>
                            </q-td>
                            <template v-if="props.row['计划时间'] >= 0">
                                <q-td auto-width>
                                    <q-icon
                                        v-if="props.row['计划时间'] < 720 || props.row['OEE2_T'] < props.row['OEETar'] || props.row['Waste'] > props.row['WasteTar']"
                                        name="info" color="negative" size="25px" />
                                </q-td>
                            </template>
                            <q-td v-for="col in props.cols" :key="col.name" :props="props" @click="toCrewData(props)">
                                <!-- <q-icon v-if="col.name == '计划时间' && col.value < 720" name="info" color="red"
														size="15px" /> -->
                                {{ col.value }}
                            </q-td>
                        </q-tr>
                    </template>
                    <!-- </transition> -->
                </q-table>
                <q-inner-loading :showing="visibleBody">
                    <q-spinner-gears size="50px" color="primary" />
                </q-inner-loading>
            </div>


        </template>
    </q-splitter>
</template>

<script>
export default {
    //props: ['AwardPerformanceType'],
    data() {
        return {
            dateRange: {
                startDate: false,
                endDate: false,
            },
            innerTab: '安全',
            splitterModel: 10,
            LineArray: [],
            Line: '',
            columns: [],
            rows: [],
            peopleColumns: [],
            peopleRows: [],
            myPagination: { rowsPerPage: 0 },
            card: false,
            visible: false,
            visibleBody: false,
            loading: false,
            rows_byMonthly: [],
            columns_byMonthly: [],
            monthlyPrompt: false,
            btnEnable: true
        }
    },
    computed: {

    },
    created() {
        //console.log('lineArray',localStorage.getItem('lineArray'))

    },
    mounted() {
        const _this = this
        _this.$nextTick(()=> {
            console.log(localStorage.getItem('lineArray').split(','))
            this.LineArray = localStorage.getItem('lineArray').split(',')
            this.AccessToBtn('安全')
        })
        


    },
    watch: {
       innerTab(newValue, oldValue) {
            console.log(oldValue + '->' + newValue)
            this.dateRange = { startDate: "", endDate: "" }
            this.Line = ''
            this.rows = []
            this.AccessToBtn(newValue)
        }
    },
    methods: {
        async AccessToBtn(awardType){
            this.btnEnable = await checkAccess(awardType)
        },
        toCrewData(col) {
            console.log('col', col)
            const shiftlinkid = col.row['日期'] + "-" + col.row['机台'] + "-" + col.row['班次']
            if (col.row['班组成员'] == null) {
                this.$q.dialog({
                    title: '无法审批',
                    message: '无法审批班组成员为空的项目'
                })
                return
            }
            this.card = true
            this.visible = true
            console.log('shiftlinkid', shiftlinkid)
            this.peopleRows = []
            this.getDailyPerformanceAPI_ByPeople(shiftlinkid, this.innerTab)
            this.visible = false
        },
        async getDailyPerformanceAPI_ByPeople(shiftlinkid, fun) {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`approve/SP_API_Award_Performance_by_People?fun=${fun}&shiftlinkid=${shiftlinkid}`)
            console.log('getDailyPerformanceAPI_ByPeople', res)
            _this.peopleRows = res.result
            _this.peopleColumns = res.columns
        },
        async getDailyPerformanceAPI(fun) {
            var _this = this
            _this.visibleBody = true
            _this[`loading`] = true
            const {
                data: res
            } = await _this.$http.get(`approve/SP_API_Award_Performance_List?fun=${fun}&startDate=${_this.dateRange.startDate}&endDate=${_this.dateRange.endDate}&Line=${_this.Line}`)
            console.log('getDailyPerformanceAPI', res)
            _this.rows = res.result
            _this.columns = res.columns
            _this.visibleBody = false
            _this[`loading`] = false
        },

        async rowClick(row, module) {
            var _this = this
            console.log(row)
            var rowIndex = row.rowIndex
            var rowData = row.row
            var id = rowData['ID']
            var shiftLinkID_row = rowData['日期'] + '-' + rowData['机台'] + '-' + rowData['班次']
            var postData = {
                ShiftLinkID: shiftLinkID_row,
                Date: rowData['日期'],
                Line: rowData['机台'],
                Shift: rowData['班次'],
                Performance_Type: _this.innerTab,
                Result: rowData[_this.innerTab],
                comment: rowData['备注'],
                Modified_By: localStorage.getItem('account'),
                id: id,
                Employee_ID: null
            }
            if (rowData['班组成员'] == null && module == 'main') {
                _this.$q.dialog({
                    title: '无法审批',
                    message: '无法审批班组成员为空的项目'
                })
                return
            }

            if (rowData[_this.innerTab] == 'Yes' || rowData[_this.innerTab] == null) {
                _this.$q.dialog({
                    title: _this.innerTab + '(' + shiftLinkID_row + ')' + '拒绝理由',
                    message: '请输入拒绝理由！',
                    prompt: {
                        model: '',
                        type: 'text' // optional
                    },
                    cancel: true,
                    persistent: true
                }).onOk(async data => {
                    // console.log('>>>> OK, received', data)
                    postData['Result'] = 'No'
                    postData['comment'] = data
                    if (module == 'crew') {
                        postData.Employee_ID = rowData['员工ID']
                    }
                    _this.updateToSql(postData, rowIndex, module)

                }).onCancel(() => {
                    // console.log('>>>> Cancel')
                    return
                }).onDismiss(() => {
                    // console.log('I am triggered on both OK and Cancel')
                })
            } else if (rowData[_this.innerTab] == 'No') {
                postData['Result'] = 'Yes'
                postData['comment'] = null
                if (module == 'crew') {
                    postData.Employee_ID = rowData['员工ID']
                }
                _this.updateToSql(postData, rowIndex, module)
            }

            //post请求

        },

        updateToSql(data, rowIndex, module) {
            var _this = this
            console.log('data', data)
            _this.$http.post('approve/API_Execute_Award_Performance_Daily', data).then(function (response) {
                console.log('response', response)
                if (response.data === '更新成功') {
                    if (module === 'main') {
                        _this.rows[rowIndex]['备注'] = data['comment']
                        _this.rows[rowIndex][_this.innerTab] = data['Result']
                    } else {
                        _this.peopleRows[rowIndex]['备注'] = data['comment']
                        _this.peopleRows[rowIndex][_this.innerTab] = data['Result']
                    }

                }
            })
        },

        updaUpdate_multiple_items() {
            var _this = this
            const data = {
                startDate: _this.dateRange.startDate,
                endDate: _this.dateRange.endDate,
                Line: _this.Line,
                Performance_Type: _this.innerTab,
                modifyName: localStorage.getItem('account')
            }
            _this.$http.post('approve/SP_API_Update_Award_Performance', data).then(function (response) {
                console.log('response', response)
                if (response.data === '更新成功') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `批量审批成功`,
                        position: 'top'
                    })
                    _this.getDailyPerformanceAPI([_this.innerTab])
                }
            })

        },
        async getMonthlyProductData() {
            const _this = this
            console.log(_this.dateRange.startDate)
            if (_this.dateRange.startDate == false) {
                _this.$q.notify({
                    type: 'negative',
                    message: `开始和结束日期不能为空！请选择开始时间`,
                    position: 'top'
                })
                return
            }
            const fun = _this.innerTab
            const {
                data: res
            } = await _this.$http.get(`approve/SP_API_Award_Performance_Monthly_List?fun=${fun}&startDate=${_this.dateRange.startDate}&endDate=${_this.dateRange.endDate}`)
            console.log('SP_API_Award_Performance_Monthly_List', res)
            _this.rows_byMonthly = res.result
            _this.columns_byMonthly = res.columns
            _this.monthlyPrompt = true
        },
        async MonthlyRowClick(row, type) {
            console.log(row)
            console.log(type)
            const _this = this
            const monthlyProductData = _this.rows_byMonthly
            var rowIndex = row.rowIndex
            var rowData = row.row
            var postData = {
                finYearMonthly: rowData['财务年月'],
                Line: rowData['机台'],
                Performance_Type: _this.innerTab,
                Result: rowData[_this.innerTab],
                comment: rowData[_this.innerTab + '备注'],
                modifyName: localStorage.getItem('account'),
                type: type
            }
            if (rowData[_this.innerTab] == 'Yes' || rowData[_this.innerTab] == null) {
                _this.$q.dialog({
                    title: _this.innerTab + '(' + rowData['机台'] + ')' + '拒绝理由',
                    message: '请输入拒绝理由！',
                    prompt: {
                        model: '',
                        type: 'text' // optional
                    },
                    cancel: true,
                    persistent: true
                }).onOk(async data => {
                    console.log('data', data)
                    postData['Result'] = 'No'
                    postData['comment'] = data
                    _this.updatePerformance_Monthly(postData, type)
                }).onCancel(() => {
                    // console.log('>>>> Cancel')
                    return
                }).onDismiss(() => {
                    // console.log('I am triggered on both OK and Cancel')
                })
            } else if (rowData[_this.innerTab] == 'No') {
                postData['Result'] = 'Yes'
                postData['comment'] = ''
                _this.updatePerformance_Monthly(postData, type)
            }



        },
        async updatePerformance_Monthly(param, type) {
            const _this = this
            console.log('param', param)
            console.log('type', type)
            var postData = {}
            const Performance_Type = _this.innerTab
            if (type === 'Multiple') {
                var postData = {
                    finYearMonthly: param,
                    Line: '',
                    Performance_Type: _this.innerTab,
                    Result: 'Yes',
                    comment: '',
                    modifyName: localStorage.getItem('account'),
                    type: type
                }
            }
            if (type === 'single') {
                postData = param
            }
            _this.$http.post('approve/SP_API_Update_Award_Performance_Monthly', postData).then(function (response) {
                console.log('response', response)
                if (response.data === '更新成功') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `审批成功`,
                        position: 'top'
                    })
                    _this.getMonthlyProductData()
                }
            })



        }


    }
}

function checkAccess(award_type) {
    return new Promise((resolve, reject) => {
        const userRole = localStorage.getItem('user_role')
        const userPosition = localStorage.getItem('Position')
        let result = true
        console.log('userRole',userRole)
        console.log('userPosition',userPosition)
        if ((userRole.indexOf('积分系统_审批') !== -1 && userPosition.indexOf(award_type) !== -1) || userRole.indexOf('Admin') !== -1) {
            console.log('有权限')
            result = false
        } else {
            console.log('没权限')
        }
		if(award_type=='安全' && userPosition.indexOf('EHS') !== -1){
		    result = false
		}
        console.log(result)
        resolve(result)
    })
}

</script>

<style></style>





















