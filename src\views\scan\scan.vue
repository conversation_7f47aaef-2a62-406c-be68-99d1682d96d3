<template>

	<base-content>
<!-- 		<div class="q-pa-md q-gutter-sm row items-start">
			<q-btn size="22px" class="q-px-xl q-py-xs" color="purple" label="LSW" @click="gotoLSW" />
			<q-btn size="22px" class="q-px-xl q-py-xs" color="secondary" label="积分系统" @click="gotoAward" />
		</div> -->


		<div>
			<video id="video" width="100%" height="auto"></video>
			<canvas id="canvas"></canvas>
		</div>
		<q-btn size="22px" class="q-px-xl q-py-xs" color="purple" label="打开扫描" @click="scanInit" />
		
	</base-content>

</template>

<script>
	import BaseContent from '../../components/BaseContent/BaseContent'
	import Quagga from 'quagga'

	export default {
		name: 'Home',
		components: {
			BaseContent,
			Quagga
		},
		mounted() {
		 console.log("初始化")

		},
		
		
		data() {
			return {

			}
		},
		
		methods: {
			scanInit(){
				Quagga.init({
				  inputStream: {
				    name: "Live",
				    type: "LiveStream",
				    target: document.querySelector("#video")
				  },
				  decoder: {
				    readers: ["ean_reader"]
				  }
				}, function(err) {
				  if (err) {
				    console.log(err);
				    return
				  }
				  Quagga.start();
				});
			}

		}
	}
</script>
<style lang="css" scoped>
	.my-card {
		width: 100%;
		min-height: 390px;
		height: 100%;
		/*max-width: 350px;*/
	}
</style>
