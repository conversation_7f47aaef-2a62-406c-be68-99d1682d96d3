<template>
	<base-content>
		<div dense style="font-size: 20px;color: black;text-align: center;margin-top: 5px;">Centerline报告</div>
		<div class="q-pa-md">
			<!--  选择框及按钮 -->
			<div class="q-gutter-md row">
				<q-select outlined dense v-model="Line" :options="LineList" style="width: 140px" label-slot clearable>
					<template v-slot:label>产线
						<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
					</template>
				</q-select>
				<q-input v-model="Start" outlined type="date" dense label-slot style="width: 150px">
					<template v-slot:label>
						<q-label for="date-input" class="cursor-pointer" style="font-size: 13px;">
							开始日期
							<em class="q-px-sm bg-deep-orange text-white rounded-borders"
								style="font-size: 10px;">必填</em>
						</q-label>
					</template>
				</q-input>
				<q-input v-model="End" outlined type="date" dense label-slot style="width: 150px">
					<template v-slot:label>
						<q-label for="date-input" class="cursor-pointer" style="font-size: 13px;">
							结束日期
							<em class="q-px-sm bg-deep-orange text-white rounded-borders"
								style="font-size: 10px;">必填</em>
						</q-label>
					</template>
				</q-input>
				<q-btn dense :loading="loading1" color="primary" label="查询" @click="getCentlineList(1)"
					style="width: 70px;height: 40px;" />
			</div>
			<div dense style="font-size: 13px;color: brown;">
				点击查询按钮获取原始数据后才可以进行Tier，Size，国家，C/L点检项的筛选
			</div>
			<div class="q-gutter-md row" style="margin-top: -5px;">
				<q-select outlined dense v-model="Tier" :options="TierList" style="width: 140px" label-slot clearable>
					<template v-slot:label>Tier</template>
				</q-select>
				<q-select outlined dense v-model="Size" :options="SizeList" style="width: 140px" label-slot clearable>
					<template v-slot:label>Size</template>
				</q-select>
				<q-select outlined dense v-model="Country" :options="CountryList" style="width: 140px" label-slot
					clearable>
					<template v-slot:label>国家</template>
				</q-select>
				<q-select outlined dense v-model="Variable" :options="VariableList" style="width: 200px" label-slot
					clearable>
					<template v-slot:label>C/L点检项</template>
				</q-select>
			</div>
		</div>
		<!--  C/L汇总数据 -->
		<div class="q-pa-md row items-start q-gutter-md">
			<q-card style="max-width: 1000px; margin-top: -5px;">
				<q-card-section class="bg-positive text-white">
					<div class="text-h6">需点检项目总次数：<span class="text_type">{{ TotalItem }}</span>
						<span class="text-h6" style="margin-left: 50px;">超范数：</span><span class="text_type">{{
					OverItem }}</span>
						<span class="text-h6" style="margin-left: 50px;">C/L达标率%：</span><span class="text_type">{{
					PASSPercent }}</span>
						<span class="text-h6" style="margin-left: 50px;">生产总班次：</span><span class="text_type">{{
					ShiftCount }}</span>
					</div>
				</q-card-section>
			</q-card>
		</div>
		<q-card-section style="display: flex;margin-top: -20px;">
			<div style="color: brown; margin-right: 10px;"><i class="fas fa-info-circle"></i>说明：1.若手工点检项目未录入实际值,记为一次超范
			</div>
			<div style="color: brown; margin-left: 10px;">2.若有实际值,但是没有录入上下限,不算超范</div>
		</q-card-section>
		<!--  C/L详情列表 -->
		<div class="q-pa-md">
			<q-table dense title="Centerline超范详情" :data="CLListAlarm" :columns="column" row-key="name"
				style="margin-top: -30px;" :pagination="{ rowsPerPage: 20 }">
			</q-table>
		</div>

	</base-content>
</template>
<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import Quagga from 'quagga'



export default {
	components: {
		BaseContent, Quagga,
	},
	mounted() {
		console.log("mounted", this.$route.query)
		this.User_ID = localStorage.getItem('account')
		this.Line = localStorage.getItem('Related_Line')
		this.getLine()
	},

	data() {
		const currentDate = new Date();
		const options = { timeZone: 'Asia/Shanghai' }; // 设置时区为UTC+8
		const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
		const formattedDate = firstDayOfMonth.toLocaleDateString('en-CA', options);
		const today = new Date();
		today.setDate(today.getDate());
		const yesterday = new Date(today);
		yesterday.setDate(today.getDate() - 1);
		return {
			loading1: false,
			comment: '',
			Line: "",
			LineList: [],
			Start: formattedDate,
			End: yesterday.toISOString().substr(0, 10),
			showDatePopup: false,
			showDatePopup2: false,
			Tier: "",
			Size: "",
			Country: "",
			Variable: "",
			TierList: [],
			SizeList: [],
			CountryList: [],
			VariableList: [],
			CLListFull: [],
			TotalItem: "",
			OverItem: "",
			PASSPercent: "",
			ShiftCount: "",
			CLListAlarm: [],
			column: [
				{ name: 'Date_1', required: true, label: '日期', align: 'left', field: 'Date_1', sortable: true },
				{ name: 'Shift', required: true, label: '班次', align: 'left', field: 'Shift', sortable: true },
				{ name: 'Prod_Code', required: true, label: 'SKU', align: 'left', field: 'Prod_Code', sortable: true },
				{ name: 'Round_Type', required: true, label: '点检频次', align: 'left', field: 'Round_Type', sortable: true },
				{ name: 'Var_Desc', required: true, label: 'C/L点检项', align: 'left', field: 'Var_Desc', sortable: true },
				{ name: 'Result', required: true, label: '实际值', align: 'left', field: 'Result', sortable: true },
				{ name: 'Entery_On_CN', required: true, label: '录入时间', align: 'left', field: 'Entery_On_CN', sortable: true },
				{ name: 'L_Reject_M', required: true, label: '下限', align: 'left', field: 'L_Reject_M', sortable: true },
				{ name: 'Target', required: true, label: 'Target', align: 'left', field: 'Target', sortable: true },
				{ name: 'U_Reject_M', required: true, label: '上限', align: 'left', field: 'U_Reject_M', sortable: true }
			]
		}
	},
	watch: {
		Tier: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterCL()
		},
		Size: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterCL()
		},
		Country: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterCL()
		},
		Variable: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterCL()
		}
	},
	methods: {
		async getLine() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`material/lineList`)
			console.log('NJSLineList', res)
			_this.LineList = res
			this.LineList = _this.LineList
		},

		async getTierSize(Line, SKUList, CLListFull) {
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`material/CLTierSize?Line=${Line}&SKUList=${SKUList}`)
			console.log('CLTierSizeList', res)
			_this.TierSizeList = res
			this.TierSizeList = _this.TierSizeList
			if (res && Array.isArray(res)) {
				const uniqueTier = new Set(res.map(item => item.Tier));
				this.TierList = Array.from(uniqueTier);
				console.log('TierList', this.TierList)
				const uniqueSize = new Set(res.map(item => item.Size));
				this.SizeList = Array.from(uniqueSize);
				console.log('SizeList', this.SizeList)
				const uniqueCountry = new Set(res.map(item => item.Country));
				this.CountryList = Array.from(uniqueCountry);
				console.log('CountryList', this.CountryList)
				this.filterCL(CLListFull)
			} else {
				throw new Error('Invalid response data');
			}
		},

		async getCentlineList(number) {
			this.CLListAlarm = []
			// we set loading state
			this[`loading${number}`] = true;
			try {
				var _this = this
				var Line = this.Line
				var Start = this.Start
				var End = this.End
				let SKUList = []
				console.log('CL参数', Line, Start, End)
				const { data: res
				} = await _this.$http.get(`mes/Centerline?Line=${Line}&Start=${Start}&End=${End}`)
				console.log('CL', res)
				_this.CLListFull = res
				this.CLListFull = _this.CLListFull
				let CLListAlarm = [];
				// 遍历数组，根据Alarm的值判断是否赋值给CLListAlarm
				res.forEach(data => {
					if (data.Alarm === 1) {
						CLListAlarm.push({
							Date_1: data.Date_1,
							Shift: data.Shift,
							Round_Type: data.Round_Type,
							Var_Desc: data.Var_Desc,
							Result: data.Result,
							Entery_On_CN: new Date(data.Entery_On_CN).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false }),
							Prod_Code: data.Prod_Code,
							L_Reject_M: data.L_Reject_M,
							Target: data.Target,
							U_Reject_M: data.U_Reject_M
						});
					}
				});
				console.log('alarmdata', CLListAlarm)
				const uniqueSKU = new Set(CLListAlarm.map(item => item.Prod_Code));
				SKUList = Array.from(uniqueSKU);
				SKUList = SKUList.join(',');
				console.log('SKUList', SKUList)
				if (SKUList) {
					this.getTierSize(Line, SKUList, this.CLListFull)
				} else {
					let data_1 = this.CLListFull
					this.TotalItem = data_1.reduce((Total_Count, item) => Total_Count + item.Total_Count, 0);
					this.OverItem = '0';
					this.PASSPercent = '100';
					var shiftSet = new Set();
					data_1.forEach(item => {
						shiftSet.add(`${item.Date_1} ${item.Shift}`);
					});
					this.ShiftCount = shiftSet.size;
				}
			} catch (error) {
				console.error(error)
			} finally {
				// reset loading state
				this[`loading${number}`] = false;
			}

		},
		async filterCL() {
			try {
				var Tier = this.Tier
				var Size = this.Size
				var Country = this.Country
				var CL = this.Variable
				var TierSizeList = this.TierSizeList
				var data = this.CLListFull
				console.log("CLbeforeFilter参数", data, TierSizeList, Tier, Size, Country, CL)
				var result = await CLFilter(data, TierSizeList, Tier, Size, Country, CL)
				this.CLListFull = result.CLListFull
				this.TotalItem = result.TotalItem
				this.OverItem = result.OverItem
				this.PASSPercent = result.PASSPercent
				this.VariableList = result.VariableList
				this.CLListAlarm = result.CLListAlarm
				this.ShiftCount = result.ShiftCount
			} catch (error) {
				console.error(error)
			}
		},


	}
}


function CLFilter(data, TSList, Tier, Size, Country, CL) {
	return new Promise((resolve, reject) => {
		if (data && Array.isArray(data)) {
			let filteredData = data;
			let TSfilterList = TSList;
			let CLListFull = data
			console.log('filterbefore', data)
			// 根据Tier过滤
			if (Tier) {
				filteredData = filteredData.filter(item => {
					const matchingTSItem = TSfilterList.find(tsItem => tsItem.SKU_Code === item.Prod_Code && tsItem.Tier === Tier);
					return matchingTSItem !== undefined;
				});
			}
			// console.log('filter-Tier', filteredData)
			// 根据Size过滤
			console.log('Size-F', Size)
			if (Size) {
				filteredData = filteredData.filter(item => {
					const matchingTSItem = TSfilterList.find(tsItem => tsItem.SKU_Code === item.Prod_Code && tsItem.Size === Size);
					return matchingTSItem !== undefined;
				});
			}
			// console.log('filter-Size', filteredData)
			// 根据Country过滤
			console.log('Country-F', Country)
			if (Country) {
				filteredData = filteredData.filter(item => {
					const matchingTSItem = TSfilterList.find(tsItem => tsItem.SKU_Code === item.Prod_Code && tsItem.Country === Country);
					return matchingTSItem !== undefined;
				});
			}
			// console.log('filter-Country', filteredData)
			// 根据CL过滤
			console.log('CLItem', CL)
			if (CL) {
				filteredData = filteredData.filter(item => item.Var_Desc === CL);
			}
			// console.log('filter-CL', filteredData)
			// 计算筛选后的点检总数，超范数，达标率及生产总班次：
			let data_1 = filteredData
			const TotalItem = data_1.reduce((Total_Count, item) => Total_Count + item.Total_Count, 0);
			const OverItem = data_1.reduce((Alarm, item) => Alarm + item.Alarm, 0);
			const PASSPercent = ((TotalItem - OverItem) / TotalItem * 100).toFixed(2);
			var shiftSet = new Set();
			data_1.forEach(item => {
				shiftSet.add(`${item.Date_1} ${item.Shift}`);
			});
			// console.log('shiftSet', shiftSet)
			const ShiftCount = shiftSet.size;
			// 初始化CLListAlarm变量
			let CLListAlarm = [];
			// 遍历数组，根据Alarm的值判断是否赋值给CLListAlarm
			data_1.forEach(data => {
				if (data.Alarm === 1) {
					CLListAlarm.push({
						Date_1: data.Date_1,
						Shift: data.Shift,
						Round_Type: data.Round_Type,
						Var_Desc: data.Var_Desc,
						Result: data.Result,
						Entery_On_CN: new Date(data.Entery_On_CN).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false }),
						Prod_Code: data.Prod_Code,
						L_Reject_M: data.L_Reject_M,
						Target: data.Target,
						U_Reject_M: data.U_Reject_M
					});
				}
			});
			// 使用 Set 去除重复项获得CL点检项筛选list
			const uniqueVar = new Set(CLListAlarm.map(item => item.Var_Desc));
			const VariableList = Array.from(uniqueVar);
			// console.log('CLListAlarm函数', CLListAlarm)
			resolve({
				CLListFull,
				TotalItem,
				OverItem,
				PASSPercent,
				ShiftCount,
				CLListAlarm,
				VariableList,
			});
		} else {
			resolve([]); // 如果data不存在或不是数组，返回空数组
		}
	})
}



</script>


<style lang="css" scoped>
.text_type {
	color: yellow;
	font-size: 20px;
	font-weight: bold
}

.tabletitle {
	font-weight: bold;
}
</style>

<style>
.bordered-table {
	border-collapse: collapse;
	border: 1px solid black;
}

.bordered-table th,
.bordered-table td {
	border: 1px solid black;
}
</style>

<style>
.bordered-table {
	border-collapse: collapse;
	border: 1px solid black;
}

.bordered-table th,
.bordered-table td {
	border: 1px solid black;
}
</style>
