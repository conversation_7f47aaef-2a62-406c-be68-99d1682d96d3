<template>
    <div style="width:100%;">

        <div class="row wrap justify-start">

            <div style="width:100%">
                <div class="text-h7 q-my-md ">问题定义</div>
                <div class="text-h5 q-my-md  q-gutter-sm">
                    <div>
                        <div class="row wrap justify-start q-gutter-xs">
                            <q-input outlined v-model="Problem_Definition_data['Mill']" style="width: 20%;" dense filled
                                label-slot>
                                <template v-slot:label>工厂:
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-input>
                            <q-select outlined v-model="Problem_Definition_data['Department']"
                                :options="dropDown['Department']" label-slot clearable style="width: 20%;" dense filled>
                                <template v-slot:label>部门/机台:
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-select>
                            <q-select outlined v-model="Problem_Definition_data['Device_Unit']"
                                :options="dropDown['Device_Unit']" label-slot clearable style="width: 20%;" dense
                                filled>
                                <template v-slot:label>设备单元:
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-select>
                            <q-select outlined v-model="Problem_Definition_data['Problem_Position']"
                                :options="dropDown['Position']" label-slot clearable style="width: 20%;" dense filled>
                                <template v-slot:label>该设备单元的具体位置:
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-select>
                            <q-select outlined v-model="Problem_Definition_data['Problem_Category']"
                                :options="dropDown['Problem_Category']" label-slot clearable style="width: 20%;" dense
                                filled>
                                <template v-slot:label>问题分类:
                                    <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-select>

                        </div>
                        <div class="row wrap justify-start q-gutter-xs" style="margin-top:10px">
                            <q-input outlined v-model="Problem_Definition_data['Problem_Name']" label-slot clearable
                                style="width:98%" dense filled>
                                <template v-slot:label>问题名称简述:
                                    <em class="q-px-xs bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-input>
                        </div>
                    </div>
                    <div class="row wrap justify-start q-gutter-xs">
                        <q-input outlined v-model="Problem_Definition_data['Team_Leader']" label-slot clearable
                            style="width: 25%;" dense filled>
                            <template v-slot:label>团队负责人:
                                <em class="q-px-xs bg-deep-orange text-white rounded-borders"
                                    style="font-size: 12px;">必填</em>
                            </template>
                        </q-input>
                        <q-input outlined v-model="Problem_Definition_data['Team_Member']" label-slot clearable
                            style="width:72%;" dense filled>
                            <template v-slot:label>团队成员:
                                <em class="q-px-xs bg-deep-orange text-white rounded-borders"
                                    style="font-size: 12px;">必填</em>
                            </template>
                        </q-input>
                    </div>


                    <div class="row wrap justify-start q-gutter-xs">
                        <q-input outlined v-model="Problem_Definition_data['Problem_Description']" label-slot clearable
                            style="width:98%;" dense filled autogrow>
                            <template v-slot:label>问题发生所产生的直接或间接后果是什么:
                                <em class="q-px-xs bg-deep-orange text-white rounded-borders"
                                    style="font-size: 12px;">必填</em>
                            </template>
                        </q-input>
                    </div>

                    <div class="row wrap justify-start q-gutter-xs">
                        <q-input v-model="Problem_Definition_data['Problem_Effect']" filled type="date" stack-label
                            label-slot dense>
                            <template v-slot:prepend>
                                <q-icon name="event" />
                            </template>
                            <template v-slot:label>问题发生时间:
                                <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                    style="font-size: 12px;">必填</em>
                            </template>
                        </q-input>

                        <q-select outlined v-model="Problem_Definition_data['Problem_Frequency']"
                            :options="['每天', '每周', '每月', '每季度', '每年']" label-slot clearable style="width: 28%;" dense
                            filled>
                            <template v-slot:label>问题发生频次:
                                <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                    style="font-size: 12px;">必填</em>
                            </template>
                        </q-select>


                    </div>

                    <div class="row wrap justify-start q-gutter-xs">
                        <q-uploader style="width:40%;height: 230px;" batch auto-upload dense
                            :factory="factoryFn_Problem_Picture" @uploaded="handleUploadSuccess_Problem_Picture"
                            label="问题现象照片或视频" />
                        <div style="width:57%;height: 230px;">
                            <q-input v-model="Problem_Definition_data['Problem_Picture_Content']" filled type="textarea"
                                label-slot clearable>
                                <template v-slot:label>
                                    <div class="text-h7"></div>照片内容简要说明
                                    <div>五感法（比如看到的、听到的、闻到的等现象）</div>
                                    <div>直观叙述、量化描述，越具体越清晰越佳</div>
                                    <em class="q-px-xs bg-deep-orange text-white rounded-borders"
                                        style="font-size: 12px;">必填</em>
                                </template>
                            </q-input>
                        </div>
                    </div>
                    <div class="text-h6">
                        <q-checkbox v-model="Problem_Definition_data['Metrics_selected']" />衡量指标 <q-btn padding="none"
                            color="primary" icon="add" @click="addMetrics()"
                            v-if="Problem_Definition_data['Metrics_selected']" />
                    </div>
                    <div v-if="Problem_Definition_data['Metrics_selected']">
                        <div v-for="(metric, index) in Metrics_data" :key="index">
                            <div class="row wrap justify-start q-gutter-sm text-body2 text-center">
                                <div>
                                    <div>Remove</div>
                                    <div style="line-height:35px;">
                                        <q-btn padding="none" color="negative" icon="horizontal_rule"
                                            @click="removeMetrics(index)"
                                            v-if="Problem_Definition_data['Metrics_selected']" />
                                    </div>
                                </div>
                                <div>
                                    <div>Metrics_Name</div>
                                    <q-input outlined v-model="metric.Metrics_Name" dense filled>
                                    </q-input>
                                </div>
                                <div>
                                    <div>Baseline</div>
                                    <q-input outlined v-model="metric.Baseline" dense filled>
                                    </q-input>
                                </div>
                                <div>
                                    <div>Goal</div>
                                    <q-input outlined v-model="metric.Goal" dense filled>
                                    </q-input>
                                </div>
                                <div>
                                    <div>Units</div>
                                    <q-input outlined v-model="metric.Units" dense filled>
                                    </q-input>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 
                    <div
                        v-if="Problem_Definition_data['Mill']!=''&& Problem_Definition_data['Department']!='' && Problem_Definition_data['Device_Unit']!='' && Problem_Definition_data['Problem_Name']!=''">
                        <Work_Principle opType="新增" :linkid="calculateLinkID" :visible="false" />
                    </div> -->
                    <!-- <div class="row wrap justify-end q-gutter-xs">
                        <q-btn color="secondary" icon="mail" label="保存提交" @click="saveProblem()"
                            style="margin-right:20px" />
                    </div> -->
                </div>
            </div>

        </div>
    </div>


</template>

<script>
import Work_Principle from './Work_Principle.vue';
export default {
    components: {
        Work_Principle
    },
    props: {
        opType: {
            type: String,
            default: '新增'
        },
        linkid: {
            type: String,
            default: ''
        }
    },
    mounted() {
        this.$store.commit('updateProblem_Definition_data', {})
        this.$store.commit('updateMetrics_data', {})
        this.$store.commit('updateWork_Principle_data', [])
        this.getDepartment()
        this.LinkID = this.linkid
        this.Problem_Definition_data['Mill'] = localStorage.getItem('mill')
        this.CI_Device_Unit()
    },

    watch: {
        Problem_Definition_data: {
            handler(newVal) {
                console.log('Problem_Definition_data 更新:', newVal);
                this.$store.commit('updateProblem_Definition_data', newVal)
            },
            deep: true
        },
        Metrics_data: {
            handler(newVal) {
                console.log('Metrics_data 更新:', newVal);
                this.$store.commit('updateMetrics_data', newVal)
            },
                deep: true
        },
        'Problem_Definition_data.Department'(newValue1, oldValue1) {
            // this.formData['Unit'] = ''
            this.Problem_Definition_data['Device_Unit']=''
            this.filterUnitList(newValue1)
        },
        'Problem_Definition_data.Device_Unit'(newValue2, oldValue2) {
            // this.formData['Position'] = ''
            this.Problem_Definition_data['Problem_Position']=''
            this.filterPositionList(this.Problem_Definition_data['Department'],newValue2)
        },
    },
    computed: {
        // calculateLinkID() {
        //     console.log('calculateLinkID',`${this.currentDate()}-${this.problem_data['Mill']}-${this.problem_data['Department']}-${this.problem_data['Device_Unit']}-${this.problem_data['Problem_Name']}`)
        //     return `${this.currentDate()}-${this.problem_data['Mill']}-${this.problem_data['Department']}-${this.problem_data['Device_Unit']}-${this.problem_data['Problem_Name']}`
        // }  
    },
    data() {
        return {
            LinkID:'',
            dropDown: {
                Mill: ['南京南厂'],
                Department: [],
                Device_Unit: ['ICU', 'XXX', 'XXX'],
                Problem_Category: ['机械问题', '电气问题', '工艺问题', '胶系统问题'],
                Position:['位置1', '位置2', '位置3']
            },
            Problem_Definition_data: {
                Mill: '',
                Department: '',
                Device_Unit: '',
                Problem_Category: '',
                Problem_Name: '',
                Team_Leader: '',
                Team_Member: '',
                Problem_Description: '',
                Problem_Effect: '',
                Problem_Frequency: '',
                Problem_Position: '',
                Problem_Picture_URL: '',
                Problem_Picture_Content: '',
                Metrics_selected: false,
                Work_Principle_selected: false,
            },
            Metrics_data: [
                {
                    "Metrics_Name": '',
                    "Baseline": '',
                    "Goal": '',
                    "Units": ''
                }
            ],
            unitRawData: [],
        }
    },




    methods: {
        currentDate() {
            const now = new Date();
            // 格式化日期
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            console.log('currentDate:', `${year}-${month}-${day}`)
            return `${year}-${month}-${day}`
        },


        async getDepartment() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getDepartment')
            console.log('getDepartment', res)
            _this.dropDown['Department'] = res
        },
        factoryFn_Problem_Picture(files) {
            var _this = this
            _this.prompt_uploadFile = true
            return {
                url: 'http://172.21.65.192:3001/upload?system=A3_Problem_Solve',
                method: 'POST'
            }
        },
        handleUploadSuccess_Problem_Picture(response) {
            // console.log('FileUploadResponse', response)
            const _this = this
            //response.files[0].xlr.responseText
            const responseURL = response['files'][0]['xhr']['responseText']
            _this.Problem_Definition_data['Problem_Picture_URL'] = responseURL
        },
        addMetrics() {
            this.Metrics_data.push({
                "Metrics_Name": '',
                "Baseline": '',
                "Goal": '',
                "Units": ''
            })
        },
        removeMetrics(index) {
            this.Metrics_data.splice(index, 1)
        },
        async CI_Device_Unit() {
            var _this = this
            // _this.rows = false
            const { data: res } = await _this.$http.get(`ciProject/CI_Device_Unit`)
            console.log('CI_Device_Unit', res)
            this.unitRawData = res
        },
        filterUnitList(Line) {
            const rawData = this.unitRawData
            const uniqueUnits = [...new Set(rawData.filter(item => item.Line === Line).map(unit => unit.Level1))]
            this.dropDown['Device_Unit'] =uniqueUnits
            console.log('filterUnitList', this.dropDown['Device_Unit'])
        },
        filterPositionList(Line,Unit) {
            const rawData = this.unitRawData
            const uniquePositions = [...new Set(rawData.filter(item => item.Line === Line && item.Level1==Unit).map(unit => unit.Level2))]
            this.dropDown['Position'] = uniquePositions
            console.log('filterPositionList', this.dropDown['Position'])
        },

        saveProblem() {
            const _this = this
            //const LinkID = `${year}-${month}-${day}-${_this.problem_data['Mill']}-${_this.problem_data['Department']}-${_this.problem_data['Device_Unit']}-${_this.problem_data['Problem_Name']}`
            // _this.Problem_Definition_data['LinkID'] = LinkID
            // _this.Metrics_data['LinkID'] = LinkID
            // _this.Work_Principle_data['LinkID'] = LinkID
            // _this.Work_Principle_data['Key_Setting']['LinkID'] = LinkID
            const parmas = {
                "Problem_Definition_data": _this.Problem_Definition_data,
                "Metrics_data": _this.Metrics_data,
                "LinkID": LinkID
            }
            console.log('parmas', parmas)
            _this.$http.post('ciProject/insert_Problem_Definition', parmas).then(res => {
                console.log('res', res)
            })
        }

    }
}


</script>

<!-- <style>
.black_border {
    border: 1px solid black;
}
</style> -->
