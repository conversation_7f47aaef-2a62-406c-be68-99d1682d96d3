﻿const express = require('express')
const router = express.Router()
const sqlexec = require('../sql/sqlCIProject')
const sql = new sqlexec()

// 获取A3报告详情
router.get('/a3_report/:id', (req, res) => {
    const { id } = req.params
    sql.getA3RecordById(id, (data) => {
        console.log(data)
        if (!data) {
            return res.status(500).json({
                success: false,
                message: '获取A3报告失败'
            })
        }

        // 解析JSON格式的子表数据
        try {
            // 安全的JSON解析函数
            function safeJSONParse(jsonString, defaultValue) {
                if (!jsonString || jsonString === 'null') return defaultValue;
                try {
                    // 修复常见的JSON格式问题
                    let fixedString = jsonString;
                    // 处理TeamMembers字段中的转义问题
                    if (fixedString.includes('\\"')) {
                        fixedString = fixedString.replace(/\\\\\"([^\"]+)\\\\\"/, '"$1"');
                    }
                    return JSON.parse(fixedString);
                } catch (e) {
                    console.error(`JSON解析错误: ${e.message}, 原始字符串: ${jsonString}`);
                    return defaultValue;
                }
            }

            data.Metrics = safeJSONParse(data.Metrics, [])
            data.WorkPrinciples = safeJSONParse(data.WorkPrinciples, [])
            data.RootCauseAnalysis = safeJSONParse(data.RootCauseAnalysis, {})
            data.ImprovementPlans = safeJSONParse(data.ImprovementPlans, [])
            data.PerformanceSummary = safeJSONParse(data.PerformanceSummary, {})
            console.log(data.Metrics)
            console.log(data.WorkPrinciples)
            console.log(data.RootCauseAnalysis)
            console.log(data.ImprovementPlans)
            console.log(data.PerformanceSummary)

            // 转换数据结构
            const transformedData = {
                reportType: data.ReportType || '绿带问题解决A3报告',
                problemDefinition: {
                    factory: data.Factory,
                    department: data.Department,
                    machine: data.Machine,
                    unit: data.Unit,
                    category: data.Category,
                    teamLeader: data.TeamLeader,
                    teamMembers: data.TeamMembers,
                    problemDescription: data.ProblemDescription,
                    whyImportant:data.WhyImportant,
                    when: data.When,
                    howOften: data.HowOften,
                    where: data.Where,
                    photoDescription: data.PhotoDescription,
                    url: data.ProblemPhotoUrl,
                    attachments: safeJSONParse(data.Attachments, []),
                    CreateBy: data.CreateBy,
                    ID: data.ID,
                },
                metrics: data.Metrics && data.Metrics.length > 0 ? data.Metrics.map(metric => ({
                    ID: metric.ID,
                    A3_ID: metric.A3_ID,
                    name: metric.MetricName,
                    baseline: metric.Baseline,
                    goal: metric.Goal,
                    actual: metric.Actual || '',
                    unit: metric.Units,
                })) : [],
                workPrinciples: data.WorkPrinciples && data.WorkPrinciples.length > 0 ? data.WorkPrinciples.map(principle => ({
                    id: principle.ID,
                    A3_ID: principle.A3_ID,
                    name: principle.PrincipleName,
                    mediaFiles: safeJSONParse(principle.MediaFiles, []),
                    mediaDescription: principle.MediaDescription || '',
                    settingsAndDescriptions: safeJSONParse(principle.SettingsAndDescriptions, []),
                })) : [],
                rootCause: data.RootCauseAnalysis && data.RootCauseAnalysis.length > 0 ? {
                    id: data.RootCauseAnalysis[0].ID,
                    A3_ID: data.RootCauseAnalysis[0].A3_ID,
                    human: safeJSONParse(data.RootCauseAnalysis[0].HumanIssues, []),
                    machine: safeJSONParse(data.RootCauseAnalysis[0].MachineIssues, []),
                    material: safeJSONParse(data.RootCauseAnalysis[0].MaterialIssues, []),
                    method: safeJSONParse(data.RootCauseAnalysis[0].MethodIssues, []),
                    environment: safeJSONParse(data.RootCauseAnalysis[0].EnvironmentIssues, []),
                    topIssues: safeJSONParse(data.RootCauseAnalysis[0].TopIssues, []),
                    whyAnalysis: safeJSONParse(data.RootCauseAnalysis[0].whyAnalysis, {}),
                } : {
                    id: null,
                    A3_ID: data.ID,
                    human: [],
                    machine: [],
                    material: [],
                    method: [],
                    environment: [],
                    topIssues: [],
                    whyAnalysis: {},
                },
                improvementPlans: data.ImprovementPlans && data.ImprovementPlans.length > 0 ? data.ImprovementPlans.map(plan => ({
                    id: plan.ID,
                    A3_ID: plan.A3_ID,
                    description: plan.Description || '',
                    actionPlan: plan.ActionPlan || '',
                    effort: plan.Effort || 0,
                    benefit: plan.Benefit || 0,
                    totalScore: plan.TotalScore || 'C',
                    responsible: plan.Responsible || '',
                    dueDate: plan.DueDate || '',
                    beforeImage: plan.BeforeImage || '',
                    afterImage: plan.AfterImage || '',
                    improvementResult: plan.ImprovementResult || ''
                })) : [],
                performanceSummary: data.PerformanceSummary && data.PerformanceSummary.length > 0 ? {
                    id: data.PerformanceSummary[0].ID,
                    A3_ID: data.PerformanceSummary[0].A3_ID,
                    description: data.PerformanceSummary[0].Description || '',
                    images: safeJSONParse(data.PerformanceSummary[0].Images, [])
                } : {
                    id: null,
                    A3_ID: data.ID,
                    description: '',
                    images: []
                }
            }
            console.log('transformedData:', transformedData)

            res.json({
                success: true,
                data: transformedData
            })
        } catch (error) {
            console.error('JSON解析错误:', error)
            return res.status(500).json({
                success: false,
                message: '数据解析失败'
            })
        }
    })
})

router.get('/getMachineUnit', (req, res) => {
    sql.getMachineUnit((data) => {
        if (!data) {
            return res.status(500).json({
                success: false,
                message: '获取设备单元失败'
            })
        }
        let dataArr=[]
        for(let i=0;i<data.length;i++){
            dataArr.push(data[i].Level2)
        }

        res.json({
            success: true,
            data: dataArr
        })
    })
})

router.get('/getA3RecordByAll', (req, res) => {

    sql.getA3RecordByAll( (data) => {
        if (!data) {
            return res.status(500).json({
                success: false,
                message: '获取A3报告列表失败'
            })
        }

        res.json({
            success: true,
            data: data
        })
    })
})

router.get('/getA3RecordByPersonal', (req, res) => {
    const account=req.query.account
    sql.getA3RecordByPersonal(account,(data) => {
        if (!data) {
            return res.status(500).json({
                success: false,
                message: '获取A3报告列表失败'
            })
        }
        res.json({
            success: true,
            data: data
        })
    })
})

// 创建新的A3报告
router.post('/a3report', async (req, res) => {
    const data = req.body
    console.log('a3report data:')
    console.log(data)
    let ID=''
    if(data.problemDefinition.ID!=null){
        ID=data.problemDefinition.ID
        const deleteResult=await deleteA3Record_function(ID)
        if(deleteResult=='已删除'){
            console.log('删除成功')
        }else{
            console.log('删除失败')
            return res.status(500).json({
                success: false,
                message: 'A3报告更新失败'
            })
        }
    }
    data.Metrics = data.Metrics || []
    data.WorkPrinciples = data.WorkPrinciples || []
    data.ImprovementPlans = data.ImprovementPlans || []
    data.PerformanceSummary = data.PerformanceSummary || {}
    sql.createA3Record(data, (newId) => {
        if (typeof newId === 'string') {
            return res.status(500).json({
                success: false,
                message: newId
            })
        }

        res.json({
            success: true,
            id: newId
        })
    })
})

// 更新A3报告
router.put('/a3-report/:id', (req, res) => {
    const { id } = req.params
    const data = req.body

    // 数据验证
    if (!data.factory || !data.department || !data.problemDescription) {
        return res.status(400).json({
            success: false,
            message: '缺少必填字段'
        })
    }

    // 处理子表数据
    data.metrics = data.metrics || []
    data.workPrinciples = data.workPrinciples || []

    sql.updateA3Report(id, data, (message) => {
        if (typeof message === 'string' && message !== '更新成功') {
            return res.status(500).json({
                success: false,
                message: message
            })
        }

        res.json({
            success: true,
            message: message
        })
    })
})


function deleteA3Record_function(ID) {
    return new Promise((resolve, reject) => {
        sql.deleteA3Record(ID, (result) => {
            if (result=='已删除') {
               resolve(result)
            }
        })
    })
}

module.exports = router
