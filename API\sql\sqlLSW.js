const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.LSW_checktheChecker_base = function (account, callBack) {
    //console.log("select a.*,isnull(b.LSW_Problem_Num,0)LSW_Problem_Num,b.LastCheckDate from [LSW_checktheChecker_base] as a left join (select LSW_Base_ID,count(*) LSW_Problem_Num,max(日期)LastCheckDate  from LSW_Problem_Data where 问题描述 is not null  group by LSW_Base_ID) as b on a.LSW_Base_ID=b.LSW_Base_ID where a.account="+account+"  order by LSW_category asc,cycle asc ")
    sqlexec.prototype.
        _query("exec dbo.SP_API_LSW_ChecktheChecker_Base " + account + "", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.my_LSW_checktheChecker_base_list = function (account, callBack) {
    //console.log("select a.*,isnull(b.LSW_Problem_Num,0)LSW_Problem_Num,b.LastCheckDate from [LSW_checktheChecker_base] as a left join (select LSW_Base_ID,count(*) LSW_Problem_Num,max(日期)LastCheckDate  from LSW_Problem_Data where 问题描述 is not null  group by LSW_Base_ID) as b on a.LSW_Base_ID=b.LSW_Base_ID where a.account="+account+"  order by LSW_category asc,cycle asc ")
    sqlexec.prototype.
        _query("select * from LSW_Check_The_Checker_Base where Employee_ID=" + account + " order by LSW_Base_ID desc", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.getSafeIncidentDept = function (mill, callBack) {
    sqlexec.prototype.
        _query("select  Area label,Area value,Employee_ID ownerID from Department_Owner_List order by Area asc", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.getLSW_Problem_Data_List = function (account, callBack) {
    sqlexec.prototype.
        _query(`SELECT 
        [Process_Status],[LSW_Base_ID]
      ,a.[Mill]
      ,[Date]
      ,a.[Employee_ID]
      ,a.[Employee_Name]
      ,[Problem_Area]
      ,[Problem_Category]
      ,[Problem_Desc]
      ,[Problem_Root_Case]
      ,[Problem_Solution]
      ,b.Employee_Name OwnerName
      ,[Assigned_Department]
	  ,c.Employee_Name Assigned_By_Name
      ,[Priority]
      ,[Action_Complete_Date]
      
      ,a.[ID]
      ,[Problem_File_Name_Array]
	  ,[Assigned_By]
	  ,[Owner_Name]
      ,[Problem_File_URL_Array]
  FROM [dbo].[LSW_Problem_Data] as a
  left join Employee_List as b on a.Owner_Name=b.Employee_ID
  left join Employee_List as c on a.Assigned_By=c.Employee_ID
  where a.Employee_ID=${account} order by a.ID desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.getLSW_Assign_Data_List_MyApproval = function (account, callBack) {
    sqlexec.prototype.
        _query(`SELECT 
        [Process_Status],
        [LSW_Base_ID]
      ,a.[Mill]
      ,[Date]
      ,a.[Employee_ID]
      ,a.[Employee_Name]
      ,[Problem_Area]
      ,[Problem_Category]
      ,[Priority]
      ,[Problem_Desc]
      ,[Problem_Root_Case]
      ,[Problem_Solution]
      ,b.Employee_Name OwnerName
      ,[Assigned_Department]
	  ,c.Employee_Name Assigned_By_Name
      ,[Action_Complete_Date]
      ,a.[ID]
      ,[Problem_File_Name_Array]
	  ,[Assigned_By]
	  ,[Owner_Name]
      ,[Problem_File_URL_Array]
  FROM [dbo].[LSW_Problem_Data] as a
  left join Employee_List as b on a.Owner_Name=b.Employee_ID
  left join Employee_List as c on a.Assigned_By=c.Employee_ID
  where a.Owner_Name='${account}' order by a.ID desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.getLSW_Assign_List = function (account, callBack) {
    sqlexec.prototype.
        _query(`SELECT 
        [Process_Status],
        [LSW_Base_ID]
      ,a.[Mill]
      ,[Date]
      ,a.[Employee_ID]
      ,a.[Employee_Name]
      ,[Problem_Area]
      ,[Problem_Category]
      ,[Priority]
      ,[Problem_Desc]
      ,[Problem_Root_Case]
      ,[Problem_Solution]
      ,b.Employee_Name OwnerName
      ,[Assigned_Department]
	  ,c.Employee_Name Assigned_By_Name
      ,[Action_Complete_Date]
      ,a.[ID]
      ,[Problem_File_Name_Array]
	  ,[Assigned_By]
	  ,[Owner_Name]
      ,[Problem_File_URL_Array]
  FROM [dbo].[LSW_Problem_Data] as a
  left join Employee_List as b on a.Owner_Name=b.Employee_ID
  left join Employee_List as c on a.Assigned_By=c.Employee_ID
  where a.Assigned_By='${account}' order by a.ID desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}


sqlexec.prototype.LSW_checktheChecker_data = function (LSW_Base_ID, callBack) {
    sqlexec.prototype.
        _query("select format(Check_Date,'yyyy-MM-dd HH:mm:ss') Check_Date,Check_Name,LSW_Base_ID from LSW_Check_The_Checker_Data where LSW_Base_ID=" + LSW_Base_ID + " order by Check_Date desc", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.LSW_Problem_Data = function (LSW_Base_ID, callBack) {
    sqlexec.prototype.
        _query("select * from LSW_Problem_Data where LSW_Base_ID=" + LSW_Base_ID + " order by id desc", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.insert_LSW_checktheChecker_data = function (username, LSW_Base_ID, callBack) {
    console.log("insert into [LSW_Check_The_Checker_Data] values (" + LSW_Base_ID + ",getdate()," + username + ")")
    sqlexec.prototype.
        _query("insert into [LSW_Check_The_Checker_Data] values (" + LSW_Base_ID + ",getdate()," + username + ")", function (err, result) {
            if (err) {
            }
            return callBack('insert completed')
        })
}

sqlexec.prototype.insert_LSW_Problem_Data = function (mill, LSW_Base_ID, account, username, Problem_Area, problemContent, Owner_Name, fileNameArray, fileURLArray, Priority, problem_category, relateID, callBack) {
    sqlexec.prototype.
        _query(`insert into [LSW_Problem_Data](Mill,LSW_Base_ID,Date,Employee_ID,Employee_Name,Problem_Area,Problem_Desc,Owner_Name,Problem_File_Name_Array,Problem_File_URL_Array,Process_Status,[Priority],[Problem_Category],RelateID) 
        values ('${mill}','${LSW_Base_ID}',getdate(),'${account}','${username}','${Problem_Area}','${problemContent}','${Owner_Name}','${fileNameArray}','${fileURLArray}','open','${Priority}','${problem_category}','${relateID}')`, function (err, result) {
            if (err) {
            }
            return callBack('insert completed')
        })
}

sqlexec.prototype.insert_LSW_check_base = function (mill, account, name, LSW_category, checkContent, cycle, baseTime, checkListFileArray, Role, callBack) {
    //console.log("insert into [LSW_checktheChecker_base](mill,account,name,LSW_category,checkContent,cycle,baseTime,checkListFileArray,checkListFileArray_URL,Role) values ("+mill+","+account+","+ name +","+ LSW_category +","+ checkContent +","+ cycle +","+ baseTime +","+ checkListFileArray +",null,"+ Role +")")
    sqlexec.prototype.
        _query("insert into LSW_Check_The_Checker_Base(Mill,Employee_ID,Employee_Name,LSW_category,Check_Content,cycle,Base_Time,Check_List_File_Array,Check_List_File_Array_URL,Role) values (" + mill + "," + account + "," + name + "," + LSW_category + "," + checkContent + "," + cycle + "," + baseTime + "," + checkListFileArray + ",null," + Role + ")", function (err, result) {
            if (err) {
            }
            return callBack('insert completed')
        })
}
sqlexec.prototype.Update_LSW_check_base = function (mill, account, name, LSW_category, checkContent, cycle, baseTime, checkListFileArray, Role, LSW_Base_ID, callBack) {
    //console.log("update [LSW_checktheChecker_base] set mill="+ mill +",account="+ account +",name"+ name +",LSW_category="+ LSW_category +",checkContent="+ checkContent +",cycle="+ cycle +",baseTime="+ baseTime +",checkListFileArray="+ checkListFileArray +",Role="+ Role +" where LSW_Base_ID="+LSW_Base_ID)
    sqlexec.prototype.
        _query("update [LSW_Check_The_Checker_Base] set Mill=" + mill + ",Employee_ID=" + account + ",Employee_Name=" + name + ",LSW_category=" + LSW_category + ",Check_Content=" + checkContent + ",Cycle=" + cycle + ",Base_Time=" + baseTime + ",Check_List_File_Array=" + checkListFileArray + ",Role=" + Role + " where LSW_Base_ID=" + LSW_Base_ID, function (err, result) {
            if (err) {
            }
            return callBack('update completed')
        })
}

sqlexec.prototype.delete_LSW_check_base = function (LSW_Base_ID, callBack) {
    //console.log("delete from [LSW_checktheChecker_base] where LSW_Base_ID="+LSW_Base_ID)
    sqlexec.prototype.
        _query("delete from [LSW_Check_The_Checker_Base] where LSW_Base_ID=" + LSW_Base_ID, function (err, result) {
            if (err) {
            }
            return callBack('delete completed')
        })
}

sqlexec.prototype.getManagerList = function (Mill, callBack) {
    //console.log("select * from [NJTraining].[dbo].[EmployeeList]  where Mill="+ Mill +" and Line like '%经理%'" )
    sqlexec.prototype.
        _query("select concat_ws('-',Business_Title,Employee_Name) DisplayName,Employee_ID from [Employee_List]  where  (Business_Title like '%经理%' or Business_Title like '%主管%'  ) and Business_Title<>'资产主管' order by Line asc", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.updateLSW_Problem = function (data, Process_Status, callBack) {
    //console.log("select * from [NJTraining].[dbo].[EmployeeList]  where Mill="+ Mill +" and Line like '%经理%'" )
    let sqlStr = ''
    if (Process_Status == 'open' || Process_Status == 'In Progress') {
        sqlStr = `update LSW_Problem_Data 
                set Problem_Category='${data['problem_Category']}',
                    Assigned_Department='${data['assignedBy_Department']}',
                    Assigned_By='${data['assignedBy'].hasOwnProperty('value') ? data['assignedBy']['value'] : data['assignedBy']}',
                    Priority='${data['Priority']}',
                    Process_Status='${data['Adopt'] == 'Yes' ? 'In Progress' : 'Rejected'}'
                    where ID=${data['id']}`
    }
    if (Process_Status == 'Final Check') {
        sqlStr = `update LSW_Problem_Data 
                set Process_Status='${data['Adopt'] == 'Yes' ? 'Completed' : 'In Progress'}'
                where ID=${data['id']}`
    }
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            return callBack('已更新')
        })
}


sqlexec.prototype.updateLSW_Problem_AssignFeedBack = function (data, Process_Status, callBack) {
    //console.log("select * from [NJTraining].[dbo].[EmployeeList]  where Mill="+ Mill +" and Line like '%经理%'" )
    let sqlStr = ''

    sqlStr = `update LSW_Problem_Data 
                set Problem_Solution=${data['problemSolution'] == null ? 'null' : `'${data['problemSolution']}'`},
                    Problem_Root_Case=${data['rootCase'] == null ? 'null' : `'${data['rootCase']}'`},
                    Action_Complete_Date=${data['completeDate'] == null ? 'null' : `'${data['completeDate']}'`},
                    Process_Status='${data['process_status']}'
                    where ID=${data['id']}`

    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            return callBack('已更新')
        })
}

sqlexec.prototype.getLSW_Assign_Data_List_MyApproval = function (account, callBack) {
    sqlexec.prototype.
        _query(`SELECT 
        [Process_Status],
        [LSW_Base_ID]
      ,a.[Mill]
      ,[Date]
      ,a.[Employee_ID]
      ,a.[Employee_Name]
      ,[Problem_Area]
      ,[Problem_Category]
      ,[Priority]
      ,[Problem_Desc]
      ,[Problem_Root_Case]
      ,[Problem_Solution]
      ,b.Employee_Name OwnerName
      ,[Assigned_Department]
	  ,c.Employee_Name Assigned_By_Name
      ,[Action_Complete_Date]
      ,a.[ID]
      ,[Problem_File_Name_Array]
	  ,[Assigned_By]
	  ,[Owner_Name]
      ,[Problem_File_URL_Array]
  FROM [dbo].[LSW_Problem_Data] as a
  left join Employee_List as b on a.Owner_Name=b.Employee_ID
  left join Employee_List as c on a.Assigned_By=c.Employee_ID
  where a.Owner_Name='${account}' order by a.ID desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.LSW_List = function (callBack) {
    sqlexec.prototype.
        _query(`SELECT 
        [Process_Status],
        [LSW_Base_ID]
      ,a.[Mill]
      ,format([Date],'yyyy-MM-dd') Date
      ,a.[Employee_ID]
      ,a.[Employee_Name]
      ,[Problem_Area]
      ,[Problem_Category]
      ,[Priority]
      ,[Problem_Desc]
      ,[Problem_Root_Case]
      ,[Problem_Solution]
      ,b.Employee_Name OwnerName
      ,[Assigned_Department]
	  ,c.Employee_Name Assigned_By_Name
      ,[Action_Complete_Date]
      ,a.[ID]
      ,[Problem_File_Name_Array]
	  ,[Assigned_By]
	  ,[Owner_Name]
      ,[Problem_File_URL_Array]
  FROM [dbo].[LSW_Problem_Data] as a
  left join Employee_List as b on a.Owner_Name=b.Employee_ID
  left join Employee_List as c on a.Assigned_By=c.Employee_ID
  order by a.ID desc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}




module.exports = sqlexec;