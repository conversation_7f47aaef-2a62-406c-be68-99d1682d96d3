const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.CI_Tools = function ( callBack) {
    sqlexec.prototype.
        _query(`select * from A3_Module_List order by tools asc`, function (err, result) {

            if (err) {
                return callBack("error")
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.CI_Device_Unit = function (callBack) {
    sqlexec.prototype.
        _query(`select * from dbo.CI_Device_Unit`, function (err, result) {

            if (err) {
                return callBack("error")
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.insert_Problem_Definition = function (data,LinkID, callBack) {
    //console.log(`insert into CI_Problem_Definition values('${data.LinkID}',	'${data.Mill}',	'${data.Department}',	'${data.Device_Unit}',	'${data.Problem_Category}',	'${data.Team_Leader}',	'${data.Team_Member}',	'${data.Problem_Name}',	'${data.Problem_Description}',	'${data.Problem_Effect}',	'${data.Problem_Frequency}',	'${data.Problem_Position}',	'${data.Problem_Picture_URL}',	'${data.Problem_Picture_Content}',	'${data.Metrics_selected ? 'Yes' : 'No'}',	'${data.Work_Principle_selected ? 'Yes' : 'No'}')`)
    sqlexec.prototype.
        _query(`insert into CI_Problem_Definition values('${LinkID}',	'${data.Mill}',	'${data.Department}',	'${data.Device_Unit}',	'${data.Problem_Category}',	'${data.Team_Leader}',	'${data.Team_Member}',	'${data.Problem_Name}',	'${data.Problem_Description}',	'${data.Problem_Effect}',	'${data.Problem_Frequency}',	'${data.Problem_Position}',	'${data.Problem_Picture_URL}',	'${data.Problem_Picture_Content}',	'${data.Metrics_selected ? 'Yes' : 'No'}',	'${data.Work_Principle_selected?'Yes':'No'}')`, function (err, result) {
            if (err) {
                return callBack("error")
            }
            return callBack(result)
        })
}
sqlexec.prototype.insert_Metrics_data = function (data, LinkID, callBack) {
    sqlexec.prototype.
        _query(`insert into CI_Problem_Definition_Metrics values('${LinkID}',	'${data.Metrics_Name}',	'${data.Units}',	'${data.Baseline}',	'${data.Goal}')`, function (err, result) {
            if (err) {
                return callBack("error")
            }
            return callBack("success")
        })
}
sqlexec.prototype.insert_Work_Principle_data = function (data, LinkID, callBack) {
    sqlexec.prototype.
        _query(`insert into CI_Work_Principle values('${LinkID}',	'${data.Work_Principle_Name}',	'${data.Work_Principle_URL}',	'${data.Work_Principle_Description}',	'${data.Key_Setting_selected ? 'Yes' : 'No'}')`, function (err, result) {
            if (err) {
                return callBack("error")
            }
            return callBack("success")
        })
}

sqlexec.prototype.insert_Key_Setting_data = function (data, LinkID, callBack) {
    sqlexec.prototype.
        _query(`insert into CI_Key_Setting values('${LinkID}',	'${data.Key_Setting_Name}',	'${data.Key_Setting_Description}')`, function (err, result) {
            if (err) {
                return callBack("error")
            }
            return callBack("success")
        })
}




module.exports = sqlexec;