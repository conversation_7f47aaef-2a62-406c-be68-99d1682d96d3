<template>
    <div style="max-width: 100%;">
        <q-dialog v-model="addCrewDialog" transition-show="flip-down">
            <q-card style="width: 800px; max-width: 100vw;" v-if="addCrewDialog">
                <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                    <div class="text-h6 ">{{ addCrewTypeValue }}人员添加</div>
                    <q-space />
                    <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                </q-card-section>
                <q-card-section class="q-pa-md q-gutter-sm">
                    <q-card-section>
                        <q-select color="teal" filled v-model="addCrewLine['value']" :options="addCrewLine['array']"
                            label="机台" style="min-width: 150px; max-width: 300px" dense />
                    </q-card-section>
                    <q-card-section>
                        <template v-for="crew in otherCrewList">
                            <q-checkbox style="width:250px" v-model="selectedOtherCrew" :val="crew"
                                :label="crew['Position'] + '-' + crew['Employee_PYName']" color="teal" />
                        </template>
                    </q-card-section>
                </q-card-section>
                <q-card-actions class="bg-white text-teal">
                    <q-space />
                    <q-btn style="width:150px" color="primary" label="确认" @click="addCrewToList()" :disable="btnEnable" />
                </q-card-actions>
            </q-card>
        </q-dialog>

        <q-dialog v-model="modifyDialog" transition-show="flip-down">
            <q-card style="width: 800px; max-width: 100vw;" v-if="modifyAward_Row">
                <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                    <div class="text-h6 ">{{ modifyAward_Row['row']['Employee_PYName'] }}积分修正</div>
                    <q-space />
                    <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                </q-card-section>
                <q-card-section class="q-pa-md q-gutter-sm">
                    <q-card-section>
                        <template v-for="award in awardList">
                            <q-checkbox v-if="modifyAward_Row['row']['Position'] == award['Position']" style="width:130px"
                                v-model="clearAwardArray" :val="award['Award_Type']" :label="award['Award_Type']"
                                color="teal" />
                            <q-checkbox v-if="award['Position'] != '工艺技师' && modifyAward_Row['row']['Position'] != '工艺技师'"
                                style="width:130px" v-model="clearAwardArray" :val="award['Award_Type']"
                                :label="award['Award_Type']" color="teal" />
                        </template>
                    </q-card-section>
                </q-card-section>
                <q-card-actions class="bg-white text-teal">
                    <q-space />
                    <q-btn style="width:150px" color="primary" label="积分清空" @click="clearAwardConfirm('清空积分')"
                        :disable="btnEnable" />
                    <q-btn style="width:150px" color="primary" label="删除人员" @click="clearAwardConfirm('清空人员')"
                        :disable="btnEnable" />
                </q-card-actions>
            </q-card>
        </q-dialog>

        <q-dialog v-model="firstDialog" transition-show="flip-down">
            <q-card style="width: 1000px; max-width: 100vw;" v-if="selectedApplication">
                <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                    <div class="text-h6 ">{{ selectedApplication['Award_Type'] }}积分申请</div>
                    <q-space />
                    <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                </q-card-section>
                <q-card-section class="q-pa-md q-gutter-sm" horizontal>
                    <!-- <q-card> -->
                    <q-card-section>
                        <!-- {{ selectedApplication.Award_Param }} -->
                        <div class="text-weight-medium text-blue-grey-4">{{ selectedApplication['Comment'] }}</div>
                        <template v-for="(value, key) in JSON.parse(selectedApplication.Award_Param)">
                            <!-- {{ key }}{{ value }} -->
                            <div style="margin-bottom: 15px; " class=" q-gutter-sm row">
                                <div v-if="key != 'selectedCrew'" class="text-bold"
                                    :style="value == 'imageUpload' ? 'height:150px;line-height: 150px;width:100px' : 'height:50px;line-height: 50px;width:100px'">
                                    {{ key }}
                                </div>

                                <q-select v-if="value == 'dropDown'" color="teal" filled v-model="singleAward_base['value']"
                                    :options="singleAward_base['array']"
                                    style="min-width: 200px; max-width: 300px;height:50px" dense>
                                </q-select>
                                <q-input v-if="value == 'input' && key.indexOf('标准') !== -1" filled
                                    style="min-width: 200px; max-width: 300px;height:50px"
                                    v-model="singleAward_base['value']" dense />
                                <q-input v-if="value == 'input' && key.indexOf('标准') === -1" filled
                                    style="min-width: 200px; max-width: 300px;height:50px" autogrow
                                    v-model="singleAward_base['comment']" dense :readonly="key.indexOf('个数') !== -1" />
                                <q-input v-if="value == 'view'" filled
                                    style="min-width: 200px; max-width: 300px;height:50px" autogrow
                                    v-model="singleAward_base['count']" dense readonly />
                                <q-separator inset />
                            </div>
                            <div>
                                <!-- {{ key }}{{ value }} -->
                                <template v-if="key == 'selectedCrew' && value == 'true'">
                                    <div>请选择奖励人员</div>
                                    <div>
                                        <template
                                            v-for="crew in (selectedApplication['Position'] == '工艺技师' ? jsData['data'] : opData['data'])">
                                            <q-checkbox style="width:130px" v-model="selectionCrew" :val="crew"
                                                :label="crew['Employee_PYName']" color="teal" />
                                        </template>
                                    </div>
                                    <!-- {{ selectionCrew }} -->
                                </template>
                                <div v-if="key == '0Delay' || key == '三连班' || key.includes('换型')">
                                </div>
                            </div>
                        </template>
                        <div v-if="selectedApplication['Award_Type'].includes('换型')">
                            <q-markup-table>
                                <thead>
                                    <tr>
                                        <th class="text-left">LinkID</th>
                                        <th class="text-left">奖励类型</th>
                                        <th class="text-left">换型类型'</th>
                                        <th class="text-left">换型子类型</th>
                                        <th class="text-left">奖励</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template v-for="item in Award_Count_byQCORows">
                                        <tr>
                                            <td class="text-left">{{ item['LinkID'] }}</td>
                                            <td class="text-left">{{ item['奖励类型'] }}</td>
                                            <td class="text-left">{{ item['换型类型'] }}</td>
                                            <td class="text-left">{{ item['换型子类型'] }}</td>
                                            <td class="text-left">{{ item['point'] }}</td>
                                        </tr>
                                    </template>
                                </tbody>
                            </q-markup-table>

                        </div>
                    </q-card-section>
                </q-card-section>
                <q-card-actions class="bg-white text-teal">
                    <q-space />
                    <q-btn style="width:150px" color="primary" label="积分分配"
                        @click="applyAward(selectedApplication['Award_Type'], selectedApplication['Position'], '更新')"
                        :disable="btnEnable" />
                </q-card-actions>
            </q-card>
        </q-dialog>

        <q-dialog v-model="summaryPrompt">
            <q-card style="width: 1000px; max-width: 100vw;">
                <q-card-section class="row items-center q-pb-none">
                    <div class="text-h6">月度积分汇总</div>
                    <q-space />
                    <q-btn icon="close" flat round dense v-close-popup />
                </q-card-section>

                <q-card-section>
                    <div class="txt-h3">申请总金额：{{ summaryAwardPoint }}元</div>
                </q-card-section>

                <q-card-section>
                    <q-table :data="newAwardAnalysisRow" />

                </q-card-section>
                <q-card-actions>
                    <q-btn v-if="summaryAwardType == '提交'" class="full-width" color="primary"
                        @click="applyToMonthly()">提交月度审批</q-btn>
                </q-card-actions>
            </q-card>
        </q-dialog>




        <div>
            <q-banner inline-actions class="text-white bg-red" v-if="awardStatus">
                积分系统申请已提交，审批状态:{{ awardStatus[0]['Approval_Status'] }},无法进行修改。如需修改请删除正在审批项目
                <template v-slot:action>
                    <q-btn flat color="white" label="转到积分申请" @click="gotoAwardPage" />
                </template>
            </q-banner>
            <div class="q-gutter-md row items-start" style="margin-bottom: 10px;">
                <q-select color="teal" filled v-model="yearMonth['value']" :options="yearMonth['array']" label="年月"
                    style="min-width: 150px; max-width: 300px" dense />
                <q-select color="teal" filled v-model="Line['value']" :options="Line['array']" label="机台"
                    style="min-width: 150px; max-width: 300px" dense />
                <q-btn push color="primary" class="btn-fixed-width" style="min-width:150px" @click="getMonthlyCrewList()"
                    :loading="loading">查询
                    <template v-slot:loading>
                        <q-spinner-hourglass class="on-left" />
                        数据获取中...
                    </template>
                </q-btn>
                <q-btn push color="purple" class="btn-fixed-width" style="min-width:150px" @click="analysisSummary('分析')"
                    :loading="loading">月度积分汇总
                </q-btn>
                <q-btn push color="secondary" class="btn-fixed-width" style="min-width:150px" @click="analysisSummary('提交')"
                    :loading="loading" :disable="btnEnable">提交月度积分
                </q-btn>



            </div>
            <div style="margin-bottom: 10px;">
                <q-card v-if="jsData && Performance_Summary_Monthly">
                    <q-table :data="jsData['data']" :columns="jsData['columns']" row-key="name"
                        :pagination.sync="myPagination" dense>

                        <template v-slot:top>
                            <div>技师人员列表</div>
                            <template v-for="item in Performance_Summary_Monthly">

                                <q-input filled v-model="item['Result']" :label="item['Performance_Type']" stack-label
                                    disable style="width:80px;margin-right:10px" dense/>
                            </template>
                            <q-input filled v-model="Performance_Summary_Monthly[0]['开机率']" label="开机率%" stack-label disable
                                style="width:200px;margin-right:10px" dense/>
                            <q-space />
                            <q-btn color="purple" class="btn-fixed-width" style="min-width:150px"
                                @click="getSummaryAward_byDaily('工艺技师')" :loading="loading1" :disable="btnEnable">获取技师点子BP积分
                                <template v-slot:loading1>
                                    <q-spinner-hourglass class="on-left" />
                                    数据更新中...
                                </template>
                            </q-btn>
                            <q-btn color="secondary" label="添加技师" class="q-ml-sm" style="width:150px"
                                @click="addCrewType('工艺技师')" :disable="btnEnable" />
                        </template>

                        <template v-slot:header="props">
                            <q-tr :props="props">
                                <q-th auto-width class="bg-primary text-white">修正</q-th>
                                <q-th v-for="col in props.cols" :key="col.name" :props="props">
                                    {{ col.name }}
                                </q-th>
                            </q-tr>
                        </template>

                        <template v-slot:body="props">
                            <q-tr :props="props">
                                <q-td auto-width>
                                    <q-icon name="far fa-edit" size="26px" @click="modifyAward(props)"
                                        :disable="btnEnable" />
                                </q-td>
                                <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                    {{ col.value }}

                                </q-td>
                            </q-tr>
                        </template>

                        <template v-slot:bottom>
                            <template v-for="award in awardList">
                                <q-btn v-if="award['Position'] === '工艺技师'" color="primary" :label="award['Award_Type']"
                                    class="q-ml-sm" @click="toApply_show(award)" :disable="btnEnable"  />
                            </template>
                            <q-space />
                        </template>
                    </q-table>
                </q-card>
            </div>
            <div >
                <q-card v-if="opData">
                    <q-table :data="opData['data']" :columns="opData['columns']" row-key="name"
                        :pagination.sync="myPagination" class="my-sticky-virtscroll-table" virtual-scroll
                        :virtual-scroll-sticky-size-start="40" :rows-per-page-options="[0]" dense>
                        <template v-slot:top>
                            <div>操作工人员列表</div>
                            <template v-for="award in awardList">
                                <q-btn v-if="award['Position'] === '操作工'" color="brown-5" :label="award['Award_Type']"
                                    class="q-ml-sm" @click="toApply_show(award)" :disable="btnEnable" />
                            </template>
                            <q-space />
                            <q-btn color="purple" class="btn-fixed-width" style="min-width:150px"
                                @click="getSummaryAward_byDaily('操作工')" :loading="loading1" :disable="btnEnable">获取每日积分并更新
                                <template v-slot:loading1>
                                    <q-spinner-hourglass class="on-left" />
                                    数据更新中...
                                </template>
                            </q-btn>
                            <q-btn color="secondary" label="添加操作工" class="q-ml-sm" style="width:150px"
                                @click="addCrewType('操作工')" :disable="btnEnable" />
                        </template>

                        <template v-slot:header="props">
                            <q-tr :props="props">
                                <q-th auto-width class="bg-primary text-white">修正</q-th>
                                <q-th v-for="col in props.cols" :key="col.name" :props="props">
                                    {{ col.name }}
                                </q-th>
                            </q-tr>
                        </template>

                        <template v-slot:body="props">
                            <q-tr :props="props">
                                <q-td auto-width>
                                    <q-icon name="far fa-edit" size="26px" @click="modifyAward(props)"
                                        :disable="btnEnable" />
                                </q-td>
                                <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                    {{ col.value }}
                                </q-td>
                            </q-tr>
                        </template>

                        <template v-slot:bottom>
                            <!-- <template v-for="award in awardList">
                                <q-btn v-if="award['Position'] === '操作工'" color="primary" :label="award['Award_Type']"
                                    class="q-ml-sm" @click="toApply_show(award)" :disable="btnEnable" />
                            </template> -->
                            <q-space />
                        </template>
                    </q-table>
                </q-card>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    name: 'awardApplication',
    mounted() {
        const _this = this
        _this.$nextTick(() => {
            _this.Line['array'] = localStorage.getItem('lineArray').split(',')
            _this.addCrewLine['array'] = localStorage.getItem('lineArray').split(',')
            _this.AccessToBtn(localStorage.getItem('Position'))
            const currentDate = new Date();
            currentDate.setMonth(currentDate.getMonth() - 1); // 获取上一个月的日期
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth() + 1;
            _this.yearMonth['value'] = year.toString() + (month < 10 ? '0' + month : month.toString());
            _this.yearMonth['array'].push(year.toString() + (month < 10 ? '0' + month : month.toString()))
        })

    },
    computed: {

    },
    watch: {
        singleAward_base(oldValue, newValue) {
            console.log('oldValue->', oldValue)
            console.log('newValue->', newValue)
        },
        async 'addCrewLine.value'(oldValue, newValue) {
            console.log('oldValue->', oldValue)
            console.log('newValue->', newValue)
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`approve/other_crew_list?Business_Title=${_this.addCrewTypeValue}&Line=${_this.addCrewLine['value']}`)
            _this.otherCrewList = false
            _this.selectedOtherCrew = []
            console.log('other_crew_list', res)
            _this.otherCrewList = res
        },

    },

    data() {
        return {
            btnEnable: true,
            // btnModifyEnable: false,
            tab: 'monthly',
            yearMonth: {
                array: [],
                value: ''
            },
            Line: {
                array: ['ND05', 'ND06'],
                value: ''
            },
            addCrewLine: {
                array: ['ND05', 'ND06'],
                value: ''
            },
            loading: false,
            loading1: false,
            myPagination: { rowsPerPage: 0 },
            jsData: false,
            opData: false,
            awardList: false,
            selectedApplication: false,
            firstDialog: false,
            modifyDialog: false,
            addCrewDialog: false,
            singleAward_base: {
                array: [],
                value: '',
                comment: '',
                count: 1
            },
            selectionCrew: [],
            modifyAward_Row: false,
            clearAwardArray: [],
            addCrewTypeValue: '',
            otherCrewList: false,
            selectedOtherCrew: [],
            summaryAwardPoint: 0,
            summaryAwardAnalysisRow: [],
            summaryPrompt: false,
            summaryAwardType: '',
            awardStatus: false,
            Performance_Summary_Monthly: false,
            newAwardAnalysisRow: false,
            Award_Count_byQCORows: []
        }
    },

    methods: {
        async AccessToBtn(awardType) {
            this.btnEnable = await checkAccess(awardType, 'approve')
        },
        async Award_Application_Summary_Monthly_verify() {
            const _this = this
            _this.loading = true
            _this.awardStatus = false
            const {
                data: res
            } = await _this.$http.get(`approve/Award_Application_Summary_Monthly_verify?yearMonth=${_this.yearMonth['value']}&Line=${_this.Line['value']}&department=生产`)
            if (res !== '未申请') {
                _this.btnEnable = true
                _this.awardStatus = res
            } else {
                _this.awardStatus = false
                _this.AccessToBtn(localStorage.getItem('Position'))
            }
        },
        async getMonthlyCrewList() {
            var _this = this
            _this.loading = true
            const {
                data: res
            } = await _this.$http.get(`approve/getMonthlyCrewList?yearMonth=${_this.yearMonth['value']}&Line=${_this.Line['value']}&department=生产`)
            console.log('getMonthlyCrewList', res)
            _this.jsData = res.jsData
            _this.opData = res.opData
            _this.awardList = res.awardList
            await _this.getMonthlyApplicationAward()
            await _this.Award_Application_Summary_Monthly_verify()
            await _this.Award_Performance_Summary_Monthly()
            _this.loading = false
        },
        async Award_Performance_Summary_Monthly() {
            const _this = this
            _this.loading = true
            const {
                data: res
            } = await _this.$http.get(`approve/Award_Performance_Summary_Monthly?yearMonth=${_this.yearMonth['value']}&Line=${_this.Line['value']}`)
            console.log('Award_Performance_Summary_Monthly', res)
            if (res.length == 0) {
                _this.$q.notify({
                    type: 'negative',
                    message: `月度审批未完成，无法进行积分申请`,
                    position: 'top'
                })
                _this.opData = false
                _this.jsData = false
                return
            }
            _this.Performance_Summary_Monthly = res
        },

        async getMonthlyApplicationAward() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`approve/getMonthlyApplicationAward?yearMonth=${_this.yearMonth['value']}&Line=${_this.Line['value']}&department=生产`)
            console.log('getMonthlyApplicationAward', res)
            const awardApplicationData = res
            const jsData = _this.jsData['data']
            const opData = _this.opData['data']
            if (awardApplicationData.length > 0) {
                for (var i = 0; i < awardApplicationData.length; i++) {
                    const employee_id = awardApplicationData[i]['Employee_ID']
                    const award_type = awardApplicationData[i]['Award_Type']
                    const award_Point = awardApplicationData[i]['Point']
                    console.log('awardApplicationData[i]', awardApplicationData[i])
                    for (var j = 0; j < opData.length; j++) {
                        console.log(' opData[j][award_type]', opData[j])
                        if (opData[j]['Employee_ID'] == employee_id) {

                            opData[j][award_type] = award_Point
                            break
                        }
                    }
                    for (var j = 0; j < jsData.length; j++) {
                        if (jsData[j]['Employee_ID'] == employee_id) {
                            jsData[j][award_type] = award_Point
                            break
                        }
                    }
                }
            }
        },
        async toApply_show(award) {
            const _this = this
            console.log('award', award)
            _this.selectedApplication = award
            _this.selectionCrew = []
            _this.singleAward_base = {
                array: [],
                value: '',
                comment: '',
                count: 0
            }
            _this.singleAward_base['array'] = award['awardbaseArray']
            _this.singleAward_base['value'] = award['awardbaseArray'][0]
            if (award['Award_Param'].indexOf('个数') !== -1) {
                const {
                    data: res
                } = await _this.$http.get(`approve/SP_API_Award_Count?yearMonth=${_this.yearMonth['value']}&Line=${_this.Line['value']}&awardType=${award['Award_Type']}&department=生产`)
                console.log('SP_API_Award_Count', res)
                _this.singleAward_base['count'] = res[0]['个数']
            }

            if (award['Award_Type'].includes('换型')) {
                const {
                    data: res1
                } = await _this.$http.get(`approve/SP_API_Award_Count_byDetail?yearMonth=${_this.yearMonth['value']}&Line=${_this.Line['value']}&department=生产&awardType=${award['Award_Type']}`)
                console.log('SP_API_Award_Count_byQCO', res1)
                if (res1.length > 0) {
                    let hxTotalCount = 0
                    for (var hxC = 0; hxC < res1.length; hxC++) {
                        hxTotalCount += res1[hxC]['point']
                    }
                    // console.log('hxTotalCount',hxTotalCount)
                    _this.singleAward_base['count'] = hxTotalCount
                    _this.singleAward_base['value'] = hxTotalCount
                    _this.Award_Count_byQCORows = res1
                    // console.log('_this.singleAward_base',_this.singleAward_base)
                }

            }


            _this.firstDialog = true
        },
        async applyAward(award_type, Position, operate) {
            const _this = this
            if (JSON.parse(_this.selectedApplication['Award_Param'])['selectedCrew'] == 'true' && _this.selectionCrew.length == 0) {
                _this.$q.notify({
                    type: 'negative',
                    message: `请选择奖励人员`,
                    position: 'top'
                })
                return
            }
            let award_base = { ..._this.singleAward_base }
            if (_this.selectedApplication['Award_Param'].indexOf('个数') !== -1 && Position == '工艺技师') {
                if (award_base['count'] == 0) {
                    _this.$q.notify({
                        type: 'negative',
                        message: `${award_type}个数为空，无法申请积分`,
                        position: 'top'
                    })
                    return
                } else {
                    award_base['value'] = award_base['value'] * award_base['count']
                }
            }
            let selectedData = _this.selectionCrew
            const selectedExistsNum = selectedData.length
            if (selectedExistsNum == 0 && Position == '操作工') {
                for (var i = 0; i < _this.opData['data'].length; i++) {
                    let opInfo = { 'Employee_ID': _this.opData['data'][i]['Employee_ID'], 'Employee_PYName': _this.opData['data'][i]['Employee_PYName'], 'Position': _this.opData['data'][i]['Position'] }
                    console.log('employeeInfo', opInfo)
                    selectedData.push(opInfo)
                }
            }
            if (selectedExistsNum == 0 && Position == '工艺技师') {
                for (var j = 0; j < _this.jsData['data'].length; j++) {
                    let jsInfo = { 'Employee_ID': _this.jsData['data'][j]['Employee_ID'], 'Employee_PYName': _this.jsData['data'][j]['Employee_PYName'], 'Position': _this.jsData['data'][j]['Position'] }
                    console.log('jsInfo', jsInfo)
                    selectedData.push(jsInfo)
                }
            }
            const postParam = {
                'yearMonth': _this.yearMonth['value'],
                'line': _this.Line['value'],
                'award_type': award_type,
                'selectedCrew': selectedData,
                'award_base': award_base['value'],
                'award_comment': _this.singleAward_base['comment'],
                'modifyName': localStorage.getItem('account'),
                'operate': operate,
                'department': '生产'
            }
            _this.$http.post('approve/SP_API_Award_Update_Application_Monthly', postParam).then(function (response) {
                console.log(response)
                if (response.data === '更新成功') {
                    let awardData = Position == '工艺技师' ? _this.jsData['data'] : _this.opData['data']
                    for (var s = 0; s < selectedData.length; s++) {
                        const employeeID = selectedData[s]['Employee_ID']
                        for (var d = 0; d < awardData.length; d++) {
                            if (awardData[d]['Employee_ID'] == employeeID) {
                                // console.log()
                                awardData[d][award_type] = award_base['value']
                            }
                        }
                    }
                    _this.$q.notify({
                        type: 'positive',
                        message: `${award_type}申请成功！`,
                        position: 'top'
                    })
                    _this.firstDialog = false

                }

            })
        },
        modifyAward(row) {
            console.log(row)
            const _this = this
            _this.modifyAward_Row = row
            _this.clearAwardArray = []

            _this.modifyDialog = true
        },
        async clearAwardConfirm(operate) {
            //operate: 清空积分/清空人员
            const _this = this
            const modifyAward_Row = _this.modifyAward_Row
            const clearAwardArray = _this.clearAwardArray
            const rowIndex = modifyAward_Row['rowIndex']
            const postParam = {
                'yearMonth': _this.yearMonth['value'],
                'line': _this.Line['value'],
                'award_type': clearAwardArray,
                'selectedCrew': modifyAward_Row['row']['Employee_ID'],
                'operate': operate
            }
            _this.$http.post('approve/delete_Application_Monthly', postParam).then(function (response) {
                console.log(response)
                if (response.data === '更新成功') {
                    let awardData = modifyAward_Row['row']['Position'] == '工艺技师' ? _this.jsData['data'] : _this.opData['data']
                    if (operate == '清空人员') {
                        awardData.splice(rowIndex, 1)
                    } else {
                        for (var s = 0; s < clearAwardArray.length; s++) {
                            awardData[rowIndex][clearAwardArray[s]] = ''
                        }
                    }
                    _this.$q.notify({
                        type: 'positive',
                        message: `清空操作成功`,
                        position: 'top'
                    })
                    _this.modifyDialog = false

                }

            })

        },
        async addCrewType(addCrewType) {
            console.log(addCrewType)
            const _this = this
            _this.addCrewTypeValue = addCrewType
            _this.otherCrewList = false
            _this.selectedOtherCrew = []
            _this.addCrewDialog = true
        },
        async addCrewToList() {
            const _this = this
            console.log(_this.selectedOtherCrew)
            const crewData = _this.addCrewTypeValue == '工艺技师' ? _this.jsData['data'] : _this.opData['data']
            const selectedOtherCrew = _this.selectedOtherCrew
            console.log('other_crew_list', selectedOtherCrew)
            console.log('crewData', crewData)
            for (let i = 0; i < selectedOtherCrew.length; i++) {
                const newCrewList = { ...crewData[0] }
                for (let key in newCrewList) {
                    if (newCrewList.hasOwnProperty(key)) {
                        newCrewList[key] = ''
                    }
                }
                newCrewList['Employee_ID'] = selectedOtherCrew[i]['Employee_ID']
                newCrewList['Employee_PYName'] = selectedOtherCrew[i]['Employee_PYName']
                newCrewList['Position'] = selectedOtherCrew[i]['Position']
                crewData.push(newCrewList)
            }
            _this.$q.notify({
                type: 'positive',
                message: `添加人员成功`,
                position: 'top'
            })
            _this.addCrewDialog = false

        },
        async getSummaryAward_byDaily(position) {
            const _this = this
            const postParam = {
                'yearMonth': _this.yearMonth['value'],
                'line': _this.Line['value'],
                'selectedCrew': position=='工艺技师'?_this.jsData['data']:_this.opData['data'],
                'modifyName': localStorage.getItem('account'),
                'department': '生产'
            }
            _this.$http.post('approve/SP_API_Award_SummaryAward_ByCrew', postParam).then(function (response) {
                console.log(response)
                const responseData = response.data
                if (response.data.length > 0) {
                    const data=position=='工艺技师'?_this.jsData['data']:_this.opData['data']
                    for (var i = 0; i < data.length; i++) {
                        const employee_id =data[i]['Employee_ID']
                        for (var j = 0; j < responseData.length; j++) {
                            if (employee_id == responseData[j]['Employee_ID']) {
                                for (const [key, value] of Object.entries(responseData[j])) {
                                    data[i][key] = value;
                                }
                            }
                        }
                    }
                    _this.$q.notify({
                        type: 'positive',
                        message: `每日积分已更新`,
                        position: 'top'
                    })
                }
            })

        },
        applyToMonthly() {
            const _this = this
            console.log('confirmData', _this.summaryAwardAnalysisRow)
            const applyData = [..._this.summaryAwardAnalysisRow]
            const paramData = {
                "data": [],
                'modifyName': localStorage.getItem('account'),
                'line': _this.Line['value'],
                'yearMonth': _this.yearMonth['value'],
                'department': '生产'
            }
            for (let i = 0; i < applyData.length; i++) {
                const sum = applyData[i].slice(1).reduce((acc, curr) => acc + curr, 0);
                const result = [applyData[i][0], sum];
                paramData['data'].push(result)
            }
            console.log('paramData', paramData)
            _this.$http.post('approve/Award_Application_Summary_Monthly', paramData).then(function (response) {
                console.log(response)
                if (response.data === '更新成功') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `提交申请成功`,
                        position: 'top'
                    })
                    _this.btnEnable = true
                    _this.summaryPrompt = false
                }

            })

        },
        analysisSummary(e) {
            const _this = this
            _this.summaryAwardPoint = 0
            // console.log('totalOPData', this.opData['data'])
            // console.log('totalJSData', this.jsData['data'])
            // console.log('AwardData', this.awardList)
            const awardListArray = [..._this.awardList]
            let awardListDistinct = []
            console.log('awardListArray', awardListArray)
            for (let a = 0; a < awardListArray.length; a++) {
                if (awardListDistinct.indexOf(awardListArray[a]['Award_Type']) === -1) {
                    awardListDistinct.push(awardListArray[a]['Award_Type'])
                }
            }
            awardListDistinct.push('点子')
            awardListDistinct.push('BP')
            const opData = _this.opData['data']
            const jsData = _this.jsData['data']
            const Award_Summary_Row = []
            for (let i = 0; i < awardListDistinct.length; i++) {
                let award = awardListDistinct[i]
                let Award_Summary_Single_Row = {}
                Award_Summary_Single_Row = [award, 0, 0]
                console.log('opData', opData)
                console.log('opDataLength', opData.length)
                for (var op = 0; op < opData.length; op++) {
                    // console.log('subAward',award)
                    // console.log('opData[op][award]',opData[op][award])
                    console.log('opdataIndex', op)
                    console.log('opData[op][award]', opData[op][award])
                    if (opData[op][award] != "" && opData[op][award] !== null && opData[op].hasOwnProperty(award) && opData[op][award] !== undefined) {
                        Award_Summary_Single_Row[1] += parseInt(opData[op][award])
                        _this.summaryAwardPoint += parseInt(opData[op][award])
                        console.log('Award_Summary_Single_Row', Award_Summary_Single_Row)
                    }
                }
                console.log('操作工获取数据结束')
                for (let js = 0; js < jsData.length; js++) {
                    console.log('subJSAward', award)
                    if (jsData[js].hasOwnProperty(award)) {
                        if (jsData[js][award] != "" && jsData[js][award] !== null && jsData[js].hasOwnProperty(award)) {
                            Award_Summary_Single_Row[2] += parseInt(jsData[js][award])
                            _this.summaryAwardPoint += parseInt(jsData[js][award])
                        }
                    }

                }

                Award_Summary_Row.push(Award_Summary_Single_Row)
            }
            console.log('Award_Summary_Row', Award_Summary_Row)
            _this.summaryAwardAnalysisRow = Award_Summary_Row
            const newAwardAnalysisRow = [{ '职位': '操作工' }, { '职位': '工艺技师' }]
            for (var x = 0; x < Award_Summary_Row.length; x++) {
                const Award_Summary_Row_single = Award_Summary_Row[x]
                newAwardAnalysisRow[0][Award_Summary_Row_single[0]] = Award_Summary_Row_single[1]
                newAwardAnalysisRow[1][Award_Summary_Row_single[0]] = Award_Summary_Row_single[2]
            }
            console.log('newAwardAnalysisRow', newAwardAnalysisRow)
            _this.newAwardAnalysisRow = newAwardAnalysisRow
            _this.summaryAwardType = e
            _this.summaryPrompt = true
        },
        gotoAwardPage() {
            this.$router.push('/awardPage/award')
        }
    }
}
function checkAccess(award_type, btnType) {
    return new Promise((resolve, reject) => {
        const userRole = localStorage.getItem('user_role')
        const userPosition = localStorage.getItem('Position')
        let result = true
        console.log('userRole', userRole)
        console.log('userPosition', userPosition)
        if (btnType === 'approve') {
            if ((userRole.indexOf('积分系统_审批') !== -1 && userPosition.indexOf('资产主管') !== -1) || userRole.indexOf('Admin') !== -1) {
                console.log('有权限')
                result = false
            }
            else {
                console.log('没权限')
            }
        }
        console.log(result)
        resolve(result)
    })
}

</script>

<style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */
  height: 500px
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0
</style>