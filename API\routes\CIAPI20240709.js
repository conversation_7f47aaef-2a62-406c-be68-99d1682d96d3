var express = require('express')
var router = express.Router()
var sqlexec_grade = require('../sql/sqlCI3S')
var moment = require('moment')
const { resolve } = require('q')

router.post("/Insert_CI_3S_Summary", (req, res) => {
    const data = req.body
    console.log(data)
    var returnQuery = new sqlexec_grade()
    returnQuery.Insert_CI_3S_Summary(data, function (result) {
        res.send(result)

    })
})

router.post("/Insert_CI_3S_Red_Statistics", (req, res) => {
    const data = req.body
    console.log(data)
    const linkid = data['LinkID']
    const red_statistics_data = data['data']
    let opArray = ['OP1', 'OP2', 'OP3', 'OP4', 'OP5']
    for (let i = 0; i < opArray.length; i++) {
        const opIndexName = opArray[i]
        let postSingleData = {
            'LinkID': linkid,
            'OP': opIndexName,
            'Red_Count': red_statistics_data[0][opIndexName],
            'Resolved_Count': red_statistics_data[1][opIndexName],
            'Pending_Count': red_statistics_data[2][opIndexName],
        }
        console.log('postSingleData', postSingleData)
        var returnQuery = new sqlexec_grade()
        returnQuery.Insert_CI_3S_Red_Statistics(postSingleData, function (result) {
            res.send(result)
        })
    }
})

router.post("/Insert_CI_3S_Problem_List", (req, res) => {
    const data = req.body
    console.log(data)
    const linkid = data['LinkID']
    const Problem_List_data = data['data']
    var returnQuery = new sqlexec_grade()
    if (Problem_List_data.length > 0) {
        for (let i = 0; i < Problem_List_data.length; i++) {
            if (Problem_List_data[i]['Problem_Desc'] != '') {
                returnQuery.Insert_CI_3S_Problem_List(linkid, Problem_List_data[i], function (result) {
                    res.send(result)
                })
            }
        }
    }

})

router.get("/query_CI_3S_Summary", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.query_CI_3S_Summary(req.query.line, function (result) {
        res.send(result)
    })

})

router.get("/query_CI_3S_byLinkid", (req, res) => {
    const linkid = req.query.LinkID
    const returnQuery = new sqlexec_grade()
    let result = {
        'CI_3S_Summary_byLinkid': [],
        'CI_3S_Red_Statistics_byLinkid': [],
        'CI_3S_Problem_List_byLinkid': [],
        'CI_3S_Score_byLinkid':[]
    }
    returnQuery.query_CI_3S_Summary_byLinkid(linkid, function (result1) {
        result['CI_3S_Summary_byLinkid'] = result1
        returnQuery.query_CI_3S_Red_Statistics_byLinkid(linkid, function (result2) {
            result['CI_3S_Red_Statistics_byLinkid'] = result2
            returnQuery.query_CI_3S_Problem_List_byLinkid(linkid, function (result3) {
                result['CI_3S_Problem_List_byLinkid'] = result3
                returnQuery.query_CI_3S_Score_byLinkid(linkid, function (result4) {
                    result['CI_3S_Score_byLinkid'] = result4
                    res.send(result)
                })
            })
        })
    })

})

router.post("/Insert_CI_3S_Score", (req, res) => {
    const data = req.body
    const approver = req.body.approver
    console.log(data)
    const linkid = data['LinkID']
    const score_data = data['data']
    let itemArray = ['清扫干净', '红牌问题描述清晰', '原因分析符合逻辑', '改善对策制定有效', '总结内容完整清晰']
    for (let i = 0; i < itemArray.length; i++) {
        const itemIndexName = itemArray[i]
        let postSingleData = {
            'LinkID': linkid,
            'item': itemIndexName,
            'score': score_data[0][itemIndexName],
        }
        console.log('postSingleData', postSingleData)
        var returnQuery = new sqlexec_grade()
        returnQuery.Insert_CI_3S_Score(postSingleData, function (result) {
            returnQuery.update_CI_3S_Summary_byApprover(linkid, approver, function (result) {
                res.send(result)
            })
        })
    }
})

//HK_SYSTEM

router.get("/CI_HK_Task_List", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.CI_HK_Task_List(req.query.Line, req.query.MachineStatus, req.query.Date, req.query.Shift, function (result) {
        const finalResult = {};

        result.forEach(item => {
            if (!finalResult[item.Area]) {
                finalResult[item.Area] = []; // 如果该区域还未创建，则初始化为空数组
            }
            finalResult[item.Area].push(item); // 将当前项添加到对应区域的数组中
        });
        res.send(finalResult);
    })

})

router.get("/CI_HK_Standard", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.CI_HK_Standard(req.query.Standard_ID, function (result) {
        res.send(result)
    })
})
router.get("/CI_HK_LSW_ID", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.CI_HK_LSW_ID(req.query.Employee_ID, function (result) {
        res.send(result)
    })
})

router.get("/CI_HK_LSW_OwnerName", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.CI_HK_LSW_OwnerName(req.query.Line, function (result) {
        res.send(result)
    })
})


router.get("/CI_HK_Check_Data", (req, res) => {
    const Task_ID = req.query.Task_ID
    const Date = req.query.Date
    const Line = req.query.Line
    const Shift = req.query.Shift
    var returnQuery = new sqlexec_grade()
    returnQuery.CI_HK_Check_Data(Task_ID, Date, Line, Shift, function (result) {
        res.send(result)
    })
})

router.post("/Insert_CI_HK_Check_Data", (req, res) => {
    console.log(req.body)
    var returnQuery = new sqlexec_grade()
    returnQuery.Insert_CI_HK_Check_Data(req.body, function (result) {
        res.send(result)
    })
})

router.get("/CI_HK_LSW_Problem_Data", (req, res) => {
    const RelateID = req.query.RelateID
    var returnQuery = new sqlexec_grade()
    returnQuery.CI_HK_LSW_Problem_Data(RelateID, function (result) {
        res.send(result)
    })
})

router.get("/CI_HK_Problem_Statistics", (req, res) => {
    var returnQuery = new sqlexec_grade()
    returnQuery.CI_HK_Problem_Statistics(req.query.Line, req.query.MachineStatus, req.query.Date, req.query.Shift, function (result) {
        const finalResult = {
            'Total': 0,
            '主机': 0,
            '外围': 0,
        };
        if (result.length > 0) {
            for (let i = 0; i < result.length; i++) {
                finalResult[result[i]['Area']] = result[i]['problemCount'];
            }
        }
        finalResult['Total'] = finalResult['主机'] + finalResult['外围'];
        res.send(finalResult);
    })

})





module.exports = router