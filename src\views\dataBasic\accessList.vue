<template>
	<base-content>
		<q-dialog v-model="firtDialog" transition-show="flip-down">
			<q-card style="width:800px; max-width: 100vw;">
				<q-card-section class=" bg-teal text-white fit row  justify-between  ">
					<div class="text-h6 ">批量增加权限</div>
					<q-space />
					<q-btn padding="xs" color="red" icon="clear" v-close-popup />
				</q-card-section>

				<q-card-section horizontal>
					<q-table :data="rows" row-key="Employee_ID" class="my-sticky-virtscroll-table" virtual-scroll
						:virtual-scroll-sticky-size-start="48" :rows-per-page-options="[0]" :filter="filter1"
						selection="multiple" :selected.sync="selected1" :columns="columns">
						<template v-slot:top-right>
							<q-input borderless dense debounce="300" v-model="filter1" placeholder="Search">
								<template v-slot:append>
									<q-icon name="search" />
								</template>
							</q-input>
						</template>
					</q-table>

					<q-card-section>
						<div class="text-h5" style="font-weight: 600;">已选人员</div>
						<div class="text-h6" v-for="item in selected1">{{ item.Employee_Name }}({{item.Employee_ID}})</div>
					
					</q-card-section>
				</q-card-section>
				<q-card-actions align="right">
					<q-btn style="width:150px" label="确认添加" color="purple" @click="insert_Multiple()" />
					<q-btn style="width:150px" label="取消" color="primary" v-close-popup />
				</q-card-actions>
			</q-card>
		</q-dialog>




		<div class="q-pa-md">
			<q-splitter v-model="splitterModel" style="height:800px">
				<template v-slot:before>
					<div class="q-pa-md" v-if="rows">
						<q-table :data="rows" row-key="Employee_ID" class="my-sticky-virtscroll-table" virtual-scroll
							:virtual-scroll-sticky-size-start="48" :rows-per-page-options="[0]" :filter="filter"
							selection="single" :selected.sync="selected" :columns="columns">
							<template v-slot:top-right>
								<q-input borderless dense debounce="300" v-model="filter" placeholder="Search">
									<template v-slot:append>
										<q-icon name="search" />
									</template>
								</q-input>
							</template>
						</q-table>
					</div>
				</template>
				<template v-slot:after>
					<div class="q-pa-md col-12 col-sm-6 q-gutter-sm">
						<div class="text-h6" style="font-weight: 600;">已选择人员 <q-btn @click="clearItem('Crew')" flat
								color="primary" label="清空" style="font-weight: 600;" /></div>

						<div>
							<!-- <span> </span> -->
							<span class="text-h7" v-for="item in selected">{{ item.Employee_Name }}, </span>
							<!-- <span>W80845</span> -->
						</div>
						<q-separator spaced />

						<div class="text-h6" style="font-weight: 600;">已选/已有权限 <q-btn flat color="primary" label="清空"
								@click="clearItem('selectedAccess')" />
							<q-btn class="glossy" color="teal" label="批量选择人员" @click="selected_Multiple_show()" />
						</div>

						<div>
							<div v-for="tick in ticked" :key="`ticked-${tick}`">
								{{ tick }}
							</div>
						</div>
						<q-separator spaced />
						<div class="q-gutter-sm">
							<q-btn align="around" class="btn-fixed-width" color="primary" label="更新权限"
								@click="updateAccess()" />
							<!-- <q-btn align="around" class="btn-fixed-width" color="red" label="删除权限" /> -->
						</div>

						<div class="text-h6" style="font-weight: 600;">权限树
							<!-- <q-btn   class="glossy" color="teal" label="新增权限组" @click="clearItem('selectedAccess')" /> -->
						</div>
						<div v-if="accessTreeData">
							<q-tree class="col-12 col-sm-6" :nodes="accessTreeData" node-key="label" tick-strategy="leaf"
								:selected.sync="selectedTree" :ticked.sync="ticked" :expanded.sync="expanded" />

						</div>

					</div>



				</template>
			</q-splitter>
		</div>

	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'

export default {
	name: 'accessList',
	components: {
		BaseContent,
	},
	mounted() {
		this.getEmployee_List()
		this.accessTree()
	},

	watch: {
		selected(newValue, oldValue) {
			console.log('oldValue', oldValue)
			console.log('newValue', newValue)
			if (newValue.length > 0) {
				if (newValue[0].Employee_ID) {
					this.getEmoloyeeAccess(newValue[0].Employee_ID)
				}
			}


		}
	},
	data() {
		return {
			splitterModel: 50,
			rows: false,
			filter: '',
			filter1: '',
			//selectedCrew:[],
			selected: [],
			selected1: [],
			selectedTree: '',
			columns: [
				{
					name: 'Employee_ID',
					required: true,
					label: '员工号',
					align: 'left',
					field: row => row.Employee_ID,
					format: val => `${val}`,
					sortable: true
				},
				{ name: 'Employee_Name', align: 'left', label: '员工姓名', field: 'Employee_Name', sortable: true },
				{ name: 'Department', align: 'left', label: '部门', field: 'Department', sortable: true, style: 'width: 10px' },
				{ name: 'Line', align: 'left', label: '机台', field: 'Line', sortable: true },
				{ name: 'Position', align: 'left', label: '职位', field: 'Position', sortable: true }
			],
			accessTreeData: false,
			ticked: [],
			expanded: [],
			firtDialog: false
		}
	},

	methods: {
		async getEmoloyeeAccess(employeeID) {
			const _this = this
			const {
				data: res
			} = await _this.$http.get('data_base/accessByPersonal?employeeid=' + employeeID)
			_this.ticked = res.ticked
			_this.expanded = res.expanded
		},
		async getEmployee_List() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('data_base/getEmployee_List')
			console.log(res)
			_this.rows = res
		},
		async accessTree() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('data_base/accessTree')
			console.log(res)
			_this.accessTreeData = res
		},
		clearItem(fun) {
			console.log('清空按钮')
			const _this = this
			if (fun == 'Crew') {
				_this.selected = []
			}
			if (fun == 'selectedAccess') {
				_this.ticked = []
			}
		},
		updateAccess() {
			const _this = this
			const postParam = {
				'employeeID': _this.selected[0]['Employee_ID'],
				'AccessList': _this.ticked
			}
			_this.$http.post('data_base/update_accessByPersonal', postParam).then(function (response) {
				console.log('response', response)
				if (response.data === '更新成功') {
					_this.$q.notify({
						type: 'positive',
						message: `权限更新成功`,
						position: 'top'
					})
				}
			})
		},
		selected_Multiple_show() {
			const _this = this
			_this.selected1 = []
			_this.filter1 = ''
			if (_this.ticked.length == 0) {
				_this.$q.notify({
					type: 'negative',
					message: `请选择所对应系统权限`,
					position: 'top'
				})
				return
			}
			_this.firtDialog = true
		},
		insert_Multiple(){
			const _this=this
			console.log('selected1',_this.selected1)
			console.log('ticked',_this.ticked)
			const postParam = {
				'employeeList': _this.selected1,
				'AccessList': _this.ticked
			}
			_this.$http.post('data_base/insert_Multiple_Access', postParam).then(function (response) {
				console.log('response', response)
				if (response.data === '更新成功') {
					_this.$q.notify({
						type: 'positive',
						message: `权限更新成功`,
						position: 'top'
					})
					_this.firtDialog=false
				}
			})
		}


	}
}
</script>
<!-- <style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */
  height: 700px

  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0
.btn-fixed-width
	width: 200px
</style> -->