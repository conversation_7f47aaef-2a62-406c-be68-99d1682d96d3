.cimo-shadow {
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px !important;
    transition: All 0.2s ease-in-out;
}
.cimo-shadow:hover{
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 13px 3px !important;
}
.base-card-shadow{
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
    border-radius: 4px;
}
.base-markdown-content{
    padding: 0px 10px 0px 10px;
    max-width: 760px;
    margin: 0 auto;
    color: #2c3e50;
    font-size: 16px;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI Emoji, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
    word-wrap: break-word;
    -webkit-font-smoothing: antialiased;
}
.bg-my-loadingBar-color{
    background-image: linear-gradient(to right, #56ccf2, #2f80ed, #eeeeee) !important;
    background-size: 400% !important;
    animation: loadingBar-animation 3s infinite;
}
@keyframes loadingBar-animation {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: -100% 0;
    }
}
