<template>
	<base-content>
		<div class="q-pa-md">
			<q-dialog v-model="prompt">
				<q-card style="min-width:99%">

					<div style="width: 100%;">

						<q-btn label="关闭窗口" push color="primary" class="full-width" v-close-popup />
						<awardComponents moduleFunction="awardMonthlyApproval" :rows="selectedRows" ></awardComponents>
						<q-card-actions align="right" class="text-primary">
							<q-btn label="关闭窗口" push color="primary" class="full-width" v-close-popup />
						</q-card-actions>
					</div>
				</q-card>

			</q-dialog>



			<q-dialog v-model="approvalShowdialog">
				<q-card style="min-width:80%">
					<div class="q-pa-md">
						<q-stepper v-model="approvalIndex" ref="stepper" color="primary" animated>
							<q-step :name="1" title="第一层审批" icon="settings" :done="approvalIndex > 1">

								<div class="q-pa-md row items-start q-gutter-md" v-if="approvalData">
									<template v-for="item in approvalData">
										<q-card class="my-card-detail" style="width:30%">
											<q-card-section
												:class="item.Approval_Status == '已批准' ? 'bg-primary  text-white' : item.Approval_Status == '已拒绝' ? 'bg-red text-white' : 'bg-white text-black '">
												<div class="text-h6">{{ item.Approver_Name }}---{{ item.Approval_Status }}</div>
												<q-separator color="white" />
												<div class="text-subtitle2">{{ item.Approval_Date}}</div>
											</q-card-section>
										</q-card>
									</template>
								</div>
							</q-step>

							<q-step :name="2" title="第二层审批" caption="Optional" icon="create_new_folder"
								:done="approvalIndex > 2">
								<div class="q-pa-md row items-start q-gutter-md" v-if="approvalData">
									<template v-for="item in approvalData">
										<q-card class="my-card-detail" style="width:30%">
											<q-card-section
												:class="item.Approval_Status == '已批准' ? 'bg-primary  text-white' : 'bg-purple text-white'">
												<div class="text-h6">{{ item.Approver_Name }}---{{ item.Approval_Status }}</div>
												<q-separator color="white" />
												<div class="text-subtitle2">{{ item.Approval_Date}}</div>
											</q-card-section>

										</q-card>
									</template>
								</div>
							</q-step>

							<q-step :name="3" title="第三层审批" icon="assignment" :done="approvalIndex > 3">
								<div class="q-pa-md row items-start q-gutter-md" v-if="approvalData">
									<template v-for="item in approvalData">
										<q-card class="my-card-detail" style="width:30%">
											<q-card-section
												:class="item.Approval_Status == '已批准' ? 'bg-primary  text-white' : 'bg-purple text-white'">
												<div class="text-h6">{{ item.Approver_Name }}---{{ item.Approval_Status }}</div>
												<q-separator color="white" />
												<div class="text-subtitle2">{{ item.Approval_Date}}</div>
											</q-card-section>

										</q-card>
									</template>
								</div>
							</q-step>

							<q-step :name="4" title="已完成审批" icon="add_comment" :done="approvalIndex == 4">
								<h3 class="text-center">已全部审批</h3>
							</q-step>
						</q-stepper>
					</div>
				</q-card>
			</q-dialog>

		
			<q-table :data="data" :columns="columns" row-key="AwardID" :filter="filter" :pagination.sync="pagination"
				hide-pagination dense>
				<template v-slot:top-left>
					<span style="font-weight: 600;font-size: 25px;line-height: 40px;">积分月度申请</span>
					<!-- 			<q-btn style="margin-left:10px;width:100px;height: 35px;line-height: 0px;" color="primary" label="添加申请" @click="add" /> -->
				</template>

				<template v-slot:top-right>
					<q-btn @click="gotoMonthlyAward" color="secondary" label="转到月度申请" icon-right="send"/>
					<q-input borderless dense debounce="300" v-model="filter" placeholder="Search">
						<template v-slot:append>
							<q-icon name="search" />
						</template>
					</q-input>
				</template>

				<template v-slot:header="props">
					<q-tr :props="props">
						<q-th auto-width class="bg-primary text-white" />
						<q-th v-for="col in props.cols" :key="col.name" :props="props">
							{{ col.label }}
						</q-th>
						<q-th auto-width class="bg-primary text-white" />
					</q-tr>
				</template>

				<template v-slot:body="props">
					<q-tr :props="props">
						<q-td auto-width>
							<q-btn v-model="props.expand" size="sm" color="accent" :label="props.row.Approval_Status"
								@click="approvalShow(props)"
								:color="props.row.Approval_Status == 'progress' ? 'secondary' : props.row.Approval_Status == 'rejected' ? 'red' : 'primary'" />
							<!-- 	<q-toggle v-model="props.expand" checked-icon="add" unchecked-icon="remove" /> -->
						</q-td>
						<q-td v-for="col in props.cols" :key="col.name" :props="props"
							@click="gotoDetail(props.row)" >
							<!-- <span :class="col.value=='生产'?'bg-primary text-white':col.value=='工程维修'?'bg-secondary text-white':''"> {{ col.value }}</span> -->
							<template v-if="col.value=='生产' || col.value=='工程维修' ">
								<q-badge align="middle" :color="col.value=='生产'?'primary':'secondary'">{{ col.value }}</q-badge>
							</template>
							<template v-if="col.value!=='生产' && col.value!=='工程维修' ">
								{{ col.value }}
							</template>
						</q-td>

						<q-td auto-width>
							<q-btn v-show="props.row.Approval_Level < 4" size="xs" @click="delApplyAward(props)" color="negative"
								icon="clear" />
						</q-td>
					</q-tr>

				</template>

			</q-table>


			<div class="row justify-center q-mt-md">
				<q-pagination v-model="pagination.page" color="grey-8" :max="pagesNumber" size="sm" />
			</div>
		</div>
	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import awardComponents from '../../components/componentsList/index.vue'
export default {
	components: {
		BaseContent,
		awardComponents
	},
	data() {
		return {
			system: '积分系统',
			queryData: false,
			step: 4,
			visible: false,
			prompt: false,
			address: '111',
			yearmonth: [202103],
			mill: ['南京南厂', '南京北厂'],
			line: ['ND04', 'ND05', 'ND06', 'ND07', 'ND08', 'ND09'],
			selectedYearmonth: '',
			selectedLine: '',
			selectedmill: '',
			userData: false,
			summaryData: false,
			summaryTotal: 0,
			applySummaryData: false,
			selectedRows:false,
			pagination: {
				descending: false,
				page: 1,
				rowsPerPage: 20
			},
			filter: '',
			columns: [],
			data: [],
			approvalShowdialog: false,
			approvalData: false,
			approvalIndex: false
		}
	},
	mounted() {
		this.getApplyAward()

	},
	computed: {
		pagesNumber() {
			return Math.ceil(this.data.length / this.pagination.rowsPerPage)
		}
	},
	methods: {
		approvalShow(props) {
			console.log('props',props)
			const awardID=props.row['Fin_YearMonth']+props.row['Line']
			const department=props.row['Department']
			this.approvalIndex = props.row.Approval_Level
			this.approvalList(awardID, department)
			this.approvalShowdialog = true
		},
		add() {
			this.prompt = true
			this.queryData = true
			this.userData = false
			this.summaryData = false
		},

		async getApplyAward() {

			var _this = this
			var awardID = _this.selectedYearmonth + _this.selectedLine
			const {
				data: res
			} = await _this.$http.get('approve/getApplyAwardSummary?system=积分系统')
			_this.data = res.data
			_this.columns=res.columns
		},

		async delApplyAward(row) {
			var data = {
				"yearMonth": row['row']['Fin_YearMonth'],
				"Line":row['row']['Line'],
				"Department":row['row']['Department']
			}
			var _this = this
			_this.$http.post('approve/delAwardApply', data).then(function (response) {
				console.log(response)
				if (response.data == '删除成功') {
					console.log("开始刷新数据")
					_this.getApplyAward()
				}
			})
		},

		gotoDetail(rows) {
			console.log(rows)
			this.selectedRows = rows
			// this.getAward(e[0])
			// this.selectedYearmonth = e[1]
			this.prompt = true
			this.visible = true
		},

		async approvalList(awardID,department) {
			this.approvalData = []

			const {
				data: res
			} = await this.$http.get('approve/getApproveStatus?department='+ department +'&awardid=' + awardID)

			console.log(res)
			this.approvalData = res

		},


		applyStatus(e) {
			console.log(e)
		},
		gotoMonthlyAward(){
			this.$router.push('/awardPage/awardApplication?tab=monthlyApplication')
		}





	}
}
</script>

<style scoped>.my-card-detail {
	width: 100%;
	max-width: 250px
}</style>
