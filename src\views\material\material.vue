<template>
	<base-content>
		<div class="q-pa-md">
			<!--  选择框及按钮 -->
			<div class="q-gutter-md row">
				<q-select outlined v-model="Line" :options="LineList" label=" 产线" style="width: 100px" />
				<q-select outlined v-model="Product1" :options="SKUList" label="换型前SKU - 计划开始时间" style="width: 250px" />
				<q-select outlined v-model="Product2" :options="SKUList" label="换型后SKU - 计划开始时间" style="width: 250px" />
				<q-btn color="primary" label="查询" @click="getOtherBom" style="width: 100px " />
				<q-btn color="primary" icon="print" label="打印" @click="printTask()" />
				<!-- <div>{{ selectedData.product1 }}</div> -->
			</div>
		</div>

		<!--  换型前差异物料列表 -->
		<div class="q-pa-md row justify-around" v-if="BOM1List" :fullscreen.sync="fullscreen">
			<div class="col" style="margin-right: 10px;">
				<q-table dense :data="BOM1List" :pagination="{ rowsPerPage: 20 }" />
			</div>
			<div class="col">
				<q-table dense :data="BOM2List" :pagination="{ rowsPerPage: 20 }" />
			</div>
		</div>
		<div class="q-pa-md row justify-around" v-if="SameBOMList" :fullscreen.sync="fullscreen">
			<div class="col" style="margin-top: 10px;">
				<q-table dense :data="SameBOMList" :pagination="{ rowsPerPage: 20 }" />
			</div>
		</div>





	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import Quagga from 'quagga'
import moment from 'moment';

export default {

	components: {
		BaseContent,
		Quagga

	},

	mounted() {
		console.log("mounted", this.$route.query)
		this.Line = this.$route.query.line
		this.getLineList()
		this.getSKUList()
		this.created()
	},

	data() {
		return {
			isNavVisible: false,
			fullscreen: false,
			leftDrawerOpen: false,
			Line: "",
			Product1: '',
			Product2: '',
			BOM1: '',
			BOM2: '',
			SKUList: [],
			LineList: [],
			BOM1List: [],
			BOM2List: [],
			SameBOMList: [],
			POData1: [],
			POData2: []
		}
	},
	watch: {
		Line: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.Product1 = ''
			this.Product2 = ''
			this.getSKUList()
			this.clearBOM()
		},
		Product1: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.clearBOM()
		},
		Product2: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.clearBOM()
		},
	},
	methods: {
		async getLineList() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('material/lineList')
			console.log('LineList', res)
			_this.LineList = res
			// _this.optionsData.line = res
			//_this.selectedData.product1=res[0]['shiftEndTime']
		},

		created() {
			this.leftDrawerOpen = false;
		},
		async clearBOM() {
			this.BOM1List = []
			this.BOM2List = []
			this.SameBOMList = []
		},

		async getSKUList() {
			var _this = this
			const Line = this.Line
			const {
				data: res
			} = await _this.$http.get(`mes/SKUList?line=${Line}`)
			console.log('SKUList', res)
			// _this.skuList=res.map(item=>item.SKU_order)
			_this.SKUList = res
		},


		async getOtherBom() {
			this.clearBOM()
			console.log('product1', this.Product1)
			console.log('product2', this.Product2)
			this.getBOM1(this.Product1['BOM'], this.Product2['BOM'])
			this.getBOM2(this.Product1['BOM'], this.Product2['BOM'])
			this.getSameBOM(this.Product1['BOM'], this.Product2['BOM'])

		},

		async getBOM1(BOM_1, BOM_2) {
			//console.log()
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`mes/BOM1List?BOM1=${BOM_1}&BOM2=${BOM_2}`)
			console.log('getBOM1', res)
			_this.BOM1List = res
			// _this.optionsData.line = res
		},
		async getBOM2(BOM_1, BOM_2) {
			//console.log()
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`mes/BOM2List?BOM1=${BOM_1}&BOM2=${BOM_2}`)
			console.log('getBOM2', res)
			_this.BOM2List = res
			// _this.optionsData.line = res
		},
		async getSameBOM(BOM_1, BOM_2) {
			//console.log()
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`mes/SameBOMList?BOM1=${BOM_1}&BOM2=${BOM_2}`)
			console.log('getSameBOM', res)
			_this.SameBOMList = res
			// _this.optionsData.line = res
		},
		async getPOdata(BOM_1, BOM_2) {
			//console.log()
			var _this = this
			const {
				data: res1
			} = await _this.$http.get(`mes/GCPOData?BOM=${BOM_1}`)
			console.log('POData1', res1)
			this.POData1 = res1
			const {
				data: res2
			} = await _this.$http.get(`mes/GCPOData?BOM=${BOM_2}`)
			console.log('POData2', res2)
			this.POData2 = res2
			// _this.optionsData.line = res
		},

		// printTask() {
		// 	window.print();
		// },
		printTask() {
			this.getPOdata(this.Product1['BOM'], this.Product2['BOM']);
			var _this = this
			const timeoutId =
				setTimeout(() => {
					console.log('PO1', this.POData1[0])
					console.log('PO2', this.POData2[0])
					const printWindow = window.open('', '_blank');
					const Line = this.Line;
					const SKU1 = this.POData1[0]['SKU'];
					const SKU2 = this.POData2[0]['SKU'];
					const PO1 = this.POData1[0]['Order_NO'];
					const PO2 = this.POData2[0]['Order_NO'];
					const SKUDesc1 = this.POData1[0]['SKU_Desc'];
					const SKUDesc2 = this.POData2[0]['SKU_Desc'];
					const P_QTY1 = this.POData1[0]['QTY'];
					const P_QTY2 = this.POData2[0]['QTY'];
					const Shift1 = this.POData1[0]['Shift'];
					const Shift2 = this.POData2[0]['Shift'];
					const P_Time1 = moment(this.POData1[0]['forecast_date']).format('YYYY-MM-DD HH:mm:ss');
					const P_Time2 = moment(this.POData2[0]['forecast_date']).format('YYYY-MM-DD HH:mm:ss');
					const SKU1BOM = this.BOM1List;
					const SKU2BOM = this.BOM2List;
					const SameBOM = this.SameBOMList;
					// 动态生成表格行
					let row1 = '';
					SKU1BOM.forEach(item => {
						row1 += '<tr>';
						// 直接引用换型前料号和换型前原料描述的键名
						row1 += `<td>${item['换型前料号']}</td><td>${item['换型前原料描述']}</td>`;
						row1 += '</tr>';
					});
					let row2 = '';
					SKU2BOM.forEach(item => {
						row2 += '<tr>';
						// 直接引用换型前料号和换型前原料描述的键名
						row2 += `<td>${item['换型后料号']}</td><td>${item['换型后原料描述']}</td>`;
						row2 += '</tr>';
					});
					let row3 = '';
					SameBOM.forEach(item => {
						row3 += '<tr>';
						// 直接引用换型前料号和换型前原料描述的键名
						row3 += `<td>${item['共用原料号']}</td><td>${item['共用原料描述']}</td>`;
						row3 += '</tr>';
					});
					console.log('printdata', SKU1BOM, SKU2BOM, SameBOM)
					const printContent = `
      <html>
	  <style>
			body {
			display: flex;
			justify-content: left;
			align-items: left;
			}
			myTable {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid black;
            page-break-inside: avoid;
            break-inside: avoid-table;
        }
		</style>
		<body onload="window.print(); window.close();">
			<div style="margin-left: 50px;margin-top:15px">
				<!-- 第一行 -->
				<div>机台：${Line}</div>
				<!-- 第二行 -->
				<div style="display: flex; align-items: center;">
					<div style="display: flex; width: 500px;">
						<div>换型前SKU：${SKU1}</div>
						<div style="margin-left: 20px;">计划订单：${PO1}</div>
					</div>
					<!-- 添加一个空div作为间隔 -->
					<div style="margin-left: 20px;"></div>
					<div>换型后SKU：${SKU2}</div>
					<div style="margin-left: 20px;">计划订单：${PO2}</div>
				</div>
				<!-- 第三行 -->
				<div style="display: flex; align-items: center;">
					<div style="width:500px">换型前SKU描述：${SKUDesc1}</div>
					<div style="margin-left: 20px;">换型后SKU描述：${SKUDesc2}</div>
				</div>
				<!-- 第四行 -->
				<div style="display: flex; align-items: center;">
					<div style="display: flex; width: 500px;">
					<div>生产箱数：${P_QTY1}</div>
					<div style="margin-left: 20px;">班次：${Shift1}</div>
					</div>
					<!-- 添加一个空div作为间隔 -->
					<div style="margin-left: 20px;"></div>
					<div>生产箱数：${P_QTY2}</div>
					<div style="margin-left: 20px;">班次：${Shift2}</div>
				</div>
				<!-- 第五行 -->
				<div style="display: flex; align-items: center;">
					<div style="width:500px">计划开始：${P_Time1}</div>
					<div style="margin-left: 20px;">计划开始：${P_Time2}</div>
				</div>
			<!-- 第六行-表格 -->
				<div style="display: flex; align-items: center;">
				<table id="myTable"  style="margin-top:10px;width: 500px;border-color: black; border-style: solid;border-collapse: collapse" border="1">
					<tr>
						<td style="width:80px">换型前料号</td>
						<td>换型前原料描述</td>
					</tr>
				 ${row1}  <!-- 插入动态生成的表格行 -->
				</table>	
				<table id="myTable"  style="margin-left:20px;margin-top:10px;width: 500px;border-color: black; border-style: solid;border-collapse: collapse" border="1">
					<tr>
						<td style="width:80px">换型后料号</td>
						<td>换型后原料描述</td>
					</tr>
				 ${row2}  <!-- 插入动态生成的表格行 -->
				</table>						
				</div>

		</div>
</body>
</html>`;
					// printWindow.document.open();
					printWindow.document.write(printContent);
					printWindow.document.close();

				}, 1000)
		}
	}
}
</script>

<style lang="css" scoped>
.fontStyle {
	color: brown;
	font-size: 20px;
	font-weight: 600;
	background-color: bisque;
	text-align: center;
	width: 500px;
	height: 200px;
	margin-top: 20px;
	margin-left: 20px
}
</style>
