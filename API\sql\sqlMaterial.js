﻿const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.lineList = function (callBack) {
    sqlexec.prototype.
        _query("select distinct line from Line_Desc Order by Line", function (err, result) {
            // _query("select line from gradeChange_BaseData group by line order by line asc", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.OPList = function (Line, callBack) {
    console.log('参数', Line)
    var sqlStr = ''
    sqlStr = `select Employee_PYName from Employee_List where Line=${Line} Order by Employee_PYName`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.RM = function (RM_Code, callBack) {
    console.log('参数', RM_Code)
    var sqlStr = ''
    sqlStr = `select distinct RM_Desc,UOM,materialGroup,
                case when materialGroup in ('辅材/Indirect Material',
                '结构胶/Cons Adh.',
                '吸水粉/SAM',
                '皮筋胶/Elastic Adh.',
                '包装袋/Bag',
                '包装箱/Case',
                '皮筋/Elastic',
                '尿显胶/Indicator Adh.',
                '蓬松棉/REL NW',
                '滑动托板/Slipsheet') then 'N' Else
				Case when materialGroup is null then 'N' Else 'Y' end end NW
                from MES_Raw_Material as a
                left join Material_Waste_Data as b on a.RM_Code=b.materialCode where RM_Code=${RM_Code}`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.PalletLine = function (callBack) {
    sqlexec.prototype.
        _query("select distinct line from NJN_Pallet_Test ", function (err, result) {
            // _query("select line from gradeChange_BaseData group by line order by line asc", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.PalletSumLine = function (callBack) {
    sqlexec.prototype.
        _query("select distinct line from NJN_Pallet_ID_Test ", function (err, result) {
            // _query("select line from gradeChange_BaseData group by line order by line asc", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.PalletSKU = function (Line, callBack) {
    sqlexec.prototype.
        _query("select SKU_Code SKU from NJN_Pallet_Test Where Line=" + Line + "order by SKU_Code", function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.PalletSumSKU = function (Line, Date, Shift, callBack) {
    sqlexec.prototype.
        _query(`select distinct SKU_Code SKU from NJN_Pallet_ID_Test 
        Where Line=${Line} and Date=${Date} and Shift=${Shift} order by SKU_Code`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.PalletData = function (Line, SKU, callBack) {
    sqlexec.prototype.
        _query(`select Product_Code,SKU_Desc,Cut_Per_Bag,Bag_Per_Case,pallet,Case_Per_Pallet,Cut_Per_Case from
         NJN_Pallet_Test Where Line=${Line} and SKU_Code=${SKU} `, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.API_Execute_PalletID = function (pallet, callBack) {
    console.log('sqlData', pallet)
    var sqlStr = ''
    sqlStr = `exec SP_API_Execute_PalletID_NJN '${pallet['Line']}','${pallet['SKU_Code']}','${pallet['pallet_ID']}',
                '${pallet['pallet_QTY']}','${pallet['Entry_By']}'`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            var status = result.recordset[0]['状态']
            return callBack(status)
        })
}

sqlexec.prototype.PalletSum = function (Line, SKU, ShiftStart, callBack) {
    console.log('参数', Line, SKU, ShiftStart)
    var sqlStr = ''
    sqlStr = `select count(*) Pallet_Sum ,sum(Pallet_QTY) Case_Sum from NJN_Pallet_ID_Test Where Line=${Line} and SKU_Code=${SKU} and entry_on>=${ShiftStart}`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}

sqlexec.prototype.PalletSumData = function (Line, SKU, Date, Shfit, callBack) {
    sqlexec.prototype.
        _query(`select Line 产线,convert(varchar(100),Date,23) 日期,Shift 班次,SKU_Code SAP号,Pallet_ID 托盘码,
        Pallet_QTY 箱数,Entry_By 扫码人,convert(varchar(100),entry_on,20) 扫码时间 
        from NJN_Pallet_ID_Test 
        Where Line=${Line} and SKU_Code=${SKU} and Date=${Date} and Shift=${Shfit}`, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.ScanPalletQTY = function (Line, SKU, Date1, Shift, callBack) {
    console.log('sql参数', Line, SKU, Date1, Shift)
    var sqlStr = ''
    sqlStr = `select COUNT(Pallet_ID) PalletCount, sum(pallet_QTY) CaseQTY 
    from NJN_Pallet_ID_Test 
    Where Line=${Line} and SKU_Code=${SKU} and Date=${Date1} and Shift=${Shift}`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}


sqlexec.prototype.API_Execute_RM_Return_Insert = function (RM_Ruturn, callBack) {
    console.log('RM_Ruturn', RM_Ruturn)
    var sqlStr = ''
    sqlStr = `exec MES_RM_Return_Insert '${RM_Ruturn['Return_Type']}','${RM_Ruturn['Pallet_Lable']}','${RM_Ruturn['Line']}',
                '${RM_Ruturn['RM_Code']}','${RM_Ruturn['RM_Desc']}','${RM_Ruturn['RM_Lot_No']}','${RM_Ruturn['RM_QTY']}',
                '${RM_Ruturn['Operator']}','${RM_Ruturn['Comment']}','${RM_Ruturn['QR_Code']}','${RM_Ruturn['Entry_By']}',
                '${RM_Ruturn['Raw_Lot']}'`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            var status = result.recordset[0]['状态']
            return callBack(status)
        })
}

sqlexec.prototype.TierSize = function (Line, callBack) {
    console.log('sql参数', Line)
    var sqlStr = ''
    sqlStr = `select Line,Tier,Size,Country,SKU_Code from product_base
    where Line=${Line} `
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.MachineWaste = function (Line, Start, End, callBack) {
    console.log('MW参数', Line, Start, End)
    var sqlStr = ''
    sqlStr = `select distinct t.SKU_Code,t.Tier, t.Size, t.Country, t.Export,t.Product_Core ProductCore,t.Shift,
t.Crew, t.Level1, t.Level2, t.Level3,t.Fault_Code,
T_Waste-coalesce(QCO_Waste,0) MW_QTY,T_Counts-coalesce(QCO_counts,0) MW_Count
from (select a.Line,a.date,a.SKU_Code, b.Tier, b.Size, b.Country, b.export,b.product_core, a.Shift,
	c.Crew, a.Level1, a.Level2, a.Level3,a.Fault_Code,
	a.Amount T_Waste,a.counts T_Counts,d.Amount QCO_Waste,d.Counts QCO_Counts
	from (select distinct Line,Date,Shift,SKU_Code,Fault_Code,sum(Amount) Amount,sum(Counts) Counts,Level1,Level2,Level3 from Mes_Waste_Data
where Process_Order like '11%' and Line=${Line} and Date>=${Start} and Date<=${End}
group by Line,Date,Shift,SKU_Code,fault_code,Level1,Level2,Level3) AS a
left join Product_Base as b on a.SKU_Code = b.SKU_Code and a.Line = b.Line
left join (select date, Line, Shift, Crew from Mes_Crew_Data where Position = 'Leader Operator') c
on a.Date = c.Date and a.Line = c.Line and a.Shift = c.Shift
left join (select distinct Line,Date,Shift,SKU_Code,fault_code,Level1,Level2,Level3,QCO_Start_Time,QCO_End_Time,sum(Amount) Amount,sum(Counts) Counts from Mes_Waste_QCO
group by Line,Date,Shift,SKU_Code,fault_code,QCO_Start_Time,QCO_End_Time,Level1,Level2,Level3) as d on a.Line=d.Line and a.Date = d.Date and a.Shift = d.Shift and a.SKU_Code = d.SKU_Code and a.Level1 = d.Level1
    and a.Level2 = d.Level2 and a.Level3 = d.Level3
where  a.Line=${Line} and a.Date>=${Start} and a.Date<=${End} )as t`
    console.log('MachineWaste_SQL', sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.QCOWaste = function (Line, Start, End, callBack) {
    console.log('MW参数', Line, Start, End)
    var sqlStr = ''
    sqlStr = `select a.SKU_Code SKU_Code, b.Tier, b.Size,b.Country,b.Export,b.Product_Core ProductCore, a.Shift, c.Crew, 
'换型废品' Level1, '换型废品' Level2, '换型废品' Level3,'8888' Fault_Code,sum(a.Amount) MW_QTY, 1 as MW_Count
from Mes_Waste_QCO as a
left join Product_Base b on a.SKU_Code = b.SKU_Code and a.Line = b.Line
left join (select date, Line, Shift, Crew from Mes_Crew_Data where Position = 'Leader Operator') c 
on a.Date = c.Date and a.Line = c.Line and a.Shift = c.Shift
where a.Line=${Line} and a.Date>=${Start} and a.Date<=${End}
group by a.SKU_Code, b.Tier, b.Size,b.Country,b.Export,b.Product_Core, a.Shift, c.Crew
 `
    console.log('QCOWaste_SQL', sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.TotalCut_Waste = function (Line, Start, End, callBack) {
    console.log('TotalCut参数', Line, Start, End)
    var sqlStr = ''
    sqlStr = `select distinct a.SKU_Code,b.Tier,b.Size,b.Country,a.Shift,a.Crew,b.Export,b.Product_Core ProductCore,sum(Total_Cuts) Total_Cuts
    from Production_Data as a
    left join Product_Base as b on a.Line=b.Line and a.SKU_Code=b.SKU_Code
    where a.Line=${Line} and a.Date>=${Start} and a.Date<=${End} and  Machine_Status='Normal Production'
    group by a.SKU_Code,b.Tier,b.Size,b.Country,a.Shift,a.Crew,b.Export,b.Product_Core`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.TierSize = function (Line, callBack) {
    var sqlStr = ''
    sqlStr = `select distinct Tier,Size,Country,SKU_Code
    from Product_Base 
    Where Line=${Line}`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.OPForQA = function (Mill, Start, End, callBack) {
    var sqlStr = ''
    sqlStr = `select a.Mill 工厂,Fin_YearMonth 财务年月,a.Date 日期,a.Shift 班次,a.Line 机台,a.Crew 班组,
    b.OP 班组人员,a.SKU_Code SKU,c.Tier
    from (select convert(varchar(10),Date,23) Date,Line,Shift,Mill,Fin_YearMonth,Crew,SKU_Code from production_Data) as a
    left join (	select Date,Line,Shift,STRING_AGG(employee_Name,',') OP 
	from (select a1.date,a1.Line, Shift, a1.Employee_ID,b1.Employee_Name
			from (SELECT Date, Line, Shift, employee_id
                    FROM Mes_Crew_Data where Date between ${Start} and ${End}) as a1 
					left join Employee_List as b1 on a1.employee_id=b1.Employee_ID) as z
			group by Date,Line,Shift ) as b on a.Line=b.Line and a.Date=b.Date and a.Shift=b.Shift
    left join Product_Base as c on a.SKU_Code=c.SKU_Code
    where a.Mill=${Mill} and b.Date between ${Start} and ${End}
	order by a.Line,a.Date,a.shift`
    // console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            // console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.CLTierSize = function (Line, SKU, callBack) {
    console.log('CLTS参数', Line, SKU)
    var sqlStr = ''
    sqlStr = `select distinct Tier,Size,Country,SKU_Code
    from Product_Base 
    Where Line=${Line} and SKU_Code in (${SKU})`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}

sqlexec.prototype.DailyKPI = function (Line, Start, End, callBack) {
    console.log('DailyKPI参数', Line, Start, End)
    var sqlStr = ''
    sqlStr = `select a.Date,a.Shift,a.Crew,a.Line,a.Total_Cuts,a.Good_Cuts,a.Total_Cuts-a.Good_Cuts Waste,
    CONVERT(DECIMAL(18,4),(b.MW/a.Total_Cuts)*100) MWPec,b.MW,b.PakW
    from (select Convert(varchar(20),Date,23) Date,Shift,Crew,Line,sum(Total_Cuts) Total_Cuts,sum(Good_Cuts) Good_Cuts 
            from Production_Data 
            where date >= ${Start} and date <${End}
            and Line=${Line} and Machine_Status='Normal Production'
            group by Convert(varchar(20),Date,23),Shift,Crew,Line) as a
    left join(select Date,Shift,Line,sum(case when Fault_Code<>1000 then Amount else 0 end) MW,
                sum(Case when Level1='包装废品' then Amount else 0 end) PakW
                from Mes_Waste_Data 
                where date >= ${Start} and date <${End} and Line=${Line}
                and Process_Order like '11%'
                group by Date,Shift,Line) as b on a.Date=b.Date and a.Shift=b.Shift and a.Line=b.Line`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}
sqlexec.prototype.WasteTop3 = function (Line, Start, End, callBack) {
    console.log('WasteTop3参数', Line, Start, End)
    var sqlStr = ''
    sqlStr = `select * from (
        SELECT ROW_NUMBER() OVER (PARTITION BY Line, Date, Shift ORDER BY MW_QTY DESC) AS ID,
        Line,Date,Shift,Level3,MW_QTY FROM
        ( SELECT Line,Convert(varchar(20),Date,23) Date,Shift,Level3,SUM(Amount) AS MW_QTY
        FROM  Mes_Waste_Data 
        WHERE date >= ${Start} and date <${End} and Line=${Line}
        and Process_Order like '11%'
        GROUP BY Line,Date,Shift,Level3 )AS t )as t1
        where t1.ID in (1,2,3)
        order by Line,Date,Shift,MW_QTY desc`
    console.log(sqlStr)
    sqlexec.prototype.
        _query(sqlStr, function (err, result) {
            if (err) {
            }
            console.log(result.recordset)
            return callBack(result.recordset)
        })
}
module.exports = sqlexec;