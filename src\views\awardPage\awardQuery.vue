<template>
    <base-content>
        <div class="q-pa-md">
            <div class="q-gutter-md row items-start">
                <q-select outlined v-model="yearMonth" :options="yearMonthDropDown" label-slot clearable
                    style="width: 200px;" dense>
                    <template v-slot:label>年月
                        <em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必选</em>
                    </template>
                </q-select>
                <q-input outlined v-model="IDCode" label-slot clearable dense>
                    <template v-slot:label>身份证后6位
                        <em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
                    </template>
                </q-input>
                <q-btn style="width:150px" label="查询积分" color="purple" @click="queryAward()" />
            </div>

            <div>
                <q-card class="my-card text-white" style="background: radial-gradient(circle, #35a2ff 0%, #014a88 100%);margin-top: 10px;"
                    v-if="data">
                    <q-card-section>
                        <div class="text-h6">{{ yearMonth }}-{{ data[0]['Employee_Name']}}总得到的积分</div>
                    </q-card-section>
                    <q-separator dark inset />
                    <q-card-section>
                        <div class="text-h5">RMB:{{ totalPoints }}</div>
                    </q-card-section>
                </q-card>
            </div>
            <div class="q-gutter-md " v-if="data">
                <template v-for="item in data">
                    <div class="q-gutter-md row items-start">
                        <div style="font-weight: 600; width: 100px;" class="text-h6">
                            {{ item['Award_Type'] }}
                        </div>
                        <q-input style="width: 200px;font-weight: 600;" filled v-model="item['Points']" dense
                            Readonly />
                    </div>

                </template>

            </div>
        </div>

    </base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
export default {
    components: {
        BaseContent
    },
    data() {
        return {
            Year:2025,
            yearMonth: '',
            yearMonthDropDown: '',
            IDCode: '',
            data: false,
            totalPoints: 0
        }
    },
    mounted() {

        this.getFinanceYearMonth()
    },
    watch: {

    },

    methods: {
        async queryAward() {
            var _this = this
            _this.totalPoints = 0
            const {
                data: res
            } = await _this.$http.get(`approve/awardQuery_byPersonal?IDCode=${_this.IDCode}&yearMonth=${_this.yearMonth}`)
            console.log('res', res)
            _this.data = res
            if (res.length > 0) {
                let Points = 0
                for (let i = 0; i < res.length; i++) {
                    Points = Points + res[i]['Points']
                }
                _this.totalPoints = Points
            }
        },
        async getFinanceYearMonth() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`data_base/FinanceYearMonth?Year=` + _this.Year)
			console.log('FinaYear', res)
            this.yearMonthDropDown=res.map(obj => obj.Fin_YearMonth)
            console.log('YearMonthT',this.yearMonthDropDown)
		
		},

    }
}
</script>
