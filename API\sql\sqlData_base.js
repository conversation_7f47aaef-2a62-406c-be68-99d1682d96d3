const _Base = require('../config/dbbase')

function sqlexec() {
    _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.queryEmployee_list = function (callBack) {
    sqlexec.prototype.
        _query(`SELECT [Employee_Name]
        ,[Employee_ID]
        ,[Mill]
        ,[Department]
        ,[Category]
        ,[Line]
        ,[Current_Level]
        ,[Promote_Level]
        ,[Business_Title]
        ,[Position]
        ,[Crew]
        ,[Employee_PYName]
        ,[ID_Code]
        ,format([Hire_Date],'yyyy-MM-dd') Hire_Date
        ,[Manager]
        ,[Manager_ID]
        ,[Modified_By]
        ,[Modified_On]
		,[Resignation_Date]
        ,[id]
    FROM [NJDatacenter].[dbo].[Employee_List]`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.updateEmployee_list = function (rowData,id, callBack) {
    console.log(`update Employee_List set ${rowData} where id=${id}`)
    sqlexec.prototype.
        _query(`update Employee_List set ${rowData} where id=${id}`, function (err, result) {
            if (err) {
            }
            return callBack('更新成功')
        })
}
sqlexec.prototype.deleteEmployee_list = function (id, callBack) {
    //console.log(`update Employee_List set ${rowData} where id=${id}`)
    sqlexec.prototype.
        _query(`delete from Employee_List where id=${id}`, function (err, result) {
            if (err) {
            }
            return callBack('更新成功')
        })
}
sqlexec.prototype.insertEmployee_list = function (rowData, callBack) {
    console.log(`insert into Employee_List([Employee_Name]
        ,[Employee_ID]
        ,[Mill]
        ,[Department]
        ,[Category]
        ,[Line]
        ,[Current_Level]
        ,[Promote_Level]
        ,[Business_Title]
        ,[Position]
        ,[Crew]
        ,[Employee_PYName]
        ,[ID_Code]
        ,[Hire_Date]
        ,[Manager]
        ,[Manager_ID]
        ,[Modified_By]
        ,[Modified_On]
		,[Resignation_Date]
		) values(${rowData})`)
    sqlexec.prototype.
        _query(`insert into Employee_List([Employee_Name]
            ,[Employee_ID]
            ,[Mill]
            ,[Department]
            ,[Category]
            ,[Line]
            ,[Current_Level]
            ,[Promote_Level]
            ,[Business_Title]
            ,[Position]
            ,[Crew]
            ,[Employee_PYName]
            ,[ID_Code]
            ,[Hire_Date]
            ,[Manager]
            ,[Manager_ID]
            ,[Modified_By]
			,[Resignation_Date]
            ,[Modified_On]) values(${rowData})`, function (err, result) {
            if (err) {
            }
            return callBack('更新成功')
        })
}

sqlexec.prototype.accessTree = function (callBack) {
    //console.log(`update Employee_List set ${rowData} where id=${id}`)
    sqlexec.prototype.
        _query(`select * from Access_Group order by App_Name asc,Group_Name asc`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.accessByPersonal = function (employeeid,callBack) {
    //console.log(`update Employee_List set ${rowData} where id=${id}`)
    sqlexec.prototype.
        _query(`select Access_Group,App_Name  from Access_List where employee_id='${employeeid}'`, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.deleteAccessByPersonal = function (employeeid,callBack) {
    //console.log(`update Employee_List set ${rowData} where id=${id}`)
    sqlexec.prototype.
        _query(`delete from Access_List where employee_id='${employeeid}'`, function (err, result) {
            if (err) {
            }
            return callBack('已删除')
        })
}
sqlexec.prototype.InsertAccessByPersonal = function (employeeid,Access_Group,callBack) {
    //console.log(`update Employee_List set ${rowData} where id=${id}`)
    sqlexec.prototype.
        _query(`insert into Access_List(Employee_ID,Employee_Name,Access_Group,App_Name,Mill,Access_LinkID) 
                select Employee_ID,Employee_Name,'${Access_Group}' ,b.App_Name,'南京南厂',CONCAT_WS('-',a.Employee_ID,'${Access_Group}')  from Employee_List as a 
                left join Access_Group as b on b.Group_Name='${Access_Group}'
                where Employee_ID='${employeeid}'`, function (err, result) {
            if (err) {
            }
            return callBack('已添加')
        })
}
sqlexec.prototype.Web_Index_Router = function (callBack) {
    console.log(`select * from dbo.Web_Index_Router order by Sort asc  `)
    sqlexec.prototype.
        _query(`select * from dbo.Web_Index_Router order by Sort asc  `, function (err, result) {
            if (err) {
            }
            return callBack(result.recordset)
        })
}
sqlexec.prototype.YearMonthQuery = function (Year,callBack) {
    console.log('query',`select * from Finance_Date_Base  where Year='${Year}'`)
    sqlexec.prototype.
        _query(`select [Fin_YearMonth]
        , format([Start_Date],'yyyy-MM-dd')Start_Date
        ,format([End_Date],'yyyy-MM-dd')End_Date
        ,[Calendar_Time]
        ,[Year]
        ,[Month] from Finance_Date_Base  where Year='${Year}' order by Start_Date asc`, function (err, result) {

            if (err) {
            }
            return callBack(result.recordset)
        })
}


// Post给数据库的

sqlexec.prototype.InsertYearMonth = function (data,callBack) {
    console.log('query',`insert into Finance_Date_Base values('${data.Fin_YearMonth}','${data.Start_Date}','${data.End_Date}','${data.Calendar_Time}','${data.Year}','${data.Month}')`)
    sqlexec.prototype.
        _query(`insert into Finance_Date_Base values('${data.Fin_YearMonth}','${data.Start_Date}','${data.End_Date}','${data.Calendar_Time}','${data.Year}','${data.Month}')`, function (err, result) {

            if (err) {
            }
            return callBack('已添加')
        })
}


sqlexec.prototype.DeLYearMonth = function (data,callBack) {
    console.log('query',`Delete from Finance_Date_Base where Fin_YearMonth='${data.Fin_YearMonth}'`)
    sqlexec.prototype.
        _query(`Delete from Finance_Date_Base where Fin_YearMonth='${data.Fin_YearMonth}'`, function (err, result) {

            if (err) {
            }
            return callBack('已删除')
        })
}

module.exports = sqlexec;