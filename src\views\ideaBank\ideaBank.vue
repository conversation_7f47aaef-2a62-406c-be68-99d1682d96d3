<template>
    <base-content>
        <div class="q-pa-md">
            <q-dialog v-model="rejectDialog" persistent v-if="approveIdea">
                <q-card style="min-width: 350px">
                    <q-card-section>
                        <div class="text-h6">拒绝理由</div>
                    </q-card-section>

                    <q-card-section class="q-pt-none">
                        <q-input dense v-model="approveIdea.row['整改确认']" autofocus />
                    </q-card-section>

                    <q-card-actions align="right" class="text-primary">
                        <q-btn flat label="确定" @click="updateIdea('No')" />
                        <q-btn flat label="取消" v-close-popup />
                    </q-card-actions>
                </q-card>
            </q-dialog>


            <q-dialog v-model="showDetailDialog" transition-show="flip-down">
                <q-card style="width:1000px; max-width: 100vw;" v-if="showDetailData">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6 ">点子查询</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>
                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template v-for="(value, key) in showDetailData">
                                <div v-if="key == '发现的问题' || key == '建议解决方案' || key == '整改人提交方案' || key == '拒绝理由'"
                                    style="width: 100%;">
                                    <q-input type="textarea" filled v-model="showDetailData[key]" dense stack-label
                                        :label="key" readonly style="height: 100px;" />
                                </div>
                                <div v-else>
                                    <q-input filled v-model="showDetailData[key]" dense stack-label :label="key"
                                        readonly style="width:300px" />
                                </div>
                            </template>
                        </div>
                    </q-card-section>
                </q-card>
            </q-dialog>


            <q-dialog v-model="firtDialog" transition-show="flip-down">
                <q-card style="width:1000px; max-width: 100vw;" v-if="approveIdea">
                    <q-card-section class=" bg-teal text-white fit row  justify-between  ">
                        <div class="text-h6 ">点子审批</div>
                        <q-space />
                        <q-btn padding="xs" color="red" icon="clear" v-close-popup />
                    </q-card-section>

                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template v-if="approveIdea.row['流程状态'] == 'open'">
                                <q-select outlined v-model="approveIdea.row['点子子类型']"
                                    :options="approveIdeaDropdown['Idea_Sub_Type']" label-slot clearable
                                    style="width: 200px;" dense>
                                    <template v-slot:label>点子分类
                                        <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必选</em>
                                    </template>
                                </q-select>
                                <q-select outlined v-model="approveIdea.row['点子价值']"
                                    :options="approveIdeaDropdown['Sharing']" label-slot clearable style="width: 200px;"
                                    dense>
                                    <template v-slot:label>点子价值
                                        <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必选</em>
                                    </template>
                                </q-select>
                                <q-select outlined v-model="approveIdea.row['Assigned_By']"
                                    :options="approveIdeaDropdown['assignedBy']" label-slot clearable
                                    style="width: 200px;" dense>
                                    <template v-slot:label>建议改善责任人
                                    </template>
                                </q-select>
                                <q-input outlined v-model="approveIdea.row['拒绝理由']" label-slot clearable dense>
                                    <template v-slot:label>拒绝理由</template>
                                </q-input>
                            </template>

                            <template
                                v-if="approveIdea.row['流程状态'] == 'open' || approveIdea.row['流程状态'] == 'Final Check'">
                                <q-btn style="width:150px" :label="approveIdea.row['流程状态'] == 'open' ? '批准' : '确认整改完成'"
                                    color="purple" @click="updateIdea('Yes')" />
                                <q-btn style="width:150px" :label="approveIdea.row['流程状态'] == 'open' ? '拒绝' : '退回给整改人'"
                                    color="red" @click="updateIdea('No')" />
                            </template>
                        </div>
                    </q-card-section>
                    <q-card-section>
                        <div class="q-gutter-md row items-start">
                            <template v-for="(value, key) in approveIdea.row">
                                <div v-if="key == '发现的问题' || key == '建议解决方案' || key == '整改人提交方案' || key == '拒绝理由'"
                                    style="width: 100%;">
                                    <q-input type="textarea" filled v-model="approveIdea.row[key]" dense stack-label
                                        :label="key" readonly style="height: 100px;" />
                                </div>
                                <div v-else>
                                    <q-input filled v-model="approveIdea.row[key]" dense stack-label :label="key"
                                        readonly style="width:300px" />
                                </div>
                            </template>
                        </div>
                    </q-card-section>
                </q-card>
            </q-dialog>






            <div class="q-gutter-y-md">
                <q-card>
                    <q-tabs v-model="tab" inline-label outside-arrows mobile-arrows align="justify"
                        class="bg-primary text-white shadow-2">
                        <q-tab name="inputIdea" icon="mail" label="点子提交" />
                        <q-tab name="queryIdea" icon="mail" label="点子查询" />
                        <q-tab name="myApproval" icon="alarm" label="点子审批" />
                    </q-tabs>
                    <q-separator />

                    <q-tab-panels v-model="tab" animated>
                        <q-tab-panel name="inputIdea">
                            <div>
                                <div class="q-gutter-md row items-start">
                                    <q-select outlined v-model="inputData['department']"
                                        :options="dropDownData['department']" label-slot clearable
                                        style="width: 200px;">
                                        <template v-slot:label>部门/生产线
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['employee_Name']"
                                        :options="dropDownData['employee_Name']" label-slot clearable
                                        style="width: 200px;">
                                        <template v-slot:label>提交人
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['ideaType']"
                                        :options="dropDownData['ideaType']" label-slot clearable style="width: 200px;">
                                        <template v-slot:label>点子类型
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['assignedBy']"
                                        :options="filteredAssignedBy.length > 0 ? filteredAssignedBy : approveIdeaDropdown['assignedBy']"
                                        label-slot clearable style="width: 200px;" use-input @filter="filterAssignedByInput"
                                        hint="请使用键盘键入首字母快速检索">
                                        <template v-slot:label>建议改善责任人
                                        </template>
                                    </q-select>

                                    <q-select outlined v-model="inputData['selfComplete']"
                                        :options="['是', '否']" label-slot clearable
                                        style="width: 250px;">
                                        <template v-slot:label>是否自主完成
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填</em>
                                        </template>
                                    </q-select>

                                    <q-btn color="secondary" label="提交点子" size="lg" @click="submitIdea()"
                                        style="margin-left: 15px;" />
                                </div>
                                <!-- 问题描述区域 -->
                                <div style="margin-top: 20px; margin-bottom: 20px;">
                                    <q-input outlined v-model="inputData['ideaProblem']" label-slot clearable
                                        type="textarea" rows="4" style="width: 100%;">
                                        <template v-slot:label>请描述发现的问题
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填
                                            </em>
                                        </template>
                                    </q-input>
                                </div>

                                <!-- 建议方案区域 -->
                                <div style="margin-bottom: 20px;">
                                    <q-input outlined v-model="inputData['ideaSolution']" label-slot clearable
                                        type="textarea" rows="4" style="width: 100%;">
                                        <template v-slot:label>请写下您的建议
                                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                                style="font-size: 12px;">必填
                                            </em>
                                        </template>
                                    </q-input>
                                </div>
                            </div>
                        </q-tab-panel>

                        <q-tab-panel name="queryIdea">
                            <div class="q-gutter-md row items-start">
                                <q-select dense outlined v-model="myIdeaSelectedData['department']"
                                    :options="myIdeaDropDownData['department']" label-slot clearable
                                    style="width:200px;">
                                    <template v-slot:label>部门/生产线 <em
                                            class="q-px-sm bg-deep-orange text-white rounded-borders"
                                            style="font-size: 12px;">必选</em></template>
                                </q-select>

                                <q-select dense outlined v-model="myIdeaSelectedData['employee_Name']"
                                    :options="myIdeaDropDownData['employee_Name']" label-slot clearable
                                    style="width: 200px;">
                                    <template v-slot:label>提交人<em class="q-px-sm bg-primary text-white rounded-borders"
                                            style="font-size: 12px;">可选</em></template>
                                </q-select>
                            </div>
                            <div>
                                <q-table :data="myIdeaData || []" row-key="name" :columns="myIdeaColumn"
                                    :pagination.sync="myPagination" :filter="filter" dense
                                    class="my-sticky-virtscroll-table" virtual-scroll
                                    :virtual-scroll-sticky-size-start="48" :rows-per-page-options="[0]">
                                    <template v-slot:top="props">
                                        <div class="text-h6" style="font-weight: 600;">点子清单</div>
                                        <q-space></q-space>
                                        <q-input borderless dense debounce="300" v-model="filter" placeholder="Search"
                                            class="bg-indigo-1">
                                            <template v-slot:append>
                                                <q-icon name="search" />
                                            </template>
                                        </q-input>

                                        <q-btn flat round dense
                                            :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
                                            @click="props.toggleFullscreen" class="q-ml-md" label="全屏" />
                                    </template>

                                    <template v-slot:body="props">
                                        <q-tr :props="props" @click="toShow(props)">
                                            <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                                {{ col.value }}
                                            </q-td>
                                        </q-tr>
                                    </template>
                                </q-table>
                            </div>
                        </q-tab-panel>

                        <q-tab-panel name="myApproval">
                            <div>
                                <q-table :data="approveIdeaList || []" row-key="name" :columns="approvalColumn"
                                    :pagination.sync="myPagination" :filter="filter1" dense
                                    class="my-sticky-virtscroll-table" virtual-scroll
                                    :virtual-scroll-sticky-size-start="48" :rows-per-page-options="[0]">
                                    <template v-slot:top="props">
                                        <div class="text-h6" style="font-weight: 600;">我的审批</div>
                                        <q-space></q-space>
                                        <div style="font-weight: 600;">如需查看所有项目请点击-></div>
                                        <q-toggle v-model="adpotFilter" color="green"
                                            :label="adpotFilter ? '未审批项目' : '所有项目'"
                                            style="font-weight: 600;margin-right: 20px;" />
                                        <q-input borderless dense debounce="300" v-model="filter1" placeholder="Search"
                                            class="bg-indigo-1">
                                            <template v-slot:append>
                                                <q-icon name="search" />
                                            </template>
                                        </q-input>

                                        <q-btn flat round dense
                                            :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
                                            @click="props.toggleFullscreen" class="q-ml-md" label="全屏" />
                                    </template>

                                    <template v-slot:body="props">
                                        <q-tr :props="props" @click="toApprove(props)">
                                            <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                                {{ col.value }}
                                            </q-td>
                                        </q-tr>
                                    </template>
                                </q-table>
                            </div>
                        </q-tab-panel>
                    </q-tab-panels>
                </q-card>

            </div>
        </div>
    </base-content>
</template>
<script>
import BaseContent from '../../components/BaseContent/BaseContent'
export default {
    components: {
        BaseContent
    },
    data() {
        return {
            filter: '',
            filter1: '',
            tab: 'inputIdea',
            myPagination: { rowsPerPage: 0 },
            employee_List: [],
            dropDownData: {
                department: [],
                employee_Name: [],
                ideaType: [],
                assignedBy: []
            },

            inputData: {
                department: '',
                employee_Name: '',
                date: '',
                ideaType: '',
                ideaProblem: '',
                ideaSolution: '',
                assignedBy: '',
                selfComplete: ''
            },
            myIdeaData: [],
            myIdeaRawData: [],
            myIdeaDropDownData: {
                department: [],
                employee_Name: [],
            },
            myIdeaSelectedData: {
                department: [],
                employee_Name: [],
            },
            approveIdeaList: [],
            approveRawIdeaList: [],
            approveIdea: false,
            approveIdeaDropdown: {
                Sharing: ['铂金', '金', '银'],
                Idea_Sub_Type: [],
                assignedBy: []
            },
            filteredAssignedBy: [],
            //visibleColumns: ['Date','Employee_Name','Owner_Adopt','Owner_Reject_Cause','Idea_Problem','Idea_Solution'],
            myIdeaColumn: [
                { name: '提交日期', align: 'left', label: '提交日期', field: '提交日期', sortable: true, format: val => `${val.substring(0, 19).replace('T', ' ')}` },
                { name: '提交人', align: 'left', label: '提交人', field: '提交人', sortable: true },
                { name: '是否采纳', align: 'left', label: '是否采纳', field: '是否采纳', sortable: true },
                { name: '点子价值', align: 'left', label: '点子价值', field: '点子价值', sortable: true },
                { name: '是否自主完成', align: 'left', label: '是否自主完成', field: '是否自主完成', sortable: true },
                { name: '发现的问题', align: 'left', label: '发现的问题', field: '发现的问题', sortable: true, style: 'width: 100px' },
                { name: '建议解决方案', align: 'left', label: '解决方案', field: '建议解决方案', sortable: true }
            ],
            approvalColumn: [
                { name: '提交日期', align: 'left', label: '提交日期', field: '提交日期', sortable: true, format: val => `${val.substring(0, 19).replace('T', ' ')}` },
                { name: '提交人', align: 'left', label: '提交人', field: '提交人', sortable: true },
                { name: '是否采纳', align: 'left', label: '是否采纳', field: '是否采纳', sortable: true },
                { name: '点子价值', align: 'left', label: '点子价值', field: '点子价值', sortable: true },
                { name: '是否自主完成', align: 'left', label: '是否自主完成', field: '是否自主完成', sortable: true },
                { name: '发现的问题', align: 'left', label: '发现的问题', field: '发现的问题', sortable: true, style: 'width: 100px' },
                { name: '建议解决方案', align: 'left', label: '解决方案', field: '建议解决方案', sortable: true }
            ],
            adpotFilter: true,
            firtDialog: false,
            showDetailDialog: false,
            showDetailData: false,
            rejectDialog: false
        }
    },
    mounted() {
        console.log('this.$query', this.$route.query)
        this.getDepartment()
        this.getIdeaType()
        this.getEmployee()
        if (this.$route.query.function) {
            console.log('有参数')
            this.tab = this.$route.query.function
            this.myIdeaSelectedData['department'] = this.$route.query.department
        }
    },
    watch: {
        'inputData.department'(newValue, oldValue) {
            this.dropDownData['employee_Name'] = []
            this.inputData['employee_Name'] = ''
            this.filterEmployee('input')
        },
        'myIdeaSelectedData.department'(newValue, oldValue) {
            this.myIdeaDropDownData['employee_Name'] = []
            this.myIdeaSelectedData['employee_Name'] = ''
            this.filterEmployee('myIdea')
            this.getMyIdead()
            // this.filterApplicant()
        },
        'myIdeaSelectedData.employee_Name'(newValue, oldValue) {
            this.filterApplicant()
        },
        adpotFilter(newValue, oldValue) {
            this.filterMyApproval()
        },
        tab() {
            if (this.tab == 'myApproval') {
                this.getMyApprovalIdea()
            }
        }
    },

    methods: {
        async getDepartment() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getDepartment')
            console.log('getDepartment', res)
            _this.dropDownData['department'] = res
            _this.myIdeaDropDownData['department'] = res
        },
        async getEmployee() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getEmployee')
            console.log('getEmployee', res)
            _this.employee_List = res
            _this.filterAssginedBy()
        },
        async getIdeaType() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getIdeaType')
            console.log('getIdeaType', res)
            _this.dropDownData['ideaType'] = res.Idea_Type
            _this.approveIdeaDropdown.Idea_Sub_Type = res.Idea_subType
        },
        async getMyIdead() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/queryIdea_ByDepartment?department=' + _this.myIdeaSelectedData['department'])
            console.log('queryIdea_ByDepartment', res)
            _this.myIdeaData = res
            _this.myIdeaRawData = res
            // const keys = Object.keys(res);
            // keys.forEach(key => {
            //     employeeList[key] = null;
            // });
            _this.filterApplicant()

        },
        async getMyApprovalIdea() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get(`ideabank/myApprovalIdea?employeeID=${localStorage.getItem('account')}&processType=ideaApprove`)
            console.log('getMyApprovalIdead', res)
            _this.approveIdeaList = res
            _this.approveRawIdeaList = res
            // const keys = Object.keys(res);
            // keys.forEach(key => {
            //     employeeList[key] = null;
            // });
            _this.filterMyApproval()
        },
        filterEmployee(fun) {
            const _this = this
            console.log('department', _this.inputData['department'])
            for (let i = 0; i < _this.employee_List.length; i++) {
                if (fun == 'input') {
                    if (_this.employee_List[i]['line'] === _this.inputData['department']) {
                        _this.dropDownData['employee_Name'].push(_this.employee_List[i]['Employee_PYName'])
                    }
                } else {
                    if (_this.employee_List[i]['line'] === _this.myIdeaSelectedData['department']) {
                        _this.myIdeaDropDownData['employee_Name'].push({ label: _this.employee_List[i]['Employee_PYName'], value: _this.employee_List[i]['Employee_ID'] })
                    }
                }

            }
            console.log('selectedEmployee', _this.dropDownData['employee_Name'])
        },
        filterAssginedBy() {
            const _this = this
            for (let i = 0; i < _this.employee_List.length; i++) {
                if (_this.employee_List[i]['Business_Title'] !== '操作工') {
                    //console.log('employee_list', _this.employee_List[i])
                    _this.dropDownData['assignedBy'].push({ label: _this.employee_List[i]['Employee_PYName'] + '-' + _this.employee_List[i]['Position'], value: _this.employee_List[i]['Employee_ID'] })
                    _this.approveIdeaDropdown['assignedBy'].push({ label: _this.employee_List[i]['Employee_PYName'] + '-' + _this.employee_List[i]['Position'], value: _this.employee_List[i]['Employee_ID'] })
                }
            }
        },
        filterApplicant() {
            const _this = this
            console.log('_this.myIdeaSelectedData', _this.myIdeaSelectedData)
            const employee_name = _this.myIdeaSelectedData['employee_Name']
            console.log('employee_name', employee_name)
            _this.myIdeaData = []
            if (employee_name != null && employee_name != '') {
                for (let i = 0; i < _this.myIdeaRawData.length; i++) {
                    if (_this.myIdeaRawData[i]['提交人'] == employee_name['label']) {
                        _this.myIdeaData.push(_this.myIdeaRawData[i])
                    }
                }
            } else {
                _this.myIdeaData = _this.myIdeaRawData
            }
        },
        filterMyApproval() {
            const _this = this
            _this.approveIdeaList = []
            for (let i = 0; i < _this.approveRawIdeaList.length; i++) {
                if (_this.adpotFilter) {
                    if (_this.approveRawIdeaList[i]['流程状态'] == 'open' || _this.approveRawIdeaList[i]['流程状态'] == 'Final Check') {
                        _this.approveIdeaList.push(_this.approveRawIdeaList[i])
                    }
                } else {
                    _this.approveIdeaList = _this.approveRawIdeaList
                }
            }
        },
        submitIdea() {
            const _this = this
            console.log('_this.inputData', _this.inputData)
            let Manager_ID = ''
            for (let i = 0; i < _this.employee_List.length; i++) {
                if (_this.employee_List[i]['Employee_PYName'] == _this.inputData['employee_Name']) {
                    Manager_ID = _this.employee_List[i]['Manager_ID']
                    break
                }
            }
            _this.$http.post('ideabank/insertIdea', { 'data': _this.inputData, 'Manager_ID': Manager_ID }).then(function (response) {
                console.log('response', response)
                if (response.data === '已发送') {
                    _this.$q.notify({
                        type: 'positive',
                        message: `点子提交成功！系统第一时间发送邮件给您的主管`,
                        position: 'top'
                    })
                    _this.inputData['ideaType'] = ''
                    _this.inputData['ideaProblem'] = ''
                    _this.inputData['ideaSolution'] = ''
                    _this.inputData['assignedBy'] = ''
                    _this.inputData['selfComplete'] = ''
                }
            })
        },
        toShow(props) {

            const _this = this
            _this.showDetailData = props.row
            console.log(_this.showDetailData)
            _this.showDetailDialog = true
        },


        toApprove(props) {
            console.log('toApprove', props)
            const _this = this
            _this.approveIdea = props
            let assignedName = ''
            if (_this.approveIdea.row['Assigned_By'] != null) {
                if (!_this.approveIdea.row['Assigned_By'].hasOwnProperty('value')) {
                    for (let i = 0; i < _this.employee_List.length; i++) {
                        if (_this.employee_List[i]['Employee_ID'] == props.row['Assigned_By']) {
                            assignedName = _this.employee_List[i]['Employee_PYName'] + '-' + _this.employee_List[i]['Position']
                            break
                        }
                    }
                    _this.approveIdea.row['Assigned_By'] = { 'label': assignedName, 'value': props.row['Assigned_By'] }
                }
            }
            _this.firtDialog = true
        },

        async updateIdea(status) {
            const _this = this
            _this.rejectDialog=false
            if (_this.approveIdea.row['流程状态'] == 'open' || _this.approveIdea.row['流程状态'] == 'In progress') {
                if (status == 'No' && _this.approveIdea.row['拒绝理由'] == null) {
                    _this.$q.dialog({
                        title: '拒绝点子无法保存',
                        message: '拒绝点子需要写拒绝原因。请填写后继续点击拒绝'
                    })
                    return
                }
                if (status == 'Yes' && _this.approveIdea.row['点子价值'] == null) {
                    _this.$q.dialog({
                        title: '点子价值没有选择',
                        message: '由于点子价值没有选择无法保存。请选择后继续点击批准'
                    })
                    return
                }
                _this.approveIdea.row['是否采纳'] = status
            }
            if (_this.approveIdea.row['流程状态'] == 'Final Check') {
                if (status == 'No' && _this.approveIdea.row['整改确认'] == null) {
                    _this.rejectDialog=true
                    return
                }
                _this.approveIdea.row['整改确认'] = status == 'Yes' ? 'Yes' : _this.approveIdea.row['整改确认']
                _this.approveIdea.row['流程状态'] = status == 'Yes' ? 'Completed' : 'In progress'
            }
            console.log('approveIdea', _this.approveIdea)

            _this.$http.post('ideabank/updateIdea', { 'data': _this.approveIdea.row, 'approver': localStorage.getItem('username'), 'processType': 'approval' }).then(function (response) {
                console.log('response', response)
                if (response.data === "已发送"|| response.data === "已更新") {
                    _this.$q.notify({
                        type: 'positive',
                        message: response.data === "已发送"?`点子审批成功！系统第一时间发送邮件给分配人`:`已审批完成`,
                        position: 'top'
                    })
                    _this.getMyApprovalIdea()
                    _this.firtDialog = false
                }
            })

        },

    }
}


// function rejectReason() {
//     return new Promise((resolve, reject) => {
//         let status = ''
//         this.$q.dialog({
//             title: '退回理由',
//             message: '请输入退回理由',
//             prompt: {
//                 model: '',
//                 type: 'text' // optional
//             },
//             cancel: true,
//             persistent: true
//         }).onOk(data => {
//             // console.log('>>>> OK, received', data)
//             status = data
//         }).onCancel(() => {
//             // console.log('>>>> Cancel')
//             status = false
//         }).onDismiss(() => {
//             // console.log('I am triggered on both OK and Cancel')
//         })
//         resolve(status)
//     })
// }



</script>

<style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */
  height: 500px
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0
</style>
