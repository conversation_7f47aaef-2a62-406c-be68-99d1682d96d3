<template>
	<base-content>
		<q-dialog v-model="prompt" persistent>
			<q-card style="min-width:85%">
				<div class="q-pa-md row items-start q-gutter-md">
					<q-card class="my-card">
						<q-card-section>
							<q-toolbar>
								<q-toolbar-title style="font-weight: 600;color: cadetblue;">LSW日常检查</q-toolbar-title>
								<q-btn color="primary" @click="insert_LSW_checktheChecker_data">确认检查完成</q-btn>
							</q-toolbar>
							<div> {{ selected['checkContent'] }}</div>
						</q-card-section>
						<q-separator />
						<q-card-section>
							<q-table virtual-scroll style="height: 100%" :data="rows_checker" :columns="columns_checker"
								:pagination.sync="pagination_checker">
							</q-table>
						</q-card-section>

						<q-separator />
						<q-card-section>
							<q-toolbar>
								<q-toolbar-title style="font-weight: 600;color: cadetblue;">LSW检查问题清单</q-toolbar-title>
								<q-btn color="primary" @click="inputProblem">添加新发现问题项</q-btn>
							</q-toolbar>
						</q-card-section>

						<q-separator />
						<q-card-section>
							<q-table virtual-scroll style="height: 100%" :data="rows_problem" :columns="columns_problem"
								:pagination.sync="pagination">

								<template v-slot:body="props">
									<q-tr :props="props">

										<q-td v-for="col in props.cols" :key="col.name" :props="props"
											@click="ShowProblem(props.row)">
											{{ col.value }}
										</q-td>
									</q-tr>
								</template>
							</q-table>

						</q-card-section>


						<q-card-actions align="right">
							<q-btn color="red" label="退出" style="width: 20%;" @click="exitSelected" />
						</q-card-actions>
					</q-card>


				</div>
			</q-card>
		</q-dialog>

		<!-- 新增、显示问题清单 -->
		<q-dialog v-model="prompt_input_problem" persistent>
			<q-card class="q-pa-md" style="min-width:70%">
				<q-toolbar>
					<q-toolbar-title>问题提交</q-toolbar-title>
				</q-toolbar>
				<q-form @submit.prevent="submit">
					<q-input v-model="form.name" filled label="姓名"></q-input>
					<q-separator />
					<q-select v-model="form.dept" filled label="区域" :options="types"></q-select>
					<q-separator />
					<q-input v-model="form.problemContent" filled type="textarea" label="描述问题" />
					{{ form.dept }}
					<div>另行通知人员</div>
					<div class="q-gutter-sm">

						<template v-for="item in managerList">
							<q-checkbox style="width:200px" v-model="notifyManager" :val="item['Employee_ID']" :label="item['DisplayName']"
								color="teal" />
						</template>
						<!-- <q-btn  @click="showValue">test</q-btn> -->
					</div>



					<q-separator />

					<q-uploader v-if="showProblemItem == false" :factory="factoryFn" label="照片&文件&视频上传" batch multiple
						auto-upload style="width: 100%" @uploaded="handleUploadSuccess" />
					<div v-if="showProblemItem">
						<div>
							支持文件
						</div>
						<q-separator />
						<div class="q-pa-md q-gutter-sm">
							<template v-for="item in form.fileNameArray">
								<q-btn color="secondary" :label="item" @click="toFileDetail(item)" />
							</template>
						</div>
					</div>
				</q-form>
				<q-card-actions align="center">
					<q-btn v-if="showProblemItem == false" color="primary" style="width: 40%;" @click="submit">提交</q-btn>
					<q-btn color="red" style="width: 40%;" v-close-popup>关闭</q-btn>
				</q-card-actions>
			</q-card>
		</q-dialog>
		<!-- --- -->

		<!-- 新增、显示问题清单 -->
		<q-dialog v-model="prompt_input_lsw_base" persistent>
			<q-card class="q-pa-md" style="min-width:70%">
				<q-toolbar>
					<q-toolbar-title>LSW检查清单维护</q-toolbar-title>
				</q-toolbar>
				<q-form @submit.prevent="submit_lsw_base">
					<q-input v-model="lsw_base.mill" filled label="工厂"></q-input>
					<q-separator />
					<q-input v-model="lsw_base.Role" filled label="职位"></q-input>
					<q-separator />
					<q-input v-model="lsw_base.account" filled label="员工号"></q-input>
					<q-separator />
					<q-input v-model="lsw_base.name" filled label="姓名"></q-input>
					<q-separator />
					<q-input v-model="lsw_base.LSW_category" filled label="LSW_分类"></q-input>
					<q-separator />
					<q-input v-model="lsw_base.checkContent" filled label="检查内容"></q-input>
					<q-separator />
					<q-input v-model="lsw_base.cycle" filled label="周期"></q-input>
					<q-separator />
					<q-input v-model="lsw_base.baseTime" filled label="花费时间"></q-input>
					<q-separator />
					<q-input v-model="lsw_base.checkListFileArray" filled label="检查清单"></q-input>
					<q-separator />
				</q-form>
				<q-card-actions align="center">
					<q-btn color="primary" style="width: 40%;" @click="submit_lsw_base">提交</q-btn>
					<q-btn color="red" style="width: 40%;" v-close-popup>关闭</q-btn>
				</q-card-actions>
			</q-card>
		</q-dialog>
		<!-- --- -->

		<q-dialog v-model="alert">
			<q-card>
				<q-card-section>
					<q-avatar icon="done_outline" color="primary" text-color="white" /> <span class="text-h6">提交成功!</span>
				</q-card-section>

				<q-card-actions align="right">
					<q-btn flat label="OK" color="primary" v-close-popup />
				</q-card-actions>
			</q-card>
		</q-dialog>

		<!-- 		<q-dialog v-model="alert_error">
			<q-card>
				<q-card-section>
					<q-avatar icon="access_alarm" color="deep-orange" text-color="white" /> <span
						class="text-h6">错误!</span>
				</q-card-section>
				<q-separator />
				<q-card-section class="q-pt-none">请选择一个区域，否则无法保存</q-card-section>
				<q-card-actions align="right">
					<q-btn flat label="OK" color="primary" v-close-popup />
				</q-card-actions>
			</q-card>
		</q-dialog> -->


		<q-dialog v-model="prompt_uploadFile" persistent>
			<q-card style="width: 300px">
				<q-card-section>
					<div class="text-h6">上传文件中.........</div>
				</q-card-section>

				<q-card-section class="q-pt-none">
					文件上传完毕后自动消失
				</q-card-section>
			</q-card>
		</q-dialog>




		<div class="q-pa-md">

			<q-tabs v-model="tab" dense class="text-grey" active-color="primary" indicator-color="primary" align="justify"
				narrow-indicator>
				<q-tab name="LSW检查" label="LSW检查" />
				<q-tab name="我发现的问题" label="我发现的问题" />
				<q-tab name="我的LSW清单" label="我的LSW清单" />
			</q-tabs>
			<q-tab-panels v-model="tab" animated>
				<q-tab-panel name="LSW检查">
					<div class="q-pa-md" v-if="rows">
						<q-table style="height: 100%" title="LSW日常检查" :data="rows" :columns="columns" row-key="LSW_Base_ID"
							:pagination.sync="pagination_main" hide-pagination>
							<template v-slot:body="props">
								<q-tr :props="props">

									<q-td v-for="col in props.cols" :key="col.name" :props="props"
										@click="gotoDetail(props.row)">
										{{ col.value }}
									</q-td>
								</q-tr>
							</template>
						</q-table>
					</div>
				</q-tab-panel>
				<q-tab-panel name="我发现的问题">

					<div class="q-pa-md" v-if="rows_myproblem_list">
						<q-table style="height: 100%" title="我发现的问题" :data="rows_myproblem_list"
							:columns="columns_myproblem_list" row-key="LSW_Base_ID" :pagination.sync="pagination_myproblem">
							<!-- 
							<template v-slot:top-right>
								<q-input borderless dense debounce="300" v-model="filter" placeholder="Search">
									<template v-slot:append>
										<q-icon name="search" />
									</template>
								</q-input>
							</template> -->

							<template v-slot:body="props">
								<q-tr :props="props">
									<!-- <q-td auto-width>
											<q-btn size="sm" color="accent"  :disable ="false"
												@click="props.expand = !props.expand"
												:icon="props.expand ? 'remove' : 'add'" />
										</q-td> -->
									<q-td v-for="col in props.cols" :key="col.name" :props="props">
										<template>
											{{ col.name == '日期' ? col.value.substring(0, 10) : col.value }}
										</template>
										<!-- <div></div>  -->
									</q-td>
								</q-tr>
							</template>
						</q-table>
					</div>
				</q-tab-panel>

				<q-tab-panel name="我的LSW清单">

					<div class="q-pa-md" v-if="rows_my_LSW_checktheChecker_base_list">
						<q-table style="height: 100%" title="我的LSW清单" :data="rows_my_LSW_checktheChecker_base_list"
							:columns="columns_my_LSW_checktheChecker_base_list" row-key="LSW_Base_ID"
							:pagination.sync="pagination_main">
							<template v-slot:top>
								<q-btn color="primary" label="添加新工作内容" @click="gotoAddLSW" />
							</template>
							<template v-slot:header="props">
								<q-tr :props="props">
									<q-th auto-width />
									<!-- <q-th auto-width /> -->
									<q-th v-for="col in props.cols" :key="col.name" :props="props">
										{{ col.label }}
									</q-th>
								</q-tr>
							</template>
							<template v-slot:body="props">
								<q-tr :props="props">
									<q-td>
										<q-btn color="primary" label="修改" @click="gotoEditLSW(props.row)" />
										<q-btn color="red" label="删除" @click="del_lsw_base(props.row)" />
									</q-td>
									<!-- 									<q-td >
										
									</q-td> -->
									<q-td v-for="col in props.cols" :key="col.name" :props="props">
										{{ col.value }}
									</q-td>
								</q-tr>
							</template>
						</q-table>
					</div>
				</q-tab-panel>
			</q-tab-panels>




		</div>
	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import qs from 'qs'
export default {
	name: 'Home',
	components: {
		BaseContent
	},
	data() {
		return {
			tab: 'LSW检查',
			username: '',
			account: '',
			mill: '',
			Position: '',
			showProblemItem: false,
			columns: [
				{
					name: 'Mill',
					align: 'left',
					label: '工厂',
					field: 'Mill',
					sortable: true
				},
				{
					name: 'LSW_category',
					align: 'left',
					label: 'LSW_分类',
					field: 'LSW_category',
					sortable: true
				}, {
					name: 'Check_Content',
					align: 'left',
					label: '检查内容',
					field: 'Check_Content',
					headerStyle: 'width: 50px',
					sortable: true
				}, {
					name: 'Cycle',
					align: 'left',
					label: '周期',
					field: 'Cycle',
					sortable: true
				}, {
					name: 'LastCheckDate',
					align: 'left',
					label: '最新确认检查日期',
					field: 'LastCheckDate',
					sortable: true
				},
				{
					name: 'Base_Time',
					align: 'left',
					label: '花费时间',
					field: 'Base_Time',
					sortable: true
				}, {
					name: 'Check_List_File_Array',
					align: 'left',
					label: '检查清单',
					field: 'Check_List_File_Array',
					sortable: true
				}, {
					name: 'LSW_Problem_Num',
					align: 'left',
					label: '问题数量',
					field: 'LSW_Problem_Num',
					sortable: true
				}
			],
			rows: false,
			rows_myproblem_list: false,
			rows_my_LSW_checktheChecker_base_list: false,
			pagination_main: {
				descending: false,
				page: 1,
				rowsPerPage: 100
			},
			pagination: {
				descending: false,
				page: 1,
				rowsPerPage: 5
			},
			pagination_checker: {
				descending: false,
				page: 1,
				rowsPerPage: 3
			},
			pagination_myproblem: {
				descending: false,
				page: 1,
				rowsPerPage: 30
			},
			prompt: false,
			prompt_input_problem: false,
			prompt_uploadFile: false,
			prompt_input_lsw_base: false,
			alert_error: false,
			alert: false,
			selected: false,
			columns_checker: [{
				name: 'Check_Date',
				align: 'left',
				label: '检查日期',
				field: 'Check_Date',
				format: (val, row) => `${val}`,
				sortable: true
			},
			{
				name: 'Check_Name',
				align: 'left',
				label: '检查人',
				field: 'Check_Name',
				sortable: true
			},
			],
			rows_checker: [],
			columns_problem: [{
				name: '日期',
				align: 'left',
				label: '日期',
				field: 'Date',
				sortable: true
			}, {
				name: '部门',
				align: 'left',
				label: '部门',
				field: 'Problem_Area',

				sortable: true
			}, {
				name: '提交人',
				align: 'left',
				label: '提交人',
				field: 'Employee_Name',

				sortable: true
			}, {
				name: '问题描述',
				align: 'left',
				label: '问题描述',
				field: 'Problem_Desc',

				sortable: true
			}],
			rows_problem: [],
			columns_myproblem_list: [

				{
					name: '工厂',
					align: 'left',
					label: '工厂',
					field: 'Mill',
					sortable: true
				}, {
					name: '日期',
					align: 'left',
					label: '日期',
					field: 'Date',
					sortable: true
				}, {
					name: '区域',
					align: 'left',
					label: '区域',
					field: 'Problem_Area',

					sortable: true
				}, {
					name: '问题分类',
					align: 'left',
					label: '问题分类',
					field: 'Problem_Category',
					sortable: true
				}, {
					name: '问题描述',
					align: 'left',
					label: '问题描述',
					field: 'Problem_Desc',
					sortable: true
				}, {
					name: '根本原因',
					align: 'left',
					label: '根本原因',
					field: 'Problem_Root_Case',
					sortable: true
				}, {
					name: '改善方案',
					align: 'left',
					label: '改善方案',
					field: 'Problem_Solution',
					sortable: true
				}, {
					name: '负责人职位',
					align: 'left',
					label: '负责人职位',
					field: 'Assigned_Department',
					sortable: true
				}, {
					name: '负责人',
					align: 'left',
					label: '负责人',
					field: 'Assigned_By',
					sortable: true
				}, {
					name: '优先级',
					align: 'left',
					label: '优先级',
					field: 'Priority',
					sortable: true
				}, {
					name: '完成日期',
					align: 'left',
					label: '完成日期',
					field: 'Action_Complete_Date',
					sortable: true
				}, {
					name: '完成状态',
					align: 'left',
					label: '完成状态',
					field: 'Process_Status',
					sortable: true
				}, {
					name: 'ID',
					align: 'left',
					label: 'ID',
					field: 'id',
					sortable: true
				}
			],
			types: [],
			columns_my_LSW_checktheChecker_base_list: [
				{
					name: 'Mill',
					align: 'left',
					label: '工厂',
					field: 'Mill',
					sortable: true
				},
				{
					name: 'LSW_category',
					align: 'left',
					label: 'LSW_分类',
					field: 'LSW_category',
					sortable: true
				}, {
					name: 'Check_Content',
					align: 'left',
					label: '检查内容',
					field: 'Check_Content',
					headerStyle: 'width: 50px',
					sortable: true
				}, {
					name: 'Cycle',
					align: 'left',
					label: '周期',
					field: 'Cycle',
					sortable: true
				}, {
					name: 'Base_Time',
					align: 'left',
					label: '花费时间',
					field: 'Base_Time',
					sortable: true
				}, {
					name: 'Check_List_File_Array',
					align: 'left',
					label: '检查清单',
					field: 'Check_List_File_Array',
					sortable: true
				}
			],
			form: {
				mill: '',
				name: '',
				account: '',
				dept: '',
				problemContent: '',
				LSW_Base_ID: '',
				fileNameArray: '',
				fileURLArray: {}
			},
			lsw_base: {
				mill: '',
				name: '',
				account: '',
				role: '',
				LSW_category: '',
				checkContent: '',
				cycle: '',
				baseTime: '',
				checkListFileArray: '',
				LSW_Base_ID: ''
			},
			lsw_base_op: '',
			managerList: ['生产经理-张绪存', '生产经理-蔡振', '工程维修部经理-张立', '电气经理-张亮', '机械经理-彭银双', '质量经理-李晓婷', 'EHS经理-赵宝翠', '持续改善经理-赵玉江'],
			notifyManager: []


		}
	},
	mounted() {
		this.username = localStorage.getItem('username')
		this.account = localStorage.getItem('account')
		this.mill = localStorage.getItem('mill')
		this.Position = localStorage.getItem('Position')
		this.getLSWData()
		this.getSafeIncidentDept(localStorage.getItem('mill'))
		this.getManagerList(localStorage.getItem('mill'))
	},
	watch: { // 简单监听
		tab(newVal, oldVal) {
			var _this = this
			if (newVal == 'LSW检查') {
				_this.getLSWData()
			} else if (newVal == '我发现的问题') {
				_this.getLSW_Problem_Data_List()
			} else if (newVal == '我的LSW清单') {
				_this.getmy_LSW_checktheChecker_base_list()
			}

			console.log(newVal, oldVal)
		},
	},
	methods: {
		showValue() {
			console.log(this.notifyManager)
		},


		factoryFn(files) {
			var _this = this
			_this.prompt_uploadFile = true
			return {
				url: 'http://172.21.65.192:3001/upload?system=lsw',
				method: 'POST'
			}
		},
		async getSafeIncidentDept(mill) {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('lsw/getSafeIncidentDept?mill=' + mill)
			console.log('getSafeIncidentDept', res)
			_this.types = res
			// for (var i = 0; i < res.length; i++) {
			// 	_this.types.push(res[i]['Area'])
			// }
		},
		async getManagerList(mill) {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('lsw/getManagerList?mill=' + mill)
			console.log('getManagerList', res)
			_this.managerList = res
		},


		async getLSWData() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('lsw/LSW_checktheChecker_base?account=' + _this.account)
			console.log('getLSWData', res)
			_this.rows = res
		},
		async getmy_LSW_checktheChecker_base_list() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('lsw/my_LSW_checktheChecker_base_list?account=' + _this.account)
			console.log('my_LSW_checktheChecker_base_list', res)
			_this.rows_my_LSW_checktheChecker_base_list = res
		},

		async getLSW_Problem_Data_List() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('lsw/getLSW_Problem_Data_List?account=' + _this.account)
			console.log('getLSW_Problem_Data_List', res)
			_this.rows_myproblem_list = res
		},

		async getCheckerList(LSW_Base_ID) {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('lsw/LSW_checktheChecker_data?LSW_Base_ID=' + LSW_Base_ID)
			console.log(res)
			_this.rows_checker = res

		},
		async getProblemList(LSW_Base_ID) {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('lsw/LSW_Problem_Data?LSW_Base_ID=' + LSW_Base_ID)
			console.log('LSW_Problem_Data', res)
			_this.rows_problem = res
		},


		async insert_LSW_checktheChecker_data() {
			var _this = this
			var insertData = {
				'username': _this.username,
				'LSW_Base_ID': _this.selected.LSW_Base_ID
			}
			_this.$http.post('lsw/insert_LSW_checktheChecker_data', qs.stringify(insertData)).then(function (
				response) {
				console.log(response)
				if (response.data == 'insert completed') {
					_this.$q.notify({
						message: '确认成功',
						icon: 'done_outline',
						position: 'top',
						type: 'positive',
					})
					_this.getCheckerList(_this.selected.LSW_Base_ID)
				}

			})

		},

		//新服务器使用
		handleUploadSuccess(response) {
			console.log(response)
			var _this = this
			var responseFileName = response['files'][0]['xhr']['responseText']
			var responseURL = response['files'][0]['xhr']['responseText']
			responseFileName = responseFileName.replace("http://172.21.65.192:3003/lsw/", "")
			_this.form.fileNameArray.push(responseFileName)
			_this.form.fileURLArray[responseFileName] = responseURL
			_this.prompt_uploadFile = false

		},

		inputProblem() {
			var _this = this
			_this.form.dept = []
			_this.form.problemContent = ''
			_this.form.fileNameArray = []
			_this.form.fileURLArray = {}
			_this.form.LSW_Base_ID = ''
			_this.form.LSW_Base_ID = _this.selected.LSW_Base_ID
			_this.form.name = _this.username
			_this.notifyManager = []
			_this.showProblemItem = false
			_this.getManagerList(_this.selected.Mill)
			_this.getSafeIncidentDept(_this.selected.Mill)
			_this.prompt_input_problem = true
		},

		ShowProblem(e) {
			console.log('ShowProblem', e)
			var _this = this
			_this.form.dept = e.Problem_Area
			_this.form.problemContent = e.Problem_Desc
			_this.form.fileNameArray = e.Problem_File_Name_Array.split(",")
			_this.form.fileURLArray = JSON.parse(e.Problem_File_URL_Array)
			_this.form.name = e.Employee_Name
			_this.showProblemItem = true
			_this.getManagerList(_this.selected.Mill)
			_this.getSafeIncidentDept(_this.selected.Mill)
			_this.prompt_input_problem = true
			console.log(_this.form)
		},

		toFileDetail(fileName) {
			var _this = this
			console.log(_this.form.fileURLArray[fileName])
			window.open(_this.form.fileURLArray[fileName], '_blank')
		},


		gotoDetail(e) {
			console.log('gotoDetail', e)
			var _this = this
			_this.selected = false
			_this.selected = e
			console.log('_this.selected', _this.selected)
			console.log('_this.selected.LSW_Base_ID', _this.selected.LSW_Base_ID)
			_this.getCheckerList(e.LSW_Base_ID)
			_this.getProblemList(e.LSW_Base_ID)
			_this.prompt = true

		},
		submit() {
			// 提交申请单

			var _this = this
			if (_this.form.dept.length == 0) {
				_this.$q.dialog({
					title: '错误',
					message: '区域必填。点击确认后继续填写'
				}).onOk(() => {
					// console.log('OK')

				}).onCancel(() => {
					// console.log('Cancel')
				}).onDismiss(() => {
					// console.log('I am triggered on both OK and Cancel')
				})
				return
			}
			console.log(_this.form)
			_this.form.fileURLArray = JSON.stringify(_this.form.fileURLArray)
			_this.form.fileNameArray = _this.form.fileNameArray.join(",")
			_this.form.mill = _this.selected.Mill
			_this.form.account = _this.account
			_this.form.NotificationMail = ''
			// _this.form.ownerMail=
			//新增通知人员
			if (_this.notifyManager.length > 0) {
				for (var s = 0; s < _this.notifyManager.length; s++) {
					_this.form.NotificationMail += _this.notifyManager[s] + "@kcc.com;"
				}
			}
			_this.$http.post('lsw/insert_LSW_Problem_Data', qs.stringify(_this.form)).then(function (response) {
				console.log(response)
				_this.$q.dialog({
					title: '保存成功',
					message: '您的问题已提交，系统会第一时间已邮件方式发给区域负责人'
				}).onOk(() => {
					// console.log('OK')
					_this.prompt_input_problem = false
					_this.getProblemList(_this.form.LSW_Base_ID)
				}).onCancel(() => {
					// console.log('Cancel')
				}).onDismiss(() => {
					// console.log('I am triggered on both OK and Cancel')
				})

			})
		},
		exitSelected() {
			this.prompt = false
			this.getLSWData()
		},
		gotoEditLSW(row) {
			var _this = this
			_this.lsw_base_op = 'update'
			console.log(row)
			_this.lsw_base.LSW_Base_ID = row.LSW_Base_ID
			_this.lsw_base.mill = row.Mill
			_this.lsw_base.LSW_category = row.LSW_category
			_this.lsw_base.Role = row.Role
			_this.lsw_base.account = row.Employee_ID
			_this.lsw_base.baseTime = row.Base_Time
			_this.lsw_base.checkContent = row.Check_Content
			_this.lsw_base.checkListFileArray = row.Check_List_File_Array
			_this.lsw_base.cycle = row.Cycle
			_this.lsw_base.name = row.Employee_Name
			_this.prompt_input_lsw_base = true
		},
		gotoAddLSW() {
			var _this = this
			_this.lsw_base_op = 'insert'
			_this.lsw_base.LSW_Base_ID = ""
			_this.lsw_base.mill = _this.mill
			_this.lsw_base.LSW_category = ''
			_this.lsw_base.Role = _this.Position
			_this.lsw_base.account = _this.account
			_this.lsw_base.baseTime = ''
			_this.lsw_base.checkContent = ''
			_this.lsw_base.checkListFileArray = ''
			_this.lsw_base.cycle = ''
			_this.lsw_base.name = _this.username
			_this.prompt_input_lsw_base = true
		},
		submit_lsw_base() {
			// 提交申请单
			var _this = this
			console.log(_this.form)
			if (_this.lsw_base_op == 'update') {
				_this.$http.post('lsw/Update_LSW_check_base', qs.stringify(_this.lsw_base)).then(function (response) {
					console.log(response)
					_this.alert = true
					_this.prompt_input_lsw_base = false
				})
			} else if (_this.lsw_base_op == 'insert') {
				_this.$http.post('lsw/insert_LSW_check_base', qs.stringify(_this.lsw_base)).then(function (response) {
					console.log(response)
					_this.alert = true
					_this.prompt_input_lsw_base = false
				})
			}
			_this.getmy_LSW_checktheChecker_base_list()
			console.log("update _this.getmy_LSW_checktheChecker_base_list()")

		},

		del_lsw_base(row) {
			var _this = this
			var LSW_ID = row.LSW_Base_ID
			_this.$http.post('lsw/delete_LSW_check_base', qs.stringify({ 'LSW_Base_ID': LSW_ID })).then(function (response) {
				console.log(response)
				_this.getmy_LSW_checktheChecker_base_list()
			})
		}


	}
}
</script>
<style lang="css" scoped>
.my-card {
	width: 100%;
	min-height: 390px;
	height: 100%;
	/*max-width: 350px;*/
}
</style>
