<template>
	<div>
		<div class="text-center text-h6 text-primary">
			积分明细
		</div>
		<div class="row  justify-center  col-md-4" style="margin-top:20px" v-if="performanceData">
			<q-card class="col-2 col-md-2 my-card">
				<q-card-section>
					{{ performanceData[0]['Performance_Type'] }}审批: <span class="text-h6">{{ performanceData[0]['Result']
					}}</span>
				</q-card-section>
			</q-card>
			<q-card class="col-2 col-md-2 offset-md-1 my-card">
				<q-card-section>
					{{ performanceData[1]['Performance_Type'] }}审批: <span class="text-h6">{{ performanceData[1]['Result']
					}}</span>
				</q-card-section>
			</q-card>
			<q-card class="col-2 col-md-2 offset-md-1 my-card">
				<q-card-section>
					{{ performanceData[2]['Performance_Type'] }}审批: <span class="text-h6">{{ performanceData[2]['Result']
					}}</span>
				</q-card-section>
			</q-card>
		</div>

		<div class="q-pa-md">
			<div>
				<q-table title="机台汇总" :data="summaryData" :columns="summaryColumn" row-key="name"
					:pagination.sync="myPagination" dense  wrap-cells />
			</div>
		</div>

		<div class="q-pa-md" v-if="userData.length > 0">
			<q-table title="个人明细" :data="userData" :columns="userDataColumn" row-key="name"
				:pagination.sync="myPagination" dense/>
		</div>

	</div>
</template>

<script>
export default {
	props: {
		rows: {
			type: Array,
		}
	},
	mounted(props) {
		const _this = this
		console.log('Detail接收到的数据', _this.rows)
		_this.$nextTick(() => {
			// _this.summaryColumn = { '姓名': '', '职位': '', '个人总计': '' }
			// let singleColumn = [
			// 	{ name: '姓名', label: '姓名', field: '姓名', align: 'left', sortable: true },
			// 	{ name: '职位', label: '职位', field: '职位', align: 'left', sortable: true },
			// 	{ name: '个人总计', label: '个人总计', field: '个人总计', align: 'left', sortable: true }
			// ]
			// for (let key in _this.rows) {
			// 	if (key != 'Department' && key != 'Approval_Level' && key != 'Approval_Status' && key != 'Fin_YearMonth' && key != 'Line') {
			// 		_this.summaryTotal += _this.rows[key]
			// 		_this.summaryColumn[key]
			// 		singleColumn.push(
			// 			{ name: key, label: key, field: key, align: 'left', sortable: true }
			// 		)
			// 	}
			// }
			_this.Award_Performance_Summary_Monthly(_this.rows.Fin_YearMonth, _this.rows.Line)
			this.getAward(_this.rows.Fin_YearMonth, _this.rows.Line, _this.rows.Department)

		})
	},
	data() {
		return {
			performanceData: false,
			summaryData: [],
			summaryTotal: 0,
			userData: [],
			userDataColumn: [],
			summaryColumn: [],
			myPagination: { rowsPerPage: 0 },
		}
	},

	methods: {
		async Award_Performance_Summary_Monthly(yearMonth, line) {
			const _this = this
			const {
				data: res
			} = await _this.$http.get('approve/Award_Performance_Summary_Monthly?yearMonth=' + yearMonth + '&Line=' + line)
			console.log('performanceData', res)
			_this.performanceData = res
		},
		async Award_Performance_Summary_Award(yearMonth, line) {
			const _this = this
			const {
				data: res
			} = await _this.$http.get('approve/Award_Performance_Summary_Monthly?yearMonth=' + yearMonth + '&Line=' + line)
			console.log('performanceData', res)
			_this.performanceData = res
		},
		async SP_API_Award_Count_Summary(yearMonth, line, department) {
			const _this = this
			const {
				data: res
			} = await _this.$http.get(`approve/SP_API_Award_Count_Summary?yearMonth=${yearMonth}&Line=${line}&department=${department}`)
			console.log('SP_API_Award_Count_Summary', res)
			_this.summaryData.push(res)
			console.log(_this.summaryData)
		},

		async getAward(yearMonth, line, department) {
			var _this = this
			_this.queryData = false
			// _this.getAwardSummary(awardID)
			const {
				data: res
			} = await this.$http.get(`approve/getMonthlyAward?yearMonth=${yearMonth}&Line=${line}&department=${department}`)
			console.log('getMonthlyAward', res)
			const data = _this.userData
			let awardExists = []
			let singleColumn = [
				{ name: '姓名', label: '姓名', field: '姓名', align: 'left', sortable: true },
				{ name: '职位', label: '职位', field: '职位', align: 'left', sortable: true },
				{ name: '个人总计', label: '个人总计', field: '个人总计', align: 'left', sortable: true }
			]
			let summaryColumn = [
				{ name: '年月', label: '年月', field: '年月', align: 'left', sortable: true },
				{ name: '机台', label: '机台', field: '机台', align: 'left', sortable: true },
				{ name: '部门', label: '部门', field: '部门', align: 'left', sortable: true },
				{ name: '机台总计', label: '机台总计', field: '机台总计', align: 'left', sortable: true },
			]
			let summaryData = [{ '年月': yearMonth, '机台': line, '部门': department, '机台总计': 0 }]
			for (let j = 0; j < res.length; j++) {
				if (awardExists.indexOf(res[j]['Award_Type']) === -1) {
					awardExists.push(res[j]['Award_Type'])
					singleColumn.push({ name: res[j]['Award_Type'], label: res[j]['Award_Type'], field: res[j]['Award_Type'], align: 'left', sortable: true })
					summaryColumn.push({ name: res[j]['Award_Type'], label: res[j]['Award_Type'], field: res[j]['Award_Type'], align: 'left', sortable: true })
				}
				if (!summaryData[0].hasOwnProperty(res[j]['Award_Type'])) {
					summaryData[0][res[j]['Award_Type']] = 0
				}
				summaryData[0][res[j]['Award_Type']] += res[j]['Point']
				summaryData[0]['机台总计'] += res[j]['Point']
			}
			console.log('summaryColumn', _this.rows.summaryColumn)
			_this.summaryData = summaryData
			_this.summaryColumn = summaryColumn
			_this.userDataColumn = singleColumn
			for (let i = 0; i < res.length; i++) {
				const employee_ID = res[i]['Employee_Name']
				const position = res[i]['Position']
				const award_type = res[i]['Award_Type']
				const award_point = res[i]['Point']
				let crewColumn = { ...singleColumn }
				crewColumn['姓名'] = employee_ID
				crewColumn['职位'] = position
				// crewColumn['个人总计']=0
				if (data.length == 0) {
					data.push(crewColumn)
				}
				let rowExists = false
				for (let j = 0; j < data.length; j++) {
					const dataEmployee_ID = data[j]['姓名']
					if (employee_ID == dataEmployee_ID) {
						data[j][award_type] = award_point
						// console.log('employee',employee_ID)
						// console.log('crewColumn个人单价', award_point)
						// console.log('crewColumn个人总计', data[j]['个人总计']  )
						if (award_point > 0) {
							data[j]['个人总计'] = parseInt(data[j]['个人总计'] == '' || data[j]['个人总计'] === undefined ? 0 : data[j]['个人总计']) + parseInt(award_point)
						}
						rowExists = true
					}
				}
				if (!rowExists) {
					crewColumn['个人总计'] = parseInt(award_point)
					crewColumn[award_type] = award_point
					data.push(crewColumn)
				}

			}
			console.log('data', data)
			_this.userData = data
			_this.SP_API_Award_Count_Summary(yearMonth, line, department)
		},

	}
}
</script>

<style></style>



