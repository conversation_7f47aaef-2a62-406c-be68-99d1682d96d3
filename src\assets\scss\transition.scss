// 全局 transition css

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.28s;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* breadcrumb transition */

.breadcrumb-enter {
  opacity: 0;
  transform: translateX(30px);
}

.breadcrumb-enter-active {
  transition: all .5s;
}

.breadcrumb-enter-to {
  opacity: 1;
}

.breadcrumb-leave {
  opacity: 1;
  transform: translateX(0px);
}

.breadcrumb-move {
  transition: all .5s;
}

.breadcrumb-leave-active {
  transition: all .5s;
  position: absolute;
  width: 500px;
}

.breadcrumb-leave-to {
  opacity: 0;
  transform: translateX(65px);
}
