<template>
	<base-content>
		<div dense style="font-size: 20px;color: black;text-align: center;margin-top: 5px;">机器废品分析报告</div>
		<div class="q-pa-md">
			<!--  选择框及按钮 -->
			<div class="q-gutter-md row">
				<q-select outlined dense v-model="Line" :options="LineList" style="width: 150px" label-slot clearable>
					<template v-slot:label>产线
						<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 12px;">必填</em>
					</template>
				</q-select>
				<q-input v-model="Start" outlined type="date" dense label-slot style="width: 200px">
					<template v-slot:label>开始日期
						<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 10px;">必填</em>
					</template>
				</q-input>
				<q-input v-model="End" outlined type="date" dense label-slot style="width: 200px">
					<template v-slot:label>结束日期
						<em class="q-px-sm bg-deep-orange text-white rounded-borders" style="font-size: 10px;">必填</em>
					</template>
				</q-input>
				<q-btn dense :loading="loading1" color="primary" label="查询" @click="getMachineWaste(1)"
					style="width: 70px;height: 40px;" />
				<q-btn dense :loading="loading1" color="primary" label="查看废品备注" @click="getMWComment"
					style="width: 100px;height: 40px;margin-left: 30px;" />
			</div>
			<!-- 废品备注弹窗 -->
			<q-dialog v-model="MW_Comment">
				<q-card style="max-width: 100%; max-height: 100%;">
					<!-- 关闭按钮 -->
					<div class="row q-mt-md">
						<div class="col text-h6 text-center" style="font-size: 20px; color: black;">
							废品备注详情
						</div>
						<div class="col-auto q-mt-md">
							<q-btn label="关 闭" color="primary" @click="MW_Comment = false"
								style="width: 80px; height: 30px; margin-top: -30px;" />
						</div>
					</div>
					<q-card-section>
						<q-table :data="MWComment" :columns="columns1" flat bordered dense
							:pagination="{ rowsPerPage: 15 }" sticky-header
							style="height: calc(100% - 50px); overflow-y: auto; margin-top: -20px;">
							<template v-slot:body-cell-[name]="{ value, props, row }">
								<q-td v-bind="props">{{ value }}</q-td>
							</template>
						</q-table>
					</q-card-section>
				</q-card>
			</q-dialog>
			<div dense style="font-size: 13px;color: brown;">
				点击查询按钮获取原始数据后才可以进行班次，班组，Tier，Size，国家，排废原因等的筛选
			</div>
			<div class="q-gutter-md row">
				<q-select outlined dense v-model="Shift" :options="ShiftList" style="width: 100px" label-slot clearable>
					<template v-slot:label>班次</template>
				</q-select>
				<q-select outlined dense v-model="Crew" :options="CrewList" style="width: 100px" label-slot clearable>
					<template v-slot:label>班组</template>
				</q-select>
				<q-select outlined dense v-model="Tier" :options="TierList" style="width: 140px" label-slot clearable>
					<template v-slot:label>Tier</template>
				</q-select>
				<q-select outlined dense v-model="Size" :options="SizeList" style="width: 100px" label-slot clearable>
					<template v-slot:label>Size</template>
				</q-select>
				<q-select outlined dense v-model="Country" :options="CountryList" style="width: 100px" label-slot
					clearable>
					<template v-slot:label>国家</template>
				</q-select>
				<q-select outlined dense v-model="Export" :options="ExportList" style="width: 150px" label-slot
					clearable>
					<template v-slot:label>是否出口</template>
				</q-select>
				<q-select outlined dense v-model="ProductCore" :options="ProductCoreList" style="width: 150px"
					label-slot clearable>
					<template v-slot:label>芯体类型</template>
				</q-select>
				<q-select outlined dense v-model="Level1" :options="Level1List" style="width: 150px;margin-top: 20px;"
					label-slot clearable>
					<template v-slot:label>Level-1</template>
				</q-select>
				<q-select outlined dense v-model="Level2" :options="Level2List" style="width: 200px;margin-top: 20px"
					label-slot clearable>
					<template v-slot:label>Level-2</template>
				</q-select>
			</div>
			<!-- 瀑布图 -->
			<div style="height: 300px; width: 100%;" v-if="data && isShow">
				<component :is="waterfallComponent" ref="waterfallChart" :chartData="data.Waste" id="sxfyChart" />
			</div>
		</div>
		<!-- 瀑布图数据显示 -->
		<div class="table-container" style="margin-top: 1px;align-items:center;">
			<table class="table" title="机械废品详情">
				<thead>
					<tr>
						<th v-for="(item, index) in BarChartTable" :key="index">{{ item.Reason }}</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td v-for="(item, index) in BarChartTable" :key="index">{{ item.QTY }}</td>
					</tr>
				</tbody>
			</table>
		</div>
		<!-- <div class="q-pa-md">
			<q-table :data="BarChartTable" :columns="column1" row-key="Reason" />
		</div> -->
		<!--  机械废品详情列表 -->
		<div>
			<div
				style="font-weight: 600; height: 40px; align-items:center;position:relative ;line-height: 40px;margin-top: 1px;">
			</div>
			<q-table class="my-sticky-header-column-table" title="机械废品详情" :data="MachineWaste" :columns="columns" flat
				bordered dense style="margin-top:5px ;" :pagination="{ rowsPerPage: 20 }">
				<template v-slot:body-cell-[name]="{ value, props, row }">
					<q-td v-bind="props">{{ value }}</q-td>
				</template>
			</q-table>
		</div>

	</base-content>
</template>
<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import Quagga from 'quagga'
import { pick } from 'lodash';
import EchartsComponent_WaterFall from '../../components/ECharts/EchartsComponent_WaterFall.vue'
import EchartsComponent_WaterFall_ND37 from '../../components/ECharts/EchartsComponent_WaterFall_ND37.vue'
import EchartsComponent from '../../components/ECharts/EchartsComponent.vue'
import EchartsComponentDouble from '../../components/ECharts/EchartsComponentDouble.vue'

export default {
	components: {
		BaseContent,
		Quagga,
		EchartsComponent,   //第二步
		EchartsComponentDouble,
		EchartsComponent_WaterFall,
		EchartsComponent_WaterFall_ND37
	},
	mounted() {
		console.log("mounted", this.$route.query)
		this.User_ID = localStorage.getItem('account')
		this.Line = localStorage.getItem('Related_Line')
		this.getLine()
	},
	data() {
		const currentDate = new Date();
		const options = { timeZone: 'Asia/Shanghai' }; // 设置时区为UTC+8
		const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
		const formattedDate = firstDayOfMonth.toLocaleDateString('en-CA', options);
		const today = new Date();
		today.setDate(today.getDate());
		return {
			data: [],
			isShow: false,
			loading1: false,
			Line: "",
			Start: formattedDate,
			End: today.toISOString().substr(0, 10),
			showDatePopup: false,
			showDatePopup2: false,
			MW_Comment: false,
			Tier: "",
			Size: "",
			Country: "",
			Export: "",
			ProductCore: "",
			Shift: "",
			Crew: "",
			Level1: '',
			Level2: '',
			TotalWasteCut: "",
			TotalCut: "",
			MWPercent: "",
			LineList: [],
			MachineWaste: [],
			MW_Per_Item: [],
			columns: [
				{ name: 'Level3', required: true, label: '排废原因', align: 'left', field: 'Level3', sortable: true },
				{ name: 'Fault_Code', required: true, label: '排废代码', align: 'left', field: 'Fault_Code', sortable: true },
				{ name: 'MW_QTY', required: true, label: '排废数量', align: 'left', field: 'MW_QTY', sortable: true },
				{ name: 'MW_Percent_Item', required: true, label: '废品率%(排废数量/TotalCuts)', align: 'left', field: 'MW_Percent_Item', sortable: true },
				{ name: 'MW_Count', required: true, label: '排废次数', align: 'left', field: 'MW_Count', sortable: true },
				{ name: 'MW_Per_Item', required: true, label: '单次排废片数', align: 'left', field: 'MW_Per_Item', sortable: true }
			],
			MWComment: [],
			columns1: [
				{ name: 'Line', required: true, label: '产线', align: 'left', field: 'Line', sortable: true },
				{ name: 'Date', required: true, label: '日期', align: 'left', field: 'Date', sortable: true },
				{ name: 'Shift', required: true, label: '班次', align: 'left', field: 'Shift', sortable: true },
				{ name: 'Level1', required: true, label: 'Level-1', align: 'left', field: 'Level1', sortable: true },
				{ name: 'Level2', required: true, label: 'Level-2', align: 'left', field: 'Level2', sortable: true },
				{ name: 'Level3', required: true, label: 'Level-3', align: 'left', field: 'Level3', sortable: true },
				{ name: 'MW_QTY', required: true, label: '排废数量', align: 'left', field: 'MW_QTY', sortable: true },
				{ name: 'MW_Timestamp', required: true, label: '排废时间', align: 'left', field: 'MW_Timestamp', sortable: true },
				{ name: 'Comment', required: true, label: '备注', align: 'left', field: 'Comment', sortable: true },
				{ name: 'Account', required: true, label: '填写账号', align: 'left', field: 'Account', sortable: true },
			],
			TierList: [],
			SizeList: [],
			CountryList: [],
			ExportList: [],
			ProductCoreList: [],
			ShiftList: ['白班', '夜班'],
			CrewList: [],
			Level1List: [],
			Level2List: [],
			MWSKUList: [],
			BarChartList: [],
			BarChartTable: [],
			// column1: [
			// 	{ name: 'name', required: true, label: 'Reason', align: 'center', field: row => row.Reason },
			// 	{ name: 'QTY', align: 'center', label: 'QTY', field: row => row.QTY }
			// ],
			ChartDate1: [],
			ChartDate2: []
		}
	},

	watch: {
		Line: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.clearData()
		},
		Shift: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterMachineWaste()
		},
		Crew: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterMachineWaste()
		},
		Tier: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterMachineWaste()
		},
		Size: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterMachineWaste()
		},
		Country: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterMachineWaste()
		},
		Export: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterMachineWaste()
		},
		ProductCore: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterMachineWaste()
		},
		Level1: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterMachineWaste()
		},
		Level2: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.filterMachineWaste()
		},
	},
	computed: {
		waterfallComponent() {
			return this.Line === 'ND37' ? 'EchartsComponent_WaterFall_ND37' : 'EchartsComponent_WaterFall';
		},
	},
	methods: {
		//打开界面时，获取产线信息
		async getLine() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`material/lineList`)
			console.log('NJSLineList', res)
			_this.LineList = res
			this.LineList = _this.LineList
		},

		async WasteBarChart() {
			this.isShow = false;
			this.data = this.BarChartList;
			// console.log('瀑布图function里的List', this.data);
			this.isShow = true;
			this.$forceUpdate();
		},
		async clearData() {
			this.data = [],
				this.BarChartTable = [],
				this.MachineWaste = []
		},
		// 点击按钮获取waste备注
		async getMWComment() {
			if (this.Line && this.Start && this.End) {
				this.MW_Comment = true; // 点击按钮后设置RM_Scan为true，弹出RM_Scan弹窗
				this.getMWCommentDetail(); // 调用getPalletSum()方法		
			} else {
				this.$q.notify({
					icon: 'announcement',
					message: '请确定已选择产线/开始/结束时间',
					color: 'red',
					position: 'top',
					timeout: 3000
				});
			}
		},
		async getMWCommentDetail() {
			var _this = this
			this.MWComment = []
			var Line = this.Line
			var Start = this.Start
			var End = this.End
			const { data: res2 } = await _this.$http.get(`mes/MWComment?Line=${Line}&Start=${Start}&End=${End}`)
			console.log('MWComment', res2)
			this.MWComment = res2
		},

		async getMachineWaste(number) {
			this.MachineWaste = []
			this.MWLevel1 = []
			// we set loading state
			this[`loading${number}`] = true;
			try {
				var _this = this
				var Line = this.Line
				var Start = this.Start
				var End = this.End
				var Shift = ""
				var Crew = ""
				var Tier = ""
				var Size = ""
				var Country = ""
				var Export = ""
				var ProductCore = ""
				var Level1 = ""
				var Level2 = ""
				console.log('MW参数', Line, Start, End)
				//获取waste详情
				const { data: res2 } = await _this.$http.get(`material/MESMachineWaste?Line=${Line}&Start=${Start}&End=${End}`)
				const { data: res3 } = await _this.$http.get(`material/MESQCOWaste?Line=${Line}&Start=${Start}&End=${End}`)
				const res = [...res2, ...res3];
				// console.log('MachineWaste原始', res)
				//获取total cuts
				const { data: res1 } = await _this.$http.get(`material/TotalCut_Waste?Line=${Line}&Start=${Start}&End=${End}`)
				// console.log('totalcuts原始', res1)
				this.TotalCutListFull = res1
				// this.TotalCut = await TotalCutdata(res1, Shift, Crew, Tier, Size, Country, Level1, Level2)
				// this.MachineWaste = await Wastedata(res, res1, Shift, Crew, Tier, Size, Country, Level1, Level2)
				const { groupedData, ChartDate, ChartDate1, ChartDate2, TotalCut } = await Wastedata(res, res1, Shift, Crew, Tier, Size, Country, Export, ProductCore, Level1, Level2);
				this.TotalCut = TotalCut
				this.MachineWaste = groupedData;
				// console.log("点查询-表格", this.MachineWaste)
				this.ChartDate1 = ChartDate1;
				this.ChartDate2 = ChartDate2;
				const BarChartList = [...ChartDate1, ...ChartDate2];
				// 按照QTY值从大到小排序
				BarChartList.sort((a, b) => b.QTY - a.QTY);
				// console.log('BarChartList', this.BarChartList)
				//在数组中加入总切和good_cut
				let data_1 = this.MachineWaste
				this.TotalWasteCut = data_1.reduce((MW_QTY, item) => MW_QTY + item.MW_QTY, 0);
				const Total = [{ Reason: "TotalCuts", QTY: this.TotalCut }]
				const Good = [{ Reason: "GoodCuts", QTY: (this.TotalCut - this.TotalWasteCut) }]
				this.BarChartList = [...Total, ...BarChartList, ...Good];
				this.BarChartTable = this.BarChartList
				// console.log('Good为NA', this.TotalCut, this.TotalWasteCut)
				console.log('瀑布图第一次查询数据', this.BarChartList)
				// 初始化结果对象-开始处理柱状图需要的数据
				const result = { Waste: { categories: [], series: [] } };
				// 计算Percent并将百分比形式保留两位小数
				const percentData = this.BarChartList.map(item => ((item.QTY / this.TotalCut) * 100).toFixed(2));
				/// 计算"rest"系列数据，按照每行数据的累计差值进行计算
				const restData = [];
				restData.push(0);// 添加第一行数据为0
				let cumulativeRest = 100; // 初始值为100
				for (let i = 1; i < percentData.length - 1; i++) {
					cumulativeRest -= parseFloat(percentData[i]);
					cumulativeRest = parseFloat(cumulativeRest.toFixed(2)); // 累计值保留两位小数
					restData.push(cumulativeRest); // 将当前累计值存入restData
				}
				restData.push(0); // 添加最后一行数据为0
				// 设置categories和series
				result.Waste.categories = this.BarChartList.map(item => item.Reason);
				result.Waste.series.push({
					name: "Percent",
					data: percentData,
					type: "bar"
				});
				// 添加"rest"系列数据
				result.Waste.series.push({
					name: "Rest",
					data: restData,
					type: "bar"
				});
				// 最终结果
				// console.log("查询BarChart原始", result);
				this.BarChartList = result
				// console.log("总切", TotalCut)
				// console.log('MachineWaste合并L3', this.MachineWaste)
				// console.log('TotalCut-afterFilter', BarChartList1)
				this.MWPercent = (this.TotalWasteCut / this.TotalCut) * 100;
				this.MWPercent = this.MWPercent.toFixed(2);
				this.MachineWasteFull = res
				console.log('MachinewasteFull', this.MachineWasteFull)
				if (res && Array.isArray(res)) {
					// 使用 Set 去除重复项获得Tier,Size,Country,Crew,Level-1,Level-2筛选list
					const uniqueTier = new Set(res.map(item => item.Tier));
					this.TierList = Array.from(uniqueTier);
					// console.log('TierList', this.TierList)
					const uniqueSize = new Set(res.map(item => item.Size));
					this.SizeList = Array.from(uniqueSize);
					// console.log('SizeList', this.SizeList)
					const uniqueCountry = new Set(res.map(item => item.Country));
					this.CountryList = Array.from(uniqueCountry);
					// console.log('CountryList', this.CountryList)
					const uniqueExport = new Set(res.map(item => item.Export));
					this.ExportList = Array.from(uniqueExport);
					console.log('ExportList', this.ExportList)
					const uniqueProductCore = new Set(res.map(item => item.ProductCore));
					this.ProductCoreList = Array.from(uniqueProductCore);
					console.log('ProductCoreList', this.ProductCoreList)
					const uniqueCrew = new Set(res.map(item => item.Crew));
					this.CrewList = Array.from(uniqueCrew);
					// console.log('CrewList', this.CrewList)
					const uniqueLevel1 = new Set(res.map(item => item.Level1));
					this.Level1List = Array.from(uniqueLevel1);
					// console.log('Level1List ', this.Level1List)
					const uniqueLevel2 = new Set(res.map(item => item.Level2));
					this.Level2List = Array.from(uniqueLevel2);
					// console.log('Level2List ', this.Level2List)
					//获取瀑布图数据
					this.WasteBarChart('myChart_oee')
					this.filterMachineWaste()
				} else {
					throw new Error('Invalid response data');
				}
			} catch (error) {
				console.error(error)
			} finally {
				// reset loading state
				this[`loading${number}`] = false;
			}
		},

		async filterMachineWaste() {
			try {
				var Shift = this.Shift
				var Crew = this.Crew
				var Tier = this.Tier
				var Size = this.Size
				var Country = this.Country
				var Export = this.Export
				var ProductCore = this.ProductCore
				var Level1 = this.Level1
				var Level2 = this.Level2
				this.TotalCut = ""
				const { groupedData, ChartDate, TotalCut } = await Wastedata(this.MachineWasteFull, this.TotalCutListFull, Shift, Crew, Tier, Size, Country, Export, ProductCore, Level1, Level2);
				this.MachineWaste = groupedData;
				this.TotalCut = TotalCut
				console.log("筛选后返回的总切", this.TotalCut)
				const BarChartList = ChartDate;
				// console.log('this.BarChartList', BarChartList)
				// 按照QTY值从大到小排序
				BarChartList.sort((a, b) => b.QTY - a.QTY);
				//在数组中加入总切和good_cut
				let data_1 = this.MachineWaste
				// 确保数组中的 MW_QTY 值都是数字类型
				// console.log('NaN检查data_1', data_1)
				const validData = data_1.filter(item => typeof item.MW_QTY === 'number' && !isNaN(item.MW_QTY));
				this.TotalWasteCut = data_1.reduce((MW_QTY, item) => MW_QTY + item.MW_QTY, 0);
				const Total = [{ Reason: "TotalCuts", QTY: this.TotalCut }]
				const Good = [{ Reason: "GoodCuts", QTY: (this.TotalCut - this.TotalWasteCut) }]
				this.BarChartList = [...Total, ...BarChartList, ...Good];
				console.log('瀑布图原始数据', this.BarChartList)
				this.BarChartTable = this.BarChartList
				// 初始化结果对象-开始处理柱状图需要的数据
				const result = { Waste: { categories: [], series: [] } };
				// 计算Percent并将百分比形式保留两位小数
				const percentData = this.BarChartList.map(item => ((item.QTY / this.TotalCut) * 100).toFixed(2));
				/// 计算"rest"系列数据，按照每行数据的累计差值进行计算
				const restData = [];
				restData.push(0);// 添加第一行数据为0
				let cumulativeRest = 100; // 初始值为100
				for (let i = 1; i < percentData.length - 1; i++) {
					cumulativeRest -= parseFloat(percentData[i]);
					cumulativeRest = parseFloat(cumulativeRest.toFixed(2)); // 累计值保留四位小数
					restData.push(cumulativeRest); // 将当前累计值存入restData
				}
				restData.push(0); // 添加最后一行数据为0
				// 设置categories和series
				result.Waste.categories = this.BarChartList.map(item => item.Reason);
				result.Waste.series.push({
					name: "Percent",
					data: percentData,
					type: "bar"
				});
				// 添加"rest"系列数据
				result.Waste.series.push({
					name: "Rest",
					data: restData,
					type: "bar"
				});
				// 最终结果
				this.BarChartList = result
				// console.log("filter里BarChart", this.BarChartList);
				await this.WasteBarChart('myChart_oee');
				this.$nextTick(() => {
					this.$refs.waterfallChart.drawChart(); // 调用瀑布图组件的更新方法
				});
			} catch (error) {
				console.error(error)
			}
			let data_1 = this.MachineWaste
			// console.log('function-data_1', data_1)
			this.TotalWasteCut = data_1.reduce((MW_QTY, item) => MW_QTY + item.MW_QTY, 0);
			this.MWPercent = (this.TotalWasteCut / this.TotalCut) * 100;
			this.MWPercent = this.MWPercent.toFixed(2);
		},
	}
}

function Wastedata(data, data1, Shift, Crew, Tier, Size, Country, Export, ProductCore, Level1, Level2) {
	return new Promise((resolve, reject) => {
		if (data && Array.isArray(data)) {
			let filteredData = data;
			let filteredData1 = data1;
			// 根据Shift过滤
			console.log('Shift-F', Shift)
			if (Shift) {
				filteredData = filteredData.filter(item => item.Shift === Shift);
				filteredData1 = filteredData1.filter(item => item.Shift === Shift);
			}
			// console.log('filter-Shift', filteredData, filteredData1)
			// 根据Crew过滤
			console.log('Crew-F', Crew)
			if (Crew) {
				filteredData = filteredData.filter(item => item.Crew === Crew);
				filteredData1 = filteredData1.filter(item => item.Crew === Crew);
			}
			// console.log('filter-Crew', filteredData, filteredData1)
			// 根据Tier过滤
			console.log('Tier-F', Tier)
			if (Tier) {
				filteredData = filteredData.filter(item => item.Tier === Tier);
				filteredData1 = filteredData1.filter(item => item.Tier === Tier);
			}
			// console.log('filter-Tier', filteredData, filteredData1)
			// 根据Size过滤
			// console.log('Size-F', Size)
			if (Size) {
				filteredData = filteredData.filter(item => item.Size === Size);
				filteredData1 = filteredData1.filter(item => item.Size === Size);
			}
			// console.log('filter-Size', filteredData, filteredData1)
			// 根据Country过滤
			// console.log('Country-F', Country)
			if (Country) {
				filteredData = filteredData.filter(item => item.Country === Country);
				filteredData1 = filteredData1.filter(item => item.Country === Country);
			}
			// console.log('filter-Country', filteredData, filteredData1)
			// 根据Export过滤
			// console.log('Export-F', Export)
			if (Export) {
				filteredData = filteredData.filter(item => item.Export === Export);
				filteredData1 = filteredData1.filter(item => item.Export === Export);
			}
			// console.log('filter-Export', filteredData, filteredData1)
			// 根据芯体类型ProductCore过滤
			// console.log('ProductCore-F', ProductCore)
			if (ProductCore) {
				filteredData = filteredData.filter(item => item.ProductCore === ProductCore);
				filteredData1 = filteredData1.filter(item => item.ProductCore === ProductCore);
			}
			// console.log('filter-ProductCore', filteredData, filteredData1)
			// 根据Level1过滤
			// console.log('Level1-F', Level1)
			if (Level1) {
				filteredData = filteredData.filter(item => item.Level1 === Level1);

			}
			// console.log('filter-L1', filteredData)
			// 根据Level1过滤
			// console.log('Level2-F', Level2)
			if (Level2) {
				filteredData = filteredData.filter(item => item.Level2 === Level2);
			}
			// console.log('filter-L2', filteredData)
			// console.log('afterfilter_WasteList', filteredData)
			// console.log('afterfilter_TotalCutlist', filteredData1)
			// 使用 Set 去除重复项获得Level3筛选list
			const uniqueLevel3 = new Set(filteredData.map(item => item.Level3));
			//定义瀑布图的废品原因(NJS固定7个废品分类)
			const Level_2 = ['Vision', '材料拼接废品', '启机废品', '手动排废'];
			const ChartWasteL2 = Level_2;
			const Level_1 = ['质量废品', '包装废品', '换型废品']
			const ChartWasteL1 = Level_1
			//计算总切
			// console.log('总切筛选后', filteredData1)
			let TotalCut = filteredData1.reduce((total, item) => total + item.Total_Cuts, 0);
			// console.log("总切-筛选后片数", TotalCut)
			// 按Level3汇总显示MW_QTY和MW_Count
			let groupedData = Array.from(uniqueLevel3).map(Level3 => {
				// 根据Level3过滤数据
				const filteredByLevel3 = filteredData.filter(item => item.Level3 === Level3);
				const Fault_Code = filteredByLevel3[0].Fault_Code;
				// 计算MW_QTY和MW_Count
				const MW_QTY = filteredByLevel3.reduce((total, item) => total + item.MW_QTY, 0);
				const MW_Count = filteredByLevel3.reduce((total, item) => total + item.MW_Count, 0);
				return {
					Level3,
					Fault_Code,
					MW_QTY,
					MW_Count,
					MW_Per_Item: (MW_QTY / MW_Count).toFixed(2),
					MW_Percent_Item: ((MW_QTY / TotalCut) * 100).toFixed(2) + '%'
				};
			}).filter(item => item.Level3); // 过滤掉 Level3 为空的对象
			groupedData.sort((a, b) => b.MW_QTY - a.MW_QTY) // 按MW_QTY降序排序
			// 统计瀑布图基础数据
			let ChartDate2 = Array.from(ChartWasteL2).map(Level_2 => {
				// 根据Level2过滤数据
				const filteredByLevel2 = filteredData.filter(item => item.Level2 === Level_2);
				// 计算MW_QTY
				const L2_MW_QTY = filteredByLevel2.reduce((total, item) => total + item.MW_QTY, 0);
				return {
					Reason: Level_2,
					QTY: L2_MW_QTY
				};
			});
			// console.log('ChartDate2',ChartDate2)
			let ChartDate1 = Array.from(ChartWasteL1).map(Level_1 => {
				// 根据Level1过滤数据
				const filteredByLevel1 = filteredData.filter(item => item.Level1 === Level_1);
				// 计算MW_QTY
				const L1_MW_QTY = filteredByLevel1.reduce((total, item) => total + item.MW_QTY, 0);
				return {
					Reason: Level_1,
					QTY: L1_MW_QTY
				};
			});
			// console.log('ChartDate1',ChartDate1)
			let ChartDate = [...ChartDate1, ...ChartDate2];
			// console.log("wastedata返回值", groupedData, ChartDate, ChartDate1, ChartDate2, TotalCut)
			resolve({ groupedData, ChartDate, ChartDate1, ChartDate2, TotalCut })// 将数据包装在一个对象中传递给resolve方法
		} else {
			resolve([]); // 如果data不存在或不是数组，返回空数组
		}
	})
}

</script>


<style lang="css" scoped>
.text_type {
	color: yellow;
	font-size: 20px;
	font-weight: bold
}

.tabletitle {
	font-weight: bold;
}
</style>

<style>
.bordered-table {
	border-collapse: collapse;
	border: 1px solid black;
}

.bordered-table th,
.bordered-table td {
	border: 1px solid black;
}
</style>

<style>
.bordered-table {
	border-collapse: collapse;
	border: 1px solid black;
}

.bordered-table th,
.bordered-table td {
	border: 1px solid black;
}
</style>

<style>
.table {
	border-collapse: collapse;
	width: 100%;
}

.table th,
.table td {
	border: 1px solid #ddd;
	padding: 8px;
}

.table th {
	background-color: #f2f2f2;
}

.table-container {
	font-weight: 600;
	margin-top: -55px;
}
</style>
