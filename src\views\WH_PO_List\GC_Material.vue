<template>
	<base-content>
		<div class="q-pa-md">
			<!--  选择框及按钮 -->
			<div class="q-gutter-md row">
				<q-select outlined v-model="Line" :options="LineList" label=" 产线" style="width: 100px" />
				<q-select outlined v-model="Product1" :options="SKUList" label="换型前SKU - 计划开始时间" style="width: 250px" />
				<q-select outlined v-model="Product2" :options="SKUList" label="换型后SKU - 计划开始时间" style="width: 250px" />
				<q-btn color="primary" label="查询" @click="getOtherBom" style="width: 100px " />
				<q-btn color="primary" icon="print" label="打印" @click="printTask()" />
				<q-btn color="primary" icon="download" label="导出CSV" @click="downloadTask()" />
				<!-- <div>{{ selectedData.product1 }}</div> -->
			</div>
		</div>

		<!--  换型前差异物料列表 -->
		<div class="q-pa-md row justify-around" v-if="BOM1List.length > 0" :fullscreen.sync="fullscreen">
			<div class="col" style="margin-right: 10px;">
				<q-table dense :data="BOM1List" :pagination="{ rowsPerPage: 20 }" />
			</div>
			<div class="col">
				<q-table dense :data="BOM2List" :pagination="{ rowsPerPage: 20 }" />
			</div>
		</div>
		<div class="q-pa-md row justify-around" v-if="SameBOMList.length > 0" :fullscreen.sync="fullscreen">
			<div class="col" style="margin-top: 10px;">
				<q-table dense :data="SameBOMList" :pagination="{ rowsPerPage: 20 }" />
			</div>
		</div>





	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import Quagga from 'quagga'
import moment from 'moment'
import * as XLSX from 'xlsx';

export default {

	components: {
		BaseContent,
		Quagga

	},

	mounted() {
		console.log("mounted", this.$route.query)
		// 如果URL中有line参数，则使用URL中的参数，否则使用localStorage中的值
		this.Line = this.$route.query.line || localStorage.getItem('Related_Line')
		this.getLineList()
		this.getSKUList()
		this.created()
	},

	data() {
		return {
			isNavVisible: false,
			fullscreen: false,
			leftDrawerOpen: false,
			Line: "",
			Product1: '',
			Product2: '',
			BOM1: '',
			BOM2: '',
			SKUList: [],
			LineList: [],
			BOM1List: [],
			BOM2List: [],
			SameBOMList: [],
			POData1: [],
			POData2: []
		}
	},
	watch: {
		Line: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.Product1 = ''
			this.Product2 = ''
			this.getSKUList()
			this.clearBOM()
		},
		Product1: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.clearBOM()
		},
		Product2: function (newVal, oldVal) {
			console.log(oldVal, '->', newVal)
			this.clearBOM()
		},
	},
	methods: {
		async getLineList() {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('material/lineList')
			console.log('LineList', res)
			_this.LineList = res
			// _this.optionsData.line = res
			//_this.selectedData.product1=res[0]['shiftEndTime']
		},

		created() {
			this.leftDrawerOpen = false;
		},
		async clearBOM() {
			this.BOM1List = []
			this.BOM2List = []
			this.SameBOMList = []
		},

		async getSKUList() {
			var _this = this
			const Line = this.Line
			const {
				data: res
			} = await _this.$http.get(`mes/SKUList?line=${Line}`)
			console.log('SKUList', res)
			// _this.skuList=res.map(item=>item.SKU_order)
			_this.SKUList = res
		},


		async getOtherBom() {
			this.clearBOM()
			console.log('product1', this.Product1)
			console.log('product2', this.Product2)
			this.getBOM1(this.Product1['BOM'], this.Product2['BOM'])
			this.getBOM2(this.Product1['BOM'], this.Product2['BOM'])
			this.getSameBOM(this.Product1['BOM'], this.Product2['BOM'])

		},

		async getBOM1(BOM_1, BOM_2) {
			console.log(BOM_1, BOM_2)
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`mes/BOM1List?BOM1=${BOM_1}&BOM2=${BOM_2}`)
			console.log('getBOM1', res)
			_this.BOM1List = res
			// _this.optionsData.line = res
		},
		async getBOM2(BOM_1, BOM_2) {
			//console.log()
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`mes/BOM2List?BOM1=${BOM_1}&BOM2=${BOM_2}`)
			console.log('getBOM2', res)
			_this.BOM2List = res
			// _this.optionsData.line = res
		},
		async getSameBOM(BOM_1, BOM_2) {
			//console.log()
			var _this = this
			const {
				data: res
			} = await _this.$http.get(`mes/SameBOMList?BOM1=${BOM_1}&BOM2=${BOM_2}`)
			console.log('getSameBOM', res)
			_this.SameBOMList = res
			// _this.optionsData.line = res
		},
		async getPOdata(BOM_1, BOM_2) {
			//console.log()
			var _this = this
			const {
				data: res1
			} = await _this.$http.get(`mes/GCPOData?BOM=${BOM_1}`)
			console.log('POData1', res1)
			this.POData1 = res1
			const {
				data: res2
			} = await _this.$http.get(`mes/GCPOData?BOM=${BOM_2}`)
			console.log('POData2', res2)
			this.POData2 = res2
			// _this.optionsData.line = res
		},

		printTask() {
			this.getPOdata(this.Product1['BOM'], this.Product2['BOM']);
			var _this = this
			const timeoutId =
				setTimeout(() => {
					console.log('PO1', this.POData1[0])
					console.log('PO2', this.POData2[0])
					const printWindow = window.open('', '_blank');
					const Line = this.Line;
					const SKU1 = this.POData1[0]['SKU'];
					const SKU2 = this.POData2[0]['SKU'];
					const PO1 = this.POData1[0]['Order_NO'];
					const PO2 = this.POData2[0]['Order_NO'];
					const SKUDesc1 = this.POData1[0]['SKU_Desc'];
					const SKUDesc2 = this.POData2[0]['SKU_Desc'];
					const P_QTY1 = this.POData1[0]['QTY'];
					const P_QTY2 = this.POData2[0]['QTY'];
					const Shift1 = this.POData1[0]['Shift'];
					const Shift2 = this.POData2[0]['Shift'];
					const P_Time1 = moment(this.POData1[0]['forecast_date']).format('YYYY-MM-DD HH:mm:ss');
					const P_Time2 = moment(this.POData2[0]['forecast_date']).format('YYYY-MM-DD HH:mm:ss');
					const SKU1BOM = this.BOM1List;
					const SKU2BOM = this.BOM2List;
					const SameBOM = this.SameBOMList;
					// 动态生成表格行
					let row1 = '';
					SKU1BOM.forEach(item => {
						row1 += '<tr>';
						// 直接引用换型前料号和换型前原料描述的键名
						row1 += `<td>${item['换型前料号']}</td><td>${item['换型前原料描述']}</td>`;
						row1 += '</tr>';
					});
					let row2 = '';
					SKU2BOM.forEach(item => {
						row2 += '<tr>';
						// 直接引用换型前料号和换型前原料描述的键名
						row2 += `<td>${item['换型后料号']}</td><td>${item['换型后原料描述']}</td>`;
						row2 += '</tr>';
					});
					let row3 = '';
					SameBOM.forEach(item => {
						row3 += '<tr>';
						// 直接引用换型前料号和换型前原料描述的键名
						row3 += `<td>${item['共用原料号']}</td><td>${item['共用原料描述']}</td>`;
						row3 += '</tr>';
					});
					console.log('printdata', SKU1BOM, SKU2BOM, SameBOM)
					const printContent = `
      <html>
	  <style>
			body {
			display: flex;
			justify-content: left;
			align-items: left;
			}
			myTable {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid black;
            page-break-inside: avoid;
            break-inside: avoid-table;
        }
		</style>
		<body onload="window.print(); window.close();">
			<div style="margin-left: 50px;margin-top:15px">
				<!-- 第一行 -->
				<div>机台：${Line}</div>
				<!-- 第二行 -->
				<div style="display: flex; align-items: center;">
					<div style="display: flex; width: 500px;">
						<div>换型前SKU：${SKU1}</div>
						<div style="margin-left: 20px;">计划订单：${PO1}</div>
					</div>
					<!-- 添加一个空div作为间隔 -->
					<div style="margin-left: 20px;"></div>
					<div>换型后SKU：${SKU2}</div>
					<div style="margin-left: 20px;">计划订单：${PO2}</div>
				</div>
				<!-- 第三行 -->
				<div style="display: flex; align-items: center;">
					<div style="width:500px">换型前SKU描述：${SKUDesc1}</div>
					<div style="margin-left: 20px;">换型后SKU描述：${SKUDesc2}</div>
				</div>
				<!-- 第四行 -->
				<div style="display: flex; align-items: center;">
					<div style="display: flex; width: 500px;">
					<div>生产箱数：${P_QTY1}</div>
					<div style="margin-left: 20px;">班次：${Shift1}</div>
					</div>
					<!-- 添加一个空div作为间隔 -->
					<div style="margin-left: 20px;"></div>
					<div>生产箱数：${P_QTY2}</div>
					<div style="margin-left: 20px;">班次：${Shift2}</div>
				</div>
				<!-- 第五行 -->
				<div style="display: flex; align-items: center;">
					<div style="width:500px">计划开始：${P_Time1}</div>
					<div style="margin-left: 20px;">计划开始：${P_Time2}</div>
				</div>
			<!-- 第六行-表格 -->
				<div style="display: flex; align-items: center;">
				<table id="myTable"  style="margin-top:10px;width: 500px;border-color: black; border-style: solid;border-collapse: collapse" border="1">
					<tr>
						<td style="width:80px">换型前料号</td>
						<td>换型前原料描述</td>
					</tr>
				 ${row1}  <!-- 插入动态生成的表格行 -->
				</table>
				<table id="myTable"  style="margin-left:20px;margin-top:10px;width: 500px;border-color: black; border-style: solid;border-collapse: collapse" border="1">
					<tr>
						<td style="width:80px">换型后料号</td>
						<td>换型后原料描述</td>
					</tr>
				 ${row2}  <!-- 插入动态生成的表格行 -->
				</table>
				</div>
				<!-- 第七行-表格 -->
				<div style="display: flex; align-items: center;">
					<table id="myTable"  style="margin-top:15px;width: 1020px;border-color: black; border-style: solid;border-collapse: collapse" border="1">
					<tr>
						<td style="width:80px">共用料号</td>
						<td>共用原料描述</td>
					</tr>
				 ${row3}  <!-- 插入动态生成的表格行 -->
				</table>
				</div>
		</div>
</body>
</html>`;
					// printWindow.document.open();
					printWindow.document.write(printContent);
					printWindow.document.close();
				}, 1000)
		},
		downloadTask() {
			this.getPOdata(this.Product1['BOM'], this.Product2['BOM']);
			var _this = this;
			const timeoutId = setTimeout(async () => {
				console.log('PO1', this.POData1[0]);
				console.log('PO2', this.POData2[0]);
				const Line = this.Line;
				const SKU1 = this.POData1[0]['SKU'];
				const SKU2 = this.POData2[0]['SKU'];
				const PO1 = this.POData1[0]['Order_NO'];
				const PO2 = this.POData2[0]['Order_NO'];
				const SKUDesc1 = this.POData1[0]['SKU_Desc'];
				const SKUDesc2 = this.POData2[0]['SKU_Desc'];
				const P_QTY1 = this.POData1[0]['QTY'];
				const P_QTY2 = this.POData2[0]['QTY'];
				const Shift1 = this.POData1[0]['Shift'];
				const Shift2 = this.POData2[0]['Shift'];
				const P_Time1 = moment(this.POData1[0]['forecast_date']).format('YYYY-MM-DD HH:mm:ss');
				const P_Time2 = moment(this.POData2[0]['forecast_date']).format('YYYY-MM-DD HH:mm:ss');
				const SKU1BOM = this.BOM1List;
				const SKU2BOM = this.BOM2List;
				const SameBOM = this.SameBOMList;

				// 构建CSV内容
				let csvContent = '﻿'; // 添加UTF-8 BOM
				csvContent += `机台:${Line}\n`;
				csvContent += `换型前SKU:${SKU1},计划订单:${PO1}\n`;
				csvContent += `换型后SKU:${SKU2},计划订单:${PO2}\n`;
				csvContent += `换型前SKU描述:${SKUDesc1}\n`;
				csvContent += `换型后SKU描述:${SKUDesc2}\n`;
				csvContent += `换型前SKU生产箱数:${P_QTY1},班次:${Shift1}\n`;
				csvContent += `换型后SKU生产箱数:${P_QTY2},班次:${Shift2}\n`;
				csvContent += `换型前SKU计划开始:${P_Time1}\n`;
				csvContent += `换型后SKU计划开始:${P_Time2}\n\n`;
				// 引入xlsx库
				const XLSX = await import('xlsx');

				// 创建通用头部数据
				const headerData = [
					['机台', Line],
					['换型前SKU', SKU1, '计划订单', PO1],
					['换型后SKU', SKU2, '计划订单', PO2],
					['换型前SKU描述', SKUDesc1],
					['换型后SKU描述', SKUDesc2],
					['换型前生产箱数', P_QTY1, '班次', Shift1],
					['换型后生产箱数', P_QTY2, '班次', Shift2],
					['换型前计划开始', P_Time1],
					['换型后计划开始', P_Time2],
					[] // 空行分隔
				];

				// 创建Sheet1（换型前）
				const sheet1Data = [
					...headerData,
					['换型前料号', '换型前原料描述'],
					...SKU1BOM.map(item => [item['换型前料号'], item['换型前原料描述']])
				];

				// 创建Sheet2（换型后）
				const sheet2Data = [
					...headerData,
					['换型后料号', '换型后原料描述'],
					...SKU2BOM.map(item => [item['换型后料号'], item['换型后原料描述']])
				];

				// 创建Sheet3（共用）
				const sheet3Data = [
					...headerData,
					['共用料号', '共用原料描述'],
					...SameBOM.map(item => [item['共用原料号'], item['共用原料描述']])
				];

				// 创建工作簿
				const wb = XLSX.utils.book_new();

				// 转换数据为工作表
				const ws1 = XLSX.utils.aoa_to_sheet(sheet1Data);
				const ws2 = XLSX.utils.aoa_to_sheet(sheet2Data);
				const ws3 = XLSX.utils.aoa_to_sheet(sheet3Data);

				// 添加工作表到工作簿
				XLSX.utils.book_append_sheet(wb, ws1, "换型前物料");
				XLSX.utils.book_append_sheet(wb, ws2, "换型后物料");
				XLSX.utils.book_append_sheet(wb, ws3, "共用物料");

				// 生成Excel文件
				const wbout = XLSX.write(wb, { type: 'array', bookType: 'xlsx' });

				// 创建下载链接
				const blob = new Blob([wbout], { type: 'application/octet-stream' });
				const url = window.URL.createObjectURL(blob);
				const link = document.createElement('a');
				link.href = url;
				link.download = `${Line}-${SKU1}换型至${SKU2}.xlsx`;
				link.click();
				setTimeout(() => window.URL.revokeObjectURL(url), 100);
			}, 1000);
		},
	}
}

</script>

<style lang="css" scoped>
.fontStyle {
	color: brown;
	font-size: 20px;
	font-weight: 600;
	background-color: bisque;
	text-align: center;
	width: 500px;
	height: 200px;
	margin-top: 20px;
	margin-left: 20px
}
</style>
