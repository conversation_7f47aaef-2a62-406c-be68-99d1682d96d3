<service>
	<id>webserver_new.exe</id>
	<name>webServer_new</name>
	<description>网页API</description>
	<executable>D:\Program Files\nodejs\node.exe</executable>
	<argument>--harmony</argument>
	<argument>D:\api\newAPI\node_modules\node-windows\lib\wrapper.js</argument>
	<argument>--file</argument>
	<argument>D:\api\newAPI\server.js</argument>
	<argument>--scriptoptions=</argument>
	<argument>--log</argument>
	<argument>webServer_new wrapper</argument>
	<argument>--grow</argument>
	<argument>0.25</argument>
	<argument>--wait</argument>
	<argument>1</argument>
	<argument>--maxrestarts</argument>
	<argument>40</argument>
	<argument>--abortonerror</argument>
	<argument>n</argument>
	<argument>--stopparentfirst</argument>
	<argument>undefined</argument>
	<logmode>rotate</logmode>
	<stoptimeout>30sec</stoptimeout>
	<serviceaccount>
		<domain>CNNBAS02</domain>
		<user>LocalSystem</user>
		<password></password>
	</serviceaccount>
	<workingdirectory>D:\api\newAPI</workingdirectory>
</service>