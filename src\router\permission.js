import _Vue from 'vue'
import router from './index'
import LoadingBar from '../components/LoadingBar/LoadingBar'
import store from '../store/index'
import constantRoutes from './constantRoutes'
import { addTagView, setTagView } from '../components/TagView/TagViewUtils'
import { setBreadcrumbs } from '../components/Breadcrumbs/BreadcrumbsUtils'

router.beforeEach((to, from, next) => {
  // 成功登录后处理
  handleTagViewAndBreadcrumbsAndKeepAlive(to)
  // 模拟获取 token
  const token = sessionStorage.getItem('access_token')
  const userRole = localStorage.getItem('user_role')
  // 存在 token 说明已经登录
  if (token) {
    // 登录过就不能访问登录界面
    if (to.path === '/login') {
      next({ path: '/' })
    }
    // 存在用户权限，并且路由不为空则放行
    if (userRole && store.getters.getRoutes.length) {
      next()
    } else {
      // 模拟不存在用户权限时，获取用户权限
      const userRole = localStorage.getItem('user_role')
      // 并根据权限设置对应的路由
      store.commit('SET_ROLES_AND_ROUTES', userRole)
      // 如果提示 addRoutes 已弃用，使用扩展运算符完成该操作
      // router.addRoute(...store.getters.getRoutes)
      router.addRoutes(store.getters.getRoutes)
      // 如果 addRoutes 并未完成，路由守卫会再执行一次
      next({ ...to, replace: true })
    }
  }
  else {
    if (constantRoutes.some((item) => { return item.path === to.path })) {
      next()
    } else {
      console.log('to',to)
      const account=localStorage.getItem('account')
      const username=localStorage.getItem('username')
      if(to.query.mail=='true' && account && username){
        // console.log('邮件进入')
        sessionStorage.setItem('access_token',*********)
        next({ path: to.fullPath })
      }else{
        next({ path: '/logon' })
      }
     
    }
  }
})
// router.beforeEach((to, from, next) => {
//   // 成功登录后处理
//   handleTagViewAndBreadcrumbsAndKeepAlive(to)
//   // 模拟获取 token
//   const token = sessionStorage.getItem('access_token')
//   const userRole = localStorage.getItem('user_role')
//   // 存在 token 说明已经登录
//   if (token) {
//     // 登录过就不能访问登录界面
//     if (to.path === '/login') {
//       next({ path: '/' })
//     }
//     // 存在用户权限，并且路由不为空则放行
//     if (userRole && store.getters.getRoutes.length) {
//       next()
//     } else {
//       // 模拟不存在用户权限时，获取用户权限
//       const userRole = localStorage.getItem('user_role')
//       // 并根据权限设置对应的路由
//       store.commit('SET_ROLES_AND_ROUTES', userRole)
//       // 如果提示 addRoutes 已弃用，使用扩展运算符完成该操作
//       // router.addRoute(...store.getters.getRoutes)
//       router.addRoutes(store.getters.getRoutes)
//       // 如果 addRoutes 并未完成，路由守卫会再执行一次
//       next({ ...to, replace: true })
//     }
//   }
//   else {
//     if (constantRoutes.some((item) => { return item.path === to.path })) {
//       next()
//     } else {
//       console.log('to',to)
//       // 处理分享链接直接访问的情况
//       if(to.query.mail=='true'){
//         console.log('分享链接直接访问')
//         // 设置临时访问令牌
//         sessionStorage.setItem('access_token', 'temp_token_for_shared_link')
//         // 设置临时用户角色，只有查看权限
//         localStorage.setItem('user_role', 'CI_Management查看')
//         // 设置临时账号信息，如果没有的话
//         if(!localStorage.getItem('account')) {
//           localStorage.setItem('account', 'guest')
//         }
//         if(!localStorage.getItem('username')) {
//           localStorage.setItem('username', '访客')
//         }
//         // 重定向到当前路径，触发路由守卫重新执行
//         next({ path: to.fullPath })
//       }else{
//         next({ path: '/logon' })
//       }

//     }
//   }
// })

router.afterEach(() => {
  // 使用多个 stop() 来保证 LoadingBar 在动态添加路由后正确关闭
  LoadingBar.stop()
  LoadingBar.stop()
})

export default router

/**
 * 处理 tagView 和 面包屑
 * @param to
 */
function handleTagViewAndBreadcrumbsAndKeepAlive (to) {
  if (to.name != null) {
    document.title = to.meta.title + _Vue.prototype.$title
    LoadingBar.start()
    // 判断要添加的 to 是否是公共路由
    for (let i = 0; i < constantRoutes.length; i++) {
      if (constantRoutes[i].path === to.path) {
        return
      }
    }
    // 判断是否为刷新操作，如果是刷新操作则从 sessionStorage 获取保存的 tagView 信息
    let tagViewOnSS = []
    JSON.parse(window.sessionStorage.getItem('tagView')) === null ? window.sessionStorage.setItem('tagView', '[]') : tagViewOnSS = JSON.parse(window.sessionStorage.getItem('tagView'))
    if (store.getters.getTagView.length === 0 && tagViewOnSS.length !== 0) {
      setTagView(tagViewOnSS)
      store.commit('SET_KEEPALIVE_LIST', tagViewOnSS)
    } else {
      addTagView(to)
    }
    setBreadcrumbs(to.matched, to.query)
    handleKeepAlive(to)
  }
}

/**
 * 处理多余的 layout : router-view，让当前组件保持在第一层 index : router-view 之下
 * 这个方法无法过滤用来做嵌套路由的按需加载的 <layout>
 * @param to
 */
function handleKeepAlive (to) {
  if (to.matched && to.matched.length > 2) {
    for (let i = 0; i < to.matched.length; i++) {
      const element = to.matched[i]
      if (element.components.default.name === 'layout') {
        to.matched.splice(i, 1)
        handleKeepAlive(to)
      }
    }
  }
}

/**
 * 这个方法可以过滤用来做嵌套路由的按需加载的 <layout>
 * @param to
 */
// async function handleKeepAlive (to) {
//   if (to.matched && to.matched.length > 2) {
//     for (let i = 0; i < to.matched.length; i++) {
//       const element = to.matched[i]
//       if (element.components.default.name === 'layout') {
//         to.matched.splice(i, 1)
//         await handleKeepAlive(to)
//       }
//       if (typeof element.components.default === 'function') {
//         await element.components.default()
//         await handleKeepAlive(to)
//       }
//     }
//   }
// }
