let express = require("express")
let app = new express();

let api_approve = require('./routes/approvalAPI');
let api_gradeChange = require('./routes/gradeChangeAPI');
let api_lsw = require('./routes/LSWAPI');
let api_mes = require('./routes/mes');
let api_material = require('./routes/MaterialAPI');
let api_dataBase = require('./routes/data_baseAPI');
let api_ideaBank = require('./routes/ideaBankAPI');
let api_CI = require('./routes/CIAPI');
var bodyParser=require("body-parser");
let api_assetmeeting = require('./routes/forminputapi');
let api_training = require('./routes/TrainingAPI');
let api_holdGoods = require('./routes/holdGoodsAPI');
let api_ciPorject = require('./routes/CIProjectAPI');
let api_tech = require('./routes/Tech_DataAPI');
let api_performanceSummary = require('./routes/performanceSummaryAPI');
let userManagement_api = require('./routes/userManagement_api');
let userInfo_api = require('./routes/userInfo_api');
var bodyParser=require("body-parser");


app.use(bodyParser.json())
app.use(bodyParser.urlencoded({ extended: false }));
app.all('*', function (req, res, next) {
    res.header("Access-Control-Allow-Origin", "*");
    res.header('Access-Control-Allow-Headers', 'Content-Type, Content-Length, Authorization, Accept, X-Requested-With , yourHeaderFeild');
    res.header("Access-Control-Allow-Methods", "PUT,POST,GET,DELETE,OPTIONS");
    res.header("X-Powered-By", ' 3.2.1')
    res.header("Content-Type", "application/json;charset=utf-8");

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    next();
  });


app.use('/approve',api_approve)
app.use('/gradeChange',api_gradeChange)
app.use('/lsw',api_lsw)
app.use('/mes',api_mes)
app.use('/material',api_material)
app.use('/forminput',api_assetmeeting)
app.use('/data_base',api_dataBase)
app.use('/ideabank',api_ideaBank)
app.use('/ci',api_CI)
app.use('/training', api_training)
app.use('/holdGoods', api_holdGoods)
app.use('/ciProject', api_ciPorject)
app.use('/tech_data', api_tech)
app.use('/performanceSummary', api_performanceSummary)
app.use('/userManagement_api', userManagement_api)
app.use('/userInfo_api', userInfo_api)

var server = app.listen(3000, function () {
    var host = server.address().address
    var port = server.address().port
    console.log("server running on 127.0.0.1", host, port)
  })
