var express = require('express')
var router = express.Router()
var sqlexec = require('../sql/sql_userManagement')

// 全局变量
let modify_by = '';

// 用户注册
router.post("/register", (req, res) => {
    if (!req.body.employee_id || !req.body.employee_name) {
        return res.json({
            status: 400,
            message: "缺少必要参数"
        })
    }

    const userData = {
        employee_id: req.body.employee_id,
        employee_name: req.body.employee_name,
        department: req.body.department || '',
        title: req.body.title || '',
        mill: req.body.mill || '南京南厂',
        related_line: req.body.related_line || ''
    }

    const sql_userManagement = new sqlexec()
    sql_userManagement.registerUser(userData, function (result) {
        if (result.success) {
            res.json({
                status: 200,
                message: "用户注册成功",
                data: {
                    employee_id: result.employee_id,
                    temporary_password: result.temporary_password
                }
            })
        } else {
            res.json({
                status: 500,
                message: "用户注册失败",
                error: result.error
            })
        }
    })
})

// 用户密码重置
router.post("/reset-password", (req, res) => {
    if (!req.body.employee_id) {
        return res.json({
            status: 400,
            message: "缺少员工ID参数"
        })
    }

    const employeeId = req.body.employee_id

    const sql_userManagement = new sqlexec()
    sql_userManagement.resetPassword(employeeId, function (result) {
        if (result.success) {
            res.json({
                status: 200,
                message: "密码重置成功",
                data: {
                    employee_id: result.employee_id,
                    new_password: result.new_password
                }
            })
        } else {
            res.json({
                status: 500,
                message: "密码重置失败",
                error: result.error
            })
        }
    })
})

// 用户信息更新
router.post("/update-info", (req, res) => {
    if (!req.body.employee_id || !req.body.department || !req.body.title) {
        return res.json({
            status: 400,
            message: "缺少必要参数"
        })
    }

    const userData = {
        employee_id: req.body.employee_id,
        department: req.body.department,
        title: req.body.title
    }

    const sql_userManagement = new sqlexec()
    sql_userManagement.updateUserInfo(userData, function (result) {
        if (result.success) {
            res.json({
                status: 200,
                message: "用户信息更新成功"
            })
        } else {
            res.json({
                status: 500,
                message: "用户信息更新失败",
                error: result.error
            })
        }
    })
})

// 获取权限组列表
router.get("/access-groups", (req, res) => {
    const sql_userManagement = new sqlexec()
    sql_userManagement.getAccessGroups(function (result) {
        if (result.success) {
            res.json({
                status: 200,
                data: result.data
            })
        } else {
            res.json({
                status: 500,
                message: "获取权限组列表失败",
                error: result.error
            })
        }
    })
})

// 获取用户权限
router.get("/user-access", (req, res) => {
    if (!req.query.employee_id) {
        return res.json({
            status: 400,
            message: "缺少员工ID参数"
        })
    }

    const employeeId = req.query.employee_id

    const sql_userManagement = new sqlexec()
    sql_userManagement.getUserAccess(employeeId, function (result) {
        if (result.success) {
            res.json({
                status: 200,
                data: result.data
            })
        } else {
            res.json({
                status: 500,
                message: "获取用户权限失败",
                error: result.error
            })
        }
    })
})

// 设置用户权限
router.post("/set-access", (req, res) => {
    if (!req.body.employee_id || !req.body.employee_name || !req.body.access_group || !req.body.app_name) {
        return res.json({
            status: 400,
            message: "缺少必要参数"
        })
    }

    const accessData = {
        employee_id: req.body.employee_id,
        employee_name: req.body.employee_name,
        access_group: req.body.access_group,
        app_name: req.body.app_name
    }

    const sql_userManagement = new sqlexec()
    sql_userManagement.setUserAccess(accessData, function (result) {
        if (result.success) {
            if (result.exists) {
                res.json({
                    status: 200,
                    message: "权限已存在"
                })
            } else {
                res.json({
                    status: 200,
                    message: "用户权限设置成功"
                })
            }
        } else {
            res.json({
                status: 500,
                message: "用户权限设置失败",
                error: result.error
            })
        }
    })
})

// 移除用户权限
router.post("/remove-access", (req, res) => {
    if (!req.body.access_link_id) {
        return res.json({
            status: 400,
            message: "缺少权限关联ID参数"
        })
    }

    const accessLinkId = req.body.access_link_id

    const sql_userManagement = new sqlexec()
    sql_userManagement.removeUserAccess(accessLinkId, function (result) {
        if (result.success) {
            res.json({
                status: 200,
                message: "用户权限移除成功"
            })
        } else {
            res.json({
                status: 500,
                message: "用户权限移除失败",
                error: result.error
            })
        }
    })
})

// 用户列表获取
router.get("/users", (req, res) => {
    const sql_userManagement = new sqlexec()
    sql_userManagement.getUsers(function (result) {
        if (result.success) {
            res.json({
                status: 200,
                data: result.data
            })
        } else {
            res.json({
                status: 500,
                message: "获取用户列表失败",
                error: result.error
            })
        }
    })
})

module.exports = router