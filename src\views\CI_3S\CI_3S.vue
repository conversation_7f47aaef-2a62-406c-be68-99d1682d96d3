<template>
    <!-- 写HTML -->
    <base-content>
        <q-dialog v-model="prompt" full-width>
            <q-card>
                <q-card-section>
                    <CustomComponents moduleFunction="CI3S_View" :opType="opType" :linkid="selectedlinkid" />
                </q-card-section>
            </q-card>
        </q-dialog>





        <div class="q-pa-md">
            <div class="q-gutter-y-md">
                <q-card>
                    <!-- 使用标准的Quasar标签栏，与点子银行保持一致 -->
                    <q-tabs v-model="tab" inline-label outside-arrows mobile-arrows align="justify"
                        class="bg-primary text-white shadow-2">
                        <q-tab name="input" icon="edit" label="AM录入" />
                        <q-tab name="query" icon="search" label="AM查询" />
                        <q-tab name="approve" icon="check_circle" label="AM审批" />
                    </q-tabs>
                    <q-separator />

                    <q-tab-panels v-model="tab" animated>
                            <q-tab-panel name="input">
                                <CustomComponents moduleFunction="CI3S_View" opType="新增" linkid="" />
                            </q-tab-panel>

                            <q-tab-panel name="query">
                                <div>
                                    <q-select outlined v-model="line" :options="lineList" label-slot clearable
                                        style="width: 200px;" dense @input="onLineChange">
                                        <template v-slot:label>机台
                                            <em class="q-px-sm bg-primary text-white rounded-borders"
                                                style="font-size: 12px;">可选</em>
                                        </template>
                                    </q-select>
                                    <template v-if="rows">
                                        <transition appear enter-active-class="animated fadeIn"
                                            leave-active-class="animated fadeOut">
                                            <q-table :data="rows" row-key="name" :columns="columns"
                                                :pagination.sync="myPagination">
                                                <template v-slot:body="props">
                                                    <q-tr :props="props">
                                                        <q-td v-for="col in props.cols" :key="col.name" :props="props">
                                                            <div v-if="col.name == 'Report_URL'">

                                                                <!-- {{ props.rowIndex }} -->
                                                                <q-btn
                                                                    :color="props.row['Report_URL'] == null ? 'primary' : 'secondary'"
                                                                    :label="props.row['Report_URL'] == null ? '生成报告' : '查看报告'"
                                                                    @click="generatePPT(props.row['LinkID'], props.rowIndex)" />
                                                            </div>
                                                            <div v-else @click="rowDetail(props.row)">{{ col.value }}
                                                            </div>
                                                        </q-td>
                                                    </q-tr>
                                                </template>
                                            </q-table>
                                        </transition>

                                        <q-inner-loading :showing="visible" label="文档生成中，请稍等..." label-class="text-teal"
                                            label-style="font-size: 1.1em" />
                                    </template>
                                </div>
                            </q-tab-panel>

                            <q-tab-panel name="approve">
                                <div>
                                    <div v-if="role == '3S系统_审批'">
                                        <q-select outlined v-model="line" :options="lineList" label-slot clearable
                                            style="width: 200px;" dense @input="onLineChange">
                                            <template v-slot:label>机台
                                                <em class="q-px-sm bg-primary text-white rounded-borders"
                                                    style="font-size: 12px;">可选</em>
                                            </template>
                                        </q-select>
                                        <template v-if="rows">
                                            <transition appear enter-active-class="animated fadeIn"
                                                leave-active-class="animated fadeOut">
                                                <q-table :data="rows" row-key="name" @row-click="approverDetail" />
                                            </transition>

                                            <q-inner-loading :showing="visible" label="数据加载中，请稍等..."
                                                label-class="text-teal" label-style="font-size: 1.1em" />
                                        </template>
                                    </div>
                                    <div v-else>
                                        你没有权限,请联系DM Leader
                                    </div>
                                </div>
                            </q-tab-panel>
                        </q-tab-panels>
                </q-card>
            </div>
        </div>

        <div class="q-pa-md ">
        </div>

    </base-content>
</template>

<script>

import BaseContent from '../../components/BaseContent/BaseContent'
import CustomComponents from '../../components/componentsList/index.vue'
export default {
    name: 'Home',
    components: {
        BaseContent,
        CustomComponents
    },
    mounted() {
        // 初始化时先获取机台列表
        this.getLine()

        // 设置初始空数组，避免undefined
        this.rows = []

        // 初始加载时查询所有AM报告
        this.$nextTick(() => {
            if (this.tab === 'query') {
                this.query_CI_3S_Summary_All()
            }
        })
    },
    watch: {
        tab(newTab) {
            console.log('tab changed to:', newTab)

            // 确保在标签切换时UI保持稳定
            this.$nextTick(() => {
                // 处理审批标签权限
                if (newTab == 'approve') {
                    const roleArray = localStorage.getItem('user_role')
                    if (roleArray && (roleArray.includes('3S系统_审批') || roleArray.includes('Admin'))) {
                        console.log('用户具有3S系统_审批权限');
                        this.role = '3S系统_审批'

                        // 如果没有选择机台，则查询所有报告
                        if (!this.line) {
                            this.query_CI_3S_Summary_All()
                        }
                    } else {
                        console.log('用户没有3S系统_审批权限');
                        this.role = ''
                    }
                } else if (newTab == 'query') {
                    // 切换到查询标签时，如果没有选择机台，则查询所有报告
                    if (!this.line) {
                        this.query_CI_3S_Summary_All()
                    }
                }
            })
        }
    },

    data() {
        return {
            tab: 'query',
            line: '',
            lineList: [], // 初始化为空数组，从API获取最新数据
            prompt: false,
            rows: [],
            opType: '新增',
            selectedlinkid: '',
            role: '',
            visible: false,
            myPagination: { rowsPerPage: 0 },
            columns: [
                {
                    name: 'Report_URL',
                    label: '报告',
                    field: 'Report_URL',
                    align: 'left',
                    sortable: true,
                },
                {
                    name: 'LinkID',
                    label: 'LinkID',
                    field: 'LinkID',
                    align: 'left',
                    sortable: true,
                },
                {
                    name: 'Date',
                    label: 'Date',
                    field: 'Date',
                    align: 'left',
                    sortable: true,
                },
                {
                    name: 'Line',
                    label: 'Line',
                    field: 'Line',
                    align: 'left',
                    sortable: true,
                },
                {
                    name: 'Crew',
                    label: 'Crew',
                    field: 'Crew',
                    align: 'left',
                    sortable: true,
                },
                {
                    name: 'Unit',
                    label: 'Unit',
                    field: 'Unit',
                    align: 'left',
                    sortable: true,
                },
                {
                    name: 'Start_Time',
                    label: 'Start_Time',
                    field: 'Start_Time',
                    align: 'left',
                    sortable: true,
                },
                {
                    name: 'End_Time',
                    label: 'End_Time',
                    field: 'End_Time',
                    align: 'left',
                    sortable: true,
                },
                {
                    name: 'Approver',
                    label: 'Approver',
                    field: 'Approver',
                    align: 'left',
                    sortable: true,
                },
                {
                    name: 'Process_Status',
                    label: 'Process_Status',
                    field: 'Process_Status',
                    align: 'left',
                    sortable: true,
                },
            ],

        }
    },
    methods: {
        showPrompt() {
            this.prompt = true
        },
        async getLine() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`approve/getLine?mill=${localStorage.getItem('mill')}`)
            console.log('line', res)
            // 确保正确更新机台列表
            this.lineList = res
        },
        async query_CI_3S_Summary() {
            var _this = this
            _this.visible = true // 显示加载状态

            try {
                const {
                    data: res
                } = await _this.$http.get(`ci/query_CI_3S_Summary?line=${_this.line}`)
                console.log('query_CI_3S_Summary', res)

                let tableData = []
                if (res.length > 0) {
                    for (let i = 0; i < res.length; i++) {
                        if (_this.tab == 'approve') {
                            if (res[i]['Process_Status'] != 'Approved') {
                                tableData.push(res[i])
                            }
                        } else {
                            tableData.push(res[i])
                        }
                    }
                }

                // 使用空数组而不是false，避免UI闪烁
                _this.rows = tableData
                console.log('_this.rows', tableData)
            } catch (error) {
                console.error('查询出错:', error)
                _this.$q.notify({
                    type: 'negative',
                    message: '查询数据失败，请稍后重试',
                    position: 'top'
                })
                _this.rows = [] // 出错时设置为空数组
            } finally {
                _this.visible = false // 无论成功失败都关闭加载状态
            }
        },

        async query_CI_3S_Summary_All() {
            var _this = this
            _this.visible = true // 显示加载状态

            try {
                const {
                    data: res
                } = await _this.$http.get(`ci/query_CI_3S_Summary_All`)
                console.log('query_CI_3S_Summary_All', res)

                let tableData = []
                if (res.length > 0) {
                    for (let i = 0; i < res.length; i++) {
                        if (_this.tab == 'approve') {
                            if (res[i]['Process_Status'] != 'Approved') {
                                tableData.push(res[i])
                            }
                        } else {
                            tableData.push(res[i])
                        }
                    }
                }

                // 使用空数组而不是false，避免UI闪烁
                _this.rows = tableData
                console.log('_this.rows', tableData)
            } catch (error) {
                console.error('查询出错:', error)
                _this.$q.notify({
                    type: 'negative',
                    message: '查询数据失败，请稍后重试',
                    position: 'top'
                })
                _this.rows = [] // 出错时设置为空数组
            } finally {
                _this.visible = false // 无论成功失败都关闭加载状态
            }
        },

        onLineChange(value) {
            if (value) {
                // 如果选择了机台，则按机台筛选
                this.query_CI_3S_Summary()
            } else {
                // 如果清除了机台选择，则查询所有报告
                this.query_CI_3S_Summary_All()
            }
        },
        async generatePPT(linkid, rowIndex) {
            var _this = this
            if (_this.rows[rowIndex]['Report_URL'] != null) {
                window.open(_this.rows[rowIndex]['Report_URL'])
                return
            }

            _this.visible = true
            const {
                data: res
            } = await _this.$http.get(`ci/3S_generatePPT?linkid=${linkid}`)
            console.log('3S_generatePPT', res)
            if (res.status == 'Success') {
                _this.rows[rowIndex]['Report_URL'] = res['fileURL']
                _this.visible = false
                _this.$q.notify({
                    type: 'positive',
                    message: '报告生成成功,报告将自动下载！',
                    position: 'top'
                })
                window.open(res['fileURL'])

            }

        },
        rowDetail(row) {
            console.log('row', row)
            this.selectedlinkid = row['LinkID']
            this.opType = '查询'
            this.prompt = true
        },
        approverDetail(_, row) {
            // 使用下划线表示未使用的参数
            console.log('row', row)
            this.selectedlinkid = row['LinkID']
            this.opType = 'Approver'
            this.prompt = true
        }





    }

}
</script>

<style>
.black_border {
    border: 1px solid black;
}

/* 确保加载状态不影响布局 */
.q-inner-loading {
    background: rgba(255, 255, 255, 0.6) !important;
}
</style>
