<template>
	<div v-if="moduleFunction == 'awardMonthlyApproval'" class="text-h5">
		<awardDetail :rows="rows" />
	</div>

	<div v-else-if="moduleFunction == 'awardPerformanceDaily'" class="text-h5">
		<awardPerformanceDaily />
	</div>

	<div v-else-if="moduleFunction == 'awardApplicationDaily'" class="text-h5">
		<awardApplicationDaily />
	</div>
	<div v-else-if="moduleFunction == 'awardApplicationMonthly'" class="text-h5">
		<awardApplicationMonthly />
	</div>
	<div v-else-if="moduleFunction == 'awardApplicationMonthlyByMaintence'" class="text-h5">
		<awardApplicationMonthlyByMaintence />
	</div>
	<div v-else-if="moduleFunction == 'CI3S_View'" class="text-h5">
		<CI3S_View :opType="opType" :linkid="linkid" />
	</div>
	<div v-else-if="moduleFunction == 'Problem_Definition'" class="text-h5">
		<CI_Problem_Definition :opType="opType" :linkid="linkid" />
	</div>
	<div v-else-if="moduleFunction == 'Working_Principle'" class="text-h5">
		<CI_Work_Principle :opType="opType" :linkid="linkid"  />
	</div>
</template>

<script>
import awardDetail from './awardDetail/awardDetailComponent.vue'
import awardPerformanceDaily from './awardPerformanceDaily/awardPerformanceDaily.vue'
import awardApplicationDaily from './awardApplicationDaily/awardApplicationDaily.vue'
import awardApplicationMonthly from './awardApplicationMonthly/awardApplicationMonthly.vue'
import awardApplicationMonthlyByMaintence from './awardApplicationMonthly/awardApplicationMonthlyByMaintence.vue'
import CI3S_View from './CI3S_View/CI3S_View.vue'
import CI_Problem_Definition from './CIProjectComponents/Problem_Definition.vue'
import CI_Work_Principle from './CIProjectComponents/Work_Principle.vue'

export default {
	components: {
		awardDetail,
		awardPerformanceDaily,
		awardApplicationDaily,
		awardApplicationMonthly,
		awardApplicationMonthlyByMaintence,
		CI3S_View,
		CI_Problem_Definition,
		CI_Work_Principle
	},
	props: {
		moduleFunction: {
			type: String,
			default: '默认显示的信息'
			// require: true // 必填
		},
		rows: {
			type: Array,
			//require:true
		},
		opType: {
			type: String,
			default: '新增'
		},
		linkid: {
			type: String,
			default: ''
		}
		// yearMonth: { type: String },
		// line: { type: String },
		// department: { type: String },
	},
	mounted() {
		//console.log('子组件收到的数据:', this.moduleFunction, this.rows)
		console.log('父组件收到的数据:', this.opType)
		console.log('父组件收到的数据:', this.linkid)
	},

	data() {
		return {
			parentMessage: '我是来自父组件的消息',
		}
	},

	methods: {

	}
}
</script>