const _Base = require('../config/dbbase_mes')

function sqlexec() {
  _Base.call(this);

}

sqlexec.prototype = new _Base()


sqlexec.prototype.getLine = function (callBack) {
  sqlexec.prototype.
    _query("select Path_Desc from Prdexec_Paths where path_Desc not like '%Rework' order by Path_Desc", function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}


sqlexec.prototype.getGCData = function (line, callBack) {
  sqlexec.prototype.
    _query(`select c.path_desc,b.prod_code SKU,a.BOM_Formulation_Id BOM,a.forecast_start_date,
    concat(b.prod_code,'-',a.BOM_Formulation_Id) SKU_BOMID
    from production_plan as a 
    left join products as b on a.prod_id=b.prod_id
    left join Prdexec_Paths as c on a.path_id=c.path_id
    where datediff(hour,getdate(),a.Forecast_Start_Date)between -168 and 48 and a.pp_status_id in (1,2,3,9)
    and a.process_order like '11%'and c.path_desc='${line}' order by c.path_desc,a.forecast_start_date`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.SKUList = function (line, callBack) {
  sqlexec.prototype.
    _query(`select c.path_desc,b.prod_code SKU,a.BOM_Formulation_Id BOM,
      concat(b.prod_code,' - ',(concat(CONVERT(varchar(5),dateadd(hour,8,a.Forecast_Start_Date),101),
      ' ',CONVERT(varchar(5),dateadd(hour,8,a.Forecast_Start_Date),108)))) label 
      from production_plan as a 
      left join products as b on a.prod_id=b.prod_id 
      left join Prdexec_Paths as c on a.path_id=c.path_id 
      where datediff(hour,getdate(),a.Forecast_Start_Date)between -72 and 48 
      and a.pp_status_id in (1,2,3,9) and a.process_order like '11%'
      and c.path_desc=${line} order by a.forecast_start_date`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.SKU1 = function (line, callBack) {
  sqlexec.prototype.
    _query(`select c.path_desc,b.prod_code SKU1,a.BOM_Formulation_Id BOM,
      concat(b.prod_code,'-',(concat(CONVERT(varchar(5),dateadd(hour,8,a.Forecast_Start_Date),101),
	  ' ',CONVERT(varchar(5),dateadd(hour,8,a.Forecast_Start_Date),108)))) label 
      from production_plan as a 
      left join products as b on a.prod_id=b.prod_id 
      left join Prdexec_Paths as c on a.path_id=c.path_id 
      where a.process_order like '11%'and a.PP_Status_Id=3 and c.Path_Desc=${line} `, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.SKU2 = function (line, callBack) {
  sqlexec.prototype.
    _query(`select c.path_desc,b.prod_code SKU2,a.BOM_Formulation_Id BOM,
      concat(b.prod_code,'-',(concat(CONVERT(varchar(5),dateadd(hour,8,a.Forecast_Start_Date),101),
	  ' ',CONVERT(varchar(5),dateadd(hour,8,a.Forecast_Start_Date),108)))) label 
      from production_plan as a 
      left join products as b on a.prod_id=b.prod_id 
      left join Prdexec_Paths as c on a.path_id=c.path_id
      where a.process_order like '11%'and a.PP_Status_Id=2 and c.Path_Desc=${line} `, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.BOM1List = function (BOM1, BOM2, callBack) {
  sqlexec.prototype.
    _query(`select distinct a.RM1 换型前料号, a.Desc1 换型前原料描述
      from (select b.prod_code RM1, b.prod_desc Desc1,c.Eng_Unit_Code 单位, 
      round((a.Quantity/d.Forecast_Quantity/(1+a.scrap_factor/100))*(d.Forecast_Quantity-d.Actual_Good_Quantity),3) 剩余需求量
      from bill_of_material_formulation_item as a
      left join products as b on a.prod_id=b.Prod_Id
      left join Engineering_Unit as c on a.Eng_Unit_Id=c.Eng_Unit_Id
      left join production_plan as d on a.BOM_Formulation_Id=d.BOM_Formulation_Id
      where a.BOM_Formulation_Id=${BOM1}) as a
      left join (select b.prod_code RM2, b.prod_desc Desc2 from bill_of_material_formulation_item as a
      left join products as b on a.prod_id=b.Prod_Id 
      where a.BOM_Formulation_Id=${BOM2}) as b on a.RM1=b.RM2
      where b.RM2 is null and a.RM1 not like '146%' order by a.Desc1`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.BOM2List = function (BOM1, BOM2, callBack) {
  sqlexec.prototype.
    _query(`select distinct b.RM2 换型后料号, b.Desc2 换型后原料描述
      from (select b.prod_code RM2, b.prod_desc Desc2 from bill_of_material_formulation_item as a
      left join products as b on a.prod_id=b.Prod_Id 
      where a.BOM_Formulation_Id=${BOM2}) as b
      left join (select b.prod_code RM1, b.prod_desc Desc1 from bill_of_material_formulation_item as a
      left join products as b on a.prod_id=b.Prod_Id
      where a.BOM_Formulation_Id=${BOM1}) as a on a.RM1=b.RM2
      where a.RM1 is null and b.RM2 not like '146%' order by b.Desc2`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.SameBOMList = function (BOM1, BOM2, callBack) {
  sqlexec.prototype.
    _query(`select distinct b.RM2 共用原料号, b.Desc2 共用原料描述
from (select b.prod_code RM2, b.prod_desc Desc2 from bill_of_material_formulation_item as a
left join products as b on a.prod_id=b.Prod_Id 
where a.BOM_Formulation_Id=${BOM2}) as b
left join (select b.prod_code RM1, b.prod_desc Desc1 from bill_of_material_formulation_item as a
left join products as b on a.prod_id=b.Prod_Id
where a.BOM_Formulation_Id=${BOM1}) as a on a.RM1=b.RM2
where a.RM1 is not null and b.RM2 not like '146%' order by b.Desc2`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.GCPOData = function (BOM, callBack) {
  sqlexec.prototype.
    _query(`select b.prod_code SKU,
case when left (b.prod_desc,3) in ('100','140') then substring(b.Prod_desc,charindex(':',b.prod_desc)+1,30)
else left(b.prod_desc,30) end SKU_Desc,
CONVERT(varchar(16),dateadd(hour,8,a.Forecast_Start_Date),20) date,
a.Process_Order Order_NO,a.Forecast_Quantity QTY,
Case when DATEPART(hh,A.Forecast_Start_Date)>=0 and DATEPART(hh,A.Forecast_Start_Date)<12 then '白班'
 else '夜班' 
 end Shift,dateadd(hour,8,a.Forecast_Start_Date) forecast_date,
case when a.Process_Order like '20%' then 'RSR'
else 'Prod' end Type
from production_plan as a
left join products as b on a.prod_id=b.prod_id
where BOM_Formulation_Id=${BOM}`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}


sqlexec.prototype.PalletSKU_t = function (Line, callBack) {
  sqlexec.prototype.
    _query(`select c.prod_code
      from production_plan as a 
      left join Production_Plan_statuses as b on a.pp_status_id=b.pp_status_id 
      left join products as c on a.prod_id=c.Prod_Id
      left join Prdexec_Paths as d on a.path_id=d.path_id
      where PP_status_desc in ('Active','Run Out') and d.Path_Desc=${Line}`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

sqlexec.prototype.MESCaseQTY = function (Line, SKU, Date1, Shift, callBack) {
  console.log('MESCaseQTY参数', Line, SKU, Date1, Shift)
  var sqlStr = ''
  sqlStr = `select substring(b.PU_Desc_Local,1,4) Line,
  CONVERT(varchar(15), c.timestamp, 23) date,e.shift_desc,f.prod_code,
  sum(a.initial_dimension_x) MES_Case_QTY,
  case when d.prodstatus_desc='Inventory' then sum (a.initial_dimension_x)
  else 0 end MES_FG_Case,
  case when d.prodstatus_desc='Hold' then sum (a.initial_dimension_x)
  else 0 end MES_SEMI_Case
  from Event_Details as a
  left join Prod_Units as b on a.PU_Id=b.PU_Id
  left join events as c on a.Event_Id=c.Event_Id
  left join production_status as d on c.event_status= d.prodstatus_id
  Left join crew_schedule as e on a.pu_id=e.pu_id and c.timestamp between e.start_time and e.end_time
  left join products as f on c.applied_product=f.prod_id
  where  b.PU_Desc_Local like '%Palletizer%' and substring(b.PU_Desc_Local,1,4)=${Line} and 
  and CONVERT(varchar(15), c.timestamp, 23)=${Date1}  and Shift_Desc=${Shift}  and Prod_Code=${SKU} 
  GROUP BY substring(b.PU_Desc_Local,1,4),
  CONVERT(varchar(15), c.timestamp, 23),e.shift_desc,f.prod_code,d.prod_status desc`
  console.log(sqlStr)
  sqlexec.prototype.
    _query(sqlStr, function (err, result) {
      if (err) {
      }
      console.log(result.recordset)
      return callBack(result.recordset)
    })
}
sqlexec.prototype.Centerline = function (Line, Start, End, callBack) {
  console.log('Centerline参数', Line, Start, End)
  var sqlStr = ''
  sqlStr = `select Date_1,Shift,ID,Line,Round_Type,start_time_CN,end_time_CN,Var_Id,Var_Desc,Result,
  Entery_On_CN,Prod_Code,Process_Order,L_Reject_M,Target,U_Reject_M ,
  --如果处理后的下限&上限是空或者下限为-9999或斜杠，则此项无需点检，Total_Count为0，否则为1
  Case when process_order is null and (L_Reject_M is null and U_Reject_M IS NULL) OR (L_Reject_M IN ('-9999','/','\') and U_Reject_M IN ('9999','/','\')) Then 0 else 1 end Total_Count,
  --如果处理后的下限&上限不是-9999或斜杠，则判断实际值是不是在上下限之间，是则为0，不是为1
  Case When (L_Reject_M NOT IN ('-9999','/','\') and U_Reject_M NOT IN ('9999','/','\') )
  then case when cast(Result as float)<cast(L_Reject_M as float) or cast(Result as float)>cast(U_Reject_M as float) or Result is null  
     then 1 else 0 end else 0 end Alarm
  from (select Date_1,Shift,ID,Line,Round_Type,start_time_CN,end_time_CN,Var_Id,Var_Desc,Result,Entery_On_CN,Prod_Code,Process_Order,
          Case when Sum_L<>0 and L_Reject is null and U_Reject is null and Target is null then '-9999' else L_Reject end L_Reject_M,Target,
          Case when Sum_L<>0 and L_Reject is null and U_Reject is null and Target is null then '9999' else U_Reject end U_Reject_M 
          from (select distinct CONVERT(varchar(100), g.Start_Time, 23) Date_1, 
                  Case when DATEPART(hh,g.start_time)>=0 and DATEPART(hh,g.start_time)<12 then '白班' else '夜班' end Shift,
                  ROW_NUMBER() OVER (PARTITION BY a.pu_desc, g.UDE_Desc, g.start_time_CN ORDER BY g.start_time_CN ASC) AS ID,
                  left(a.pu_desc,4) Line,g.UDE_Desc Round_Type,g.start_time_CN,g.end_time_CN,a.Var_Id,a.Var_Desc,
                  case when b.Result is null then null 
                  else case when ISNUMERIC(b.Result)=1 then b.Result 
                          else '9998' end end as Result,
                  dateadd(hour,8,b.Entry_On) Entery_On_CN,
                  e.Prod_Code,d.process_order,f.L_Reject,f.Target,f.U_Reject,
                  --如果下限不为空那么判断下限是不是数字，是数字则取下限值和，若不是数字则为0,然后按产线，点检频次和round开始时间为一组对其求和
                  sum(cast(case when f.L_Reject is not null  then case when ISNUMERIC(f.L_Reject) = 1 Then L_Reject else '0' end 
                          else '0' end as float)) 
                      OVER (PARTITION BY a.pu_desc, g.UDE_Desc, g.start_time_CN ORDER BY g.start_time_CN ASC) as Sum_L
                  from (select distinct a.Var_id,a.Eng_Units,d.var_id Pvar_id,d.Var_Desc,C.PU_Desc,a.PU_Id,e.PUG_Desc
                          from Variables_Base as a
                          left join PU_Groups as b on a.pu_id=b.PU_Id
                          left join Prod_Units_Base as c on a.PU_Id=c.PU_Id
                          left join Variables_Base as d on a.PVar_Id=d.Var_Id
                          left join PU_Groups as e on a.PUG_Id=e.PUG_Id
                          where a.DS_id=50012 and a.PVar_Id is not null and a.Is_Active=1 
                          and b.PUG_Desc LIKE '%Centerline%' and c.PU_Desc like 'ND%Centerline%' and c.PU_Desc not like 'ND04%') as a
                  left join (select PU_ID,UDE_Desc,start_time,
                              case when UDE_Desc='Every Shift' then  dateadd(hour,12,End_Time) else dateadd(hour,1,End_Time) end  End_time,
                              dateadd(hour,8,start_time) start_time_CN, 
                              case when UDE_Desc='Every Shift' then dateadd(hour,20,End_Time) else dateadd(hour,9,End_Time) end  end_time_CN 
                              from  User_Defined_Events 
                              where UDE_Desc like '%Event%Trigger%' or UDE_Desc like 'Every%' and PU_ID<>32) as g 
                  on a.PU_Id=g.PU_Id and a.PUG_desc like '%'+g.UDE_Desc+'%'
                  left join tests as b on a.Var_Id=b.Var_Id and b.Entry_On between g.Start_Time and g.End_time
                  left join Production_Plan_Starts as c on a.pu_id=c.pu_id and g.Start_Time between c.Start_Time and c.End_Time
                  left join production_plan as d on c.PP_Id=d.PP_Id
                  left join products as e on d.Prod_Id=e.Prod_Id
                  left join (select var_id,prod_id,L_Reject,Target,U_Reject,Effective_Date,Expiration_Date from var_specs ) as f 
                  on a.Pvar_id=f.Var_Id and d.Prod_Id=f.Prod_Id 
                  and b.Entry_On between f.Effective_Date and 
                  case when f.expiration_date is null then getdate() else f.expiration_date end 
                  where g.Start_Time>=${Start} and g.Start_Time<${End} and left(a.pu_desc,4)=${Line} and d.process_order is not null)as t1) as t2
  group by Date_1,Shift,ID,Line,Round_Type,start_time_CN,end_time_CN,Var_Id,Var_Desc,Result,Entery_On_CN,Prod_Code,Process_Order,L_Reject_M,Target,U_Reject_M 
  order by Date_1,Line,shift,Round_type,start_time_CN,ID asc`
  console.log(sqlStr)
  sqlexec.prototype.
    _query(sqlStr, function (err, result) {
      if (err) {
      }
      console.log(result.recordset)
      return callBack(result.recordset)
    })
}

sqlexec.prototype.ShiftChange_MESKPI = function (Line1, Line2, Start, End, callBack) {
  console.log('ShiftChange_MESKPI', Line1, Line2, Start, End)
  var sqlStr = ''
  sqlStr = `select a.Date,a.Line,a.Shift,a.SKU,a.Total_Cuts,
  c.Target_Speed,d.FG,d.SEMI_FG,d.FG*e.Target FG_Cut
  from (select convert(varchar(100),c.timestamp,23) Date, substring(b.PU_Desc_Local,1,4) Line,
      Case when DATEPART(hh,c.TIMESTAMP)>=0 and DATEPART(hh,c.TIMESTAMP)<12 then 'Day'
      else 'Night'  end Shift,f.Prod_Code SKU,
      sum(a.Initial_Dimension_X) Total_Cuts
      from [UMDB].[dbo].[Event_Details] as a
      left join [UMDB].[dbo].Prod_Units as b on a.PU_Id=b.PU_Id
      left join [UMDB].[dbo].events as c on a.Event_Id=c.Event_Id
      left join production_plan_starts  as d
      on a.PU_Id=d.PU_Id 	and c.timeStamp>= d.Start_Time and c.timeStamp<
      case when d.End_Time is null then getdate() else d.end_time end
      left join production_plan as e on d.PP_Id=e.pp_id
      left join products as f on e.Prod_Id=f.prod_id
      where PU_Desc_Local like ${Line1} 
      and c.TimeStamp>=${Start} AND c.TimeStamp<=${End} and a.Initial_Dimension_X is not null
      and e.Process_Order like '11%'
      group by convert(varchar(100),c.timestamp,23), substring(b.PU_Desc_Local,1,4),
      Case when DATEPART(hh,c.TIMESTAMP)>=0 and DATEPART(hh,c.TIMESTAMP)<12 then 'Day'
      else 'Night'  end,f.Prod_Code) as a 
  left join( select distinct a.Char_Id,left(b.char_desc,4) Line,right(b.Char_Desc,8) SKU,a.Target Target_Speed,a.Effective_Date 
        from Active_Specs as a
        left join Characteristics as b on a.Char_Id=b.Char_Id
        where a.Spec_Id=4418 and a.Expiration_Date is null) as c on a.SKU=c.SKU and a.Line=c.Line
  left join(select Date,Line,Shift,SKU,sum(FG) FG,SUM(SEMI_FG) SEMI_FG 
        FROM(select convert(varchar(100),c.timestamp,23) Date, substring(b.PU_Desc_Local,1,4) Line,
          Case when DATEPART(hh,c.TIMESTAMP)>=0 and DATEPART(hh,c.TIMESTAMP)<12 then 'Day'
          else 'Night'  end Shift, f.Prod_Code SKU,
          case when g.prodstatus_desc='Inventory' then sum(a.Initial_Dimension_X) else '0' end FG,
          case when g.prodstatus_desc in ('Hold','In Progress') then sum(a.Initial_Dimension_X) else '0' end SEMI_FG
          from [CNNBAS70].[UMDB].[dbo].[Event_Details] as a
          left join [CNNBAS70].[UMDB].[dbo].Prod_Units as b on a.PU_Id=b.PU_Id
          left join [CNNBAS70].[UMDB].[dbo].events as c on a.Event_Id=c.Event_Id
          left join [CNNBAS70].[UMDB].[dbo].production_status as g on c.event_status= g.prodstatus_id
          left join production_plan_starts  as d
          on a.PU_Id=d.PU_Id 	and c.timeStamp>= d.Start_Time and c.timeStamp<
          case when d.End_Time is null then getdate() else d.end_time end
          left join production_plan as e on d.PP_Id=e.pp_id
          left join products as f on e.Prod_Id=f.prod_id
          where b.PU_Desc_Local like ${Line2} 
          and c.TimeStamp>=${Start} AND c.TimeStamp<${End} 
          and e.Process_Order like '11%'
          group by convert(varchar(100),c.timestamp,23), substring(b.PU_Desc_Local,1,4),
          Case when DATEPART(hh,c.TIMESTAMP)>=0 and DATEPART(hh,c.TIMESTAMP)<12 then 'Day'
          else 'Night'  end, f.Prod_Code,g.prodstatus_desc)as t
          group by  Date,Line,Shift,SKU) as d on a.Date=d.Date and a.Line=d.Line and a.Shift=d.Shift and a.SKU=d.SKU
  left join(select distinct a.Char_Id,b.char_desc SKU,a.Target,a.Effective_Date 
        from Active_Specs as a
        left join Characteristics as b on a.Char_Id=b.Char_Id
        where a.Spec_Id=5975 and a.Expiration_Date is null) as e on a.SKU=E.SKU
        where a.Total_Cuts>0
        order by a.SKU `
  // console.log(sqlStr)
  sqlexec.prototype.
    _query(sqlStr, function (err, result) {
      if (err) {
      }
      console.log(result.recordset)
      return callBack(result.recordset)
    })
}

sqlexec.prototype.ShiftChange_MESWaste = function (Line, Start, End, callBack) {
  console.log('ShiftChange_MESWaste', Line, Start, End)
  var sqlStr = ''
  sqlStr = `select b.Date,b.Line,b.Shift,b.SKU,b.MWaste,b.QAWaste_Auto,b.QAWaste_Mua,b.PacWaste,b.ReWorkWaste,b.TotalWaste
  from (select Date,Line,Shift,SKU,sum(MWaste) MWaste,SUM(QAWaste_Auto) QAWaste_Auto,sum(QAWaste_Mua) QAWaste_Mua,sum(PacWaste) PacWaste,sum(ReWorkWaste)  ReWorkWaste,
        sum(MWaste)+SUM(QAWaste_Auto)+sum(QAWaste_Mua)+sum(PacWaste)+sum(ReWorkWaste) TotalWaste
        from (select convert(varchar(20),a.TimeStamp,23) Date,left(b.pu_desc,4) Line,
        case when DATEPART(hh,a.TimeStamp)>=0 and DATEPART(hh,a.TimeStamp)<12 then 'Day'else 'Night' end Shift,
        g.Prod_Code SKU,
        case when Event_Reason_Name_Local='机器排废' then sum(a.Amount) else '0' end MWaste,
        case when Event_Reason_Name_Local='质量废品' and d.WEFault_Value is not null  then sum(a.Amount) else '0' end QAWaste_Auto,
        case when Event_Reason_Name_Local='质量废品' and d.WEFault_Value is null  then sum(a.Amount) else '0' end QAWaste_Mua,
        case when Event_Reason_Name_Local='包装废品' then sum(a.Amount) else '0' end PacWaste,
        case when Event_Reason_Name_Local='返工废品' then sum(a.Amount) else '0' end ReWorkWaste
        from  Waste_Event_Details  as a
        left join Prod_Units_Base as b on a.pu_id=b.pu_id
        left join Event_Reasons as c1 on a.Reason_Level1=c1.Event_Reason_Id
        left join Waste_Event_Fault as d on d.WEFault_Id=a.WEFault_Id
        left join production_plan_starts as e on a.TimeStamp between e.Start_Time 
        and case when e.End_Time is null then getdate() else e.End_Time end 
        and a.PU_Id=e.PU_Id
        left join production_plan as f on e.PP_Id=f.PP_Id
        left join products as g on f.Prod_Id=g.Prod_Id
        where b.PU_Desc like ${Line}  and  a.TimeStamp >=${Start} and a.TimeStamp<${End}
        and f.process_order like '11%'
        group by convert(varchar(20),a.TimeStamp,23),left(b.pu_desc,4), 
        case when DATEPART(hh,a.TimeStamp)>=0 and DATEPART(hh,a.TimeStamp)<12 then 'Day'else 'Night' end,
        g.Prod_Code,Event_Reason_Name_Local, d.WEFault_Value) as t 
        group by  Date,Line,Shift,SKU)as b
        order by b.SKU`
  // console.log(sqlStr)
  sqlexec.prototype.
    _query(sqlStr, function (err, result) {
      if (err) {
      }
      console.log(result.recordset)
      return callBack(result.recordset)
    })
}

sqlexec.prototype.DailyCLKPI = function (Line, Start, End, callBack) {
  console.log('日报CL-KPI', Line, Start, End)
  var sqlStr = ''
  sqlStr = `select z.Date_1,z.Shift,z.Line,sum(z.Total_Count) Total,sum(z.Alarm) Alarm
  From (select Date_1,Shift,ID,Line,Round_Type,start_time_CN,end_time_CN,Var_Id,Var_Desc,Result,
  Entery_On_CN,Prod_Code,Process_Order,L_Reject_M,Target,U_Reject_M ,
  --如果处理后的下限&上限是空或者下限为-9999或斜杠，则此项无需点检，Total_Count为0，否则为1
  Case when process_order is null and (L_Reject_M is null and U_Reject_M IS NULL) OR (L_Reject_M IN ('-9999','/','\') and U_Reject_M IN ('9999','/','\')) Then 0 else 1 end Total_Count,
  --如果处理后的下限&上限不是-9999或斜杠，则判断实际值是不是在上下限之间，是则为0，不是为1
  Case When (L_Reject_M NOT IN ('-9999','/','\') and U_Reject_M NOT IN ('9999','/','\') )
  then case when cast(Result as float)<cast(L_Reject_M as float) or cast(Result as float)>cast(U_Reject_M as float) or Result is null  
     then 1 else 0 end else 0 end Alarm
  from (select Date_1,Shift,ID,Line,Round_Type,start_time_CN,end_time_CN,Var_Id,Var_Desc,Result,Entery_On_CN,Prod_Code,Process_Order,
          Case when Sum_L<>0 and L_Reject is null and U_Reject is null and Target is null then '-9999' else L_Reject end L_Reject_M,Target,
          Case when Sum_L<>0 and L_Reject is null and U_Reject is null and Target is null then '9999' else U_Reject end U_Reject_M 
          from (select distinct CONVERT(varchar(100), g.Start_Time, 23) Date_1, 
                  Case when DATEPART(hh,g.start_time)>=0 and DATEPART(hh,g.start_time)<12 then 'Day' else 'Night' end Shift,
                  ROW_NUMBER() OVER (PARTITION BY a.pu_desc, g.UDE_Desc, g.start_time_CN ORDER BY g.start_time_CN ASC) AS ID,
                  left(a.pu_desc,4) Line,g.UDE_Desc Round_Type,g.start_time_CN,g.end_time_CN,a.Var_Id,a.Var_Desc,
                  case when b.Result is null then null 
                  else case when ISNUMERIC(b.Result)=1 then b.Result 
                          else '9998' end end as Result,
                  dateadd(hour,8,b.Entry_On) Entery_On_CN,
                  e.Prod_Code,d.process_order,f.L_Reject,f.Target,f.U_Reject,
                  --如果下限不为空那么判断下限是不是数字，是数字则取下限值和，若不是数字则为0,然后按产线，点检频次和round开始时间为一组对其求和
                  sum(cast(case when f.L_Reject is not null  then case when ISNUMERIC(f.L_Reject) = 1 Then L_Reject else '0' end 
                          else '0' end as float)) 
                      OVER (PARTITION BY a.pu_desc, g.UDE_Desc, g.start_time_CN ORDER BY g.start_time_CN ASC) as Sum_L
                  from (select distinct a.Var_id,a.Eng_Units,d.var_id Pvar_id,d.Var_Desc,C.PU_Desc,a.PU_Id,e.PUG_Desc
                          from Variables_Base as a
                          left join PU_Groups as b on a.pu_id=b.PU_Id
                          left join Prod_Units_Base as c on a.PU_Id=c.PU_Id
                          left join Variables_Base as d on a.PVar_Id=d.Var_Id
                          left join PU_Groups as e on a.PUG_Id=e.PUG_Id
                          where a.DS_id=50012 and a.PVar_Id is not null and a.Is_Active=1 
                          and b.PUG_Desc LIKE '%Centerline%' and c.PU_Desc like 'ND%Centerline%' and c.PU_Desc not like 'ND04%') as a
                  left join (select PU_ID,UDE_Desc,start_time,
                              case when UDE_Desc='Every Shift' then  dateadd(hour,12,End_Time) else dateadd(hour,1,End_Time) end  End_time,
                              dateadd(hour,8,start_time) start_time_CN, 
                              case when UDE_Desc='Every Shift' then dateadd(hour,20,End_Time) else dateadd(hour,9,End_Time) end  end_time_CN 
                              from  User_Defined_Events 
                              where (UDE_Desc like '%Event%Trigger%' or UDE_Desc like 'Every%') and PU_ID<>32) as g 
                  on a.PU_Id=g.PU_Id and a.PUG_desc like '%'+g.UDE_Desc+'%'
                  left join tests as b on a.Var_Id=b.Var_Id and b.Entry_On between g.Start_Time and g.End_time
                  left join Production_Plan_Starts as c on a.pu_id=c.pu_id and g.Start_Time between c.Start_Time and c.End_Time
                  left join production_plan as d on c.PP_Id=d.PP_Id
                  left join products as e on d.Prod_Id=e.Prod_Id
                  left join (select var_id,prod_id,L_Reject,Target,U_Reject,Effective_Date,Expiration_Date from var_specs ) as f 
                  on a.Pvar_id=f.Var_Id and d.Prod_Id=f.Prod_Id 
                  and b.Entry_On between f.Effective_Date and 
                  case when f.expiration_date is null then getdate() else f.expiration_date end 
                  where g.Start_Time>=${Start} and g.Start_Time<${End} and left(a.pu_desc,4)=${Line} and d.process_order is not null)as t1) as t2
  group by Date_1,Shift,ID,Line,Round_Type,start_time_CN,end_time_CN,Var_Id,Var_Desc,Result,Entery_On_CN,Prod_Code,Process_Order,L_Reject_M,Target,U_Reject_M 
  ) as z group by z.Date_1,z.Shift,z.Line`
  // console.log(sqlStr)
  sqlexec.prototype.
    _query(sqlStr, function (err, result) {
      if (err) {
      }
      console.log(result.recordset)
      return callBack(result.recordset)
    })
}

sqlexec.prototype.RollWeight = function (Line, RM_Code, Start, End, callBack) {
  console.log('单卷重量SQL', Line, RM_Code, Start, End)
  var sqlStr = ''
  sqlStr = `select top 3 left(e.PU_Desc,4) Line,
left(b.Event_Num,8) RM_Code,f.initial_dimension_x RollQTY
from PrdExec_Input_Event_Transitions as a
left join events as b on a.Event_Id=b.Event_Id
left join event_details as f on a.Event_Id=f.Event_Id
left join PrdExec_Inputs as c on a.PEI_Id=c.PEI_Id
left join PrdExec_Input_Positions as d on a.PEIP_Id=d.PEIP_Id
left join Prod_Units as e on c.pu_id=e.PU_Id
where e.pu_desc like ${Line} and b.event_num not like 'MSCAN%'
and d.PEIP_Desc='Running'
and a.start_time between ${Start} and ${End}
and left(b.Event_Num,8)=${RM_Code}
order by a.Start_Time desc`
  console.log(sqlStr)
  sqlexec.prototype.
    _query(sqlStr, function (err, result) {
      if (err) {
      }
      console.log(result.recordset)
      return callBack(result.recordset)
    })
}


sqlexec.prototype.ActivePO = function (callBack) {
  var sqlStr = ''
  sqlStr = `select distinct b.Path_Desc line,e.prod_code SKU,
  a.Forecast_Quantity Plan_QTY,a.Forecast_Quantity-a.Actual_Good_Quantity Remain_QTY,
g.target 托容,f.target 箱容,y.target 目标RO,
Substring((CONVERT(varchar(100), dateadd(hour,8,dateadd(second,case when (a.Forecast_Quantity-a.Actual_Good_Quantity)>=0
Then (a.Forecast_Quantity*f.Target-a.Actual_Good_Quantity*f.Target)/1000/convert(float,y.Target)*60*60
else 0 end ,getdate())),20)),6,11) Est_Time,
case when left(a.Process_Order,2)=20 then 'RSR' else 'Prod' end Order_Type,a.Process_Order
from production_plan as a
left join Prdexec_Paths as b on a.Path_Id=b.Path_Id
left join products as e on a.prod_id=e.Prod_Id
--取托容Case/Pallet
LEFT JOIN (select a.Char_Id,b.Char_Desc,a.Target,a.Effective_Date from Active_Specs as a
			left join Characteristics as b on a.Char_Id=b.Char_Id
			where a.Spec_Id=4439 and a.Expiration_Date is null) as g on e.Prod_Code=g.Char_Desc
--取箱容Cuts/Case
left join (select a.Char_Id,b.Char_Desc,a.Target,a.Effective_Date from Active_Specs as a
			left join Characteristics as b on a.Char_Id=b.Char_Id
			where a.Spec_Id=5975 and a.Expiration_Date is null ) as f on e.Prod_Code=f.Char_Desc
--取产线目标RO
left join (select distinct a.Char_Id,b.Char_Desc,a.Target,a.Effective_Date from Active_Specs as a
			left join Characteristics as b on a.Char_Id=b.Char_Id
			where a.Spec_Id=4417 and a.Expiration_Date is null ) as y on b.Path_Desc=left(y.Char_Desc,4) and e.prod_code=right(y.Char_Desc,8)
where a.PP_Status_Id=3`
  console.log(sqlStr)
  sqlexec.prototype.
    _query(sqlStr, function (err, result) {
      if (err) {
      }
      console.log(result.recordset)
      return callBack(result.recordset)
    })
}

sqlexec.prototype.NextPO = function (callBack) {
  var sqlStr = ''
  sqlStr = `select c.Path_Desc line,
  case when d.PP_Status_Desc='Pending' Then '待定'
else case when d.PP_Status_Desc='Next' Then '下一个'
else '暂停' end end Status,
b.prod_code SKU,
case when a.Process_Order like '20%' then 'RSR'
else CAST(a.Forecast_Quantity AS VARCHAR(10)) end Plan_QTY,
substring((convert(varchar(50),dateadd(hour,8,a.Forecast_Start_Date),20)),6,11) Plan_Start
from production_plan as a
left join products as b on a.prod_id=b.prod_id
left join Prdexec_Paths as c on a.Path_Id=c.Path_Id
left join Production_Plan_statuses as d on a.pp_status_id=d.pp_status_id
where datediff(hour,getdate(),a.Forecast_Start_Date) between -24 and 48 and a.PP_Status_Id in (1,2,9)
order by c.Path_Desc,a.Forecast_Start_Date asc`
  console.log(sqlStr)
  sqlexec.prototype.
    _query(sqlStr, function (err, result) {
      if (err) {
      }
      console.log(result.recordset)
      return callBack(result.recordset)
    })
}

sqlexec.prototype.MWComment = function (Line, Start, End, callBack) {
  console.log('废品备注-SQL', Line, Start, End)
  var sqlStr = ''
  sqlStr = ` select left(c.pu_desc,4) Line,CONVERT(varchar(100), A.TimeStamp, 23) Date,
 case when DATEPART(hh,A.TimeStamp)>=0 and DATEPART(hh,A.TimeStamp)<12 then 'Day'
 else 'Night' end Shift,
 c1.Event_Reason_Name_Local Level1,c2.Event_Reason_Name_Local Level2,c3.Event_Reason_Name_Local Level3,
 a.Amount MW_QTY,
DATEADD(hour,8,a.TimeStamp) MW_Timestamp,
 d.Comment_Text Comment,e.username Account
 from Waste_Event_Details as a
 left join events as b on a.Event_Id=b.Event_Id
 left join Prod_Units as c on a.pu_id=c.PU_Id
 left join Comments as d on a.Cause_Comment_Id=d.Comment_Id
 left join Event_Reasons as c1 on a.Reason_Level1=c1.Event_Reason_Id
left join Event_Reasons as c2 on a.Reason_Level2=c2.Event_Reason_Id
left join Event_Reasons as c3 on a.Reason_Level3=c3.Event_Reason_Id
left join Users_Base as e on a.User_Id=e.user_id
left join production_plan_starts as f on a.pu_id=f.pu_id and a.TimeStamp between f.Start_Time and f.End_Time
left join production_plan as g on f.pp_id=g.pp_id
where A.TIMESTAMP>=${Start} and A.TIMESTAMP<=${End} and c.pu_desc like ${Line}
and a.Cause_Comment_Id is not null and g.Process_Order like '11%'
order by a.TimeStamp`
  console.log(sqlStr)
  sqlexec.prototype.
    _query(sqlStr, function (err, result) {
      if (err) {
      }
      console.log(result.recordset)
      return callBack(result.recordset)
    })
}

sqlexec.prototype.RSR_List = function (Line, Start, End, callBack) {
  console.log('RSR_List-SQL', Line, Start, End)
  var sqlStr = ''
  sqlStr = ` select a.pp_id,a.Process_Order RSR_Order,c.prod_code SKU_Code,c.prod_desc SKU_Desc,
CONVERT(varchar(100),a.Forecast_Start_Date,20) Plan_Start,CONVERT(varchar(100),a.Forecast_End_Date,20) Plan_End,
CONVERT(varchar(100),a.Actual_Start_Time,20) Act_Start,CONVERT(varchar(100),a.Actual_End_Time,20) Act_End,
round(cast(DATEDIFF(second,a.Actual_Start_Time,a.Actual_End_Time) as float)/3600,2) Act_Run_Time,
sum(g.Initial_Dimension_X) Act_Total_Cuts
from production_plan as a
left join Prdexec_Paths as b on a.path_id=b.path_id
left join products as c on a.prod_id=c.prod_id
left join production_plan_starts as d on a.pp_id=d.pp_id
left join prod_units as e on d.pu_id=e.pu_id
left join events as f on d.pu_id=f.pu_id and f.TimeStamp>=d.Start_Time and f.TimeStamp<=d.End_Time
left join Event_Details as g on f.Event_Id=g.Event_Id
where b.Path_Desc like ${Line} and Actual_Start_Time between ${Start} and ${End}
and a.Process_Order not like '11%' and e.pu_desc like '%Convert%'
group by a.pp_id,a.Process_Order,c.prod_code,c.prod_desc,
CONVERT(varchar(100),a.Forecast_Start_Date,20),CONVERT(varchar(100),a.Forecast_End_Date,20),
CONVERT(varchar(100),a.Actual_Start_Time,20),CONVERT(varchar(100),a.Actual_End_Time,20),
round(cast(DATEDIFF(second,a.Actual_Start_Time,a.Actual_End_Time) as float)/3600,2)`
  console.log(sqlStr)
  sqlexec.prototype.
    _query(sqlStr, function (err, result) {
      if (err) {
      }
      console.log(result.recordset)
      return callBack(result.recordset)
    })
}

sqlexec.prototype.RSR_BOM_Detail = function (pp_id, callBack) {
  sqlexec.prototype.
    _query(`select distinct h.pp_id,
f.prod_code as RM_Code,
f.prod_Desc_local as RM_Desc,
round((e.quantity/(1+e.scrap_factor/100))/c.standard_quantity*1000*i.Target,2) as BOM_Usage,
g.eng_unit_CODE as Unit,
e.scrap_factor as Scrap
from Production_Plan as h
left join products as b on h.prod_id=b.prod_id
left join bill_of_material_formulation as c on c.BOM_Formulation_Id= h.BOM_Formulation_Id
left join Bill_Of_Material as a on a.BOM_Id=c.BOM_Id
LEFT JOIN Engineering_Unit as d on c.eng_unit_id=d.eng_unit_id
left join bill_of_material_formulation_item as e on c.bom_formulation_id=e.bom_formulation_id
left join products as f on e.prod_id=f.prod_id
left join Engineering_Unit as g on e.eng_unit_id=g.eng_unit_id
--取箱容SU/Case
left join (select a.Char_Id,b.Char_Desc,a.Target,a.Effective_Date from Active_Specs as a
			left join Characteristics as b on a.Char_Id=b.Char_Id
			where a.Spec_Id=4440 and a.Expiration_Date is null ) as i on b.Prod_Code=i.Char_Desc
where h.pp_id=${pp_id} and f.prod_code not like '146%'`, function (err, result) {
      if (err) {
      }
      return callBack(result.recordset)
    })
}

module.exports = sqlexec;