var express = require('express')
var router = express.Router()
var sqlexec_grade = require('../sql/Product_Base')
var moment = require('moment')
const { resolve } = require('q')

router.get("/PB1", (req, res) => {
    // console.log('req',req.query)
    // var Mill = "'" + req.query.Mill + "'"
    // var Start = "'" + req.query.Start + "'"
    // var End = "'" + req.query.End + "'"
    // console.log('参数',Mill,Start,End)
    var returnQuery = new sqlexec_grade()
    returnQuery.PBF(function (result) {
        res.send(result)

    })
})




module.exports = router