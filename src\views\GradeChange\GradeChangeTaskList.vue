<template>
	<base-content>

		<div class="q-pa-md">
			<div class="q-gutter-md flex ">
				<q-btn color="primary" icon="arrow_back" label="返回"
					style="width: 100px; font-weight: 600;font-size: 15px;" @click="gotoTaskList" />
				<q-select filled v-model="poisition" :options="poisitionGroup" label="岗位筛选" style="width: 300px;" />
				<q-btn color="primary" icon-right="archive" label="导出到CSV" no-caps @click="exportTable" />
				<q-btn color="primary" icon="print" label="打印" @click="printTask()" />
				<q-btn color="primary" label="显示拼接设定" @click="gotoPJChat" />
				<q-btn color="primary" label="显示胶枪配置图" @click="gotoJQChat" />
				<q-btn color="primary" label="打印换型物料差异清单" @click="gotoMaterial" />


			</div>

			<div class="" id="printTest">
				<q-markup-table dense>
					<thead>
						<tr style="font-size: 10px">
							<!-- 	<th class="text-left">操作</th> -->
							<th class="text-left">任务ID</th>
							<th class="text-left">换型类别</th>
							<th class="text-left">任务类别</th>
							<th class="text-left">任务</th>
							<th class="text-left">岗位</th>
							<th class="text-left">现有设定</th>
							<th class="text-left">换型设定</th>
							<th class="text-left">备注</th>
							<!-- <th class="text-left">是否完成</th> -->
							<!-- 							<th class="text-left ">必须项</th>
							<th class="text-left">开始时间</th>
							<th class="text-left">结束时间</th>
							<th class="text-left">持续时间</th> -->
						</tr>
					</thead>
					<tbody>

						<template v-for="item in filterData">
							<tr style="font-size: 10px">
								<!-- 								<td class="text-left">
									<q-btn size="xs" :color="item.startTask==0?'primary':item.startTask==2?'purple':'red'" :label="item.startTask==0?'开始':item.startTask==2?'完成':'停止'" @click="startTask(item.taskID,item.startTask)" />
								</td> -->
								<td class="text-left" style="width: 15px;">{{ item.taskID }}</td>
								<td class="text-left" style="width: 80px;">{{ item.category }}</td>
								<td class="text-left" style="width: 80px;">{{ item.taskType }}</td>
								<td class="text-left" style="width: 100px;">{{ item.taskList }}</td>
								<td class="text-left" style="width: 100px;">{{ item.position }}</td>
								<td class="text-left" style="width: 250px;">{{ item.taskContent }}</td>
								<td class="text-left" style="width: 250px;">{{ item.newTaskContent }}</td>
								<td class="text-left">{{ item.newComment }}</td>
								<!-- <td class="text-left" ></td> -->
								<!-- 								<td class="text-left">{{item.requirment}}</td>
								<td class="text-left">{{item.startTime}}</td>
								<td class="text-left">{{item.endTime}}</td>
								<td class="text-left">{{item.duration}}</td> -->
							</tr>
						</template>


					</tbody>
				</q-markup-table>
			</div>
			<!-- 			<q-btn v-print="'#printTest'">qbtn</q-btn>
			<button v-print="'#printTest'">btn</button> -->
			<!-- 			<div style="height: 300px;"></div> -->

		</div>

	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import qs from 'qs'
import Papa from 'papaparse';
export default {
	components: {
		BaseContent,

	},
	data() {
		return {
			baseData: null,
			data: [],
			filterData: [],
			poisitionGroup: ['所有项目'],
			poisition: '所有项目',
			title: false,
			line: false

		}
	},

	mounted() {
		// console.log(this.$route.query)
		this.baseData = this.$route.query
		this.title = this.$route.query.line + '换型计划：' + this.$route.query.product1 + " 到 " + this.$route.query.product2
		this.getTaskList(this.$route.query.line, this.$route.query.product1, this.$route.query.product2)
	},
	watch: {
		poisition: function (newVal, oldVal) {
			console.log(oldVal, newVal)
			if (newVal !== '所有项目') {
				this.filterData = this.data.filter((e => { return e.position === newVal }))
			} else {
				this.filterData = this.data
			}

		}
	},

	methods: {
		gotoMaterial() {
			this.$router.push('/material?line=' + this.$route.query.line)
		},


		gotoJQChat() {
			window.open('http://cnnbas02/jq.pdf', '_blank')
		},

		gotoPJChat() {
			window.open('http://cnnbas02/pj.pdf', '_blank')
		},

		printTask() {

			const printWindow = window.open('', '_blank');
			const tableHtml = document.querySelector('table').outerHTML;
			printWindow.document.open();
			printWindow.document.write(`
				        <html>
				          <head>
				            <title >换型任务清单</title>
				            <style>
				              table { width: 100%; border-collapse: collapse; }
				              th, td {border: 1px solid #ccc;}
							  title {font-size:20px;font-weight:600}
				            </style>
				          </head>
				          <body>
						  <div style="margin: 0 auto; text-align: center;font-weight:600">${this.title}</div>
							<div style="font-size:4px">${tableHtml}</div>

						  </body>
				        </html>
				      `);
			printWindow.document.close();
			//   printWindow.document.close();
			printWindow.print();

		},


		gotoTaskList() {
			var _this = this
			_this.$router.go(-1)
		},

		async getTaskList(line, product1, product2) {
			var _this = this
			const {
				data: res
			} = await _this.$http.get('gradeChange/taskContent?line=' + line + '&product1=' + product1 + '&product2=' +
				product2)
			console.log(res)
			_this.data = res
			_this.filterData = res
			for (var i = 0; i < res.length; i++) {
				if (_this.poisitionGroup.indexOf(res[i].position) === -1) {
					console.log("发现了", res[i].position)
					_this.poisitionGroup.push(res[i].position)
				}
			}
			console.log(_this.poisitionGroup)
		},

		startTask(taskID, startTask) {
			console.log(taskID,)
			var _this = this
			for (var i = 0; i < _this.data.length; i++) {
				if (_this.data[i].taskID === taskID && startTask == 0) {
					var currentTime = new Date()
					_this.data[i].startTask = 1
					_this.data[i].startTaskData = new Date()
					_this.data[i].startTime = check(currentTime.getHours()) + ":" + check(currentTime.getMinutes()) + ":" + check(currentTime.getSeconds())
					break
				} else if (_this.data[i].taskID === taskID && startTask == 1) {
					var currentTime = new Date()
					_this.data[i].startTask = 0
					_this.data[i].endTime = check(currentTime.getHours()) + ":" + check(currentTime.getMinutes()) + ":" + check(currentTime.getSeconds())
					console.log(_this.data[i].startTaskData)
					// _this.data[i].duration=
					var dur = durationNum(_this.data[i].startTaskData, currentTime)
					console.log(dur)
					_this.data[i].startTask = 2
					_this.data[i].duration = dur + "分钟"
					break
				}
			}

			function durationNum(startTime, endTime) {
				const durationN = Math.round(endTime - startTime) / 1000 / 60
				return durationN.toFixed(2)
			}
			function check(i) {
				const num = (i < 10) ? ("0" + i) : i;
				return num;
			}

		},




		async updateStatus(e) {
			var _this = this
			console.log(e)
			var data = {
				"id": e.id,
				"approveStatus": e.status,
				"system": e.system,
				"billID": e.billID
			}
			_this.$http.post('approve/updateApproveStatus', data).then(function (response) {
				console.log('response', response)
				_this.getApproveList()
			})
		},
		async exportTable() {
			const csvData = Papa.unparse(this.filterData);
			const blob = new Blob(["\uFEFF" + csvData], { type: 'text/csv;charset=utf-8' });
			const url = URL.createObjectURL(blob);
			const link = document.createElement('a');
			link.style.display = 'none';
			link.href = url;
			link.download = '换型任务清单.csv';
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
			URL.revokeObjectURL(url);
		},








	}
}
</script>

<style scoped></style>
