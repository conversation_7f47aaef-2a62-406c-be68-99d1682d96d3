<template>

		<div>
			<q-table :data="data" :columns="columns" row-key="AwardID" :filter="filter" :pagination.sync="pagination"
			 hide-pagination>
				<template v-slot:top-left>
					<span style="font-weight: 600;font-size: 25px;line-height: 40px;">积分月度申请</span>
					<!-- 			<q-btn style="margin-left:10px;width:100px;height: 35px;line-height: 0px;" color="primary" label="添加申请" @click="add" /> -->
				</template>
			
				<template v-slot:top-right>
					<q-input borderless dense debounce="300" v-model="filter" placeholder="Search">
						<template v-slot:append>
							<q-icon name="search" />
						</template>
					</q-input>
				</template>
				<template v-slot:header="props">
					<q-tr :props="props">
						<q-th auto-width />
			
						<q-th v-for="col in props.cols" :key="col.name" :props="props">
							{{ col.label }}
						</q-th>
						<q-th auto-width />
					</q-tr>
				</template>
			
				<template v-slot:body="props">
					<q-tr :props="props">
						<q-td auto-width>
							<q-btn v-model="props.expand" size="sm" color="accent" :label="props.row.审批状态" @click="approvalShow(props)"
							 :color="props.row.审批状态=='progress' ? 'primary' : 'purple'" />
							<!-- 	<q-toggle v-model="props.expand" checked-icon="add" unchecked-icon="remove" /> -->
						</q-td>
						<q-td v-for="col in props.cols" :key="col.name" :props="props" @click="gotoDetail([props.row.AwardID,props.row.年月])">
							{{ col.value }}
						</q-td>
			
						<q-td auto-width>
							<q-btn v-show="props.row.审批进度ID<4" size="xs" @click="delItem([props])" color="negative" icon="clear" />
						</q-td>
			
					</q-tr>
			
				</template>
			
			</q-table>
			<div class="row justify-center q-mt-md">
				<q-pagination v-model="pagination.page" color="grey-8" :max="pagesNumber" size="sm" />
			</div>
		</div>
	
</template>

<script>

	
	import qs from 'qs'
	export default {

		props: {
			parentMessage: {
				type: String,
				default: '默认显示的信息'
				// require: true // 必填
			},
		},
		mounted(props) {
			
			this.getApplyAward()
		},
		computed: {
			pagesNumber() {
				return Math.ceil(this.data.length / this.pagination.rowsPerPage)
			}
		},


		data() {
			return {
				columns: [{
						name: '工厂',
						label: '工厂',
						align: 'left',
						field: '工厂',
					},
					{
						name: '机台',
						label: '机台',
						align: 'left',
						field: '机台',
					},
					{
						name: '年月',
						label: '年月',
						align: 'left',
						field: '年月',
						sortable: true
					},
					{
						name: 'oee',
						label: 'OEE',
						align: 'left',
						field: 'oee'
					},
					{
						name: '优秀班组',
						align: 'left',
						label: '优秀班组',
						field: '优秀班组',
						sortable: true
					},
					{
						name: 'Waste',
						align: 'left',
						label: 'Waste',
						field: 'Waste',
						sortable: true
					},
					{
						name: '安全',
						align: 'left',
						label: '安全',
						field: '安全'
					},
					{
						name: '质量',
						align: 'left',
						label: '质量',
						field: '质量',
						sortable: true
					},
					{
						name: 'ODelay',
						align: 'left',
						label: 'ODelay',
						field: 'ODelay',
						sortable: true
					},
					{
						name: '三连班',
						align: 'left',
						label: '三连班',
						field: '三连班'
					},
					{
						name: '破纪录',
						align: 'left',
						label: '破纪录',
						field: '破纪录',
						sortable: true
					},
					{
						name: '换型',
						align: 'left',
						label: '换型',
						field: '换型',
						sortable: true
					},
					{
						name: '点子',
						align: 'left',
						label: '点子',
						field: '点子'
					},
					{
						name: 'BP',
						align: 'left',
						label: 'BP',
						field: 'BP'
					},
					{
						name: '积分调整',
						align: 'left',
						label: '积分调整',
						field: '积分调整'
					},
					{
						name: '项目奖励',
						align: 'left',
						label: '项目奖励',
						field: '项目奖励'
					},
					{
						name: 'OT奖励',
						align: 'left',
						label: 'OT奖励',
						field: 'OT奖励'
					},
					{
						name: '申请人',
						align: 'left',
						label: '申请人',
						field: '申请人'
					},
					{
						name: '审批状态',
						align: 'left',
						label: '审批状态',
						field: '审批状态'
					},
					{
						name: 'AwardID',
						align: 'left',
						label: 'AwardID',
						field: 'AwardID',
						required: true,
					}
				],
				data: [],
				pagination: {
					descending: false,
					page: 1,
					rowsPerPage: 20
					// rowsNumber: xx if getting data from a server
				},
				filter: '',

			}
		},

		methods: {
			async getApplyAward() {
			
				var _this = this
				var awardID = _this.selectedYearmonth + _this.selectedLine
				const {
					data: res
				} = await _this.$http.get('approve/getApplyAwardSummary')
				_this.data = res
			},
			
		}
	}
</script>
