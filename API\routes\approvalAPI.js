var express = require('express')
var router = express.Router()
var sqlexec = require('../sql/sqlExcute_approve')
var moment = require('moment')
const web_url = require('../config/webURL');
const { resolve } = require('q')
const request = require('request')

router.post("/modifyPsw", (req, res) => {
	console.log(req.body)
	var account = "'" + req.body.username + "'"
	var password = "'" + req.body.oldPassword + "'"
	var newPassword = "'" + req.body.newPassword + "'"
	//var repeatPassword="'" + req.body.repeatPassword + "'"
	// var repeatPsw="'" + req.body.password + "'"
	var login = new sqlexec()
	login.checkAccount(account, password, function (result) {
		console.log(result)
		let msg = {}
		if (result.length > 0) {
			login.modifyPSW(account, newPassword, function (result1) {
				console.log(result1)
				if (result1 == '修改成功') {
					msg = {
						msg: '修改密码成功',
					}
					res.json(msg)
				} else {
					msg = {
						msg: '修改密码失败',
					}
					res.json(msg)
				}
			})
		} else {
			msg = {
				msg: '修改密码失败,原密码错误',
			}
			res.json(msg)
		}
	})
})

router.post("/login", (req, res) => {
	console.log(req.body)
	var account = "'" + req.body.account + "'"
	var password = "'" + req.body.password + "'"
	var login = new sqlexec()
	login.checkAccount(account, password, function (result) {
		console.log(result)
		let msg = {}
		if (result.length > 0) {
			msg = {
				msg: '登陆成功',
				username: result[0],
				Access_Group: []
			}
			for (var i = 0; i < result.length; i++) {
				msg.Access_Group.push(result[i]['Access_Group'])
			}
			console.log(msg)
			res.json(msg)
		} else {
			msg = {
				msg: '登陆失败',
			}
			res.json(msg)
		}
	})
})

router.get("/getMonthlyAward", (req, res) => {
	var yearmonth = req.query.yearMonth
	var line = req.query.Line
	var department = req.query.department
	var returnQuery = new sqlexec()
	returnQuery.getMonthlyAward(yearmonth, line, department, function (result) {
		res.send(result)
	})
})
router.get("/SP_API_Award_Count_Summary", (req, res) => {
	var yearmonth = req.query.yearMonth
	var line = req.query.Line
	var department = req.query.department
	var returnQuery = new sqlexec()
	returnQuery.SP_API_Award_Count_Summary(department, yearmonth, line, function (A) {
		console.log(A)
		if (A.length > 0) {
			// 创建一个空对象来存放结果
			let result = {};
			// 遍历变量A
			A.forEach(item => {
				if (item.Award_Type !== '换型') {
					result[item.Award_Type] = item.count.toString();
				}
			});
			// 处理"Award_Type"为"换型"的项
			let changeTypeCounts = A.filter(item => item.Award_Type === '换型')
				.map(item => `${item.value}:${item.count}`)
				.join(' | ');
			result['换型'] = changeTypeCounts;
			result['机台'] = A[0].Line;
			result['年月'] = yearmonth
			result['部门'] = department
			result['机台总计'] = '次数统计'
			console.log(result);
			res.send(result)
		}

	})
})

router.get("/getMonthlyAwardSummary", (req, res) => {
	var awardID = "'" + req.query.awardID + "'"
	var system = "'" + req.query.system + "'"
	console.log(req.query)
	var returnQuery = new sqlexec()
	returnQuery.getMonthlyAwardSummary(awardID, function (result) {
		res.send(result)
	})
})

router.get("/getApplyAwardSummary", (req, res) => {
	console.log(req.query)
	var system = "'" + req.query.system + "'"
	var returnQuery = new sqlexec()
	returnQuery.getApplyAwardSummary(system, async function (result) {
		returnQuery.Award_List_All(async function (result1) {
			console.log(result)
			const awardList = []
			const columns = [
				{ name: '机台', label: '机台', field: 'Line', align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
				{ name: '年月', label: '年月', field: 'Fin_YearMonth', align: 'left', headerClasses: 'bg-primary text-white', sortable: true },
				{ name: '部门', label: '部门', field: 'Department', align: 'left', headerClasses: 'bg-primary text-white', sortable: true }
			]

			for (let i = 0; i < result1.length; i++) {
				if (awardList.indexOf(result1[i]['Award_Type']) === -1) {
					awardList.push(result1[i]['Award_Type'])
					columns.push({ name: result1[i]['Award_Type'], label: result1[i]['Award_Type'], field: result1[i]['Award_Type'], align: 'left', headerClasses: 'bg-primary text-white', sortable: true })
				}
			}
			columns.push({ name: 'Approval_Level', label: 'Approval_Level', field: 'Approval_Level', align: 'left', headerClasses: 'bg-primary text-white', sortable: true })
			columns.push({ name: 'Approval_Status', label: 'Approval_Status', field: 'Approval_Status', align: 'left', headerClasses: 'bg-primary text-white', sortable: true })
			const rows = []
			for (let j = 0; j < result.length; j++) {
				const line = result[j]['Line']
				const yearmonth = result[j]['Fin_YearMonth']
				const department = result[j]['Department']
				const award_type = result[j]['Award_Type']
				let singleRow = { 'Fin_YearMonth': yearmonth, 'Line': line, 'Department': department }
				if (rows.length == 0) {
					rows.push(singleRow)
				}
				let rowExists = false
				for (let s = 0; s < rows.length; s++) {
					if (yearmonth == rows[s]['Fin_YearMonth'] && department == rows[s]['Department'] && line == rows[s]['Line']) {
						rows[s][award_type] = result[j]['Points']
						rows[s]['Approval_Status'] = result[j]['Approval_Status']
						rows[s]['Approval_Level'] = result[j]['Approval_Level']
						rowExists = true
					}
				}
				if (!rowExists) {
					singleRow[award_type] = result[j]['Points']
					singleRow['Approval_Status'] = result[j]['Approval_Status']
					singleRow['Approval_Level'] = result[j]['Approval_Level']
					rows.push(singleRow)
				}
			}

			const data = {
				columns: columns,
				data: rows
			}
			res.send(data)
		})
		// res.send(result)
	})
})



router.post("/api_insertAwardSummary", (req, res) => {
	console.log(req.body)
	var awardID = "'" + req.body.awardID + "'"
	var name = "'" + req.body.name + "'"
	var system = "'" + req.body.system + "'"
	var returnQuery = new sqlexec()
	returnQuery.api_insertAwardSummary(name, awardID, async function (result) {
		if (result === '添加成功') {
			console.log("开始获取审批人信息")
			var system = '积分系统'
			var level = 1
			let approverList = await getApproverName(level, system, awardID)
			console.log(approverList)
			res.send(approverList)
		}
	})
})


router.get("/getApproveStatus", (req, res) => {
	var awardid = req.query.awardid
	var department = req.query.department
	var returnQuery = new sqlexec()
	returnQuery.getApproveStatus(awardid, department, function (result) {
		console.log('getApproveStatus', result)
		res.send(result)
	})
})



router.post("/delAwardApply", (req, res) => {
	const yearMonth = req.body.yearMonth
	const Line = req.body.Line
	const Department = req.body.Department
	var returnQuery = new sqlexec()
	returnQuery.delAwardApply(yearMonth, Line, Department, function (result) {
		res.send(result)
	})
})

router.get("/getMyApproveList", (req, res) => {
	var username = req.query.username
	var returnQuery = new sqlexec()
	returnQuery.getMyApproveList(username, function (result) {
		console.log('result', result)
		let approvalList = { 'approve': [], 'approved': [] }
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				if (result[i]['Approval_Status'] === '审批中') {
					approvalList.approve.push(result[i])
				} else {
					approvalList.approved.push(result[i])
				}
			}
		}

		res.json(approvalList)
	})
})


router.post("/updateApproveStatus", (req, res) => {
	console.log('req.body', req.body)
	const department = req.body.department
	const billID = req.body.billID
	const id = req.body.id
	const approveStatus = req.body.approveStatus
	const line = req.body.billID.substring(6, 10)
	const yearMonth = req.body.billID.substring(0, 6)
	const returnQuery = new sqlexec()
	returnQuery.updateApproveStatus(id, approveStatus, function (result) {
		console.log(result)
		if (result == '更新成功') {
			console.log("开始更新流程控制", department, billID)
			returnQuery.getLevelStatus(department, billID, async function (result1) {
				console.log('流程控制返回', result1)
				if (approveStatus === '已拒绝') {
					console.log("开始执行拒绝指令")
					let status = "rejected"
					let updateSummaryStatus = await updateAwardStatus(department, yearMonth, line, status, result1[0]['Approval_Level'])
					console.log('拒绝指令返回', updateSummaryStatus)
					res.send("rejected")
				}
				if (result1[0].status == 'next') {
					var nextLevel = parseInt(result1[0]['Approval_Level']) + 1
					console.log('next Level', nextLevel)
					// console.log(nextLevel, system, billID)
					var nextApproveStatus = await getApproverName(nextLevel, department, yearMonth, line)

					// if (system=='积分系统'){
					// 	var status="'progress'"
					// 	updateSummaryStatus= await updateAwardStatus(billID,status,nextLevel)
					// }
					var status = "progress"
					let updateSummaryStatus = await updateAwardStatus(department, yearMonth, line, status, nextLevel)
					console.log('nextApproveStatus', nextApproveStatus)
					console.log('updateSummaryStatus', updateSummaryStatus)
					res.send('更新成功')
				} else if (result1[0].status == 'completed') {
					var status = "approved"
					updateSummaryStatus = await updateAwardStatus(department, yearMonth, line, status, 4)
					res.send('approved')
				} else {
					res.send('更新成功')
				}
			})
		}

	})
})

router.get("/getCalendar", (req, res) => {
	var startDate = "'" + req.query.startDate + "'"
	var endDate = "'" + req.query.endDate + "'"

	var returnQuery = new sqlexec()
	returnQuery.getCalendar(startDate, endDate, function (result) {
		res.send(result)
	})
})

router.get("/SP_API_Award_Performance_List", (req, res) => {
	var startDate = "'" + req.query.startDate + "'"
	var endDate = "'" + req.query.endDate + "'"
	var Line = "'" + req.query.Line + "'"
	var fun = "'" + req.query.fun + "'"
	console.log(req.query)
	var returnQuery = new sqlexec()
	returnQuery.SP_API_Award_Performance_List(startDate, endDate, Line, fun, function (result) {
		const keys = Object.keys(result[0]);
		let columns = []
		if (keys.length > 0) {
			for (k in keys) {
				let singleColumns = { name: keys[k], label: keys[k], field: keys[k], align: 'left', headerClasses: 'bg-primary text-white', sortable: true }
				columns.push(singleColumns)
			}
		}
		const data = {
			columns: columns,
			result: result
		}
		// console.log(columns)
		res.json(data)
	})
})

router.get("/SP_API_Award_Performance_Monthly_List", (req, res) => {
	var startDate = "'" + req.query.startDate + "'"
	var endDate = "'" + req.query.endDate + "'"
	var fun = "'" + req.query.fun + "'"
	console.log(req.query)
	var returnQuery = new sqlexec()
	returnQuery.SP_API_Award_Performance_Monthly_List(startDate, endDate, fun, function (result) {
		const keys = Object.keys(result[0]);
		let columns = []
		if (keys.length > 0) {
			for (k in keys) {
				let singleColumns = { name: keys[k], label: keys[k], field: keys[k], align: 'left', headerClasses: 'bg-primary text-white', sortable: true }
				columns.push(singleColumns)
			}
		}
		const data = {
			columns: columns,
			result: result
		}
		console.log('data', data)
		res.json(data)
	})
})

router.get("/SP_API_Award_Performance_by_People", (req, res) => {
	var shiftlinkid = req.query.shiftlinkid
	var fun = req.query.fun
	console.log(req.query)
	var returnQuery = new sqlexec()
	returnQuery.SP_API_Award_Performance_by_People(shiftlinkid, fun, function (result) {
		const keys = Object.keys(result[0]);
		let columns = []
		if (keys.length > 0) {
			for (k in keys) {
				let singleColumns = { name: keys[k], label: keys[k], field: keys[k], align: 'left', headerClasses: 'bg-primary text-white', sortable: true }
				columns.push(singleColumns)
			}
		}
		const data = {
			columns: columns,
			result: result
		}
		// console.log(columns)
		res.json(data)
	})
})

router.post("/API_Execute_Award_Performance_Daily", (req, res) => {
	// console.log(req.body.awardID)
	var dataArray = req.body
	console.log(dataArray)
	var returnQuery = new sqlexec()
	returnQuery.API_Execute_Award_Performance_Daily(dataArray, function (result) {
		res.send(result)
	})
})

router.post("/SP_API_Update_Award_Performance", (req, res) => {
	// console.log(req.body.awardID)
	var dataArray = req.body
	console.log(dataArray)
	var returnQuery = new sqlexec()
	returnQuery.SP_API_Update_Award_Performance(dataArray, function (result) {
		res.send(result)
	})
})

router.post("/SP_API_Update_Award_Performance_Monthly", (req, res) => {
	// console.log(req.body.awardID)
	var dataArray = req.body
	console.log(dataArray)
	var returnQuery = new sqlexec()
	returnQuery.SP_API_Update_Award_Performance_Monthly(dataArray, function (result) {
		res.send(result)
	})
})

router.get("/getLine", (req, res) => {
	var returnQuery = new sqlexec()
	returnQuery.getLine(req.query.mill, function (result) {
		console.log(result)
		const lines = result.map(item => item.Line);
		res.send(lines)
	})
})

router.get("/getAward_Production_List", (req, res) => {
	var returnQuery = new sqlexec()
	returnQuery.Award_Production_List(req.query.startDate, req.query.endDate, req.query.Line, function (result) {
		// console.log('getAward_Production_List',result)
		res.send(result)
	})
})
router.get("/getAward_Application_Summary_Daily", (req, res) => {
	var returnQuery = new sqlexec()
	returnQuery.Award_Application_Summary_Daily(req.query.startDate, req.query.endDate, req.query.Line, function (result) {
		console.log('getAward_Application_Summary_Daily', result)
		// 创建一个空对象，用于存储汇总结果
		const summary = {};
		// 遍历数组中的每个元素
		for (const item of result) {
			const { Date, Line, Shift, Award_Type, totalPoint, Approval_Status, Award_Param } = item;
			// 拼接Date、Line和shift作为联合键
			const key = `${Date}-${Line}-${Shift}`;
			// 如果联合键在汇总对象中不存在，则创建一个新的对象
			if (!summary[key]) {
				summary[key] = {
					Date,
					Line,
					Shift
				};
			}
			// 将Award_Type作为属性名，totalPoint作为属性值，存储到汇总对象中
			summary[key][Award_Type] = totalPoint + "|" + Approval_Status + "|" + Award_Param;
		}
		// 将汇总结果转换为数组形式
		const output = Object.values(summary);
		console.log(output);
		res.send(output)
	})
})


router.get("/getAward_Application_Production_Single_List", (req, res) => {
	var returnQuery = new sqlexec()
	returnQuery.Award_Application_Single_Daily(req.query.Date, req.query.Line, req.query.Shift, function (result) {
		// console.log('result',result)
		returnQuery.Award_Application_Crew_Single_Daily(req.query.Date, req.query.Line, req.query.Shift, function (result_crew) {
			// console.log('result_crew',result_crew)
			returnQuery.Award_Application_Single_AwardParam_Daily(req.query.Date, req.query.Line, req.query.Shift, function (result_award) {
				// console.log('result_award',result_award)
				const singleData = {
					product: result,
					crew: result_crew,
					award: result_award
				}
				res.send(singleData)
			})
		})

	})
})

router.get("/getAward_Application_CrewAward_Single_Daily", (req, res) => {
	var returnQuery = new sqlexec()
	returnQuery.Award_Application_CrewAward_Single_Daily(req.query.Date, req.query.Line, req.query.Shift, function (result) {
		console.log('Award_Application_CrewAward_Single_Daily', result)
		// 创建一个空对象，用于存储汇总结果
		const summary = {};
		// 遍历数组中的每个元素
		for (const item of result) {
			const { Employee_ID, Award_Type, Point } = item;
			// 拼接Date、Line和shift作为联合键
			const key = `${Employee_ID}`;
			// 如果联合键在汇总对象中不存在，则创建一个新的对象
			if (!summary[key]) {
				summary[key] = {
					Employee_ID,
				};
			}
			// 将Award_Type作为属性名，totalPoint作为属性值，存储到汇总对象中
			summary[key][Award_Type] = Point;
			// summary[key][Award_Type+'ID'] = ID;
		}
		// 将汇总结果转换为数组形式
		const output = Object.values(summary);
		console.log(output);
		res.send(output)
	})
})

router.get("/getAward_Base", (req, res) => {
	var returnQuery = new sqlexec()
	returnQuery.Award_Base(req.query.yearMonth, req.query.Line, req.query.department, req.query.award_Type, function (result) {
		res.send(result)
	})
})

router.get("/SP_Update_Application_Daily", (req, res) => {
	// console.log(req.body.awardID)
	var dataArray = req.query.param
	console.log('dataArray', dataArray)
	dataArray = JSON.parse(dataArray)
	var returnQuery = new sqlexec()
	returnQuery.SP_API_Award_Update_Application_Daily(dataArray, function (result) {
		console.log('result', result)
		res.send(result)
	})
})

router.get("/getAward_Base_dropdown", (req, res) => {
	// console.log(req.body.awardID)
	var award_Type = req.query.award_Type
	var Mill = req.query.mill
	var returnQuery = new sqlexec()
	returnQuery.Award_Base_dropdown(award_Type, Mill, function (array) {
		console.log('Award_Base_dropdown', array)
		const result = array.reduce((acc, curr) => {
			const key = curr.subAward_Type;
			const value = curr.DropDown_Value;

			if (acc[key]) {
				acc[key].push(value);
			} else {
				acc[key] = [value];
			}

			return acc;
		}, {});
		console.log(result);
		res.send(result)
	})
})

router.get("/getAward_Base_QCO_Target_Manage", async (req, res) => {
	var Line = req.query.Line
	var Date = req.query.Date
	var returnQuery = new sqlexec()
	returnQuery.Award_Base_QCO_Target_Manage(Line, Date, function (result) {
		console.log('result', result)
		res.send(result)
	})
})


router.post("/Award_Clear_singleCrew_Award", (req, res) => {
	console.log(req.body)
	const dataArray = req.body
	const Employee_ID = dataArray['crew_Employee_ID']
	const modifyName = dataArray['modifyName']
	var returnQuery = new sqlexec()
	for (var i = 0; i < dataArray['clearAward'].length; i++) {
		let award_shiftLinkid = dataArray['shiftLinkid'] + '-' + dataArray['clearAward'][i]
		returnQuery.Award_Clear_singleCrew_Award(award_shiftLinkid, Employee_ID, modifyName, function (result) {
			console.log(result)
			if (result != '更新成功') {
				res.send('更新失败')
			}
		})
	}
	res.send('更新成功')
})

router.get("/getAward_List", async (req, res) => {
	var Award_Cycle = req.query.Award_Cycle
	var Position = req.query.Position
	const data = await getAwardTypeArray(Award_Cycle, Position)
	console.log(data)
	res.send(data)

})

router.post("/Award_Update_Application_Approved", (req, res) => {
	//console.log(req.body)
	const dataArray = req.body
	const startDate = dataArray['startDate']
	const endDate = dataArray['endDate']
	const Line = dataArray['Line']
	const modifyName = dataArray['modifyName']
	var returnQuery = new sqlexec()
	returnQuery.Award_Update_Application_Approved(startDate, endDate, Line, modifyName, function (result) {
		console.log(result)
		if (result == '更新成功') {
			res.send('更新成功')
		}
	})
})
router.post("/Award_Update_Application_CancelApproved_byShift", (req, res) => {
	const dataArray = req.body
	const shiftLinkid = dataArray['ShiftLinkid']
	var returnQuery = new sqlexec()
	returnQuery.Award_Update_Application_CancelApproved_byShift(shiftLinkid, function (result) {
		console.log(result)
		if (result == '更新成功') {
			res.send('更新成功')
		}
	})
})

router.get("/getMonthlyCrewList", (req, res) => {
	var yearMonth = req.query.yearMonth
	var Line = req.query.Line
	var department = req.query.department
	// console.log(req.query)
	const position = department == '生产' ? '操作工' : '工程维修'
	var returnQuery = new sqlexec()
	returnQuery.getMonthlyCrewList(yearMonth, Line, department, function (result) {
		returnQuery.getMonthlyAwardList(yearMonth, Line, department, function (result1) {
			returnQuery.Award_List('Daily', position, 'Monthly', function (result2) {
				// console.log('result1',result1)
				const keys = Object.keys(result[0]);
				let jsColumn = []
				let opColumn = []
				if (keys.length > 0) {
					for (k in keys) {
						let singleColumns = { name: keys[k], label: keys[k], field: keys[k], align: 'left', headerClasses: 'bg-primary text-white', sortable: true }
						jsColumn.push(singleColumns)
						opColumn.push(singleColumns)
					}
				}
				let opData = result
				let jsData = []
				for (var i = 0; i < opData.length; i++) {
					if (opData[i]['Position'] === '工艺技师') {
						jsData.push(opData[i])
						opData.splice(i, 1)
						i--
					}
				}
				console.log('opData', opData)
				for (var j = 0; j < result1.length; j++) {
					let column = {}
					column = { name: result1[j]['Award_Type'], label: result1[j]['Award_Type'], field: result1[j]['Award_Type'], align: 'left', headerClasses: 'bg-primary text-white', sortable: true }
					if (result1[j]['Position'] === '工艺技师') {
						jsColumn.push(column)
						console.log(result1[j]['Award_Type'])
						for (var sub_i = 0; sub_i < jsData.length; sub_i++) {
							console.log(opData[sub_i])
							jsData[sub_i][result1[j]['Award_Type']] = ''
						}
					}
					// else {
					// 	opColumn.push(column)
					// 	for (var sub_i1 = 0; sub_i1 < opData.length; sub_i1++) {
					// 		opData[sub_i1][result1[j]['Award_Type']] = ''
					// 	}
					// }
					if (result1[j]['Position'] === '操作工') {
						opColumn.push(column)
						for (var sub_i1 = 0; sub_i1 < opData.length; sub_i1++) {
							opData[sub_i1][result1[j]['Award_Type']] = ''
						}
					}

					console.log(result1[j]['Position'])
					if (result1[j]['Position'] == '工程维修') {

						opColumn.push(column)
						for (var sub_i1 = 0; sub_i1 < opData.length; sub_i1++) {
							opData[sub_i1][result1[j]['Award_Type']] = ''
						}
					}

					let uniqueArray = [...new Set(result1[j]['awardbaseArray'].split(','))];
					console.log(uniqueArray)
					result1[j]['awardbaseArray'] = uniqueArray
					// {array:[],value:''}
				}


				for (var s = 0; s < result2.length; s++) {
					let singleColumns = { name: result2[s]['Award_Type'], label: result2[s]['Award_Type'], field: result2[s]['Award_Type'], align: 'left', headerClasses: 'bg-primary text-white', sortable: true }
					opColumn.push(singleColumns)
					if (result2[s]['Award_Type'] == 'BP' || result2[s]['Award_Type'] == '点子') {
						jsColumn.push(singleColumns)
						if (jsData.length > 0) {
							jsData[0][result2[s]['Award_Type']] = ''
						}
					}
					for (var sub_i2 = 0; sub_i2 < opData.length; sub_i2++) {
						opData[sub_i2][result2[s]['Award_Type']] = ''
					}

				}
				console.log('jsData', jsData)
				const data = {
					jsData: { data: jsData, columns: jsColumn },
					opData: { data: opData, columns: opColumn },
					awardList: result1
				}


				// console.log('data', data)
				res.json(data)
			})
		})

	})
})


router.post("/SP_API_Award_Update_Application_Monthly", async (req, res) => {
	try {
		const dataArray = req.body
		console.log('SP_API_Award_Update_Application_Monthly request:', dataArray)
		const yearMonth = dataArray['yearMonth']
		const line = dataArray['line']
		let award_base = dataArray['award_base']
		const award_comment = dataArray['award_comment']
		const award_type = dataArray['award_type']
		const modifyName = dataArray['modifyName']
		const operate = dataArray['operate']
		const department = dataArray['department']

		// 添加调试信息
		console.log('yearMonth:', yearMonth)
		console.log('line:', line)
		console.log('award_base:', award_base)
		console.log('award_type:', award_type)
		console.log('department:', department)

		const crewArray = dataArray['selectedCrew']

		// 使用Promise.all等待所有数据库操作完成
		const updatePromises = crewArray.map((crew, index) => {
			return new Promise((resolve, reject) => {
				const Employee_ID = crew['Employee_ID']
				const Employee_PYName = crew['Employee_PYName']
				const Position = crew['Position']

				// 添加调试信息
				console.log(`Processing ${index + 1}/${crewArray.length} - Employee_ID:`, Employee_ID)
				console.log('Employee_PYName:', Employee_PYName)
				console.log('Position:', Position)

				let currentAwardBase = award_base
				if (department == '工程维修' && award_type != '积分调整' && award_type != '质量' && award_type != 'PM' && award_type != '三连班' && award_type != '项目奖励') {
					currentAwardBase = crew['award_base']
				}

				// 确保 award_base 不为 undefined 或 null，设置默认值为 0
				if (currentAwardBase === undefined || currentAwardBase === null || currentAwardBase === '') {
					currentAwardBase = 0
				}

				console.log('final award_base:', currentAwardBase)

				const returnQuery = new sqlexec()
				returnQuery.SP_API_Award_Update_Application_Monthly(yearMonth, line, award_type, Employee_ID, Employee_PYName, Position, currentAwardBase, modifyName, operate, award_comment, department, function (result) {
					console.log(`SP_API_Award_Update_Application_Monthly result for ${Employee_ID}:`, result)
					if (result === '更新成功') {
						resolve({ Employee_ID, success: true })
					} else {
						console.log('Update failed for Employee_ID:', Employee_ID)
						reject(new Error(`更新失败 - Employee_ID: ${Employee_ID}`))
					}
				})
			})
		})

		// 等待所有数据库操作完成
		const results = await Promise.all(updatePromises)
		console.log('All updates completed successfully:', results)
		res.send('更新成功')

	} catch (error) {
		console.error('SP_API_Award_Update_Application_Monthly error:', error)
		res.status(500).send('更新失败')
	}
})

router.get("/getMonthlyApplicationAward", async (req, res) => {
	var line = req.query.Line
	var yearMonth = req.query.yearMonth
	var department = req.query.department
	var returnQuery = new sqlexec()
	returnQuery.getMonthlyApplicationAward(line, yearMonth, department, function (result) {
		res.send(result)
	})
})

router.post("/delete_Application_Monthly", async (req, res) => {
	try {
		const dataArray = req.body
		const yearMonth = dataArray['yearMonth']
		const line = dataArray['line']
		const award_type = dataArray['award_type']
		const Crew_account = dataArray['selectedCrew']
		const operate = dataArray['operate']

		// 使用Promise.all等待所有删除操作完成
		const deletePromises = []

		if (award_type.length > 0) {
			for (let i = 0; i < award_type.length; i++) {
				const award_type_single = award_type[i]
				const deletePromise = new Promise((resolve, reject) => {
					const returnQuery = new sqlexec()
					returnQuery.delete_Application_Monthly(yearMonth, line, award_type_single, Crew_account, operate, function (result) {
						console.log(`Delete result for ${award_type_single}:`, result)
						if (result === '更新成功') {
							resolve({ award_type: award_type_single, success: true })
						} else {
							reject(new Error(`删除失败 - Award_Type: ${award_type_single}`))
						}
					})
				})
				deletePromises.push(deletePromise)
			}
		} else {
			const deletePromise = new Promise((resolve, reject) => {
				const returnQuery = new sqlexec()
				returnQuery.delete_Application_Monthly(yearMonth, line, award_type[0], Crew_account, operate, function (result) {
					console.log(`Delete result for ${award_type[0]}:`, result)
					if (result === '更新成功') {
						resolve({ award_type: award_type[0], success: true })
					} else {
						reject(new Error(`删除失败 - Award_Type: ${award_type[0]}`))
					}
				})
			})
			deletePromises.push(deletePromise)
		}

		// 等待所有删除操作完成
		const results = await Promise.all(deletePromises)
		console.log('All delete operations completed successfully:', results)
		res.send('更新成功')

	} catch (error) {
		console.error('delete_Application_Monthly error:', error)
		res.status(500).send('更新失败')
	}
})


router.get("/other_crew_list", async (req, res) => {
	var line = req.query.Line
	var Business_Title = req.query.Business_Title
	var returnQuery = new sqlexec()
	returnQuery.other_crew_list(line, Business_Title, function (result) {
		res.send(result)
	})
})

router.get("/SP_API_Award_Count", async (req, res) => {
	var line = req.query.Line
	var yearMonth = req.query.yearMonth
	var awardType = req.query.awardType
	var department = req.query.department
	var returnQuery = new sqlexec()
	returnQuery.SP_API_Award_Count(line, yearMonth, awardType, department, function (result) {
		res.send(result)
	})
})

router.get("/SP_API_Award_Count_byDetail", async (req, res) => {
	var line = req.query.Line
	var yearMonth = req.query.yearMonth
	var department = req.query.department
	var awardType = req.query.awardType
	var returnQuery = new sqlexec()
	returnQuery.SP_API_Award_Count_byDetail(line, yearMonth, department, awardType, function (result) {
		res.send(result)
	})
})

router.post("/SP_API_Award_SummaryAward_ByCrew", async (req, res) => {
	// console.log(req.body)
	const dataArray = req.body
	const yearMonth = dataArray['yearMonth']
	const line = dataArray['line']
	const crewArray = dataArray['selectedCrew']
	const modifyName = dataArray['modifyName']
	const department = dataArray['department']
	console.log(dataArray)
	const returnData = await Award_SummaryAward_ByCrew(yearMonth, line, crewArray, modifyName, department)
	console.log('returnData', returnData)
	res.send(returnData)
})
router.post("/Award_Application_Summary_Monthly", async (req, res) => {
	try {
		// console.log(req.body)
		const dataArray = req.body
		const yearMonth = dataArray['yearMonth']
		const line = dataArray['line']
		const data = dataArray['data']
		const modifyName = dataArray['modifyName']
		const department = dataArray['department']

		// 使用Promise.all等待所有数据库操作完成
		const summaryPromises = data.map((item, index) => {
			return new Promise((resolve, reject) => {
				const awardType = item[0]
				const awardPoints = item[1]
				const returnQuery = new sqlexec()

				console.log(`Processing summary ${index + 1}/${data.length} - Award_Type: ${awardType}, Points: ${awardPoints}`)

				returnQuery.Award_Application_Summary_Monthly(line, yearMonth, department, awardType, awardPoints, modifyName, function (result) {
					console.log(`Summary result for ${awardType}:`, result)
					if (result === '更新成功') {
						resolve({ awardType, awardPoints, success: true })
					} else {
						reject(new Error(`汇总失败 - Award_Type: ${awardType}`))
					}
				})
			})
		})

		// 等待所有汇总操作完成
		const summaryResults = await Promise.all(summaryPromises)
		console.log('All summary operations completed successfully:', summaryResults)

		// 然后处理审批人信息
		let level = 1
		let award = await getApproverName(level, department, yearMonth, line)
		if (award == '添加成功') {
			res.send('更新成功')
		} else {
			res.status(500).send('审批人设置失败')
		}

	} catch (error) {
		console.error('Award_Application_Summary_Monthly error:', error)
		res.status(500).send('更新失败')
	}
})

router.get("/Award_Application_Summary_Monthly_verify", async (req, res) => {
	var line = req.query.Line
	var yearMonth = req.query.yearMonth
	var department = req.query.department
	var returnQuery = new sqlexec()
	returnQuery.Award_Application_Summary_Monthly_verify(line, yearMonth, department, function (result) {
		console.log('result', result)
		if (result.length > 0) {
			res.send(result)
		} else {
			res.send('未申请')
		}

	})
})

router.get("/Award_Performance_Summary_Monthly", async (req, res) => {
	var line = req.query.Line
	var yearMonth = req.query.yearMonth
	var returnQuery = new sqlexec()
	returnQuery.Award_Performance_Summary_Monthly(line, yearMonth, function (result) {
		res.send(result)
	})
})

router.get("/awardQuery_byPersonal", (req, res) => {
	console.log(req.query)
	var returnQuery = new sqlexec()
	returnQuery.awardQuery_byPersonal(req.query.IDCode, req.query.yearMonth, function (result) {
		res.send(result)
	})
})

function Award_SummaryAward_ByCrew(yearMonth, line, crewArray, modifyName, department) {
	return new Promise((resolve, reject) => {
		const returnData = [];
		const promises = [];

		for (var i = 0; i < crewArray.length; i++) {
			const Employee_ID = crewArray[i]['Employee_ID'];
			const Employee_PYName = crewArray[i]['Employee_PYName'];
			const position = crewArray[i]['Position'];

			const promise = new Promise((resolveQuery, rejectQuery) => {
				const returnQuery = new sqlexec()

				console.log(`Processing crew ${i + 1}/${crewArray.length} - Employee_ID: ${Employee_ID}`)

				returnQuery.SP_API_Award_SummaryAward_ByCrew(line, yearMonth, Employee_ID, Employee_PYName, position, modifyName, department, function (result) {
					try {
						if (result && result.length > 0) {
							const newObj = { 'Employee_ID': result[0]['employee_ID'] };
							for (let j = 0; j < result.length; j++) {
								newObj[result[j]['Award_Type']] = result[j]['totalPoint'];
							}
							returnData.push(newObj);
							console.log(`Successfully processed crew: ${Employee_ID}`)
						} else {
							console.log(`No data found for crew: ${Employee_ID}`)
						}
						resolveQuery();
					} catch (error) {
						console.error(`Error processing crew ${Employee_ID}:`, error)
						rejectQuery(error);
					}
				});
			});
			promises.push(promise);
		}

		Promise.all(promises)
			.then(() => {
				console.log('All crew summary operations completed successfully')
				resolve(returnData);
			})
			.catch((error) => {
				console.error('Award_SummaryAward_ByCrew error:', error)
				reject(error);
			});
	});
}

function getAwardTypeArray(Award_Cycle, Position) {
	return new Promise((resolve, reject) => {
		var returnQuery = new sqlexec()
		returnQuery.Award_List(Award_Cycle, Position, 'Daily', function (result) {
			const awardBase = result.reduce((acc, curr) => {
				const { Award_Type, Award_Param } = curr;
				const params = JSON.parse(Award_Param);
				const obj = {};
				// console.log('obj',obj)
				Object.keys(params).forEach(key => {
					if (key !== Award_Type && Award_Type != '点子' && Award_Type != 'BP') {
						obj[key] = {
							value: '',
							array: []
						};
					}
				});
				acc[Award_Type] = obj;
				return acc;
			}, {});
			// console.log(awardBase)
			const data = {
				column: result,
				awardBase: awardBase
			}
			resolve(data)
		})

	})
}

function getApproverName(level, department, yearMonth, line) {
	return new Promise((resolve, reject) => {
		console.log('接受到的YearMonth', yearMonth)
		let awardID = yearMonth.toString() + line
		console.log('收到的AwardID:', awardID)
		console.log('Line:', line)
		var returnQuery1 = new sqlexec()
		returnQuery1.getApproverName(level, department, line, async function (result) {
			console.log("获取审批人信息:", result)
			if (result.length > 0) {
				for (var i = 0; i < result.length; i++) {
					var employee_id = result[i]['Approver_ID']
					var employee_name = result[i]['Approver_Name']
					const htmlbody = await MailBodyToApprover(line, yearMonth, department)
					// console.log('htmlbody', htmlbody)
					const sendMailStatus = await sendMailtoApprover(employee_id + '@kcc.com', yearMonth + '-' + department + '-' + line + '积分审批', htmlbody, '积分审批系统')
					returnQuery1.insertApprovalRecord(department, level, awardID, employee_id, employee_name, async function (result1) {
						console.log('getApproverName', result1)
						// if (result1)
					})
				}
				resolve('添加成功')
			}
		})
	})
}


function updateAwardStatus(department, yearMonth, line, status, levelID) {
	return new Promise((resolve, reject) => {
		var returnQuery1 = new sqlexec()
		returnQuery1.updateAwardStatus(department, yearMonth, line, status, levelID, function (result) {
			resolve(result)
		})
	})
}

function MailBodyToApprover(line, yearMonth, department) {
	return new Promise((resolve, reject) => {
		var sql = new sqlexec()
		sql.Award_Application_Summary_byApproverMail(line, yearMonth, department, function (result) {
			console.log('result', result)
			const htmlBody = `<caption style="font-weight:600">${yearMonth}${department}${line}的积分申请需要您的审批 <a href="${web_url.webSite + '/#/awardPage/ApprovalPage?mail=true'}" style="text-decoration:underline;">点击此处登录网页</a></caption>
			<table border="2" cellpadding="6" cellspacing="0"  width="100%">
			<tr>
			  <th>财务年月</th>
			  <th>部门</th>
			  <th>机台</th>
			  <th>积分名称</th>
			  <th>积分</th>
			  <th>申请人</th>
			  <th>申请日期</th>
			</tr>
		   ${result.map(item => `<tr>
			  <td>${item.Fin_YearMonth}</td>
			  <td>${item.Department}</td>
			  <td>${item.Line}</td>
			  <td>${item.Award_Type}</td>
			  <td>${item.Points}</td>
			  <td>${item.Create_By}</td>
			  <td>${item.Create_Date}</td>
			</tr>`).join('')}
		  </table>`
			resolve(htmlBody)
		})
	})
}

function sendMailtoApprover(to, subject, htmlbody, fromName) {
	return new Promise((resolve, reject) => {
		// 定义 API 接口地址
		const apiUrl = web_url.mailAPI
		const data = {
			'to': to,
			'subject': subject,
			'htmlbody': htmlbody,
			'fromName': fromName
		}
		request({
			method: "POST",
			url: apiUrl,
			form: data
		}, (err, response, data) => {
			// if(err){resolve(err)}
			// console.log('response',response.statusCode)
			if (response.statusCode == 200) {
				resolve('已发送')
			} else {
				resolve('发送失败')
			}

		})
	})
}








// [
// 	{
// 		Award_Type: '0Delay',
// 		Award_Cycle: 'Daily',
// 		Award_Param: '{"0Delay照片":"imageUpload"}',
// 		Position: '操作工',
// 		Mill: '南京南厂'
// 	},
// 	{
// 		Award_Type: '三连班',
// 		Award_Cycle: 'Daily',
// 		Award_Param: '{"三连班ID":"dropDown"}',
// 		Position: '操作工',
// 		Mill: '南京南厂'
// 	},
// 	{
// 		Award_Type: '换型',
// 		Award_Cycle: 'Daily',
// 		Award_Param: '{"换型照片":"imageUpload","换型类型":"dropDown","换型子类型":"dropDown","奖励类型":"dropDown"}',
// 		Position: '操作工',
// 		Mill: '南京南厂'
// 	}
// ],

// {
// 	'0Delay': {
// 		"base": [],
// 		"value": '',
// 		"0Delay照片": {
// 			"value": '',
// 			"array": ''
// 		},
// 	},
// 	'三连班': {
// 		"base": [],
// 		"value": '',
// 		"三连班ID": {
// 			"value": '',
// 			"array": ''
// 		},
// 	},
// 	'换型': {
// 		"base": [],
// 		"value": '',
// 		"换型照片": {
// 			"value": '',
// 			"array": ''
// 		},
// 		"换型类型": {
// 			"value": '',
// 			"array": ''
// 		},
// 		"换型子类型": {
// 			"value": '',
// 			"array": ''
// 		},
// 		"奖励类型": {
// 			"value": '',
// 			"array": ''
// 		}
// 	}


// }






module.exports = router



