<template>
	<base-content>
		<div class="q-pa-md">
			<q-card class="my-card">
				<q-card-section>
					<div class="text-h6">南京南厂绩效汇总</div>
					<div class="text-subtitle2">OEE_T, OEE_A, Waste 绩效指标</div>
				</q-card-section>

				<q-separator />

				<q-card-section>
					<div class="row q-col-gutter-md">
						<!-- 日期选择器 -->
						<div class="col-12 col-md-6">
							<div class="row q-col-gutter-sm">
								<div class="col-6">
									<q-input filled v-model="dateRange.startDate" label="开始日期" mask="date" :rules="['date']">
										<template v-slot:append>
											<q-icon name="event" class="cursor-pointer">
												<q-popup-proxy ref="qDateProxy" transition-show="scale" transition-hide="scale">
													<q-date v-model="dateRange.startDate" @input="() => $refs.qDateProxy.hide()" />
												</q-popup-proxy>
											</q-icon>
										</template>
									</q-input>
								</div>
								<div class="col-6">
									<q-input filled v-model="dateRange.endDate" label="结束日期" mask="date" :rules="['date']">
										<template v-slot:append>
											<q-icon name="event" class="cursor-pointer">
												<q-popup-proxy ref="qDateProxy2" transition-show="scale" transition-hide="scale">
													<q-date v-model="dateRange.endDate" @input="() => $refs.qDateProxy2.hide()" />
												</q-popup-proxy>
											</q-icon>
										</template>
									</q-input>
								</div>
							</div>
						</div>

						<!-- 生产线选择 -->
						<div class="col-12 col-md-4">
							<q-select filled v-model="selectedLine" :options="lineOptions" label="生产线" emit-value map-options
								multiple use-chips />
						</div>

						<!-- 查询按钮 -->
						<div class="col-12 col-md-2">
							<q-btn color="primary" label="查询" class="full-width" @click="fetchData" :loading="loading" />
						</div>
					</div>
				</q-card-section>

				<q-separator />

				<!-- 选项卡 -->
				<q-tabs v-model="activeTab" class="text-primary">
					<q-tab name="mtd" label="MTD (Month to Date)" />
					<q-tab name="ytd" label="YTD (Year to Date)" />
					<q-tab name="line" label="生产线绩效" />
				</q-tabs>

				<q-separator />

				<q-tab-panels v-model="activeTab" animated>
					<!-- MTD 面板 -->
					<q-tab-panel name="mtd">
						<div class="row q-col-gutter-md">
							<!-- OEE_T 图表 -->
							<div class="col-12 col-md-4">
								<q-card>
									<q-card-section>
										<div class="text-h6">OEE_T (MTD)</div>
										<div style="height: 300px;" v-if="mtdChartData.OEE_T">
											<EchartsComponent ref="oeeT_mtd_chart" :chartData="mtdChartData.OEE_T" />
										</div>
									</q-card-section>
								</q-card>
							</div>

							<!-- OEE_A 图表 -->
							<div class="col-12 col-md-4">
								<q-card>
									<q-card-section>
										<div class="text-h6">OEE_A (MTD)</div>
										<div style="height: 300px;" v-if="mtdChartData.OEE_A">
											<EchartsComponent ref="oeeA_mtd_chart" :chartData="mtdChartData.OEE_A" />
										</div>
									</q-card-section>
								</q-card>
							</div>

							<!-- Waste 图表 -->
							<div class="col-12 col-md-4">
								<q-card>
									<q-card-section>
										<div class="text-h6">Waste (MTD)</div>
										<div style="height: 300px;" v-if="mtdChartData.Waste">
											<EchartsComponent ref="waste_mtd_chart" :chartData="mtdChartData.Waste" />
										</div>
									</q-card-section>
								</q-card>
							</div>
						</div>

						<!-- MTD 数据表格 -->
						<div class="q-mt-md">
							<q-table title="MTD 绩效数据" :data="mtdTableData" :columns="mtdColumns" row-key="line"
								:pagination.sync="pagination">
								<template v-slot:body-cell-OEE_T="props">
									<q-td :props="props">
										{{ formatPercentage(props.value) }}
									</q-td>
								</template>
								<template v-slot:body-cell-OEE_A="props">
									<q-td :props="props">
										{{ formatPercentage(props.value) }}
									</q-td>
								</template>
								<template v-slot:body-cell-Waste="props">
									<q-td :props="props">
										{{ formatPercentage(props.value) }}
									</q-td>
								</template>
							</q-table>
						</div>
					</q-tab-panel>

					<!-- YTD 面板 -->
					<q-tab-panel name="ytd">
						<div class="row q-col-gutter-md">
							<!-- OEE_T 图表 -->
							<div class="col-12 col-md-4">
								<q-card>
									<q-card-section>
										<div class="text-h6">OEE_T (YTD)</div>
										<div style="height: 300px;" v-if="ytdChartData.OEE_T">
											<EchartsComponent ref="oeeT_ytd_chart" :chartData="ytdChartData.OEE_T" />
										</div>
									</q-card-section>
								</q-card>
							</div>

							<!-- OEE_A 图表 -->
							<div class="col-12 col-md-4">
								<q-card>
									<q-card-section>
										<div class="text-h6">OEE_A (YTD)</div>
										<div style="height: 300px;" v-if="ytdChartData.OEE_A">
											<EchartsComponent ref="oeeA_ytd_chart" :chartData="ytdChartData.OEE_A" />
										</div>
									</q-card-section>
								</q-card>
							</div>

							<!-- Waste 图表 -->
							<div class="col-12 col-md-4">
								<q-card>
									<q-card-section>
										<div class="text-h6">Waste (YTD)</div>
										<div style="height: 300px;" v-if="ytdChartData.Waste">
											<EchartsComponent ref="waste_ytd_chart" :chartData="ytdChartData.Waste" />
										</div>
									</q-card-section>
								</q-card>
							</div>
						</div>

						<!-- YTD 数据表格 -->
						<div class="q-mt-md">
							<q-table title="YTD 绩效数据" :data="ytdTableData" :columns="ytdColumns" row-key="line"
								:pagination.sync="pagination">
								<template v-slot:body-cell-OEE_T="props">
									<q-td :props="props">
										{{ formatPercentage(props.value) }}
									</q-td>
								</template>
								<template v-slot:body-cell-OEE_A="props">
									<q-td :props="props">
										{{ formatPercentage(props.value) }}
									</q-td>
								</template>
								<template v-slot:body-cell-Waste="props">
									<q-td :props="props">
										{{ formatPercentage(props.value) }}
									</q-td>
								</template>
							</q-table>
						</div>
					</q-tab-panel>

					<!-- 生产线绩效面板 -->
					<q-tab-panel name="line">
						<div class="row q-col-gutter-md">
							<!-- 生产线选择 -->
							<div class="col-12">
								<q-select filled v-model="selectedLineForDetail" :options="lineOptions" label="选择生产线"
									emit-value map-options @input="fetchLineData" />
							</div>
						</div>

						<div class="row q-col-gutter-md q-mt-md" v-if="selectedLineForDetail">
							<!-- OEE_T 趋势图 -->
							<div class="col-12 col-md-4">
								<q-card>
									<q-card-section>
										<div class="text-h6">OEE_T 趋势</div>
										<div style="height: 300px;" v-if="lineChartData.OEE_T">
											<EchartsComponent ref="oeeT_line_chart" :chartData="lineChartData.OEE_T" />
										</div>
									</q-card-section>
								</q-card>
							</div>

							<!-- OEE_A 趋势图 -->
							<div class="col-12 col-md-4">
								<q-card>
									<q-card-section>
										<div class="text-h6">OEE_A 趋势</div>
										<div style="height: 300px;" v-if="lineChartData.OEE_A">
											<EchartsComponent ref="oeeA_line_chart" :chartData="lineChartData.OEE_A" />
										</div>
									</q-card-section>
								</q-card>
							</div>

							<!-- Waste 趋势图 -->
							<div class="col-12 col-md-4">
								<q-card>
									<q-card-section>
										<div class="text-h6">Waste 趋势</div>
										<div style="height: 300px;" v-if="lineChartData.Waste">
											<EchartsComponent ref="waste_line_chart" :chartData="lineChartData.Waste" />
										</div>
									</q-card-section>
								</q-card>
							</div>
						</div>

						<!-- 生产线详细数据表格 -->
						<div class="q-mt-md" v-if="selectedLineForDetail">
							<q-table title="生产线详细数据" :data="lineTableData" :columns="lineColumns" row-key="id"
								:pagination.sync="pagination">
								<template v-slot:body-cell-OEE_T="props">
									<q-td :props="props">
										{{ formatPercentage(props.value) }}
									</q-td>
								</template>
								<template v-slot:body-cell-OEE_A="props">
									<q-td :props="props">
										{{ formatPercentage(props.value) }}
									</q-td>
								</template>
								<template v-slot:body-cell-Waste="props">
									<q-td :props="props">
										{{ formatPercentage(props.value) }}
									</q-td>
								</template>
							</q-table>
						</div>
					</q-tab-panel>
				</q-tab-panels>
			</q-card>
		</div>
	</base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
import EchartsComponent from '../../components/ECharts/EchartsComponent.vue'
import moment from 'moment'

export default {
	name: 'NanjingSouthPerformance',
	components: {
		BaseContent,
		EchartsComponent
	},
	data() {
		return {
			// 日期范围
			dateRange: {
				startDate: moment().subtract(30, 'days').format('YYYY/MM/DD'),
				endDate: moment().format('YYYY/MM/DD')
			},
			// 选项卡
			activeTab: 'mtd',
			// 生产线选择
			lineOptions: [],
			selectedLine: [],
			selectedLineForDetail: null,
			// 加载状态
			loading: false,
			// 分页设置
			pagination: {
				rowsPerPage: 10
			},
			// 原始数据
			rawData: null,
			// MTD 图表数据
			mtdChartData: {
				OEE_T: null,
				OEE_A: null,
				Waste: null
			},
			// YTD 图表数据
			ytdChartData: {
				OEE_T: null,
				OEE_A: null,
				Waste: null
			},
			// 生产线图表数据
			lineChartData: {
				OEE_T: null,
				OEE_A: null,
				Waste: null
			},
			// MTD 表格数据
			mtdTableData: [],
			// YTD 表格数据
			ytdTableData: [],
			// 生产线表格数据
			lineTableData: [],
			// MTD 表格列定义
			mtdColumns: [
				{ name: 'line', label: '生产线', field: 'line', align: 'left', sortable: true },
				{ name: 'OEE_T', label: 'OEE_T', field: 'OEE_T', align: 'center', sortable: true },
				{ name: 'OEE_A', label: 'OEE_A', field: 'OEE_A', align: 'center', sortable: true },
				{ name: 'Waste', label: 'Waste', field: 'Waste', align: 'center', sortable: true }
			],
			// YTD 表格列定义
			ytdColumns: [
				{ name: 'line', label: '生产线', field: 'line', align: 'left', sortable: true },
				{ name: 'OEE_T', label: 'OEE_T', field: 'OEE_T', align: 'center', sortable: true },
				{ name: 'OEE_A', label: 'OEE_A', field: 'OEE_A', align: 'center', sortable: true },
				{ name: 'Waste', label: 'Waste', field: 'Waste', align: 'center', sortable: true }
			],
			// 生产线表格列定义
			lineColumns: [
				{ name: 'date', label: '日期', field: 'date', align: 'left', sortable: true },
				{ name: 'OEE_T', label: 'OEE_T', field: 'OEE_T', align: 'center', sortable: true },
				{ name: 'OEE_A', label: 'OEE_A', field: 'OEE_A', align: 'center', sortable: true },
				{ name: 'Waste', label: 'Waste', field: 'Waste', align: 'center', sortable: true }
			]
		}
	},
	mounted() {
		this.getProductionLines();
	},
	methods: {
		// 获取生产线列表
		async getProductionLines() {
			try {
				const { data: lines } = await this.$http.get('performanceSummary/getProductionLines');
				this.lineOptions = lines.map(line => ({
					label: line,
					value: line
				}));
			} catch (error) {
				console.error('获取生产线列表失败:', error);
				this.$q.notify({
					type: 'negative',
					message: '获取生产线列表失败',
					position: 'top'
				});
			}
		},
		// 获取绩效数据
		async fetchData() {
			if (!this.dateRange.startDate || !this.dateRange.endDate) {
				this.$q.notify({
					type: 'warning',
					message: '请选择开始和结束日期',
					position: 'top'
				});
				return;
			}

			this.loading = true;
			try {
				const { data } = await this.$http.get('performanceSummary/getSummaryData', {
					params: {
						startDate: this.dateRange.startDate,
						endDate: this.dateRange.endDate
					}
				});

				this.rawData = data;
				this.processData();
			} catch (error) {
				console.error('获取绩效数据失败:', error);
				this.$q.notify({
					type: 'negative',
					message: '获取绩效数据失败',
					position: 'top'
				});
			} finally {
				this.loading = false;
			}
		},
		// 获取特定生产线的数据
		async fetchLineData() {
			if (!this.selectedLineForDetail || !this.rawData) {
				return;
			}

			// 从原始数据中筛选选定生产线的数据
			const lineData = this.rawData.lineData[this.selectedLineForDetail] || [];

			// 按日期排序
			const sortedData = [...lineData].sort((a, b) => moment(a.Date).diff(moment(b.Date)));

			// 准备表格数据
			this.lineTableData = sortedData.map((item, index) => {
				// 计算单日的OEE_T, OEE_A, Waste
				const oeeT = this.calculateOEE_T([item]);
				const oeeA = this.calculateOEE_A([item]);
				const waste = this.calculateWaste([item]);

				return {
					id: index, // 添加唯一ID作为键
					date: moment(item.Date).format('YYYY-MM-DD'),
					OEE_T: oeeT,
					OEE_A: oeeA,
					Waste: waste
				};
			});

			// 准备图表数据
			const dates = sortedData.map(item => moment(item.Date).format('YYYY-MM-DD'));
			const oeeTValues = this.lineTableData.map(item => item.OEE_T * 100);
			const oeeAValues = this.lineTableData.map(item => item.OEE_A * 100);
			const wasteValues = this.lineTableData.map(item => item.Waste * 100);

			this.lineChartData = {
				OEE_T: {
					name: `${this.selectedLineForDetail} OEE_T 趋势`,
					categories: dates,
					series: [{
						name: 'OEE_T',
						type: 'bar', // 添加图表类型
						data: oeeTValues
					}]
				},
				OEE_A: {
					name: `${this.selectedLineForDetail} OEE_A 趋势`,
					categories: dates,
					series: [{
						name: 'OEE_A',
						type: 'bar', // 添加图表类型
						data: oeeAValues
					}]
				},
				Waste: {
					name: `${this.selectedLineForDetail} Waste 趋势`,
					categories: dates,
					series: [{
						name: 'Waste',
						type: 'bar', // 添加图表类型
						data: wasteValues
					}]
				}
			};

			// 更新图表
			this.$nextTick(() => {
				if (this.$refs.oeeT_line_chart) this.$refs.oeeT_line_chart.drawChart();
				if (this.$refs.oeeA_line_chart) this.$refs.oeeA_line_chart.drawChart();
				if (this.$refs.waste_line_chart) this.$refs.waste_line_chart.drawChart();
			});
		},
		// 处理数据
		processData() {
			if (!this.rawData) return;

			// 处理MTD数据
			this.processMTDData();

			// 处理YTD数据
			this.processYTDData();

			// 如果已选择生产线，更新生产线详细数据
			if (this.selectedLineForDetail) {
				this.fetchLineData();
			}
		},
		// 处理MTD数据
		processMTDData() {
			const mtdData = this.rawData.mtdData;

			// 准备表格数据
			this.mtdTableData = Object.keys(mtdData).map(line => ({
				line,
				OEE_T: mtdData[line].OEE_T,
				OEE_A: mtdData[line].OEE_A,
				Waste: mtdData[line].Waste
			}));

			// 筛选选定的生产线
			let filteredData = this.mtdTableData;
			if (this.selectedLine && this.selectedLine.length > 0) {
				filteredData = filteredData.filter(item => this.selectedLine.includes(item.line));
			}

			// 准备图表数据
			const lines = filteredData.map(item => item.line);
			const oeeTValues = filteredData.map(item => item.OEE_T * 100);
			const oeeAValues = filteredData.map(item => item.OEE_A * 100);
			const wasteValues = filteredData.map(item => item.Waste * 100);

			this.mtdChartData = {
				OEE_T: {
					name: 'OEE_T (MTD)',
					categories: lines,
					series: [{
						name: 'OEE_T',
						type: 'bar', // 添加图表类型
						data: oeeTValues
					}]
				},
				OEE_A: {
					name: 'OEE_A (MTD)',
					categories: lines,
					series: [{
						name: 'OEE_A',
						type: 'bar', // 添加图表类型
						data: oeeAValues
					}]
				},
				Waste: {
					name: 'Waste (MTD)',
					categories: lines,
					series: [{
						name: 'Waste',
						type: 'bar', // 添加图表类型
						data: wasteValues
					}]
				}
			};

			// 更新图表
			this.$nextTick(() => {
				if (this.$refs.oeeT_mtd_chart) this.$refs.oeeT_mtd_chart.drawChart();
				if (this.$refs.oeeA_mtd_chart) this.$refs.oeeA_mtd_chart.drawChart();
				if (this.$refs.waste_mtd_chart) this.$refs.waste_mtd_chart.drawChart();
			});
		},
		// 处理YTD数据
		processYTDData() {
			const ytdData = this.rawData.ytdData;

			// 准备表格数据
			this.ytdTableData = Object.keys(ytdData).map(line => ({
				line,
				OEE_T: ytdData[line].OEE_T,
				OEE_A: ytdData[line].OEE_A,
				Waste: ytdData[line].Waste
			}));

			// 筛选选定的生产线
			let filteredData = this.ytdTableData;
			if (this.selectedLine && this.selectedLine.length > 0) {
				filteredData = filteredData.filter(item => this.selectedLine.includes(item.line));
			}

			// 准备图表数据
			const lines = filteredData.map(item => item.line);
			const oeeTValues = filteredData.map(item => item.OEE_T * 100);
			const oeeAValues = filteredData.map(item => item.OEE_A * 100);
			const wasteValues = filteredData.map(item => item.Waste * 100);

			this.ytdChartData = {
				OEE_T: {
					name: 'OEE_T (YTD)',
					categories: lines,
					series: [{
						name: 'OEE_T',
						type: 'bar', // 添加图表类型
						data: oeeTValues
					}]
				},
				OEE_A: {
					name: 'OEE_A (YTD)',
					categories: lines,
					series: [{
						name: 'OEE_A',
						type: 'bar', // 添加图表类型
						data: oeeAValues
					}]
				},
				Waste: {
					name: 'Waste (YTD)',
					categories: lines,
					series: [{
						name: 'Waste',
						type: 'bar', // 添加图表类型
						data: wasteValues
					}]
				}
			};

			// 更新图表
			this.$nextTick(() => {
				if (this.$refs.oeeT_ytd_chart) this.$refs.oeeT_ytd_chart.drawChart();
				if (this.$refs.oeeA_ytd_chart) this.$refs.oeeA_ytd_chart.drawChart();
				if (this.$refs.waste_ytd_chart) this.$refs.waste_ytd_chart.drawChart();
			});
		},
		// 格式化百分比
		formatPercentage(value) {
			return (value * 100).toFixed(2) + '%';
		},
		// 计算OEE_T
		// OEE_T计算公式: DIVIDE(sum([Plan_Time])-sum([Delay_Time])-sum([MachineWasteMin])-sum([Speed_Loss_T_Min]),sum('[Plan_Time]));
		calculateOEE_T(data) {
			let sumPlanTime = 0;
			let sumDelayTime = 0;
			let sumMachineWasteMin = 0;
			let sumSpeedLossTMin = 0;

			data.forEach(item => {
				sumPlanTime += item.Plan_Time || 0;
				sumDelayTime += item.Delay_Time || 0;
				sumMachineWasteMin += item.MachineWasteMin || 0;
				sumSpeedLossTMin += item.Speed_Loss_T_Min || 0;
			});

			if (sumPlanTime === 0) {
				return 0;
			}

			return (sumPlanTime - sumDelayTime - sumMachineWasteMin - sumSpeedLossTMin) / sumPlanTime;
		},
		// 计算OEE_A
		// OEE_A计算公式: DIVIDE(sum([Plan_Time])-sum([Delay_Time])-sum([MachineWasteMin])-sum([Speed_Loss_A_Min]),sum('[Plan_Time]));
		calculateOEE_A(data) {
			let sumPlanTime = 0;
			let sumDelayTime = 0;
			let sumMachineWasteMin = 0;
			let sumSpeedLossAMin = 0;

			data.forEach(item => {
				sumPlanTime += item.Plan_Time || 0;
				sumDelayTime += item.Delay_Time || 0;
				sumMachineWasteMin += item.MachineWasteMin || 0;
				sumSpeedLossAMin += item.Speed_Loss_A_Min || 0;
			});

			if (sumPlanTime === 0) {
				return 0;
			}

			return (sumPlanTime - sumDelayTime - sumMachineWasteMin - sumSpeedLossAMin) / sumPlanTime;
		},
		// 计算Waste
		// Waste计划公式: DIVIDE([CullCuts],[TotalCuts]);
		calculateWaste(data) {
			let sumCullCuts = 0;
			let sumTotalCuts = 0;

			data.forEach(item => {
				sumCullCuts += item.CullCuts || 0;
				sumTotalCuts += item.TotalCuts || 0;
			});

			if (sumTotalCuts === 0) {
				return 0;
			}

			return sumCullCuts / sumTotalCuts;
		}
	}
}
</script>

<style scoped>
.my-card {
	width: 100%;
	max-width: 100%;
}
</style>
