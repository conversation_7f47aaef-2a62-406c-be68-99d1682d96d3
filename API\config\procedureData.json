[{"procedureDB": "NJDatacenter", "procedureName": "dbo.邮件发送_日报白班", "cycle": "daily", "startHour": 21, "startMinutes": 30, "process_id": 4}, {"procedureDB": "NJDatacenter", "procedureName": "dbo.邮件发送_点子银行日报_南厂", "cycle": "daily", "startHour": 9, "startMinutes": 0, "process_id": 6}, {"procedureDB": "NJDatacenter", "procedureName": "dbo.邮件发送_点子银行日报_北厂", "cycle": "daily", "startHour": 9, "startMinutes": 0, "process_id": 7}, {"procedureDB": "NJDatacenter", "procedureName": "dbo.邮件发送_TOP2_Delay_D", "cycle": "daily", "startHour": 21, "startMinutes": 30, "process_id": 5}, {"procedureDB": "NJDatacenter", "procedureName": "dbo.邮件发送_日报夜班", "cycle": "daily", "startHour": 10, "startMinutes": 0, "process_id": 8}, {"procedureDB": "NJDatacenter", "procedureName": "dbo.邮件发送_TOP2_Delay_N", "cycle": "daily", "startHour": 10, "startMinutes": 0, "process_id": 9}, {"procedureDB": "NJDatacenter", "procedureName": "dbo.邮件发送_异常报告", "cycle": "daily", "startHour": 10, "startMinutes": 0, "process_id": 10}, {"procedureDB": "NJDatacenter", "procedureName": "dbo.月度维修人员名单获取", "cycle": "daily", "startHour": 10, "startMinutes": 0, "process_id": 11}, {"procedureDB": "NJDatacenter", "procedureName": "dbo.已返工片数更新", "cycle": "daily", "startHour": 10, "startMinutes": 0, "process_id": 12}, {"procedureDB": "NJDatacenter", "procedureName": "dbo.月度人员名单获取", "cycle": "daily", "startHour": 10, "startMinutes": 0, "process_id": 13}, {"procedureDB": "NJDatacenter", "procedureName": "dbo.邮件发送_TOP3延误周报", "cycle": "weekly", "startHour": 11, "startMinutes": 30, "process_id": 14}, {"procedureDB": "NJDatacenter", "procedureName": "dbo.邮件发送_点子银行周报", "cycle": "weekly", "startHour": 11, "startMinutes": 30, "process_id": 15}, {"procedureDB": "system_config", "procedureName": "dbo.insertDataTest1", "cycle": "daily", "startHour": 16, "startMinutes": 16, "process_id": 16}, {"procedureDB": "system_config", "procedureName": "dbo.insertDataTest2", "cycle": "daily", "startHour": 16, "startMinutes": 17, "process_id": 17}]