<template>
	<base-content>
		<div class="q-pa-md">
			<q-dialog v-model="prompt">
				<q-card style="min-width:99%">

					<div style="width: 100%;">

						<q-btn label="关闭窗口" push color="primary" class="full-width" v-close-popup />
			  <awardComponents :moduleFunction="system" :billID="selectedAwardID"></awardComponents>

						<q-card-actions align="right" class="text-primary">
							<q-btn label="关闭窗口" push color="primary" class="full-width" v-close-popup />
						</q-card-actions>
					</div>
				</q-card>

		</q-dialog>
		
		
		
		<q-dialog v-model="approvalShowdialog">
			<q-card style="min-width:60%">
			<div class="q-pa-md">
				<q-stepper v-model="approvalIndex" ref="stepper" color="primary" animated>
					<q-step :name="1" title="第一层审批" icon="settings" :done="approvalIndex > 1">
			
						<div class="q-pa-md row items-start q-gutter-md" v-if="approvalData">
							<template v-for="item in approvalData">
								<q-card class="my-card-detail" style="width:30%">
									<q-card-section :class="item.审批状态=='已批准'?'bg-primary  text-white':item.审批状态=='已拒绝'?'bg-red text-white':'bg-white text-black '">
										<div class="text-h6">{{item.审批人姓名}}---{{item.审批状态}}</div>
										<q-separator color="white" />
										<div class="text-subtitle2">{{item.审批时间.substring(0,10)}}</div>
									</q-card-section>
								</q-card>
							</template>
						</div>
					</q-step>
			
					<q-step :name="2" title="第二层审批" caption="Optional" icon="create_new_folder" :done="approvalIndex > 2">
						<div class="q-pa-md row items-start q-gutter-md" v-if="approvalData">
							<template v-for="item in approvalData">
								<q-card class="my-card-detail" style="width:30%">
									<q-card-section :class="item.审批状态=='已批准'?'bg-primary  text-white':'bg-purple text-white'">
										<div class="text-h6">{{item.审批人姓名}}---{{item.审批状态}}</div>
										<q-separator color="white" />
										<div class="text-subtitle2">{{item.审批时间.substring(0,10)}}</div>
									</q-card-section>
									
								</q-card>
							</template>
						</div>
					</q-step>
			
					<q-step :name="3" title="第三层审批" icon="assignment" :done="approvalIndex > 3">
						<div class="q-pa-md row items-start q-gutter-md" v-if="approvalData">
							<template v-for="item in approvalData">
								<q-card class="my-card-detail" style="width:30%">
									<q-card-section :class="item.审批状态=='已批准'?'bg-primary  text-white':'bg-purple text-white'">
										<div class="text-h6">{{item.审批人姓名}}---{{item.审批状态}}</div>
										<q-separator color="white" />
										<div class="text-subtitle2">{{item.审批时间.substring(0,10)}}</div>
									</q-card-section>
									
								</q-card>
							</template>
						</div>
					</q-step>
			
					<q-step :name="4" title="已完成审批" icon="add_comment" :done="approvalIndex==4">
						<h3 class="text-center">已全部审批</h3>
					</q-step>
				</q-stepper>
			</div>
			</q-card>	
		</q-dialog>
			
		
		
		
			<q-table :data="data" :columns="columns" row-key="AwardID" :filter="filter" :pagination.sync="pagination"
			 hide-pagination>
				<template v-slot:top-left>
					<span style="font-weight: 600;font-size: 25px;line-height: 40px;">积分维修月度申请</span>
				</template>

				<template v-slot:top-right>
					<q-input borderless dense debounce="300" v-model="filter" placeholder="Search">
						<template v-slot:append>
							<q-icon name="search" />
						</template>
					</q-input>
				</template>


				<template v-slot:header="props">
					<q-tr :props="props">
						<q-th auto-width />

						<q-th v-for="col in props.cols" :key="col.name" :props="props">
							{{ col.label }}
						</q-th>
						<q-th auto-width />
					</q-tr>
				</template>

				<template v-slot:body="props">
					<q-tr :props="props">
						<q-td auto-width>
							<q-btn v-model="props.expand" size="sm" color="accent" :label="props.row.审批状态" @click="approvalShow(props)"
							 :color="props.row.审批状态=='progress' ? 'secondary' : props.row.审批状态=='rejected'?'red':'primary'" />
						</q-td>
						<q-td v-for="col in props.cols" :key="col.name" :props="props" @click="gotoDetail(props.row.AwardID)">
							{{ col.value }}
						</q-td>

						<q-td auto-width>
							<q-btn v-show="props.row.审批进度ID<4" size="xs" @click="delItem([props])" color="negative" icon="clear" />
						</q-td>

					</q-tr>

				</template>

			</q-table>


			<div class="row justify-center q-mt-md">
				<q-pagination v-model="pagination.page" color="grey-8" :max="pagesNumber" size="sm" />
			</div>
		</div>
	</base-content>
</template>

<script>
	import BaseContent from '../../components/BaseContent/BaseContent'
	import awardComponents from '../../components/componentsList/index.vue'
	import qs from 'qs'
	export default {
		components: {
			BaseContent,
			awardComponents
		},
		data() {
			return {
				system: '积分系统维修',
				queryData: false,
				step: 4,
				visible: false,
				prompt: false,
				address: '111',
				yearmonth: [202103],
				mill: ['南京南厂', '南京北厂'],
				line: ['ND04', 'ND05', 'ND06', 'ND07', 'ND08', 'ND09'],
				selectedYearmonth: '',
				selectedLine: '',
				selectedmill: '',
				userData: false,
				summaryData: false,
				summaryTotal: 0,
				applySummaryData: false,
				selectedAwardID: false,
				pagination: {

					descending: false,
					page: 1,
					rowsPerPage: 20
					// rowsNumber: xx if getting data from a server
				},
				filter: '',
				columns: [{
						name: '工厂',
						label: '工厂',
						align: 'left',
						field: '工厂',
					},
					{
						name: '机台',
						label: '机台',
						align: 'left',
						field: '机台',
					},
					{
						name: '年月',
						label: '年月',
						align: 'left',
						field: '年月',
						sortable: true
					},
					{
						name: '机台小计',
						label: '机台小计',
						align: 'left',
						field: '总金额',
						sortable: true
					},
					{
						name: 'oee',
						label: 'OEE',
						align: 'left',
						field: 'oee'
					},
					{
						name: '优秀班组',
						align: 'left',
						label: '优秀班组',
						field: '优秀班组',
						sortable: true
					},
					{
						name: 'Waste',
						align: 'left',
						label: 'Waste',
						field: 'Waste',
						sortable: true
					},
					{
						name: '安全',
						align: 'left',
						label: '安全',
						field: '安全'
					},
					{
						name: '质量',
						align: 'left',
						label: '质量',
						field: '质量',
						sortable: true
					},
					{
						name: 'ODelay',
						align: 'left',
						label: 'ODelay',
						field: 'ODelay',
						sortable: true
					},
					{
						name: '三连班',
						align: 'left',
						label: '三连班',
						field: '三连班'
					},
					{
						name: '破纪录',
						align: 'left',
						label: '破纪录',
						field: '破纪录',
						sortable: true
					},
					{
						name: '换型',
						align: 'left',
						label: '换型',
						field: '换型',
						sortable: true
					},
					{
						name: '点子',
						align: 'left',
						label: '点子',
						field: '点子'
					},
					{
						name: 'BP',
						align: 'left',
						label: 'BP',
						field: 'BP'
					},
					{
						name: '积分调整',
						align: 'left',
						label: '积分调整',
						field: '积分调整'
					},
					{
						name: '项目奖励',
						align: 'left',
						label: '项目奖励',
						field: '项目奖励'
					},
					{
						name: 'OT奖励',
						align: 'left',
						label: 'OT奖励',
						field: 'OT奖励'
					},
					{
						name: '申请人',
						align: 'left',
						label: '申请人',
						field: '申请人'
					},
					{
						name: '审批状态',
						align: 'left',
						label: '审批状态',
						field: '审批状态'
					},
					{
						name: 'AwardID',
						align: 'left',
						label: 'AwardID',
						field: 'AwardID',
						required: true,
					}
				],
				data: [],
				approvalShowdialog:false,
				approvalData:false,
				approvalIndex:false
			}
		},
		mounted() {
			this.getApplyAward()

		},
		computed: {
			pagesNumber() {
				return Math.ceil(this.data.length / this.pagination.rowsPerPage)
			}
		},
		methods: {
			approvalShow(props) {
				console.log(props)
				this.approvalIndex=props.row.审批进度ID
				this.approvalList(props.row.AwardID, props.row.审批进度ID)
				this.approvalShowdialog=true
			},
			add() {
				this.prompt = true
				this.queryData = true
				this.userData = false
				this.summaryData = false
			},

			delItem(e) {
				console.log(e)
				this.delApplyAward(e[0].row.AwardID)
			},


			async getApplyAward() {

				var _this = this
				var awardID = _this.selectedYearmonth + _this.selectedLine
				const {
					data: res
				} = await _this.$http.get('approve/getApplyAwardSummary?system=积分系统维修')
				console.log(res)
				_this.data = res
			},

			async delApplyAward(awardID) {
				var data = {
					"awardID": awardID
				}
				var _this = this
				_this.$http.post('approve/delAwardApply', data).then(function(response) {
					console.log(response)
					if (response.data == '删除成功') {
						console.log("开始刷新数据")
						_this.getApplyAward()
					}
				})
			},

			gotoDetail(e) {
				this.selectedAwardID = e
				// this.getAward(e[0])
				// this.selectedYearmonth = e[1]
				this.prompt = true
				this.visible = true
			},

			async approvalList(awardID, processID,item) {
				this.approvalData=[]
				const {
					data: res
				} = await this.$http.get('approve/getApproveStatus?system=积分系统维修&applyID=' + awardID + '&levelID=' + processID)

				console.log(res)
				this.approvalData=res

			},


			applyStatus(e) {
				console.log(e)
			}





		}
	}
</script>

<style scoped>
	.my-card-detail {
		width: 100%;
		max-width: 250px
	}
</style>
