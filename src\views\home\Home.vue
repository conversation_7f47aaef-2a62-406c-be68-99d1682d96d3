<template>
    <!-- 写HTML -->
    <base-content>
        <div class="">
            <div style="background-color: #0046C8; width: 100%;height: 160px;">
                <img src="http://172.21.118.163:3003/Web_image/Logo.png"
                    style="margin-left: 40px;margin-top: 5px;width: 30%;height: 80px;">
                
                <div style="color: white;position:absolute;top:80px;left: 68px;font-size: 40px;font-weight: 600">
                    南京南厂数字化平台
                </div>
                
                <div style="color:white;position:absolute;top:80px;right: 40px;font-size: 15px;font-weight: 600">
                    愿景：成为客户信赖,员工自豪,精益智能的世界标杆工厂
                </div>
                <!-- <div style="color:white;position:absolute;top:110px;right: 40px;font-size: 15px;font-weight: 600">
                    使命：我们始终致力于：为消费者提供超越期望的产品和服务，同时关爱员工，敏捷协作，共创价值，支持公司可持续发展
                </div> -->
                <div style="color:white;position:absolute;top:50px;right: 40px;font-size: 15px;font-weight: 600">
                    {{ username }},你好！ 欢迎登陆南京南厂数字化平台
                </div>
            </div>
        </div>
        <div class="">
            <div class="q-pa-md row items-start q-gutter-md" v-if="data">
                <q-card style="width: 100%;height:100%;">
                    <q-tabs v-model="tab" align="justify" class="text-grey-5  shadow-2" active-color="primary"
                        style="height: 50px;" indicator-color="primary">
                        <template v-for="(value, key) in data">
                            <!-- {{ key }} -->
                            <q-tab :name="key" :label="key" style="font-weight: 600" />
                        </template>
                    </q-tabs>
                    <q-separator />
                    <q-tab-panels v-model="tab" animated>
                        <q-tab-panel :name="tab">
                            <!-- <q-icon left size="3em" name="fas fa-solid fa-award" /> -->
                            <div class="q-pa-md row items-start q-gutter-md">
                                <template v-for="(value, sub_category) in data[tab]">
                                    <q-card style="width: 100%;">
                                        <div class="text-h6 text-center text-bold">{{ sub_category }}</div>
                                        <div class="q-pa-md q-gutter-md">
                                            <template v-for="(value, app_name) in data[tab][sub_category]">
                                                <q-btn class=" text-bold " size="md"
                                                    style="background-color: #0046C8; color: white; width: 250px;">
                                                    <q-icon left size="3em"
                                                        :name="data[tab][sub_category][app_name]['icon']" />
                                                    <div>{{ app_name }}</div>
                                                    <q-menu fit anchor="bottom left" self="top left"
                                                        transition-show="flip-right" transition-hide="flip-left">
                                                        <template
                                                            v-for="item in data[tab][sub_category][app_name]['Menu_Name']">
                                                            <q-item clickable>
                                                                <q-item-section
                                                                    @click="gotoPage(item['Link_Side'], item['Link'])">
                                                                    {{ item['name'] }}
                                                                </q-item-section>
                                                            </q-item>
                                                        </template>
                                                    </q-menu>
                                                </q-btn>


                                            </template>
                                        </div>

                                    </q-card>
                                </template>
                            </div>
                        </q-tab-panel>
                    </q-tab-panels>
                </q-card>
            </div>
        </div>
    </base-content>
</template>

<script>
import BaseContent from '../../components/BaseContent/BaseContent'
export default {
    name: 'Home',
    components: {
        BaseContent,
    },
    mounted() {
        if (sessionStorage.getItem('web_Index_router')) {
            console.log('有记录')
            console.log(JSON.parse(sessionStorage.getItem('web_Index_router')))
            this.data = JSON.parse(sessionStorage.getItem('web_Index_router'))
            this.tab = Object.keys(JSON.parse(sessionStorage.getItem('web_Index_router')))[0]
        } else {
            this.Web_Index_Router()
        }
        console.log('getLine')
        // this.getPIData()
        this.getLine()
        this.username = localStorage.getItem('username')

    },
    data() {
        return {
            tab: '',
            tabList: [],
            data: false,
            username: ''
        }
    },
    methods: {
        gotoPage(Link_Side, Link) {
            console.log('Link_Side', Link_Side)
            console.log('Link', Link)
            if (Link_Side == 'in_site') {
                this.$router.push(Link)
            }
            if (Link_Side == 'out_site') {
                window.open(Link, '_blank');
            }
        },
        async Web_Index_Router() {
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`data_base/Web_Index_Router`)
            console.log('Web_Index_Router', res)
            _this.data = res
            this.tab = Object.keys(res)[0]
            sessionStorage.setItem('web_Index_router', JSON.stringify(res))
        },
        async getLine() {
            console.log('getLine_internal')
            var _this = this
            const {
                data: res
            } = await _this.$http.get(`approve/getLine?mill=${localStorage.getItem('mill')}`)
            console.log('line', res)
            _this.$store.commit('updateLineArray', res)
            localStorage.setItem('lineArray', res)
        },
        // getPIData() {
        //     var _this = this
        //     const query = {
        //         url: 'http://localhost:3000/osi/post_data',
        //         type: 'FORM',
        //         data: {
        //             server: 'CNNBA020',
        //             data: ['ND05.LOGIX_MMC.MMC_HmiProdShfDisplay_0_2', 'ND05.LOGIX_MMC.MMC_HmiProdShfDisplay_0_3', 'ND05.LOGIX_MMC.MMC_HMI.MchSpdFbk'],
        //             data_Len: 3
        //         },

        //     }
        //     _this.$fetchData(query).then(res => {
        //         console.log(res.data)

        //     }).catch(error => {
        //         console.log(error)
        //     })
        // },

        // async getPIData() {
        //     var _this = this
        //     const server = 'CNNJA020'
        //     let PIPointData = {
        //         'MachineHealthRate': 'CNNJ_NJ02_MachineHealthRate',
        //         'ElectricalControlRate': 'CNNJ_NJ02_ElectricalControlRate'
        //     }
        //     PIPointData = JSON.stringify(PIPointData)
        //     const {
        //         data: res
        //     } = await _this.$piAPI.get(`api/getPIData?server=${server}&PIPointArray=${PIPointData}`)
        //     console.log(res)
        // },

    }

}
</script>
<!-- 
<style lang="sass" scoped>
.my-card
  width: 100%
  max-width: 250px
</style> -->

<!-- <style lang="css" scoped>
.my-card {
    width: 100%;
    min-height: 250px;
    height: 100%;
    /*max-width: 350px;*/
}

.my-card-system {
    width: 250px;
    height: 150px;
} -->
<!-- </style> -->
