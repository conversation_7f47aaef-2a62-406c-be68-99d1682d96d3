<template>
  <div class="user-registration">
    <h2 class="text-h6 q-mb-md">用户注册</h2>

    <q-form @submit="registerUser" class="q-gutter-md">
      <div class="row q-col-gutter-md">
        <div class="col-xs-12 col-sm-6">
          <q-input
            filled
            v-model="formData.employee_id"
            label="员工ID *"
            :rules="[val => !!val || '员工ID不能为空']"
          />
        </div>

        <div class="col-xs-12 col-sm-6">
          <q-input
            filled
            v-model="formData.employee_name"
            label="员工姓名 *"
            :rules="[val => !!val || '员工姓名不能为空']"
          />
        </div>
      </div>

      <div class="row justify-end q-mt-md">
        <q-btn label="重置" type="reset" color="grey-7" flat class="q-ml-sm" @click="resetForm" />
        <q-btn label="注册" type="submit" color="primary" :loading="loading" />
      </div>
    </q-form>

    <!-- 注册成功对话框 -->
    <q-dialog v-model="successDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section class="bg-positive text-white">
          <div class="text-h6">注册成功</div>
        </q-card-section>

        <q-card-section class="q-pt-md">
          <p>用户 <strong>{{ registerResult.employee_id }}</strong> 注册成功！</p>
          <p>临时密码: <q-badge color="primary">{{ registerResult.temporary_password }}</q-badge></p>
          <p class="text-negative">请记下此密码，页面关闭后将不再显示！</p>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="关闭" color="primary" v-close-popup @click="resetForm" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script>
export default {
  name: 'UserRegistration',
  data() {
    return {
      formData: {
        employee_id: '',
        employee_name: '',
        mill: '南京南厂'
      },
      loading: false,
      successDialog: false,
      registerResult: {
        employee_id: '',
        temporary_password: ''
      }
    }
  },
  methods: {
    async registerUser() {
      // 验证表单
      if (!this.formData.employee_id ||
          !this.formData.employee_name) {
        this.$q.notify({
          color: 'negative',
          message: '请填写所有必填字段',
          icon: 'warning'
        })
        return
      }

      this.loading = true

      try {
        // 创建要发送的数据对象，添加默认值
        const userData = {
          employee_id: this.formData.employee_id,
          employee_name: this.formData.employee_name,
          mill: this.formData.mill,
          // 为了兼容后端API，添加空的department和title字段
          department: '',
          title: ''
        }

        const response = await this.$http.post('/userManagement_api/register', userData)

        if (response.data.status === 200) {
          // 注册成功
          this.registerResult = response.data.data
          this.successDialog = true

          // 添加通知提示
          this.$q.notify({
            color: 'positive',
            message: '用户注册成功',
            icon: 'check'
          })
        } else {
          // 注册失败
          console.error('注册失败：', response.data)
          this.$q.notify({
            color: 'negative',
            message: response.data.message || '用户注册失败',
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('注册请求错误：', error)
        // 显示详细错误信息
        let errorMsg = '网络请求错误，请稍后重试'
        if (error.response && error.response.data) {
          errorMsg = `错误: ${error.response.data.message || error.message}`
        } else if (error.message) {
          errorMsg = `错误: ${error.message}`
        }
        this.$q.notify({
          color: 'negative',
          message: errorMsg,
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    resetForm() {
      this.formData = {
        employee_id: '',
        employee_name: '',
        mill: '南京南厂'
      }
    }
  }
}
</script>

<style scoped>
.user-registration {
  max-width: 800px;
  margin: 0 auto;
}
</style>