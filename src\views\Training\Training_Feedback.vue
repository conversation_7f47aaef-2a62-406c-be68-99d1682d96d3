<!-- 写HTML -->
<template>

    <base-content>
        <div class="q-pa-md">
            <div class="text-center text-h5 text-weight-bold">学员评价</div>
        </div>
        <div class="fit column  content-center">
            <div class="q-gutter-md row wrap justify-center">
                <div class="row wrap justify-start">
                    <div style="font-size: 15px;line-height: 45px;font-weight: 600;">部门/机台：</div>
                    <q-select outlined v-model="myIdeaSelectedData['department']"
                        :options="myIdeaDropDownData['department']" label-slot clearable style="width: 200px;" dense>
                        <template v-slot:label>部门/机台
                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                style="font-size: 12px;">必填</em>
                        </template>
                    </q-select>
                </div>
                <div class="row wrap justify-start">
                    <div style="font-size: 15px;line-height: 45px;font-weight: 600;">学员姓名：</div>
                    <q-select outlined v-model="myIdeaSelectedData['employee_Name']"
                        :options="myIdeaDropDownData['employee_Name']" label-slot clearable style="width: 200px;" dense>
                        <template v-slot:label>学员姓名
                            <em class="q-px-sm bg-deep-orange text-white rounded-borders"
                                style="font-size: 12px;">必填</em>
                        </template>
                    </q-select>
                </div>
            </div>

            <div class="q-gutter-md row wrap justify-center" style="margin-top:10px;">
                <q-input v-model="Advantage" filled type="textarea" style="width: 600px;" label="好的方面" />
            </div>
            <div class="q-gutter-md row wrap justify-center" style="margin-top:10px;">
                <q-input v-model="Disadvantage" filled type="textarea" style="width: 600px;" label="待改进的方面" />
            </div>

            <div style="margin-top:10px;">
                <div class="row wrap justify-end">
                    <q-input v-model="Create_By" filled label="反馈人" style="margin-right: 20px;" />
                    <q-btn color="primary" label="提交反馈" @click="submitFeedback" :loading="loading" />
                </div>

            </div>





        </div>
    </base-content>
</template>
<script>
import BaseContent from '../../components/BaseContent/BaseContent'

export default {
    name: 'Home',
    components: {
        BaseContent,
    },
    //页面首次进入的默认
    mounted() {
        this.getDepartment()
        this.getEmployee()
    },
    watch: {
        'myIdeaSelectedData.department'(newValue, oldValue) {
            this.myIdeaSelectedData['employee_Name'] = ''
            this.filterEmployee()
        },
    },
    // 以下放变量
    data() {
        return {
            employee_List: [],
            myIdeaSelectedData: {
                department: '',
                employee_Name: '',
            },
            myIdeaDropDownData: {
                department: [],
                employee_Name: [],
            },
            Advantage: '',
            Disadvantage: '',
            Create_By: '',
            loading:false
        }
    },
    //以下搞方法
    methods: {
        async getDepartment() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getDepartment')
            console.log('getDepartment', res)
            _this.myIdeaDropDownData['department'] = res
        },
        async getEmployee() {
            const _this = this
            const {
                data: res
            } = await _this.$http.get('ideabank/getEmployee')
            console.log('getEmployee', res)
            _this.employee_List = res
        },
        filterEmployee() {
            const _this = this
            _this.myIdeaDropDownData['employee_Name'] = []
            for (let i = 0; i < _this.employee_List.length; i++) {
                if (_this.employee_List[i]['line'] === _this.myIdeaSelectedData['department']) {
                    _this.myIdeaDropDownData['employee_Name'].push({ label: _this.employee_List[i]['Employee_PYName'], value: _this.employee_List[i]['Employee_ID'] })
                }

            }
            console.log('selectedEmployee', _this.dropDownData['employee_Name'])
        },

        submitFeedback() {
            const _this = this
            if (_this.Create_By === '' || _this.myIdeaSelectedData['department'] === '' || _this.myIdeaSelectedData['employee_Name'] === '' ||
                (_this.Advantage === '' && _this.Disadvantage === '')) {
                _this.$q.notify({
                    message: '信息不完整，请填写相关信息',
                    type: 'negative',
                    position: 'top',
                    timeout: 2000,
                })
                return
            }
            _this.loading=true
            const data = {
                Employee_ID: _this.myIdeaSelectedData['employee_Name']['value'],
                Employee_Name: _this.myIdeaSelectedData['employee_Name']['label'],
                Advantage: _this.Advantage,
                Disadvantage: _this.Disadvantage,
                Create_By: _this.Create_By,
            }
            console.log('submitFeedback', data)
            _this.$http.post('training/Training_Student_Feedback', data).then(res => {
                console.log('submitFeedback', res)
                if (res.status === 200) {
                    _this.$q.notify({
                        message: '反馈成功',
                        type: 'success',
                        position: 'top',
                        timeout: 2000,
                    })
                } else {
                    _this.$q.notify({
                        message: '提交失败，请稍后再试',
                        type: 'negative',
                        position: 'top',
                        timeout: 2000,
                    })
                }
                _this.loading = false
            })
            
        }



    }
}
</script>
